[tool.poetry]
name = "backend-api-module"
version = "1.0.1"
description = "Backend API module for smart agent"
authors = ["Kotilabs Tech <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.9"
fastapi = "0.116.1"
uvicorn = {extras = ["standard"], version = "0.35.0"}
websockets = "12.0"
pydantic = "2.11.7"
pydantic-settings = "2.10.1"
python-jose = {extras = ["cryptography"], version = "3.5.0"}
passlib = {extras = ["bcrypt"], version = "1.7.4"}
python-multipart = "0.0.6"
httpx = "0.27.2"
structlog = "23.2.0"
python-dotenv = "1.1.0"
PyYAML = "6.0.1"
packaging = ">=23.2,<25.0"

[tool.poetry.group.optional.dependencies]
firebase = ["firebase-admin==6.8.0"]
database = ["couchdb==1.2"]
ai = [
    "langfuse==3.2.1",
    "langchain-core>=0.1.21,<0.2.0",
    "langchain-community>=0.0.20,<0.1.0",
    "langchain-openai>=0.0.5,<0.1.0",
    "langchain-google-genai>=0.0.9,<0.1.0"
]

[tool.poetry.group.dev.dependencies]
black = "23.12.1"
isort = "5.13.2"
mypy = "1.8.0"
pytest-timeout = "2.2.0"
psutil = "5.9.8"
flake8 = "7.0.0"
pre-commit = "3.6.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[project]
name = "aagmanai-backend"
version = "1.0.0"