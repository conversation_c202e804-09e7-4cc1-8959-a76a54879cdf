"""Observability helpers

Initialises a global Langfuse client (v3) that can be imported from anywhere in
`backend_api_module.logic`.  This keeps initialisation DRY and guarantees we
only create one client instance per process.

Usage
-----
>>> from logic.observability import langfuse
>>> with langfuse.start_as_current_span(name="my-operation", metadata={"user_id": "u1"}) as span:
...     span.update(output="result")

The module reads credentials from environment variables:
    LANGFUSE_PUBLIC_KEY, LANGFUSE_SECRET_KEY, LANGFUSE_HOST (optional)
"""
from __future__ import annotations

import os
from dotenv import load_dotenv
from langfuse import Langfuse

# Load .env file explicitly
load_dotenv()

__all__ = ["langfuse"]

langfuse = Langfuse(
    public_key=os.getenv("LANGFUSE_PUBLIC_KEY"),
    secret_key=os.getenv("LANGFUSE_SECRET_KEY"),
    host=os.getenv("LANGFUSE_HOST", "https://cloud.langfuse.com"),
)
