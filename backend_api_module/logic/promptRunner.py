import os
import json
import inspect
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
from logic.promptHandler import load_prompt, render_prompt
from logic.observability import langfuse

# Load environment variables
load_dotenv()


def create_traced_chat_model(model_name: str, provider: str, additional_config: Optional[Dict[str, Any]] = None) -> Any:
    """
    Create a chat model instance
    
    Args:
        model_name: Model name to use
        provider: Provider (openai/gemini)
        additional_config: Additional configuration options
        
    Returns:
        Configured chat model instance
    """
    if additional_config is None:
        additional_config = {}
    
    if provider == 'gemini':
        return ChatGoogleGenerativeAI(
            google_api_key=os.getenv('GOOGLE_API_KEY'),
            model=model_name,
            temperature=0,
            max_output_tokens=1000,
            convert_system_message_to_human=True,
            timeout=30,  # 30 second timeout
            max_retries=2,  # Retry failed requests
            **additional_config
        )
    else:
        return ChatOpenAI(
            api_key=os.getenv('OPENAI_API_KEY'),
            model=model_name,
            temperature=0,
            max_tokens=1000,
            timeout=30,  # 30 second timeout
            max_retries=2,  # Retry failed requests
            **additional_config
        )

def validate_json(json_str: str) -> Dict[str, Any]:
    """
    Validate if a string is valid JSON
    
    Args:
        json_str: String to validate
        
    Returns:
        Dictionary with isValid, parsed, and error keys
    """
    try:
        parsed = json.loads(json_str)
        return {"isValid": True, "parsed": parsed, "error": None}
    except json.JSONDecodeError as error:
        return {"isValid": False, "parsed": None, "error": str(error)}

def handle_openai_error(error: Exception) -> str:
    """Handle OpenAI API errors and return user-friendly messages"""
    print(f'OpenAI API Error: {error}')
    
    error_message = str(error)
    
    # Check for specific error types
    if 'rate_limit_exceeded' in error_message:
        return "I'm currently experiencing high traffic. Please try again in a few moments."
    
    if 'quota_exceeded' in error_message:
        return "I've reached my rate limit. Please wait a moment before trying again."
    
    if 'invalid_request_error' in error_message:
        return "I couldn't process your request. Please try rephrasing your message."
    
    if 'authentication_error' in error_message or 'invalid_api_key' in error_message:
        return "Authentication error. Please check your API configuration."
    
    if 'timeout' in error_message:
        return "Request timed out. Please try again."
    
    if 'network' in error_message:
        return "Network error. Please check your internet connection and try again."
    
    # Default error message
    return "I'm having trouble processing your request right now. Please try again later."

async def generate_response_v2(user_input: str, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Generate response from OpenAI API using LangChain
    
    Args:
        user_input: The user's input
        options: Configuration options
        
    Returns:
        Dictionary containing response and metadata
    """
    if options is None:
        options = {}
    
    start_time = time.time() * 1000  # Convert to milliseconds
    model_name = options.get('model', 'gpt-4o-mini')  # Fast, cost-effective default
    chat_history = options.get('chatHistory', [])
    user_id = options.get('userId', 'unknown')
    session_id = options.get('sessionId', f'session_{int(time.time() * 1000)}')
    provider = options.get('provider', 'openai')
    firebase_uid = options.get('firebase_uid', 'unknown')
    
    # Initialize Langfuse tracing (v3 syntax)
    # Note: user_id is backend's user_id, session_id is backend's conversation_id
    with langfuse.start_as_current_span(
        name=inspect.currentframe().f_code.co_name,
        metadata={"user_id": firebase_uid, "session_id": session_id, "prompt": "TradingPrompt.md"}
    ) as span:
        try:
            print(f'🚀 Generating response with model: {model_name}, provider: {provider}')
            print(f'⏱️ Request start time: {datetime.now().isoformat()}')
            
            # Load the system prompt
            system_prompt = await load_prompt("TradingPrompt")        
            # Create a new chat model instance
            chat_model_for_request = create_traced_chat_model(model_name, provider)
            
            # Build conversation history for context using LangChain message types
            messages = [SystemMessage(content=system_prompt)]
            
            if len(chat_history) > 0:
                # Add recent conversation context
                print(f'Chat history length: {len(chat_history)}')
                for index, msg in enumerate(chat_history):
                    print(f'Processing message {index}: user_message="{msg.get("user_message")}", llm_response type={type(msg.get("llm_response"))}')
                    
                    user_message = msg.get('user_message').get('message',"")
                    llm_response = msg.get('llm_response').get('message',"")
                    
                    if (not user_message or 
                        not isinstance(user_message, str) or 
                        (isinstance(llm_response, dict) and 
                         llm_response.get('primitives', [{}])[0].get('action') == "llmChat")):
                        continue
                    
                    messages.append(HumanMessage(content=user_message))
                    
                    # Only add assistant message if llm_response exists
                    if llm_response:
                        assistant_content = llm_response if isinstance(llm_response, str) else json.dumps(llm_response)
                        print(f'Adding assistant message {index}: content type={type(assistant_content)}')
                        messages.append(AIMessage(content=assistant_content))
                    else:
                        print(f'Skipping assistant message {index}: llm_response is null/undefined')
            
            # Add the current user message
            messages.append(HumanMessage(content=user_input))
            
            # Validate all messages have content
            messages = [msg for msg in messages if msg.content and isinstance(msg.content, str)]
            print(f'Final messages array length: {len(messages)}')
            
            # Update span input with actual messages and additional context
            span.update(
                input={"messages": [msg.content for msg in messages]},
                metadata={
                    "chat_history_length": len(chat_history),
                    "final_messages_count": len(messages),
                    "system_prompt_length": len(system_prompt)
                }
            )
            
            # Call the LLM (OpenAI / Gemini) and capture observability data
            with span.start_as_current_span(name="core_llm_api_call", metadata={"provider": provider, "model_name": model_name}) as llm_span:
                response = await chat_model_for_request.ainvoke(messages)

                assistant_msg = response.content
                end_time = time.time() * 1000
                latency = end_time - start_time

                print(f'✅ LLM Response received in {latency:.2f}ms')
                print(f'📊 Response length: {len(assistant_msg)} characters')

                # Extract usage information from response metadata
                usage = response.response_metadata.get('usage', {}) if hasattr(response, 'response_metadata') else {}
                input_tokens = usage.get('prompt_tokens', 0)
                output_tokens = usage.get('completion_tokens', 0)
                total_tokens = usage.get('total_tokens', 0)

                # Update span outputs & metadata so they show up in Langfuse UI
                llm_span.update(
                    output=assistant_msg,
                    metadata={
                        "latency_ms": latency,
                        "input_tokens": input_tokens,
                        "output_tokens": output_tokens,
                        "total_tokens": total_tokens,
                    }
                )
            
            print(f'Response generated successfully. Latency: {latency}ms, Tokens: {total_tokens}')
            
            # Clean the response
            import re
            cleaned_response = re.sub(r'```json\s*|\s*```', '', assistant_msg).strip()
            
            # Validate if the cleaned response is valid JSON
            json_validation = validate_json(cleaned_response)

            # Update span with final output and validation
            span.update(
                output=cleaned_response,
                metadata={
                    "json_validation": json_validation["isValid"]
                }
            )
            
            # Log JSON parse error if invalid
            if not json_validation["isValid"]:
                print(f'JSON parse error: {json_validation["error"]}')
                print(f'Response content: {cleaned_response[:500]}...')
            
            return {
                "response": cleaned_response,
                "isValidJson": json_validation["isValid"],
                "parsedJson": json_validation["parsed"],
                "metadata": {
                    "latency_ms": latency,
                    "generation_config": {
                        "model": model_name,
                        "temperature": 0
                    },
                    "model_name": model_name,
                    "input_tokens": input_tokens,
                    "output_tokens": output_tokens,
                    "total_tokens": total_tokens,
                    "json_validation": json_validation["isValid"],
                    "session_id": session_id,
                    "user_id": user_id
                }
            }
        
        except Exception as error:
            # Record the error in Langfuse and re-raise
            span.update(status_message=str(error))
            print(f'Error generating LangChain OpenAI V2 response: {error}')
            
            user_friendly_message = handle_openai_error(error)
            raise Exception(user_friendly_message)

async def log_custom_event(event_name: str, event_data: Dict[str, Any], run_id: Optional[str] = None) -> None:
    """
    Log custom event - placeholder for your implementation
    
    Args:
        event_name: Name of the event
        event_data: Event data to log
        run_id: Optional run ID to associate with
    """
    # TODO: Implement your custom event logging logic here
    print(f'Custom event logged: {event_name} - {event_data}')



# Export the functions
__all__ = [
    'generate_response_v2',
    'log_custom_event',
    'create_traced_chat_model',
    'validate_json',
    'load_prompt'
]