import json
import inspect
import os
from datetime import datetime
from typing import Dict, List, Optional, Any

from logic.promptRunner import generate_response_v2 
from logic.promptHandler import load_prompt, render_prompt
from logic.utils.commonUtils import detect_plan_confirmation_from_history, getStaticText, getActions
from logic.utils.summaryUtils import getSummary
from logic.observability import langfuse

# Mock LLM response function for testing
def get_mock_llm_response(message: str, message_type: str = "chat") -> Dict[str, Any]:
    """Generate mock LLM responses for testing to avoid API rate limiting."""
    
    # Simulate processing delay (much faster than real API calls)
    import time
    time.sleep(0.1)
    
    # Generate different responses based on message type and content
    if "test" in message.lower():
        return {
            "response": {
                "textMessage": f"Mock response for test message: {message}",
                "messageType": "order_execution",  # Use valid WebSocketResponseMessageType
                "primitives": [
                    {
                        "action": "llmChat",
                        "arguments": {},
                        "human_friendly_explanation": "This is a mock response for testing",
                        "need_more_info": [],
                        "clarification": "Mock response generated for testing purposes."
                    }
                ],
                "sender": "system",
                "actions": []
            },
            "summary": {
                "summary_text": f"Mock summary for {message_type} conversation",
                "llm_model_version": "mock-llm-v1",
                "meta_json": {
                    "context_topics": ["testing"],
                    "message_count": 1,
                    "broker": "zerodha"
                }
            }
        }
    
    # Default mock response
    return {
        "response": {
            "textMessage": "Mock LLM response for testing",
            "messageType": "order_execution",  # Use valid WebSocketResponseMessageType
            "primitives": [
                {
                    "action": "llmChat",
                    "arguments": {},
                    "human_friendly_explanation": "Mock response for testing",
                    "need_more_info": [],
                    "clarification": "This is a mock response."
                }
            ],
            "sender": "system",
            "actions": []
        },
        "summary": {
            "summary_text": f"Mock conversation summary for {message_type}",
            "llm_model_version": "mock-llm-v1",
            "meta_json": {
                "context_topics": ["general"],
                "message_count": 1,
                "broker": "zerodha"
            }
        }
    }

async def render_chat_mock(message: str, user_id: str, session_id: str,
                       recent_messages: List[Dict], provider: str = "gemini",
                       model_name: str = "gemini-2.0-flash") -> Dict[str, Any]:
    """
    Mock version of render_chat for testing to avoid API rate limiting
    
    Args:
        message: The user's message
        user_id: User identifier
        session_id: Session identifier
        recent_messages: Recent messages from the session
        provider: AI provider name (ignored in mock)
        model_name: Model name to use (ignored in mock)
        
    Returns:
        Dictionary containing the response and metadata
    """
    # Use mock response instead of real LLM call
    return get_mock_llm_response(message, "chat")

async def process_chat_message(message: str, user_id: str, session_id: str, 
                              recent_messages: List[Dict], provider: str, 
                              model_name: str, firebase_uid: str = "unknown") -> Dict:
    """
    Process a chat message and return the response
    
    Args:
        message: The user's message
        user_id: User identifier (backend's user_id = session_id)
        session_id: Session identifier (backend's conversation_id)
        recent_messages: Recent messages from the session
        provider: AI provider name
        model_name: Model name to use
        semantic_user_id: Firebase UID for user tracking in Langfuse
        
    Returns:
        Dictionary containing the response and metadata
    """

    message_type = "order_execution"
    # ────────── Langfuse trace initialisation (v3 syntax) ──────────
    # Note: user_id is backend's user_id, session_id is backend's conversation_id
    with langfuse.start_as_current_span(
        name=inspect.currentframe().f_code.co_name,
        input={"message": message, "recent_messages_len": len(recent_messages)},
        metadata={
            "user_id": firebase_uid,
            "session_id": session_id,
            "model_name": model_name, 
            "provider": provider,
            "message_length": len(message),
            "recent_messages_count": len(recent_messages)
        }
    ) as span:
        # Begin processing logic
        # Check if this message is a response to a plan of action
        with span.start_as_current_span(name="plan_confirmation_detection") as span_detect:
            plan_confirmation_info = detect_plan_confirmation_from_history(recent_messages)
            span_detect.update(output=plan_confirmation_info)
            if plan_confirmation_info:
                with span.start_as_current_span(name="summary_generation") as span_summary:
                    summary = await getSummary(recent_messages,model_name,provider,user_id=user_id, session_id=session_id)
                    span_summary.update(output=summary)
                print('Detected plan confirmation response, routing to plan confirmation handler')
                message_type = "order_confirmation"
                # Load plan confirmation prompt
                prompt_template = await load_prompt("OrderConfirmation")
                # Ensure originalRequest is a string
                original_request_str = (
                    plan_confirmation_info['originalRequest']
                    if isinstance(plan_confirmation_info['originalRequest'], str)
                    else json.dumps(plan_confirmation_info['originalRequest'])
                )
                plan_confirmation_prompt = render_prompt(prompt_template, {
                    'originalRequest': original_request_str,
                    'planDetails': plan_confirmation_info['planDetails'],
                    'message': message
                })

                # Generate response from LLM using the specialized prompt
                llm_response = None
                usage_metadata = None
                options = {
                    'chatHistory': recent_messages,
                    'model': model_name,
                    'userId': user_id,
                    'sessionId': session_id,
                    'provider': provider,
                    'firebase_uid': firebase_uid
                }
                
                try:
                    with span.start_as_current_span(name="plan_confirmation_llm_call") as span_llm:
                        result = await generate_response_v2(plan_confirmation_prompt, options)
                        span_llm.update(output=result.get('response', result))
                    llm_response = result.get('response', result)  # Handle both structured and unstructured responses
                    usage_metadata = result.get('metadata')
                except Exception as llm_error:
                    print(f'LLM API error for plan confirmation: {llm_error}')
                    span.update(status_message=str(llm_error))
                    raise Exception(str(llm_error))

                # Validate that LLM response is valid JSON
                structured = None
                try:
                    structured = json.loads(llm_response)

                    # Validate the response structure
                    if (not structured.get('intent') or 
                        structured['intent'] not in ['agree', 'disagree', 'modify']):
                        raise Exception('Invalid intent in response')
                except json.JSONDecodeError as json_error:
                    print(f'Invalid JSON response from LLM for plan confirmation: {json_error}')
                    print(f'Raw response: {llm_response}')
                    span.update(status_message=str(json_error))
                    raise Exception('Invalid response format from AI service. Please try again.')

                # Save message and response to database
                message_id = None
                current_session_id = session_id
                # Log the validated response
                print(json.dumps(structured, indent=2))

                # Return the plan confirmation response
                response_data = {
                    'success': True,
                    'messageId': message_id,
                    'sessionId': current_session_id,
                    'responseType': 'plan_confirmation', 
                    'intent': structured.get('intent', ''),
                    'reasoning': structured.get('reasoning', ''),
                    'timestamp': datetime.now().isoformat(),
                    'summary': summary
                }
                
                # If user agreed to the plan, include the same primitives from order confirmation
                if structured['intent'] == 'agree' and plan_confirmation_info.get('primitives'):
                    # Put primitives in the response structure where render_chat expects them
                    response_data['response'] = {
                        'primitives': plan_confirmation_info['primitives']
                    }
                    response_data['messageType'] = 'order_execution'
                    print(f"🚀 User agreed - including {len(plan_confirmation_info['primitives'])} primitives for order execution")
                
                # For negative/modify intent: do NOT return plan confirmation intent
                # Instead, route the user's message to the normal function flow for the LLM
                if structured['intent'] in ['disagree', 'modify']:
                    print("⚠️ User did not agree to the plan; routing message to function flow for modification decision")
                    try:
                        with span.start_as_current_span(name="negative_intent_reroute_llm_call") as span_llm:
                            result = await generate_response_v2(message, options)
                            span_llm.update(output=result.get('response', result))
                        llm_response = result.get('response', result)
                    except Exception as llm_error:
                        print(f'LLM API error after negative intent: {llm_error}')
                        span.update(status_message=str(llm_error))
                        raise Exception(str(llm_error))

                    # Validate that LLM response is valid JSON
                    try:
                        json.loads(llm_response)
                    except json.JSONDecodeError as json_error:
                        print(f'Invalid JSON response from LLM (negative intent reroute): {json_error}')
                        print(f'Raw response: {llm_response}')
                        span.update(status_message=str(json_error))
                        raise Exception('Invalid response format from AI service. Please try again.')

                    # Prepare normal chat-like return (without any plan intent fields)
                    structured_reroute = json.loads(llm_response)
                    return_dict = {
                        'success': True,
                        'messageId': message_id,
                        'sessionId': current_session_id,
                        'response': structured_reroute,
                        'timestamp': datetime.now().isoformat(),
                        'message_type': message_type
                    }
                    span.update(output=return_dict)
                    return return_dict
                
                # For agree intent, return the plan confirmation response
                span.update(output=response_data)
                return response_data

            # Normal chat flow - generate response from LLM
            llm_response = None
            usage_metadata = None
            options = {
                'chatHistory': recent_messages,
                'model': model_name,
                'userId': user_id,
                'sessionId': session_id,
                'provider': provider,
                'firebase_uid': firebase_uid
            }
            
            try:
                with span.start_as_current_span(name="chat_llm_call") as span_llm:
                    result = await generate_response_v2(message, options)
                    span_llm.update(output=result.get('response', result))
                llm_response = result.get('response', result)  # Handle both structured and unstructured responses
                usage_metadata = result.get('metadata')
            except Exception as llm_error:
                print(f'LLM API error: {llm_error}')
                span.update(status_message=str(llm_error))
                raise Exception(str(llm_error))

            # Validate that LLM response is valid JSON
            try:
                json.loads(llm_response)
            except json.JSONDecodeError as json_error:
                print(f'Invalid JSON response from LLM: {json_error}')
                print(f'Raw response: {llm_response}')
                span.update(status_message=str(json_error))
                raise Exception('Invalid response format from AI service. Please try again.')

            # Save message and response to database
            message_id = None
            current_session_id = session_id

            # Log the validated JSON response
            structured = json.loads(llm_response)
            print('\n📦 Structured Output:')
            print(json.dumps(structured, indent=2))

            # Return the response with parsed JSON
            return_dict = {
                'success': True,
                'messageId': message_id,
                'sessionId': current_session_id,
                'response': structured,  # Send parsed JSON object instead of string
                'timestamp': datetime.now().isoformat(),
                'message_type': message_type
            }

            span.update(output=return_dict)
            return return_dict

def refactorActions(primitives: List[Dict]) -> List[Dict]:
    """
    Refactor the primitives to be in the format expected by the frontend
    """
    primitives_map = {}
    for primitive in primitives:
            if primitive.get('id'):
                if primitive.get('id') in primitives_map:
                    primitive['id'] = primitives_map[primitive.get('id')]
                else:
                    primitives_map[primitive.get('id')] = os.urandom(16).hex()
            else:
                primitive['id'] = os.urandom(16).hex()
                primitives_map[primitive.get('id')] = primitive.get('id')
            
            if primitive.get('depends_on'):
                # Replace depends_on with existing ID from map if available, otherwise create new
                if primitive.get('depends_on') in primitives_map:
                    primitive['depends_on'] = primitives_map[primitive.get('depends_on')]
                else:
                    new_id = os.urandom(16).hex()
                    primitives_map[primitive.get('depends_on')] = new_id
                    primitive['depends_on'] = new_id
                
    return primitives

async def render_chat(message: str, user_id: str, session_id: str,
                       recent_messages: List[Dict], provider: str = "openai",
                       model_name: str = "gpt-4.1", firebase_uid: str = "unknown") -> Dict[str, Any]:

    """
    Render a chat message and return the response
    
    Args:
        message: The user's message
        user_id: User identifier (backend's user_id = session_id)
        session_id: Session identifier (backend's conversation_id)
        recent_messages: Recent messages from the session
        provider: AI provider name
        model_name: Model name to use
        semantic_user_id: Firebase UID for user tracking in Langfuse
        
    Returns:
        Dictionary containing the response and metadata
    """

    with langfuse.start_as_current_span(
        name=inspect.currentframe().f_code.co_name,
        input={"message": message, "recent_messages_len": len(recent_messages)},
        metadata={"user_id": firebase_uid, "session_id": session_id, "model_name": model_name, "provider": provider}
    ) as span:
        try:
            # ── Span: process_chat_message ─────────────────────────────
            with span.start_as_current_span(name="process_chat_message") as span_proc:
                chat = await process_chat_message(
                    message, user_id, session_id, recent_messages, provider, model_name, firebase_uid
                )
                span_proc.update(output=chat)

            # Post-processing (no external side-effects):
            output = {}
            output['response'] = {}
            response = output['response']
            response['textMessage'], response['messageType'] = getStaticText(chat, message)
            primitives_list = chat.get('response', {}).get('primitives', [])
            response['primitives'] = primitives_list
            # Log primitives separately in span metadata for dashboarding
            if primitives_list:
                span.update(metadata={
                    "primitives_requested": primitives_list
                })
                # Optional: emit an event per primitive for fine-grained analytics
                for idx, prim in enumerate(primitives_list):
                    try:
                        span.create_event(name="primitive_requested", metadata={"index": idx, **prim})
                    except Exception:
                        # fail-safe: event logging should never break the main flow
                        pass
            response['sender'] = "system"
            # response['actions'] = getActions(chat, response)
            chat_response = chat.get('response', {})
            if 'intent' in chat:
                response['intent'] = chat['intent']
                output['intent'] = chat['intent']
            if 'reasoning' in chat_response:
                response['reasoning'] = chat_response.get('reasoning', '')
                output['reasoning'] = chat_response['reasoning']
            if 'timestamp' in chat:
                response['timestamp'] = chat['timestamp']
            if 'summary' in chat:
                output['summary'] = chat['summary']
            if 'group_human_friendly_explanation' in chat_response:
                output['group_human_friendly_explanation'] = chat_response['group_human_friendly_explanation']
            if 'group_clarification_message' in chat_response:
                output['group_clarification_message'] = chat_response['group_clarification_message']

            # Flatten primitives for order_execution
            if chat.get('messageType') == 'order_execution':
                output['messageType'] = 'order_execution'
                output['primitives'] = refactorActions(response.get('primitives', []))
                print(f"🚀 [render_chat] Moving order_execution messageType and {len(response.get('primitives', []))} primitives to top level")

            span.update(output=output)
            return output

        except Exception as err:
            span.update(status_message=str(err))
            raise
