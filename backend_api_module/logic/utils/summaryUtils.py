import json
import os
import re
from datetime import datetime
from typing import Dict, List, Optional, Any
import asyncio


async def getSummary(recent_messages: List[Dict], user_id: Optional[str] = None, session_id: Optional[str] = None) -> str:
    """
    Generate a summary of the conversation history using the summarize_history prompt
    
    Args:
        recent_messages: List of recent messages from the session
        user_id: Optional user identifier to propagate into Langfuse traces
        session_id: Optional session identifier for consistent Langfuse grouping
    Returns:
        str: A concise summary of the conversation
    """
    try:
        # Import here to avoid circular imports
        from logic.promptHandler import load_prompt, render_prompt
        from logic.promptRunner import generate_response_v2
        
        # Load the summarize_history prompt
        prompt_template = await load_prompt("summarize_history")
        
        # Convert recent_messages to a readable format for the LLM
        conversation_text = ""
        for i, msg in enumerate(recent_messages):
            if msg.get('user_message'):
                conversation_text += f"User: {msg['user_message']}\n"
            
            if msg.get('llm_response'):
                llm_response = msg['llm_response']
                if isinstance(llm_response, dict):
                    # Extract text from structured response
                    if 'primitives' in llm_response:
                        # Get human-friendly explanations from primitives
                        explanations = []
                        for primitive in llm_response.get('primitives', []):
                            if isinstance(primitive, dict) and primitive.get('human_friendly_explanation'):
                                explanations.append(primitive['human_friendly_explanation'])
                        if explanations:
                            conversation_text += f"Assistant: {' '.join(explanations)}\n"
                        else:
                            conversation_text += f"Assistant: {json.dumps(llm_response, indent=2)}\n"
                    else:
                        conversation_text += f"Assistant: {json.dumps(llm_response, indent=2)}\n"
                else:
                    conversation_text += f"Assistant: {llm_response}\n"
        
        # Render the prompt with the conversation history
        rendered_prompt = render_prompt(prompt_template, {
            'conversation_history': conversation_text
        })
        
        # Generate summary using the LLM
        options = {
            'model': 'gpt-4o-mini',  # Use a smaller model for summarization
            'provider': 'openai',
            'userId': user_id or 'unknown',
            'sessionId': session_id or f'session_{datetime.now().timestamp()}'
        }
        
        result = await generate_response_v2(rendered_prompt, options)
        
        # Extract the summary from the response
        if isinstance(result, dict) and 'response' in result:
            summary = result['response']
        else:
            summary = str(result)
        
        # Clean up the response (remove any JSON formatting if present)
        cleaned_summary = re.sub(r'```json\s*|\s*```', '', summary).strip()
        
        return cleaned_summary
        
    except Exception as error:
        print(f"Error generating summary: {error}")
        # Return a fallback summary
        return f"Conversation summary unavailable. {len(recent_messages)} messages in history." 