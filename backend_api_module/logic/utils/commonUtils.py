import json
import os
from datetime import datetime
from typing import Dict, List, Optional, Any

def detect_plan_confirmation_from_history(recent_messages: List[Dict]) -> Optional[Dict]:
    """
    Detect if the last message in chat history was a plan of action
    
    Args:
        recent_messages: Array of recent messages from the session
        
    Returns:
        Object with plan details, original request, and executable primitives, or None if not a plan confirmation
    """
    if not recent_messages or len(recent_messages) < 2:
        return None

    # Get the last two messages (user message and LLM response)
    last_message = recent_messages[-1]
    if (not last_message or 
        not last_message.get('llm_response') or 
        last_message['llm_response']['primitives'][0]['action'] == "llmChat" or 
        last_message['llm_response']['primitives'][0]['action'] == "llmChat()"):
        return None  # No LLM response to check
    
    second_last_message = recent_messages[-2]

    # Check if the last message is from LLM and contains explanations (plan of action)
    if last_message.get('llm_response') and second_last_message.get('user_message'):
        try:
            llm_response = last_message['llm_response']['primitives']
            has_need_more_info = False
            explanations = []


            def check_need_more_info(item):
                nonlocal has_need_more_info
                if item and isinstance(item, dict):
                    # Check if need_more_info array exists and is not empty
                    if (item.get('need_more_info') and 
                        isinstance(item['need_more_info'], list) and 
                        len(item['need_more_info']) > 0):
                        has_need_more_info = True

                    # Always collect explanations if available
                    if (item.get('human_friendly_explanation') and 
                        item['human_friendly_explanation'].strip() != ''):
                        explanations.append(item['human_friendly_explanation'])

            # NOTE: Removed problematic dictionary-based checks here since llm_response 
            # is a list (primitives array), not a dictionary. The correct list-based 
            # logic is handled below in lines 68-77.

            # Also check if the response itself is an array with explanations
            if isinstance(llm_response, list):
                for item in llm_response:
                    check_need_more_info(item)

                # If no need_more_info found and we have explanations, treat as plan of action
                if not has_need_more_info and len(explanations) > 0:
                    return {
                        'planDetails': '\n'.join(explanations),
                        'originalRequest': second_last_message['user_message'],
                        'primitives': llm_response  # Just use the same primitives
                    }

            # Check if response has a response field that's an array
            if (isinstance(llm_response, dict) and 
                llm_response.get('response') and 
                isinstance(llm_response['response'], list)):
                for item in llm_response['response']:
                    check_need_more_info(item)

                # If no need_more_info found and we have explanations, treat as plan of action
                if not has_need_more_info and len(explanations) > 0:
                    return {
                        'planDetails': '\n'.join(explanations),
                        'originalRequest': second_last_message['user_message'],
                        'primitives': llm_response['response']
                    }

            # Check if response has a data field that might contain explanations
            if (isinstance(llm_response, dict) and 
                llm_response.get('data') and 
                isinstance(llm_response['data'], list)):
                for item in llm_response['data']:
                    check_need_more_info(item)

                # If no need_more_info found and we have explanations, treat as plan of action
                if not has_need_more_info and len(explanations) > 0:
                    return {
                        'planDetails': '\n'.join(explanations),
                        'originalRequest': second_last_message['user_message'],
                        'primitives': llm_response['data']
                    }

        except Exception as error:
            # If parsing fails, it's not a JSON response, so no plan confirmation
            print(f'Failed to parse LLM response for plan detection: {error}')

    return None

def getStaticText(chat: Dict[str, Any], user_message: str) -> tuple[str, str]:
    """
    Get static text response based on chat response type

    Args:
        chat: Chat response dictionary
        user_message: The user's message

    Returns:
        tuple[str, str]: Static text response and response type
    """
    # First check if intent is present in chat response
    if "intent" in chat:
        return "Order is being executed", "order_execution"
    
    # Check if there's a response with primitives
    if 'response' in chat and isinstance(chat['response'], dict):
        primitives = chat['response'].get('primitives', [])
        
        # Check if any primitive has need_more_info that is not empty
        has_empty_need_more_info = True
        for primitive in primitives:
            if primitive.get('action') == 'llmChat':
                has_empty_need_more_info = False
                break
            if isinstance(primitive, dict) and 'need_more_info' in primitive:
                need_more_info = primitive['need_more_info']
                if isinstance(need_more_info, list):
                    if len(need_more_info) > 0:
                        has_empty_need_more_info = False
                        break
        
        # If need_more_info is an empty array, this is order confirmation
        if has_empty_need_more_info :
            return "Please confirm the order", "order_confirmation"
        else:
            # If there is need_more_info with items or missing need_more_info, this is order planning
            return "Please Clarify the following:", "order_planning"  
    
    # Fallback: return order planning
    return "Please Clarify the following:", "order_planning"
    



def getActions(chat: Dict[str, Any], response: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Get actions from chat response

    Args:
        chat: Chat response dictionary
        response: Response dictionary containing message_type

    Returns:
        List of actions (buttons)
    """
    actions = []
    
    # Check if response type is order_confirmation
    if response.get('messageType') == 'order_confirmation':
        actions = [
            {
                "description": "Yes",
                "type": "chat",
                "message": "agree"
            },
            {
                "description": "No", 
                "type": "chat",
                "message": "disagree"
            }
        ]
    else:
        # Check if productType is needed in the response
        if 'response' in chat and isinstance(chat['response'], dict):
            primitives = chat['response'].get('primitives', [])
            for primitive in primitives:
                if isinstance(primitive, dict) and 'need_more_info' in primitive:
                    need_more_info = primitive['need_more_info']
                    if isinstance(need_more_info, list) and 'productType' in need_more_info:
                        actions = [
                            {
                                "description": "Intraday",
                                "type": "chat", 
                                "message": "productType: MIS"
                            },
                            {
                                "description": "Delivery",
                                "type": "chat",
                                "message": "productType: CNC"
                            }
                        ]
                        break
    
    return actions