# Unified Backend API and Data Layer Setup

This document provides instructions for setting up both the backend API module and data layer module in a unified environment.

> **⚠️ Important**: This project requires Python virtual environments. Please follow the setup instructions below to create and activate virtual environments before running any Python commands.

## Overview

The Smart Agent platform consists of two main backend modules:

- **Backend API Module**: FastAPI-based REST API and WebSocket service
- **Data Layer Module**: CouchDB integration and data management

## Prerequisites

Before you begin, ensure you have `uv` installed. `uv` is a fast Python package installer and resolver.

### Required API Keys

The backend API requires LLM API keys for chat functionality:

- **OpenAI API Key**: Required for OpenAI models (gpt-4, gpt-3.5-turbo, gpt-4o-mini)
- **Gemini API Key**: Optional, for Google Gemini models (gemini-pro)

You can get these API keys from:

- **OpenAI**: https://platform.openai.com/api-keys
- **Google AI Studio**: https://makersuite.google.com/app/apikey

### Installing uv

**On macOS and Linux:**

```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

**On Windows:**

```powershell
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

**Using pip:**

```bash
pip install uv
```

### System Requirements

- Python 3.9 or higher
- CouchDB server running locally or remotely
- Firebase project (for production)
- Google Cloud service account credentials (for production)

## Local CouchDB Setup with Docker

For local development, you can quickly set up CouchDB using Docker:

```bash
# Run CouchDB container
docker run -d --name couchdb \
  -p 5984:5984 \
  -e COUCHDB_USER=admin \
  -e COUCHDB_PASSWORD=password \
  apache/couchdb:3

# Check if container is running
docker ps

# View logs if needed
docker logs couchdb
```

**CouchDB Management Interface**: Once the container is running, you can access the CouchDB management interface at:

- **URL**: http://127.0.0.1:5984/_utils/
- **Username**: admin (or your chosen username)
- **Password**: password (or your chosen password)

**Note**: Update your `.env` file with the credentials you used in the Docker command:

```bash
COUCHDB_USERNAME=admin
COUCHDB_PASSWORD=password
COUCHDB_HOST=localhost
COUCHDB_PORT=5984
COUCHDB_DATABASE=aagmanai
COUCHDB_USE_SSL=false
```

**Useful Docker Commands**:

```bash
# Stop the CouchDB container
docker stop couchdb

# Start the container again
docker start couchdb

# Remove the container (data will be lost)
docker rm couchdb

# View container logs
docker logs couchdb

# Access CouchDB container shell
docker exec -it couchdb bash
```

## Unified Setup

### 1. Clone and Setup Repository

```bash
git clone <repository-url>
cd smart-agent
```

### 2. Data Layer Setup

First, set up the data layer module:

```bash
cd data_layer_v3

# Create virtual environment with uv
uv venv --seed

# Activate the virtual environment
source .venv/bin/activate  # On macOS/Linux
# .venv\Scripts\activate   # On Windows

# Install dependencies
uv pip install -r requirements.txt

# Copy environment template
cp env.example .env

# Edit .env with your CouchDB credentials
# See data_layer_v3/README.md for detailed configuration
```

### 3. Backend API Setup

```bash
cd ../backend_api_module

# Create virtual environment with uv
uv venv --seed

# Activate the virtual environment
source .venv/bin/activate  # On macOS/Linux
# .venv\Scripts\activate   # On Windows

# Install dependencies
uv pip install -r requirements.txt

# Copy environment template
cp env.example .env

# Edit .env with your configuration
# Make sure to add your OpenAI API key:
# OPENAI_API_KEY=your_openai_api_key_here
```

### 4. Environment Configuration

#### Backend API Configuration

Ensure your `backend_api_module/.env` contains:

```bash
# LLM Configuration (Required)
OPENAI_API_KEY=your_openai_api_key_here

# Optional: Gemini API Key (for Google Gemini models)
# GEMINI_API_KEY=your_gemini_api_key_here
```

#### Data Layer Configuration

The backend API automatically loads CouchDB configuration from `data_layer_v3/.env`. Ensure your `data_layer_v3/.env` contains:

```bash
# CouchDB Configuration
COUCHDB_HOST=localhost
COUCHDB_PORT=5984
COUCHDB_USERNAME=your_username
COUCHDB_PASSWORD=your_password
COUCHDB_DATABASE=aagmanai
COUCHDB_USE_SSL=false
```

### 5. Running the Services

#### Option A: Separate Processes

```bash
# Terminal 1: Start data layer services (if needed)
cd data_layer_v3
source .venv/bin/activate  # On macOS/Linux
# .venv\Scripts\activate   # On Windows
python -m your_data_layer_service

# Terminal 2: Start backend API
cd backend_api_module
source .venv/bin/activate  # On macOS/Linux
# .venv\Scripts\activate   # On Windows
python -m src.main
```

#### Option B: Using the Runner Script

```bash
cd backend_api_module

# Ensure your virtual environment is activated
source .venv/bin/activate  # On macOS/Linux
# .venv\Scripts\activate   # On Windows

# Standard execution (no hot reload)
./run_backend.sh

# With hot reload (development mode)
./run_backend.sh --reload
```

## API Endpoints

Once running, the backend API provides:

- **REST API**: http://localhost:8000/api/v1/
- **WebSocket**: ws://localhost:8000/ws/chat
- **Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

## Development Workflow

1. **Local Development**: Set `APP_ENV=local` for mock authentication
2. **Production Testing**: Set `APP_ENV=production` with Firebase credentials
3. **Database Changes**: Update schemas in `data_layer_v3/tables_schema/`
4. **API Changes**: Modify endpoints in `backend_api_module/src/api/routes.py`

## Virtual Environment Best Practices

### Why Use Virtual Environments?

Virtual environments isolate your project dependencies from your system Python installation, preventing conflicts between different projects and ensuring reproducible builds.

### Managing Virtual Environments with uv

```bash
# Create a new virtual environment
uv venv --seed

# Activate the virtual environment
source .venv/bin/activate  # On macOS/Linux
# .venv\Scripts\activate   # On Windows

# Deactivate when done
deactivate

# Remove the virtual environment
rm -rf .venv
```

### Working with Multiple Modules

```bash
# Each module should have its own virtual environment
cd data_layer_v3
uv venv --seed
source .venv/bin/activate
# Work on data layer

cd ../backend_api_module
uv venv --seed
source .venv/bin/activate
# Work on backend API
```

### Checking Your Environment

```bash
# Check if you're in a virtual environment
which python
# Should show: /path/to/your/project/.venv/bin/python

# Check installed packages
uv pip list

# Check Python version
python --version
```

## Troubleshooting

### Common Issues

1. **LLM API errors**: Ensure `OPENAI_API_KEY` is set correctly in your `backend_api_module/.env` file
2. **CouchDB Connection**: Ensure CouchDB is running and credentials are correct
3. **Environment Variables**: Check that both `data_layer_v3/.env` and `backend_api_module/.env` are properly configured
4. **Dependencies**: Ensure all requirements are installed in both modules
5. **Port Conflicts**: Verify ports 8000 (API) and 5984 (CouchDB) are available
6. **Virtual Environment**: Ensure you're in the correct virtual environment for each module

### Debug Mode

Enable debug logging:

```bash
export LOG_LEVEL=DEBUG
export DEBUG=true
```

## Architecture

```
smart-agent/
├── backend_api_module/     # FastAPI REST API + WebSocket
│   ├── src/
│   │   ├── api/           # API routes
│   │   ├── services/      # WebSocket and connection services
│   │   └── models/        # Pydantic schemas
│   └── logic/             # LLM integration
└── data_layer_v3/         # CouchDB integration
    ├── datastore.py       # CouchDB data store functions
    └── tables_schema/     # Database schemas
```

## Dependency Management

### Using uv (Recommended)

Both modules use `uv` for fast dependency management:

```bash
# Data Layer Module
cd data_layer_v3
uv pip install -r requirements.txt

# Backend API Module
cd ../backend_api_module
uv pip install -r requirements.txt
```

### Alternative: Using pip

If you prefer to use pip:

```bash
# Data Layer Module
cd data_layer_v3
pip install -r requirements.txt

# Backend API Module
cd ../backend_api_module
pip install -r requirements.txt
```

## LLM Configuration

The backend API supports multiple LLM providers for chat functionality and trading assistance.

### Required Setup

1. **Get an OpenAI API key**:

   - Visit https://platform.openai.com/api-keys
   - Create a new API key
   - Add it to your `backend_api_module/.env` file:
     ```bash
     OPENAI_API_KEY=sk-your-actual-api-key-here
     ```

2. **Optional: Get a Gemini API key**:
   - Visit https://makersuite.google.com/app/apikey
   - Create a new API key
   - Add it to your `backend_api_module/.env` file:
     ```bash
     GEMINI_API_KEY=your-gemini-api-key-here
     ```

### Supported Models

- **OpenAI**: `gpt-4o-mini` (default), `gpt-4`, `gpt-3.5-turbo`
- **Gemini**: `gemini-pro`

### Model Selection

The API supports model selection through the request payload:

```json
{
  "message": "Your message here",
  "options": {
    "model": "gpt-4o-mini",
    "provider": "openai"
  }
}
```

## Next Steps

- Configure Firebase for production authentication
- Set up monitoring and logging
- Deploy to your preferred hosting platform
- Configure CI/CD pipelines

For detailed documentation on individual modules:

- [Backend API Module README](README.md)
- [Data Layer Module README](../data_layer_v3/README.md)
