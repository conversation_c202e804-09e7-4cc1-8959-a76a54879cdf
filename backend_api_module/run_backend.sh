#!/bin/bash

# Backend API Module Runner Script
# This script sets up the virtual environment and runs the backend server
#
# Usage:
#   ./run_backend.sh          # Standard execution (no hot reload)
#   ./run_backend.sh --reload # Development mode with hot reload

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

print_status "Starting Backend API Module setup..."

# Check if Python 3 is available
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is not installed or not in PATH"
    exit 1
fi

PYTHON_VERSION=$(python3 --version)
print_status "Using $PYTHON_VERSION"

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    print_warning "Virtual environment not found. Creating new virtual environment..."
    
    # Create virtual environment
    python3 -m venv .venv
    print_success "Virtual environment created successfully"
else
    print_success "Virtual environment found"
fi

# Activate virtual environment
print_status "Activating virtual environment..."
source .venv/bin/activate

# Check if requirements.txt exists
if [ ! -f "requirements.txt" ]; then
    print_error "requirements.txt not found in $(pwd)"
    exit 1
fi

# Check for .env file
if [ ! -f ".env" ]; then
    print_warning ".env file not found"
    print_warning "Creating .env from env.example if available..."
    if [ -f "env.example" ]; then
        cp env.example .env
        print_success "Created .env from env.example"
        print_warning "Please review and update .env with your configuration"
    else
        print_warning "env.example not found - you may need to create .env manually"
    fi
else
    print_success ".env file found"
fi

# Install/upgrade pip
print_status "Upgrading pip..."
python -m pip install --upgrade pip

# Install dependencies
print_status "Installing dependencies from requirements.txt..."
pip install -r requirements.txt
print_success "Dependencies installed successfully"

# Check if main.py exists
if [ ! -f "src/main.py" ]; then
    print_error "src/main.py not found"
    exit 1
fi

# Check for data layer environment file
DATA_LAYER_ENV="../data_layer_v3/.env"
if [ ! -f "$DATA_LAYER_ENV" ]; then
    print_warning "data_layer_v3/.env not found at $DATA_LAYER_ENV"
    print_warning "CouchDB configuration may not be available"
    print_warning "Please ensure data_layer_v3 module is set up properly"
else
    print_success "Data layer environment file found"
fi

# Set default environment variables if not already set
if [ -z "$APP_ENV" ]; then
    export APP_ENV=local
    print_status "Setting APP_ENV=local (default)"
fi

# Ensure OpenTelemetry service name for Langfuse traces
if [ -z "$OTEL_SERVICE_NAME" ]; then
    export OTEL_SERVICE_NAME="backend_api"
    print_status "Setting OTEL_SERVICE_NAME=backend_api for Langfuse resource attr"
fi

if [ -z "$JWT_SECRET_KEY" ]; then
    export JWT_SECRET_KEY="your-secret-key-change-in-production"
    print_status "Setting default JWT_SECRET_KEY"
fi

# Validate Langfuse keys presence (warn only)
if [ -z "$LANGFUSE_PUBLIC_KEY" ] || [ -z "$LANGFUSE_SECRET_KEY" ]; then
    print_warning "LANGFUSE_PUBLIC_KEY / LANGFUSE_SECRET_KEY not set – Langfuse client will run in noop mode"
fi

print_success "Setup completed successfully!"
print_status "Starting backend server..."

# Display startup information
print_status "Backend API will be available at:"
echo "  - API Documentation: http://localhost:8000/docs"
echo "  - Health Check: http://localhost:8000/health"
echo "  - WebSocket: ws://localhost:8000/ws/chat"
echo ""

# Check if hot reload is requested
if [ "$1" = "--reload" ] || [ "$1" = "-r" ]; then
    print_status "Starting with hot reload (development mode)..."
    uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
else
    print_status "Running backend server from src.main module..."
    python -m src.main
fi 