[tool:pytest]
# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Markers for different test types
markers =
    unit: Unit tests for individual functions and methods
    integration: Integration tests for component interactions
    performance: Performance and load tests
    security: Security vulnerability tests
    edge_cases: Edge case tests for boundary conditions
    concurrency: Concurrency and threading tests
    real_data: Tests that use real CouchDB data
    mock: Tests that use mocked data (deprecated - use real data instead)
    slow: Tests that take longer to run
    websocket: WebSocket-specific tests

# Test execution options
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --timeout=30
    --timeout-method=thread

# Coverage configuration (when using --cov)
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

# Environment variables for testing
env =
    COUCHDB_USERNAME=admin
    COUCHDB_PASSWORD=123
    COUCHDB_HOST=localhost
    COUCHDB_PORT=5984
    COUCHDB_DATABASE=aagmanai
    COUCHDB_USE_SSL=false
    MOCK_LLM_RESPONSES=true
    MOCK_LLM_RESPONSE_TYPE="orders" # or "monitoring"
    PYTHONPATH=src 