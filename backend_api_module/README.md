# Smart Agent Backend API

A FastAPI-based backend service for the Smart Agent trading platform with Firebase Authentication support.

> **⚠️ Important**: This project requires a Python virtual environment. Please follow the setup instructions below to create and activate a virtual environment before running any Python commands.

## 🚀 Quick Start (Unified Environment)

For a unified setup that includes both the backend API and data layer modules, see [README_UNIFIED.md](README_UNIFIED.md).

This provides a single environment where both modules can run together with all dependencies managed in one place.

## Features

- **Environment-Specific Authentication**:
  - Production: Firebase Admin SDK authentication
  - Local Development: Mock authentication for seamless development
- **RESTful API**: Complete REST API for chat, orders, monitoring, and notifications
- **WebSocket Support**: Real-time communication for chat and notifications
- **Data Layer Integration**: Integration with the data layer module
- **Structured Logging**: Comprehensive logging with structlog
- **Observability**: Langfuse v3 traces for every LLM call (see "Observability" section)
- **LLM Integration**: LangChain-based chat responses and trading assistance
- **CouchDB Integration**: Automatic configuration loading from data layer module

## Environment Configuration

The application uses the `APP_ENV` environment variable to control behavior:

### Local Development (APP_ENV=local)

- Authentication is bypassed
- Mock user is automatically provided
- No Firebase credentials required
- All endpoints work without authentication headers

### Production (APP_ENV=production)

- Full Firebase Authentication
- Firebase ID tokens are validated
- Requires Firebase Admin SDK configuration
- All endpoints require valid Firebase tokens

## Quick Start

### Prerequisites

Before you begin, ensure you have `uv` installed. `uv` is a fast Python package installer and resolver.

#### Required API Keys

The backend API requires LLM API keys for chat functionality:

- **OpenAI API Key**: Required for OpenAI models (gpt-4, gpt-3.5-turbo, gpt-4o-mini)
- **Gemini API Key**: Optional, for Google Gemini models (gemini-pro)

You can get these API keys from:

- **OpenAI**: https://platform.openai.com/api-keys
- **Google AI Studio**: https://makersuite.google.com/app/apikey

#### Installing uv

**On macOS and Linux:**

```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

**On Windows:**

```powershell
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

**Using pip:**

```bash
pip install uv
```

### System Requirements

- Python 3.9 or higher
- CouchDB server (local or remote)
- Firebase project (for production)
- Google Cloud service account credentials (for production)
- Data layer module setup (see [README_UNIFIED.md](README_UNIFIED.md))

### Local CouchDB Setup with Docker

For local development, you can quickly set up CouchDB using Docker:

```bash
# Run CouchDB container
docker run -d --name couchdb \
  -p 5984:5984 \
  -e COUCHDB_USER=admin \
  -e COUCHDB_PASSWORD=password \
  apache/couchdb:3

# Check if container is running
docker ps

# View logs if needed
docker logs couchdb
```

**CouchDB Management Interface**: Once the container is running, you can access the CouchDB management interface at:

- **URL**: http://127.0.0.1:5984/_utils/
- **Username**: admin (or your chosen username)
- **Password**: password (or your chosen password)

**Note**: Update your `data_layer_v3/.env` file with the credentials you used in the Docker command:

```bash
COUCHDB_USERNAME=admin
COUCHDB_PASSWORD=password
COUCHDB_HOST=localhost
COUCHDB_PORT=5984
COUCHDB_DATABASE=aagmanai
COUCHDB_USE_SSL=false
```

**Useful Docker Commands**:

```bash
# Stop the CouchDB container
docker stop couchdb

# Start the container again
docker start couchdb

# Remove the container (data will be lost)
docker rm couchdb

# View container logs
docker logs couchdb

# Access CouchDB container shell
docker exec -it couchdb bash
```

### Installation

1. **Clone the repository**:

```bash
git clone <repository-url>
cd smart-agent/backend_api_module
```

2. **Create and activate a virtual environment**:

```bash
# Create a virtual environment with uv
uv venv --seed

# Activate the virtual environment
# On macOS/Linux:
source .venv/bin/activate
# On Windows:
.venv\Scripts\activate
```

3. **Install dependencies**:

```bash
   # Install all dependencies
   uv pip install -r requirements.txt
```

### Environment Setup

#### Local Development

1. **Ensure your virtual environment is activated**:

```bash
source .venv/bin/activate  # On macOS/Linux
# .venv\Scripts\activate   # On Windows
```

2. **Copy the environment template**:

```bash
cp env.example .env
```

2. **Edit the .env file** with your configuration:

```bash
# Application Environment
APP_ENV=local

# Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=false
LOG_LEVEL=INFO

# JWT Configuration (for local development)
JWT_SECRET_KEY=your-secret-key-change-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# LLM Configuration (Required)
OPENAI_API_KEY=your_openai_api_key_here

# Optional: Gemini API Key (for Google Gemini models)
# GEMINI_API_KEY=your_gemini_api_key_here
```

3. **Configure CouchDB** (required):

The backend API automatically loads CouchDB configuration from `data_layer_v3/.env`. Ensure you have set up the data layer module first:

```bash
# In data_layer_v3/.env
COUCHDB_HOST=localhost
COUCHDB_PORT=5984
COUCHDB_USERNAME=your_actual_username
COUCHDB_PASSWORD=your_actual_password
COUCHDB_DATABASE=aagmanai
COUCHDB_USE_SSL=false
```

**Important**: Never commit the `.env` file to version control. It contains sensitive credentials.

#### Production

Set environment variables for production:

```bash
export APP_ENV=production
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/service-account-key.json"
```

### Running the Application

#### Local Development

**Option 1: With Hot Reload (Recommended for Development)**

```bash
# Ensure your virtual environment is activated
source .venv/bin/activate  # On macOS/Linux
# .venv\Scripts\activate   # On Windows

# Set environment
export APP_ENV=local

# Start the server with hot reload
uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
```

**Option 2: Direct Python Execution**

```bash
# Ensure your virtual environment is activated
source .venv/bin/activate  # On macOS/Linux
# .venv\Scripts\activate   # On Windows

# Set environment
export APP_ENV=local

# Start the server
python -m src.main
```

#### Production

```bash
# Ensure your virtual environment is activated
source .venv/bin/activate  # On macOS/Linux
# .venv\Scripts\activate   # On Windows

# Set environment
export APP_ENV=production
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/service-account-key.json"

# Start the server
python -m src.main
```

#### Using the Runner Script

```bash
# Ensure your virtual environment is activated
source .venv/bin/activate  # On macOS/Linux
# .venv\Scripts\activate   # On Windows

# Standard execution (no hot reload)
./run_backend.sh

# With hot reload (development mode)
./run_backend.sh --reload
```

**Note**: The runner script supports both modes. Use `--reload` flag for development with hot reload functionality.

## API Documentation

Once the server is running, you can access:

- **Interactive API Docs**: http://localhost:8000/docs
- **OpenAPI Schema**: http://localhost:8000/openapi.json
- **Health Check**: http://localhost:8000/health

## Authentication

### Local Development Mode

In local development mode (`APP_ENV=local`):

- Authentication is completely bypassed
- A mock user is automatically provided (`test-user-123`)
- No authentication headers are required for API calls
- All endpoints work seamlessly for frontend development
- Simplified user ID extraction for testing purposes

### Production Mode

In production mode (`APP_ENV=production`):

- Firebase Admin SDK validates Firebase ID tokens
- All protected endpoints require a valid `Authorization: Bearer <token>` header
- User information is extracted from the Firebase token
- Invalid tokens return 401 Unauthorized errors

## API Endpoints

### Chat History

- `POST /api/v1/chatHistory` - Get chat history for a conversation
- `POST /api/v1/orders` - Get user orders
- `POST /api/v1/monitoring/instances` - Get monitoring instances

### WebSocket Endpoints

- `ws://localhost:8000/ws/chat` - Chat WebSocket with real-time messaging

### Authentication

- **Local Development**: Authentication is bypassed, mock user provided
- **Production**: Firebase ID token validation required

## LLM Configuration

The backend API supports multiple LLM providers for chat functionality and trading assistance.

### OpenAI Configuration

**Required for chat functionality**

1. **Get an OpenAI API key**:

   - Visit https://platform.openai.com/api-keys
   - Create a new API key
   - Copy the key (it starts with `sk-`)

2. **Add to your .env file**:

   ```bash
   OPENAI_API_KEY=sk-your-actual-api-key-here
   ```

3. **Supported models**:
   - `gpt-4o-mini` (default, recommended)
   - `gpt-4`
   - `gpt-3.5-turbo`

### Gemini Configuration (Optional)

**For Google Gemini models**

1. **Get a Gemini API key**:

   - Visit https://makersuite.google.com/app/apikey
   - Create a new API key
   - Copy the key

2. **Add to your .env file**:

   ```bash
   GEMINI_API_KEY=your-gemini-api-key-here
   ```

3. **Supported models**:
   - `gemini-pro`

### Model Selection

The API supports model selection through the request payload:

```json
{
  "message": "Your message here",
  "options": {
    "model": "gpt-4o-mini",
    "provider": "openai"
  }
}
```

**Available providers**:

- `openai`: Uses OpenAI models (requires `OPENAI_API_KEY`)
- `gemini`: Uses Google Gemini models (requires `GEMINI_API_KEY`)

## Firebase Configuration

### Setting up Firebase for Production

1. **Create a Firebase project** at https://console.firebase.google.com/
2. **Generate service account credentials**:
   - Go to Project Settings > Service Accounts
   - Click "Generate new private key"
   - Save the JSON file securely
3. **Set the environment variable**:
   ```bash
   export GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/service-account-key.json"
   ```

### Firebase Admin SDK Initialization

The application automatically initializes Firebase Admin SDK in production mode:

- If `GOOGLE_APPLICATION_CREDENTIALS` is set, uses service account credentials
- If not set, uses default credentials (for Google Cloud environments)

## Development Workflow

### Local Development

1. Set `APP_ENV=local`
2. Start the backend server
3. Frontend can make API calls without authentication
4. All endpoints work with mock user

### Production Testing

1. Set `APP_ENV=production`
2. Configure Firebase credentials
3. Frontend must authenticate with Firebase
4. All API calls require valid Firebase tokens

## Testing

Run the test suite:

```bash
# Ensure your virtual environment is activated
source .venv/bin/activate  # On macOS/Linux
# .venv\Scripts\activate   # On Windows

pytest
```

Run with coverage:

```bash
# Ensure your virtual environment is activated
source .venv/bin/activate  # On macOS/Linux
# .venv\Scripts\activate   # On Windows

pytest --cov=src
```

## Environment Variables

| Variable                          | Description                                     | Required        | Default   |
| --------------------------------- | ----------------------------------------------- | --------------- | --------- |
| `APP_ENV`                         | Environment mode (`local` or `production`)      | Yes             | `local`   |
| `JWT_SECRET_KEY`                  | JWT secret key (local only)                     | Local only      | -         |
| `JWT_ALGORITHM`                   | JWT algorithm (local only)                      | No              | `HS256`   |
| `JWT_ACCESS_TOKEN_EXPIRE_MINUTES` | JWT expiry (local only)                         | No              | `30`      |
| `OPENAI_API_KEY`                  | OpenAI API key for LLM functionality            | Yes             | -         |
| `GEMINI_API_KEY`                  | Google Gemini API key (optional)                | No              | -         |
| `GOOGLE_APPLICATION_CREDENTIALS`  | Firebase service account path (production only) | Production only | -         |
| `HOST`                            | Server host                                     | No              | `0.0.0.0` |
| `PORT`                            | Server port                                     | No              | `8000`    |
| `DEBUG`                           | Debug mode                                      | No              | `False`   |
| `LOG_LEVEL`                       | Logging level                                   | No              | `INFO`    |

| `WEBSOCKET_PING_INTERVAL` | WebSocket ping interval (seconds) | No | `20` |
| `WEBSOCKET_PING_TIMEOUT` | WebSocket ping timeout (seconds) | No | `20` |

**Note**: CouchDB configuration is automatically loaded from `data_layer_v3/.env` file.

## Observability

The backend captures detailed execution traces for every LLM request using **Langfuse&nbsp;v3**.

1. **Environment variables** required (add these to your `.env`):

   | Variable              | Description                                                        |
   | --------------------- | ------------------------------------------------------------------ |
   | `LANGFUSE_PUBLIC_KEY` | Langfuse public key                                                |
   | `LANGFUSE_SECRET_KEY` | Langfuse secret key                                                |
   | `LANGFUSE_HOST`       | Langfuse host (optional, defaults to `https://cloud.langfuse.com`) |

2. **What gets recorded?**

   - Top-level trace per user request (`generate_response_v2`) identified by `session_id` and `user_id`.
   - **Input**: full array of conversation messages plus prompt template name.
   - **Spans**: 
     - `core_llm_api_call`: raw LLM output, latency, and token usage from the API
     - `chat_llm_call`: normal conversation LLM calls in askLLM.py
     - `plan_confirmation_llm_call`: order confirmation LLM calls in askLLM.py
   - **Output**: cleaned assistant reply and JSON-validation status.
   - Errors are flagged automatically via `trace.set_error`.

3. **Viewing traces**

   Log in to your Langfuse workspace and filter by `session_id` to see all requests that belong to the same chat session.

---

## Troubleshooting

### Common Issues

1. **LLM API errors**: Ensure `OPENAI_API_KEY` is set correctly in your `.env` file
2. **Firebase not initialized**: Ensure `GOOGLE_APPLICATION_CREDENTIALS` is set correctly
3. **Import errors**: Make sure `firebase-admin` is installed (`pip install firebase-admin`)
4. **Authentication errors**: Verify environment variables are set correctly
5. **CORS issues**: Check that CORS middleware is configured properly

### Debug Mode

Enable debug logging by setting:

```bash
export LOG_LEVEL=DEBUG
```

## Architecture

The application follows a modular architecture:

- **API Layer**: FastAPI routes and WebSocket handlers (`src/api/`)
- **Authentication**: Environment-specific authentication logic
- **Services**: WebSocket services and connection management (`src/services/`)
- **Models**: Pydantic schemas for request/response validation (`src/models/`)
- **Utils**: Configuration and data mapping utilities (`src/utils/`)
- **Logic**: LLM integration and prompt handling (`logic/`)
- **Data Layer Integration**: Automatic loading from `data_layer_v3` module

### Key Dependencies

- **FastAPI**: Web framework and API documentation
- **WebSockets**: Real-time communication
- **Pydantic**: Data validation and serialization
- **Structlog**: Structured logging
- **Firebase Admin**: Production authentication
- **LangChain**: LLM integration for chat responses
- **CouchDB**: Data persistence (via data_layer_v3)

### Dependency Management with uv

This project uses `uv` for fast dependency management. The dependencies are defined in `pyproject.toml` and can be installed using:

```bash
# Install all dependencies
uv pip install -r requirements.txt

# Install specific dependencies
uv pip install fastapi uvicorn websockets
```

### Alternative: Using pip

If you prefer to use pip instead of uv:

```bash
# Install from requirements.txt
pip install -r requirements.txt
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License.
