"""WebSocket service for isolated 1:1 chat functionality."""

import json
import logging
import uuid
import asyncio
import os
import inspect
from datetime import datetime
from typing import Dict, Optional, List, Any
from logic.askLLM import render_chat, render_chat_mock
from logic.observability import langfuse
from fastapi import WebSocket, WebSocketDisconnect

from ..models.schemas import (
    WebSocketChatRequest, 
    WebSocketChatResponse, 
    WebSocketAction,
    WebSocketMessageType,
    WebSocketResponseMessageType,
    BrokerName
)
from .connection_manager import connection_manager
# Import Firebase authentication middleware
from ..middleware.auth_middleware import verify_firebase_token_websocket
# Import CouchDB-compatible functions from data_layer_v3
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'data_layer_v3'))
from sql_queries import (
    extract_chat_history,
    save_chat_message,
    fetch_summary,
    save_summary
)
# Don't need data mapper functions as CouchDB functions handle data preparation internally


class WebSocketChatService:
    """WebSocket service for handling isolated 1:1 chat streams."""
    
    def __init__(self):
        """Initialize the WebSocket chat service."""
        # Active WebSocket connections: user_id -> WebSocket
        self.active_connections: Dict[str, WebSocket] = {}
        # Connection metadata: WebSocket -> user_info  
        self.connection_metadata: Dict[WebSocket, Dict[str, str]] = {}
    
    async def connect(self, websocket: WebSocket, user_id: str, firebase_token: Optional[str] = None):
        """Accept WebSocket connection with Firebase authentication and store it.
        
        Args:
            websocket: WebSocket connection
            user_id: User ID (session ID)
            firebase_token: Firebase ID token for authentication
        """
        # Authenticate Firebase token if provided
        user_info = None
        if firebase_token:
            user_info = await verify_firebase_token_websocket(firebase_token)
            if not user_info:
                await websocket.close(code=4001, reason="Authentication failed")
                logging.warning(f"WebSocket authentication failed for user {user_id}")
                return False
            
            # Verify that the session user_id matches the authenticated user
            if user_info.get('user_id') != user_id:
                await websocket.close(code=4002, reason="User ID mismatch")
                logging.warning(f"User ID mismatch: session={user_id}, auth={user_info.get('user_id')}")
                return False
                
            logging.info(f"FirebaseUID {user_info['firebase_uid']} authenticated for session {user_id}")
        else:
            logging.warning(f"WebSocket connection attempted without Firebase token for user {user_id}")
        
        # Simple Replace: Always close existing connection and create fresh one
        if user_id in self.active_connections:
            existing_ws = self.active_connections[user_id]
            
            # Close existing connection (regardless of its state)
            try:
                await existing_ws.close(code=1000, reason="Replaced by new connection")
                logging.info(f"Closed existing connection for user {user_id}")
            except Exception as e:
                # Connection might already be dead, that's fine
                logging.debug(f"Could not close existing connection for user {user_id}: {e}")
            
            # Clean up existing connection from tracking
            del self.active_connections[user_id]
            if existing_ws in self.connection_metadata:
                del self.connection_metadata[existing_ws]
        
        # Accept new WebSocket connection
        await websocket.accept()
                
        # Store new connection
        self.active_connections[user_id] = websocket
        metadata = {"user_id": user_id}
        if user_info:
            metadata.update({
                "firebase_uid": user_info['firebase_uid'],
                "authenticated": True
            })
        else:
            metadata["authenticated"] = False
        self.connection_metadata[websocket] = metadata
        
        logging.info(f"WebSocket connection established for user {user_id} (authenticated: {metadata['authenticated']})")
        return True
    
    def disconnect(self, websocket: WebSocket):
        """Handle WebSocket disconnection.
        
        Args:
            websocket: WebSocket connection
        """
        if websocket in self.connection_metadata:
            user_id = self.connection_metadata[websocket]["user_id"]
            
            # Remove from active connections
            if user_id in self.active_connections:
                del self.active_connections[user_id]
            
            # Remove metadata
            del self.connection_metadata[websocket]
            
            logging.info(f"WebSocket disconnected for user {user_id}")
    
    async def handle_message(self, websocket: WebSocket, message: str) -> bool:
        """Process incoming WebSocket message and send response.
        
        Args:
            websocket: WebSocket connection
            message: JSON message from client
            
        Returns:
            True if message was processed successfully, False otherwise
        """
        
        # Quick pre-parse to derive message type for span naming
        try:
            _pre = json.loads(message)
            _msg_type = _pre.get("typeOfMessage", "unknown")
        except Exception:
            _msg_type = "invalid_json"

        # Get IDs from connection metadata for tracing
        connection_meta = self.connection_metadata.get(websocket, {})
        firebase_uid = connection_meta.get('firebase_uid', 'unknown')  # backend's firebase_uid
        
        # Validate that we have required IDs for proper Langfuse tracking
        if firebase_uid == "unknown":
            logging.warning("⚠️ firebase_uid is unknown - User tracking in Langfuse will be limited")
            logging.warning("⚠️ This may indicate authentication issues or missing Firebase token during connection")
            
        # Log the IDs we're using for tracing
        logging.info(f"🔍 TRACING IDs: firebase_uid='{firebase_uid}' websocket_authenticated={connection_meta.get('authenticated', False)}")
        
        with langfuse.start_as_current_span(
            name=inspect.currentframe().f_code.co_name,
            input={"message": message},
            metadata={"user_id": firebase_uid, "websocket_connected": websocket in self.connection_metadata, "message_type": _msg_type}
        ) as span:
            try:
                # Parse incoming message
                message_data = json.loads(message)
                request = WebSocketChatRequest(**message_data)
                recent_len = len(message_data.get("recent_messages", []))
                # ─── Add rich metadata to parent span ───
                # Update structured input & metadata for richer tracing
                span.update(
                    input={
                        "message": request.message,
                        "recent_messages_len": recent_len
                    },
                    metadata={
                        "session_id": request.conversation_id,
                        "user_id": firebase_uid,
                        "message_type": request.typeOfMessage.value if hasattr(request, "typeOfMessage") else "chat",
                        "broker_name": request.brokerName.value if hasattr(request, "brokerName") else "unknown",
                        "chat_history_len": recent_len
                    }
                )
                
                # Basic validation - sender must be "user"
                if request.sender != "user":
                    await websocket.send_text(json.dumps({
                        "error": "Invalid sender. Must be 'user'",
                        "status": "error"
                    }))
                    return False

                # Handle ID generation before storage
                if not request.user_id or request.user_id.strip() == "":
                    # Get user_id (firebase_uid) from authenticated connection if available
                    real_user_id = connection_meta.get('firebase_uid')
                    
                    if real_user_id:
                        # Create proper auth mapping in database (user_id -> session_id)
                        try:
                            # Import the function here to avoid circular imports
                            import sys
                            import os
                            sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'data_layer_v3'))
                            from sql_queries import create_user_session_mapping_simple
                            
                            request.user_id = create_user_session_mapping_simple(real_user_id)
                            logging.info(f"🆔 Created auth mapping: user_id={real_user_id} -> session_id={request.user_id}")
                        except Exception as e:
                            logging.error(f"Failed to create auth mapping: {e}")
                            # Fallback to random UUID
                            request.user_id = str(uuid.uuid4())
                            logging.info(f"🆔 Fallback: Generated random session_id: {request.user_id}")
                    else:
                        # No user_id available, use random UUID
                        request.user_id = str(uuid.uuid4())
                        logging.info(f"🆔 No user_id: Generated random session_id: {request.user_id}")
                else:
                    logging.info(f"🆔 Using existing session_id: {request.user_id}")
                
                if not request.conversation_id or request.conversation_id.strip() == "":
                    request.conversation_id = str(uuid.uuid4())
                    logging.info(f"🗣️ Generated new conversation_id: {request.conversation_id}")
                else:
                    logging.info(f"🗣️ Using existing conversation_id: {request.conversation_id}")
                    
                # Update trace with semantic meaning in Langfuse keys
                span.update_trace(user_id=firebase_uid, session_id=request.conversation_id)
                
                # 🔍 DEBUG: Log the backend IDs that will be used for this message
                logging.info(f"🔍 BACKEND DEBUG: Processing message with firebase_uid='{firebase_uid}' user_id='{request.user_id}' conversation_id='{request.conversation_id}' message='{request.message[:50]}...'")
                
                
                # Process message and generate system response (may include summary)
                response, summary = await self._process_message(request, firebase_uid=firebase_uid)
                
                # Add metadata to response as a post-processing step
                response_with_metadata = self._add_metadata_to_response(response, request)

                # Prepare storage tasks - now including user message storage
                storage_tasks = [
                    self._store_message(request),  # Store user message
                    self._store_system_response(request, response)  # Store system response
                ]
                
                # Add summary storage task if summary was returned
                if summary:
                    storage_tasks.append(
                        self._store_summary_async(request.user_id, request.conversation_id, summary)
                    )
                    logging.info(f"LLM returned summary - executing parallel storage for conversation {request.conversation_id}")
                else:
                    logging.info(f"No summary returned by LLM - executing response storage only for conversation {request.conversation_id}")
                
                # Execute storage operations AND send response to user in parallel
                await asyncio.gather(
                    *storage_tasks,
                    websocket.send_text(response_with_metadata.json())
                )
                # Emit primitive events for dashboarding
                if response_with_metadata.primitives:
                    for primitive in response_with_metadata.primitives:
                        span.create_event(name="primitive_requested", metadata=primitive)
                
                # Get session_id for logging (keeping existing variable name for backward compatibility)
                log_user_id = self.connection_metadata[websocket]["user_id"]
                logging.info(f"✓ Parallel execution complete - session {log_user_id}, conversation {request.conversation_id} (response sent + storage completed)")
                
                # Update span with full response payload for dashboard parity
                span.update(
                    output=response_with_metadata.dict(),
                    metadata={
                        "summary_returned": summary is not None,
                        
                    }
                )
                return True
                
            except json.JSONDecodeError as json_error:
                span.update(level="ERROR", status_message=f"JSON decode error: {str(json_error)}")
                await websocket.send_text(json.dumps({
                    "error": "Invalid JSON format",
                    "status": "error"
                }))
                return False
            except Exception as e:
                span.update(level="ERROR", status_message=f"WebSocket processing error: {str(e)}")
                logging.error(f"Error processing WebSocket message: {e}")
                try:
                    await websocket.send_text(json.dumps({
                        "error": "Failed to process message",
                        "status": "error",
                        "details": str(e)
                    }))
                except Exception as send_error:
                    logging.error(f"Failed to send error response to client: {send_error}")
                return False
    
    async def _store_message(self, request: WebSocketChatRequest):
        """Store user message in database.
        
        Args:
            request: WebSocket chat request
        """
        try:
            with connection_manager.get_transaction() as cursor:
                # Save user message using CouchDB function
                chat_id = save_chat_message(
                    cursor=cursor,
                    user_id=request.user_id,
                    conversation_id=request.conversation_id,
                    role=request.sender,
                    message=request.message,
                    llm_model_version=request.modelId,
                    meta_json={
                        "broker_name": request.brokerName.value,
                        "message_type": request.typeOfMessage.value
                    },
                    order_id=None,
                    type=request.typeOfMessage.value
                )
                
                logging.info(f"💾 USER MSG STORED: chat_id={chat_id} user_id='{request.user_id}' conversation_id='{request.conversation_id}' message='{request.message[:50]}...'")
                
                
        except Exception as e:
            logging.error(f"Failed to store user message: {e}")
            raise
    
    async def _store_system_response(self, request: WebSocketChatRequest, response: WebSocketChatResponse):
        """Store system response in database.
        
        Args:
            request: Original user request
            response: System response
        """
        try:
            with connection_manager.get_transaction() as cursor:
                # Convert actions to dictionaries
                actions_list = [action.dict() for action in response.actions]
                
                # Save system response using CouchDB function
                chat_id = save_chat_message(
                    cursor=cursor,
                    user_id=request.user_id,
                    conversation_id=request.conversation_id,
                    role=response.sender,
                    message=response.textMessage,
                    llm_model_version=None,
                    meta_json={
                        "broker_name": request.brokerName.value,
                        "message_type": response.messageType.value,
                        "actions": actions_list,
                        "primitives": response.primitives
                    },
                    order_id=None,
                    type=response.messageType.value
                )
                
                logging.info(f"💾 SYSTEM MSG STORED: chat_id={chat_id} user_id='{request.user_id}' conversation_id='{request.conversation_id}' response='{response.textMessage[:50]}...'")
                
            
        except Exception as e:
            logging.error(f"CRITICAL ERROR: Failed to store system response: {e}")
            logging.error(f"CRITICAL ERROR: Exception type: {type(e)}")
            import traceback
            logging.error(f"CRITICAL ERROR: Full traceback: {traceback.format_exc()}")
            raise
    
    async def _process_message(self, request: WebSocketChatRequest, firebase_uid: str = "unknown") -> tuple[WebSocketChatResponse, Optional[Dict[str, Any]]]:
        """Process user message with detailed workflow.
        
        Args:
            request: WebSocket chat request
            firebase_uid: Firebase UID for user tracking in Langfuse
            
        Returns:
            Tuple of (WebSocket chat response, summary dict if any)
        """
        with langfuse.start_as_current_span(
            name=inspect.currentframe().f_code.co_name,
            input={"message": request.message, "user_id": request.user_id, "conversation_id": request.conversation_id},
            metadata={"user_id": firebase_uid, "session_id": request.conversation_id, "broker_name": request.brokerName.value, "message_type": request.typeOfMessage.value}
        ) as span:
            try:
                # Step 1: Get all summaries for this conversation_id
                summaries = await self._get_conversation_summaries(request.conversation_id, request.user_id)
                
                # Step 2: Get conversation history after latest summary
                latest_summary_datetime = None
                if summaries:
                    latest_summary_datetime = max(summary.get("datetime") for summary in summaries)
                
                conversation_history = await self._get_conversation_history_after_datetime(
                    request.conversation_id,
                    request.user_id,
                    latest_summary_datetime
                )
                
                # Step 3: Prepare and send to LLM
                llm_input = self._prepare_llm_input(request, summaries, conversation_history)
                # Pass firebase_uid through for Langfuse tracking
                llm_response = await self._call_llm_function(llm_input, firebase_uid=firebase_uid)
                
                # Step 4: Process LLM response
                response = await self._process_llm_response(request, llm_response)
                
                # Step 5: Extract summary if provided by LLM
                summary = llm_response.get("summary") if "summary" in llm_response else None
                
                # Update span with success
                span.update(
                    output={"response_type": "success", "summary_returned": summary is not None},
                    metadata={"summaries_count": len(summaries), "history_count": len(conversation_history)}
                )
                
                return response, summary
                
            except Exception as e:
                span.update(status_message=f"Process message error: {str(e)}")
                logging.error(f"Error in detailed message processing: {e}")
                # Fallback to simple response
                fallback_response = WebSocketChatResponse(
                    textMessage="I'm sorry, there was an error processing your message. Please try again.",
                    messageType=WebSocketResponseMessageType.ORDER_CONFIRMATION,
                    primitives=[],
                    sender="system",
                    actions=[],
                    user_id=request.user_id,
                    conversation_id=request.conversation_id
                )
                return fallback_response, None
    
    
    
    
    async def _get_conversation_summaries(self, conversation_id: str, user_id: str) -> List[Dict[str, Any]]:
        """Get all summaries for a conversation.
        
        Args:
            conversation_id: Conversation ID
            user_id: User ID
            
        Returns:
            List of summary dictionaries
        """
        try:
            with connection_manager.get_transaction() as cursor:
                # Get summaries using SQL function
                summaries = fetch_summary(cursor, user_id, conversation_id)
                logging.info(f"Retrieved {len(summaries)} summaries for conversation {conversation_id}")
                return summaries
        
        except Exception as e:
            logging.error(f"Failed to get conversation summaries: {e}")
            return []
    
    async def _get_conversation_history_after_datetime(
        self, 
        conversation_id: str, 
        user_id: str,
        latest_summary_datetime: Optional[str]
    ) -> List[Dict[str, Any]]:
        """Get conversation history after latest summary datetime.
        
        Args:
            conversation_id: Conversation ID
            user_id: User ID
            latest_summary_datetime: Latest summary datetime (or None for all history)
            
        Returns:
            List of conversation message dictionaries (excluding the latest message)
        """
        try:
            with connection_manager.get_transaction() as cursor:
                # Get all chat history using CouchDB function (no cursor needed)
                logging.info(f"📖 RETRIEVING HISTORY: user_id='{user_id}' conversation_id='{conversation_id}'")
                history = extract_chat_history(user_id, conversation_id)
                logging.info(f"📖 RAW HISTORY RETRIEVED: {len(history)} messages")
                
                # Log each message for debugging
                for i, msg in enumerate(history):
                    logging.info(f"📖 History[{i}]: role={msg.get('role')} message='{str(msg.get('message', ''))[:50]}...' timestamp={msg.get('timestamp')}")
                
                # Filter by timestamp if summary datetime is provided
                if latest_summary_datetime:
                    # Convert datetime string to comparable format
                    history = [
                        msg for msg in history 
                        if msg["timestamp"] > latest_summary_datetime
                    ]
                    logging.info(f"📖 FILTERED HISTORY: {len(history)} messages after {latest_summary_datetime}")
                
                logging.info(f"📖 FINAL HISTORY: Returning {len(history)} messages for LLM")
                return history
            
        except Exception as e:
            logging.error(f"Failed to get conversation history: {e}")
            return []
    
    def _prepare_llm_input(
        self, 
        request: WebSocketChatRequest, 
        summaries: List[Dict[str, Any]], 
        conversation_history: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Prepare input for LLM function.
        
        Args:
            request: Current user request
            summaries: Previous conversation summaries
            conversation_history: Recent conversation history
            
        Returns:
            Dictionary with LLM input data
        """
        # Step 5: Club consecutive conversations (user -> system pairs)
        paired_conversations = []
        current_pair = {}
        
        for msg in conversation_history:
            # Use 'role' field (from database) or 'sender' field (from request)
            sender = msg.get("role") or msg.get("sender")
            
            if sender == "user":
                # Unpack meta_json into the message object
                user_msg = {k: v for k, v in msg.items() if k != "meta_json"}
                if msg.get("meta_json"):
                    user_msg.update(msg["meta_json"])
                
                current_pair = {"user_message": user_msg}
            elif sender == "system" and "user_message" in current_pair:
                # Unpack meta_json into the message object
                system_msg = {k: v for k, v in msg.items() if k != "meta_json"}
                if msg.get("meta_json"):
                    system_msg.update(msg["meta_json"])
                
                current_pair["llm_response"] = system_msg
                paired_conversations.append(current_pair)
                current_pair = {}
        
        # Do not add incomplete pair if exists
        # if current_pair:
        #     paired_conversations.append(current_pair)
        
        # Prepare LLM input (using backend field names for LLM functions)
        llm_input = {
            "user_id": request.user_id,  # backend's user_id (still needed for DB operations)
            "session_id": request.conversation_id,  # backend's conversation_id
            # "broker_name": request.brokerName.value,
            # "message_type": request.typeOfMessage.value,
            "message": request.message,
            # "summaries": summaries,
            "recent_messages": paired_conversations,
            # "provider": None, #TODO: Replace with actual provider if needed
            # "model_name": None
        }
        
        logging.info(f"Prepared LLM input with {len(summaries)} summaries and {len(paired_conversations)} conversation pairs")
        
        return llm_input
    
    async def _call_llm_function(self, llm_input: Dict[str, Any], firebase_uid: str = "unknown") -> Dict[str, Any]:
        """Call LLM function (improved mock implementation using actual context).
        
        Args:
            llm_input: Input data for LLM
            firebase_uid: Firebase UID for user tracking in Langfuse
        
        Returns:
            LLM response dictionary
        """
        
        # 🚀 CRITICAL DEBUG: Log that this function is being called
        logging.info(f"🔴 _call_llm_function CALLED with message: {llm_input.get('message', 'N/A')[:50]}")
        logging.info(f"🔴 _call_llm_function input keys: {list(llm_input.keys())}")
        logging.info(f"🔴 _call_llm_function firebase_uid: {firebase_uid}")
        # Check if we're in testing mode
        testing_mode = os.getenv("TESTING_MODE", "false").lower() == "true"
        logging.info(f"🔍 WebSocket LLM call - testing_mode: {testing_mode}")
        
        with langfuse.start_as_current_span(
            name=inspect.currentframe().f_code.co_name,
            input={"message": llm_input.get('message', 'N/A')[:100]},
            metadata={"testing_mode": testing_mode, "user_id": firebase_uid, "session_id": llm_input.get('session_id')}
        ) as span:
            if testing_mode:
                # Use mock LLM for testing to avoid API rate limiting
                logging.info("Using mock LLM for testing")
                message = llm_input.get("current_message", "")
                message_type = llm_input.get("message_type", "chat")
                
                # Import mock function
                from logic.askLLM import get_mock_llm_response
                mock_response = get_mock_llm_response(message, message_type)
                
                logging.info(f"Mock LLM processed {message_type} message")
                logging.info(f"🔍 Mock response structure: {list(mock_response.keys())}")
                
                # Update span with mock response
                span.update(
                    output={"response_type": "mock", "message_type": message_type},
                    metadata={"mock_response_keys": list(mock_response.keys())}
                )
                return mock_response
            else:
                # Use real LLM for production
                try:
                    # Call render_chat with properly mapped parameters and firebase_uid for Langfuse tracking
                    response_data = await render_chat(
                        message=llm_input["message"],
                        user_id=llm_input["user_id"],
                        session_id=llm_input["session_id"],
                        recent_messages=llm_input["recent_messages"],
                        provider="openai",  # TODO: Make this configurable
                        model_name="gpt-4o-mini",  # TODO: Make this configurable
                        firebase_uid=firebase_uid  # Properly pass firebase_uid for Langfuse tracking
                    )
                    logging.info(f"🔍 render_chat response structure: {list(response_data.keys())}")
                    logging.info(f"🔍 render_chat response messageType: {response_data.get('messageType')}")
                    logging.info(f"🔍 render_chat response primitives: {response_data.get('primitives')}")

                    mock_response = {
                        "response": response_data.get("response")
                    }
                    summary = response_data.get("summary")
                    if summary:
                        mock_response["summary"] = summary
                    
                    # 🚀 FIX: Preserve messageType and primitives at top level if present (for order_execution)
                    if response_data.get("messageType"):
                        mock_response["messageType"] = response_data.get("messageType")
                        logging.info(f"🚀 Preserving messageType: {response_data.get('messageType')}")
                        
                        # Also preserve top-level primitives for order_execution
                        if response_data.get("messageType") == "order_execution" and response_data.get("primitives"):
                            mock_response["primitives"] = response_data.get("primitives")
                            logging.info(f"🚀 Preserving {len(response_data.get('primitives', []))} primitives for order_execution")
                    
                    logging.info(f"Real LLM processed {llm_input.get('message_type', 'chat')} message")
                    logging.info(f"🔍 Final WebSocket response structure: {list(mock_response.keys())}")
                    
                    # Update span with real response
                    span.update(
                        output={"response_type": "real", "message_type": response_data.get('messageType')},
                        metadata={"summary_returned": summary is not None, "primitives_count": len(response_data.get('primitives', []))}
                    )
                    return mock_response
                except Exception as e:
                    span.update(status_message=f"LLM call error: {str(e)}")
                    logging.error(f"Error in LLM call: {e}")
                    raise
    
    def _analyze_conversation_context(
        self, 
        summaries: List[Dict[str, Any]], 
        conversation_pairs: List[Dict[str, str]], 
        current_message: str
    ) -> Dict[str, Any]:
        """Analyze conversation context to generate better responses."""
        
        # Extract topics from summaries
        topics = []
        for summary in summaries:
            summary_text = summary.get("summary", "") or summary.get("summary_text", "")
            if "order" in summary_text.lower():
                topics.append("orders")
            if "monitor" in summary_text.lower():
                topics.append("monitoring")
            if "portfolio" in summary_text.lower():
                topics.append("portfolio")
        
        # Analyze recent conversation patterns
        recent_user_messages = []
        for pair in conversation_pairs[-3:]:  # Last 3 pairs
            if "user" in pair:
                recent_user_messages.append(pair["user"].lower())
        
        # Detect intent from current and recent messages
        all_messages = " ".join(recent_user_messages + [current_message.lower()])
        
        intent_keywords = {
            "buy_intent": ["buy", "purchase", "acquire", "long"],
            "sell_intent": ["sell", "exit", "close", "short"],
            "monitor_intent": ["watch", "monitor", "alert", "notify", "track"],
            "portfolio_intent": ["portfolio", "holdings", "positions", "performance"],
            "help_intent": ["help", "how", "what", "explain", "guide"]
        }
        
        detected_intents = []
        for intent, keywords in intent_keywords.items():
            if any(keyword in all_messages for keyword in keywords):
                detected_intents.append(intent)
        
        return {
            "topics": list(set(topics)),
            "recent_messages": recent_user_messages,
            "detected_intents": detected_intents,
            "conversation_length": len(conversation_pairs),
            "has_prior_context": len(summaries) > 0
        }
    
    def _generate_orders_response(
        self, 
        current_message: str, 
        broker_name: str, 
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate orders-specific response using context."""
        
        message_lower = current_message.lower()
        
        if any(intent in context["detected_intents"] for intent in ["buy_intent", "sell_intent"]):
            if "buy_intent" in context["detected_intents"]:
                action_type = "purchase"
                response_text = f"I'll help you buy securities through {broker_name}."
            else:
                action_type = "sale"
                response_text = f"I'll help you sell securities through {broker_name}."
            
            if context["has_prior_context"]:
                response_text += f" Based on our previous discussions about {', '.join(context['topics'])}, "
            
            response_text += f" Your request: '{current_message}' is being processed."
            
            actions = [
                {"description": "Confirm Order", "type": "orders", "message": f"confirm_{action_type}"},
                {"description": "View Portfolio", "type": "monitoring", "message": "view_portfolio"},
                {"description": "Cancel", "type": "chat", "message": "cancel_order"}
            ]
            message_type_response = "order_confirmation"
            
        elif "portfolio" in message_lower or "portfolio_intent" in context["detected_intents"]:
            response_text = f"Here's your portfolio overview from {broker_name}."
            if context["conversation_length"] > 0:
                response_text += " I see we've been discussing your investments."
            
            actions = [
                {"description": "Detailed Portfolio", "type": "monitoring", "message": "detailed_portfolio"},
                {"description": "Place New Order", "type": "orders", "message": "place_new_order"}
            ]
            message_type_response = "order_execution"
            
        else:
            response_text = f"Here are your recent orders from {broker_name}."
            if context["conversation_length"] > 2:
                response_text += f" You currently have {context['conversation_length']} recent transactions."
            
            actions = [
                {"description": "Place New Order", "type": "orders", "message": "place_new_order"},
                {"description": "Order History", "type": "orders", "message": "order_history"}
            ]
            message_type_response = "order_execution"
        
        return {
            "textMessage": response_text,
            "messageType": message_type_response,
            "primitives": context.get("topics", []),
            "sender": "system",
            "actions": actions
        }
    
    def _generate_monitoring_response(
        self, 
        current_message: str, 
        broker_name: str, 
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate monitoring-specific response using context."""
        
        if "monitor_intent" in context["detected_intents"]:
            response_text = f"I'll set up monitoring for your {broker_name} account."
            if context["has_prior_context"]:
                response_text += f" Building on our previous discussions about {', '.join(context['topics'])}."
            response_text += f" Your monitoring request: '{current_message}' is being configured."
            
            actions = [
                {"description": "Configure Alert", "type": "monitoring", "message": "configure_alert"},
                {"description": "View Existing Monitors", "type": "monitoring", "message": "view_monitors"}
            ]
        else:
            response_text = f"Here are your active monitoring instances for {broker_name}."
            if context["conversation_length"] > 0:
                response_text += f" You have {context['conversation_length']} conversation topics being tracked."
            
            actions = [
                {"description": "Create New Alert", "type": "monitoring", "message": "create_alert"},
                {"description": "Modify Existing", "type": "monitoring", "message": "modify_monitors"}
            ]
        
        return {
            "textMessage": response_text,
            "messageType": "monitor_order",
            "primitives": context.get("detected_intents", []),
            "sender": "system",
            "actions": actions
        }
    
    def _generate_chat_response(
        self, 
        current_message: str, 
        broker_name: str, 
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate general chat response using context."""
        
        if "help_intent" in context["detected_intents"]:
            response_text = f"I'm here to help with your {broker_name} trading needs."
            if context["has_prior_context"]:
                response_text += f" I see we've discussed {', '.join(context['topics'])} before."
        else:
            response_text = f"Thanks for your message about {broker_name}."
            if context["conversation_length"] > 0:
                response_text += f" I understand you said: '{current_message}'."
        
        # Suggest actions based on conversation context
        actions = []
        if "orders" not in context["topics"]:
            actions.append({"description": "Place Order", "type": "orders", "message": "place_order"})
        if "monitoring" not in context["topics"]:
            actions.append({"description": "Set Up Monitoring", "type": "monitoring", "message": "setup_monitoring"})
        
        actions.append({"description": "View Portfolio", "type": "monitoring", "message": "show_portfolio"})
        
        return {
            "textMessage": response_text,
            "messageType": "order_confirmation",
            "primitives": context.get("recent_messages", []),
            "sender": "system",
            "actions": actions
        }
    
    def _generate_conversation_summary(
        self, 
        summaries: List[Dict[str, Any]], 
        conversation_pairs: List[Dict[str, str]], 
        message_type: str, 
        broker_name: str
    ) -> str:
        """Generate intelligent conversation summary."""
        
        # Count message types from recent conversation
        topics_discussed = []
        recent_topics = {}
        
        for pair in conversation_pairs[-5:]:  # Last 5 pairs
            user_msg = pair.get("user", "").lower()
            system_msg = pair.get("system", "").lower()
            
            if any(word in user_msg for word in ["buy", "sell", "order"]):
                recent_topics["orders"] = recent_topics.get("orders", 0) + 1
            if any(word in user_msg for word in ["monitor", "alert", "watch"]):
                recent_topics["monitoring"] = recent_topics.get("monitoring", 0) + 1
            if any(word in user_msg for word in ["portfolio", "holdings"]):
                recent_topics["portfolio"] = recent_topics.get("portfolio", 0) + 1
        
        # Build summary
        summary_parts = [f"User engaged with {broker_name} assistant"]
        
        if recent_topics:
            most_discussed = max(recent_topics, key=recent_topics.get)
            summary_parts.append(f"primarily discussing {most_discussed}")
        
        if len(conversation_pairs) > 5:
            summary_parts.append(f"over {len(conversation_pairs)} message exchanges")
        
        if len(summaries) > 0:
            summary_parts.append("continuing from previous conversation context")
        
        return ". ".join(summary_parts) + "."
    
    async def _process_llm_response(
        self, 
        request: WebSocketChatRequest, 
        llm_response: Dict[str, Any]
    ) -> WebSocketChatResponse:
        """Process LLM response and create WebSocket response.
        
        Args:
            request: Original user request
            llm_response: LLM response data
        
        Returns:
            WebSocket chat response
        """
        response_data = llm_response.get("response", {})
        
        # 🔧 FIX: Check for messageType at top level first (for order_execution), then nested level
        message_type = llm_response.get("messageType") or response_data.get("messageType", "order_confirmation")
        
        # 🔧 Also pass through responseType if present (for plan confirmations)
        response_type = llm_response.get("responseType")
        logging.info(f"🔄 WebSocket forwarding messageType: {message_type}, responseType: {response_type}")
        
        # 🚀 DEBUG: Log the entire llm_response structure for order_execution
        if message_type == "order_execution":
            logging.info(f"🔍 [WebSocket] llm_response keys: {list(llm_response.keys())}")
            logging.info(f"🔍 [WebSocket] llm_response.get('primitives'): {llm_response.get('primitives')}")
            logging.info(f"🔍 [WebSocket] response_data.get('primitives'): {response_data.get('primitives')}")
        
        # 🚀 FIX: For order_execution, primitives are at top level, not nested
        primitives = llm_response.get("primitives", []) if message_type == "order_execution" else response_data.get("primitives", [])
        
        # 🚀 Special logging for order execution
        if message_type == "order_execution":
            primitives_count = len(primitives)
            logging.info(f"🚀 ORDER_EXECUTION detected! Forwarding {primitives_count} primitives to frontend for execution")
        
        # Convert actions to WebSocketAction objects
        actions = []
        for action_data in response_data.get("actions", []):
            actions.append(WebSocketAction(
                description=action_data.get("description", ""),
                type=WebSocketMessageType(action_data.get("type", "chat")),
                message=action_data.get("message", "")
            ))
        
        return WebSocketChatResponse(
            textMessage=response_data.get("textMessage", "I'm here to help!"),
            messageType=WebSocketResponseMessageType(message_type),
            primitives=primitives,
            sender=response_data.get("sender", "system"),
            actions=actions,
            user_id=request.user_id,
            conversation_id=request.conversation_id
        )
    
    def _add_metadata_to_response(
        self, 
        response: WebSocketChatResponse, 
        request: WebSocketChatRequest
    ) -> WebSocketChatResponse:
        """Add metadata to WebSocket response as a post-processing step.
        
        This method handles adding request context and other metadata to the response
        before sending it to the client. It's a separate step from LLM processing.
        
        Args:
            response: The WebSocket response from LLM processing
            request: Original WebSocket request
            
        Returns:
            WebSocket response with metadata fields unpacked at root level
        """
        metadata = self._build_response_metadata(request)
        
        # Create a new response object with metadata fields unpacked at root level
        return WebSocketChatResponse(
            textMessage=response.textMessage,
            messageType=response.messageType,
            primitives=response.primitives,
            sender=response.sender,
            actions=response.actions,
            user_id=response.user_id,
            conversation_id=response.conversation_id,
            **metadata  # Unpack metadata fields directly into the response
        )

    def _build_response_metadata(self, request: WebSocketChatRequest) -> Dict[str, Any]:
        """Build metadata for WebSocket response from request context.
        
        This method makes it easy to add new metadata fields in the future.
        Simply add new fields to the metadata dictionary here.
        
        Args:
            request: Original WebSocket request
            
        Returns:
            Dictionary containing metadata fields to be unpacked into the response
        """
        metadata = {
            "typeOfMessage": request.typeOfMessage.value,  # Include the original message type
            }
        
        # TODO: Add new metadata fields here as needed
        # Example:
        # metadata["sessionId"] = request.sessionId
        # metadata["requestId"] = request.requestId
        # metadata["priority"] = request.priority
        
        return metadata

    async def _store_summary_async(self, user_id: str, conversation_id: str, summary: Dict[str, Any]):
        """Store summary in DB asynchronously (parallel operation).
        
        Args:
            user_id: User ID
            conversation_id: Conversation ID
            summary: Summary data returned by LLM
        """
        try:
            # Normalize summary input: allow plain strings or dicts
            if isinstance(summary, str):
                summary = {"summary_text": summary}
            # Validate summary data
            if not summary or not isinstance(summary, dict) or not summary.get("summary_text"):
                logging.warning(f"Empty or invalid summary provided for conversation {conversation_id}")
                return
            
            with connection_manager.get_transaction() as cursor:
                # Save summary using CouchDB function
                summary_id = save_summary(
                    cursor=cursor,
                    user_id=user_id,
                    conversation_id=conversation_id,
                    summary=summary.get("summary_text", ""),
                    llm_model_version=summary.get("llm_model_version"),
                    meta_json=summary.get("meta_json", {})
                )
                
                logging.info(f"Summary storage completed - conversation {conversation_id}, summary_id: {summary_id}")
            
        except Exception as e:
            logging.error(f"Summary storage failed for conversation {conversation_id}: {e}")
            # Don't re-raise - this is a parallel operation and shouldn't block the main flow


# Global instance
websocket_chat_service = WebSocketChatService() 
