"""
CouchDB Connection Manager for Backend API Module.

This module provides connection management for CouchDB operations.
"""

import os
import logging
from typing import Optional, Dict, Any
from contextlib import contextmanager

import couchdb
from couchdb import Database, Server

logger = logging.getLogger(__name__)


class ConnectionManager:
    """Manages CouchDB connections and transactions."""
    
    def __init__(self):
        """Initialize the connection manager with CouchDB configuration."""
        self.server: Optional[Server] = None
        self.database: Optional[Database] = None
        self._connected = False
        
    def _get_couchdb_connection_url(self) -> str:
        """Get CouchDB connection URL from environment variables."""
        host = os.getenv("COUCHDB_HOST", "localhost")
        port = os.getenv("COUCHDB_PORT", "5984")
        username = os.getenv("COUCHDB_USERNAME")
        password = os.getenv("COUCHDB_PASSWORD")
        
        if not username:
            raise ValueError("COUCHDB_USERNAME must be set in environment variables")
        if not password:
            raise ValueError("COUCHDB_PASSWORD must be set in environment variables")
        
        # URL encode username and password to handle special characters
        import urllib.parse
        encoded_username = urllib.parse.quote(username, safe='')
        encoded_password = urllib.parse.quote(password, safe='')
        
        return f"http://{encoded_username}:{encoded_password}@{host}:{port}/"
    
    def connect(self) -> Database:
        """Connect to CouchDB and return the database instance."""
        if not self._connected:
            try:
                url = self._get_couchdb_connection_url()
                self.server = Server(url)
                
                db_name = os.getenv("COUCHDB_DATABASE", "aagmanai")
                
                # Test connection by listing databases (works with older versions)
                try:
                    databases = list(self.server)
                    logger.info(f"Connected to CouchDB. Available databases: {databases}")
                except Exception as e:
                    logger.warning(f"Warning during connection test: {e}")
                
                # Get or create database
                if db_name in self.server:
                    self.database = self.server[db_name]
                    logger.info(f"Using existing database: {db_name}")
                else:
                    self.database = self.server.create(db_name)
                    logger.info(f"Created new database: {db_name}")
                
                self._connected = True
                logger.info(f"Connected to CouchDB database: {db_name}")
                
            except Exception as e:
                logger.error(f"Failed to connect to CouchDB: {e}")
                raise
        
        return self.database
    
    def disconnect(self):
        """Disconnect from CouchDB."""
        self.server = None
        self.database = None
        self._connected = False
        logger.info("Disconnected from CouchDB")
    
    def is_connected(self) -> bool:
        """Check if connected to CouchDB."""
        return self._connected
    
    @contextmanager
    def get_transaction(self):
        """
        Get a CouchDB database context for operations.
        
        Note: CouchDB doesn't have traditional transactions, but this provides
        a consistent interface for database operations.
        """
        try:
            db = self.connect()
            yield db
        except Exception as e:
            logger.error(f"Database operation failed: {e}")
            raise
        finally:
            # CouchDB operations are atomic at document level
            pass


# Global connection manager instance
connection_manager = ConnectionManager() 