"""Authentication Middleware for Firebase Token Verification"""

import logging
import time
from typing import Optional, Dict, Any, Callable
from functools import wraps
from fastapi import Request, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import J<PERSON>NResponse

# Import Firebase service and database functions
from ..services.firebase_service import verify_firebase_token
from ..datastore import get_user_id_by_auth_uid_simple, check_user_exists_simple

# Configure logging
logger = logging.getLogger(__name__)

# Security scheme for extracting Bearer tokens
security = HTTPBearer()

# Rate limiting storage (in production, use Redis)
auth_attempts = {}
RATE_LIMIT_WINDOW = 300  # 5 minutes
RATE_LIMIT_MAX_ATTEMPTS = 10


def rate_limit_check(firebase_uid: str) -> bool:
    """
    Check if user has exceeded authentication rate limit.
    
    Args:
        firebase_uid: Firebase user UID
        
    Returns:
        True if within rate limit, False if exceeded
    """
    current_time = time.time()
    
    # Clean up old entries
    expired_keys = [
        key for key, (count, timestamp) in auth_attempts.items()
        if current_time - timestamp > RATE_LIMIT_WINDOW
    ]
    for key in expired_keys:
        del auth_attempts[key]
    
    # Check current user's attempts
    if firebase_uid in auth_attempts:
        count, first_attempt = auth_attempts[firebase_uid]
        if current_time - first_attempt < RATE_LIMIT_WINDOW:
            if count >= RATE_LIMIT_MAX_ATTEMPTS:
                logger.warning(f"⚠️  Rate limit exceeded for user: {firebase_uid}")
                return False
            # Increment attempt count
            auth_attempts[firebase_uid] = (count + 1, first_attempt)
        else:
            # Reset counter for new window
            auth_attempts[firebase_uid] = (1, current_time)
    else:
        # First attempt
        auth_attempts[firebase_uid] = (1, current_time)
    
    return True


async def extract_firebase_token(request: Request) -> Optional[str]:
    """
    Extract Firebase token from request headers.
    
    Args:
        request: FastAPI request object
        
    Returns:
        Firebase ID token or None
    """
    try:
        # Try Authorization header first
        authorization = request.headers.get("Authorization")
        if authorization and authorization.startswith("Bearer "):
            return authorization.split(" ", 1)[1]
        
        # Try custom Firebase-Token header
        firebase_token = request.headers.get("Firebase-Token")
        if firebase_token:
            return firebase_token
        
        # Try X-Firebase-Token header
        x_firebase_token = request.headers.get("X-Firebase-Token")
        if x_firebase_token:
            return x_firebase_token
        
        return None
        
    except Exception as e:
        logger.error(f"❌ Error extracting Firebase token: {e}")
        return None


async def verify_and_get_user_info(firebase_token: str) -> Optional[Dict[str, Any]]:
    """
    Verify Firebase token and get user information.
    
    Args:
        firebase_token: Firebase ID token
        
    Returns:
        User information with session mapping or None
    """
    try:
        # Verify Firebase token
        token_claims = verify_firebase_token(firebase_token)
        if not token_claims:
            logger.warning("⚠️  Invalid Firebase token")
            return None
        
        firebase_uid = token_claims['firebase_uid']
        
        # Rate limiting check
        if not rate_limit_check(firebase_uid):
            logger.warning(f"⚠️  Rate limit exceeded for Firebase UID: {firebase_uid}")
            return None
        
        # Check if user exists in our database
        user_exists = check_user_exists_simple(firebase_uid)
        if not user_exists:
            logger.warning(f"⚠️  User not found in database: {firebase_uid}")
            return None
        
        # Get session user_id mapping
        session_user_id = get_user_id_by_auth_uid_simple(firebase_uid)
        if not session_user_id:
            logger.warning(f"⚠️  No session mapping found for Firebase UID: {firebase_uid}")
            return None
        
        # Return combined user information
        user_info = {
            'firebase_uid': firebase_uid,
            'user_id': session_user_id,  # This is the session ID used in chat_history
            'email': token_claims.get('email'),
            'phone_number': token_claims.get('phone_number'),
            'email_verified': token_claims.get('email_verified', False),
            'auth_time': token_claims.get('auth_time'),
            'token_exp': token_claims.get('exp'),
            'token_iat': token_claims.get('iat')
        }
        
        logger.debug(f"✅ User authenticated: {firebase_uid} -> session: {session_user_id}")
        return user_info
        
    except Exception as e:
        logger.error(f"❌ Error verifying user: {e}")
        return None


def firebase_auth_required(func: Callable) -> Callable:
    """
    Decorator to require Firebase authentication for route handlers.
    
    Usage:
        @router.get("/protected")
        @firebase_auth_required
        async def protected_route(request: Request):
            # Access user info via request.state.user
            user_info = request.state.user
            return {"message": f"Hello {user_info['firebase_uid']}"}
    """
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # Find the request object in args
        request = None
        for arg in args:
            if isinstance(arg, Request):
                request = arg
                break
        
        if not request:
            logger.error("❌ Request object not found in decorated function")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error"
            )
        
        # Extract and verify Firebase token
        firebase_token = await extract_firebase_token(request)
        if not firebase_token:
            logger.warning("⚠️  No Firebase token provided")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Firebase token required",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        # Verify token and get user info
        user_info = await verify_and_get_user_info(firebase_token)
        if not user_info:
            logger.warning("⚠️  Firebase authentication failed")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Invalid or expired Firebase token"
            )
        
        # Store user info in request state
        request.state.user = user_info
        
        # Call the original function
        return await func(*args, **kwargs)
    
    return wrapper


async def verify_firebase_token_websocket(token: str) -> Optional[Dict[str, Any]]:
    """
    Verify Firebase token for WebSocket connections.
    
    Args:
        token: Firebase ID token
        
    Returns:
        User information or None if invalid
    """
    try:
        user_info = await verify_and_get_user_info(token)
        if user_info:
            logger.info(f"✅ WebSocket authentication successful: {user_info['firebase_uid']}")
        else:
            logger.warning("⚠️  WebSocket authentication failed")
        
        return user_info
        
    except Exception as e:
        logger.error(f"❌ WebSocket token verification error: {e}")
        return None


class FirebaseAuthMiddleware:
    """
    ASGI middleware for Firebase authentication (optional - for global auth).
    """
    
    def __init__(self, app):
        self.app = app
        self.excluded_paths = {
            "/docs", "/redoc", "/openapi.json", 
            "/health", "/metrics", "/favicon.ico"
        }
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        path = scope["path"]
        
        # Skip authentication for excluded paths
        if any(path.startswith(excluded) for excluded in self.excluded_paths):
            await self.app(scope, receive, send)
            return
        
        # Skip authentication for OPTIONS requests
        if scope["method"] == "OPTIONS":
            await self.app(scope, receive, send)
            return
        
        # Extract token from headers
        headers = dict(scope.get("headers", []))
        auth_header = headers.get(b"authorization", b"").decode()
        
        if not auth_header or not auth_header.startswith("Bearer "):
            # Return 401 for missing token
            response = JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Firebase token required"}
            )
            await response(scope, receive, send)
            return
        
        token = auth_header.split(" ", 1)[1]
        user_info = await verify_and_get_user_info(token)
        
        if not user_info:
            # Return 403 for invalid token
            response = JSONResponse(
                status_code=status.HTTP_403_FORBIDDEN,
                content={"detail": "Invalid or expired Firebase token"}
            )
            await response(scope, receive, send)
            return
        
        # Add user info to scope for access in route handlers
        scope["user"] = user_info
        
        await self.app(scope, receive, send)


# Convenience functions for manual authentication checks
async def authenticate_request(request: Request) -> Optional[Dict[str, Any]]:
    """
    Manually authenticate a request (for custom authentication logic).
    
    Args:
        request: FastAPI request object
        
    Returns:
        User information or None
    """
    firebase_token = await extract_firebase_token(request)
    if not firebase_token:
        return None
    
    return await verify_and_get_user_info(firebase_token)


def get_current_user(request: Request) -> Optional[Dict[str, Any]]:
    """
    Get current authenticated user from request state.
    
    Args:
        request: FastAPI request object
        
    Returns:
        User information or None
    """
    return getattr(request.state, 'user', None) 