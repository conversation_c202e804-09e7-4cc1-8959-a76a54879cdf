"""
Data Mapper for Backend API Module.

This module provides data mapping functions for converting between
CouchDB documents and API response formats.
"""

from typing import Dict, List, Any, Optional
from datetime import datetime


def chat_history_dict_to_response(chat_doc: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert CouchDB chat document to API response format.
    
    Args:
        chat_doc: CouchDB chat document
    
    Returns:
        Dict[str, Any]: Chat history record in API format
    """
    return {
        "chat_id": chat_doc.get("_id") or "",
        "user_id": chat_doc.get("user_id") or "",
        "conversation_id": chat_doc.get("conversation_id") or "",
        "timestamp": chat_doc.get("timestamp") or "",
        "role": chat_doc.get("role") or "",
        "message": chat_doc.get("message") or "",
        "llm_model_version": chat_doc.get("llm_model_version"),
        "meta_json": chat_doc.get("meta_json") or {},
        "order_id": chat_doc.get("order_id"),
        "type": chat_doc.get("message_type") or "message"
    }


def orders_dict_to_response(order_doc: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert CouchDB order document to API response format.
    
    Args:
        order_doc: CouchDB order document
    
    Returns:
        Dict[str, Any]: Order record in API format
    """
    return {
        "order_id": order_doc.get("order_id") or "",
        "user_id": order_doc.get("user_id") or "",
        "account_id": order_doc.get("account_id") or "",
        "broker_id": order_doc.get("broker_id"),
        "instrument_id": order_doc.get("instrument_id") or "",
        "symbol": order_doc.get("symbol") or "",
        "broker_order_id": order_doc.get("broker_order_id"),
        "order_type": order_doc.get("order_type") or "",
        "transaction_type": order_doc.get("transaction_type") or "",
        "quantity": order_doc.get("quantity") or 0,
        "price": order_doc.get("price"),
        "trigger_price": order_doc.get("trigger_price"),
        "status": order_doc.get("status") or "",
        "validity": order_doc.get("validity") or "",
        "product_type": order_doc.get("product_type") or "",
        "created_at": order_doc.get("created_at") or "",
        "updated_at": order_doc.get("updated_at") or "",
        "completed_at": order_doc.get("completed_at"),
        "parent_order_id": order_doc.get("parent_order_id"),
        "comments": order_doc.get("comments"),
        "submitted_by": order_doc.get("submitted_by") or "",
        "source": order_doc.get("source") or "",
        "llm_intent_id": order_doc.get("llm_intent_id"),
        "strategy_id": order_doc.get("strategy_id"),
        "is_automated": order_doc.get("is_automated") or False,
        "risk_score": order_doc.get("risk_score") or 0.0,
        "stop_loss_price": order_doc.get("stop_loss_price"),
        "take_profit_price": order_doc.get("take_profit_price"),
        "trailing_stop_percent": order_doc.get("trailing_stop_percent"),
        "portfolio_id": order_doc.get("portfolio_id"),
        "goal_id": order_doc.get("goal_id")
    }


def monitoring_dict_to_response(monitoring_doc: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert CouchDB monitoring document to API response format.
    
    Args:
        monitoring_doc: CouchDB monitoring document
    
    Returns:
        Dict[str, Any]: Monitoring record in API format
    """
    return {
        "monitoring_id": monitoring_doc.get("monitoring_id") or "",
        "user_id": monitoring_doc.get("user_id") or "",
        "account_id": monitoring_doc.get("account_id") or "",
        "broker_id": monitoring_doc.get("broker_id") or "",
        "symbol": monitoring_doc.get("symbol") or "",
        "broker_trade_id": monitoring_doc.get("broker_trade_id") or "",
        "execution_time": monitoring_doc.get("execution_time") or "",
        "executed_quantity": monitoring_doc.get("executed_quantity") or 0,
        "executed_price": monitoring_doc.get("executed_price") or 0.0,
        "transaction_type": monitoring_doc.get("transaction_type") or "",
        "exchange": monitoring_doc.get("exchange") or "",
        "brokerage_fee": monitoring_doc.get("brokerage_fee") or 0.0,
        "taxes_fees": monitoring_doc.get("taxes_fees") or 0.0,
        "net_amount": monitoring_doc.get("net_amount") or 0.0,
        "status": monitoring_doc.get("status") or "",
        "exec_ref": monitoring_doc.get("exec_ref") or "",
        "created_at": monitoring_doc.get("created_at") or "",
        "order_type": monitoring_doc.get("order_type") or "",
        "quantity": monitoring_doc.get("quantity") or 0,
        "price": monitoring_doc.get("price") or 0.0,
        "trigger_price": monitoring_doc.get("trigger_price"),
        "validity": monitoring_doc.get("validity") or "",
        "product_type": monitoring_doc.get("product_type") or "",
        "desc": monitoring_doc.get("desc") or "",
        "stop_loss_price": monitoring_doc.get("stop_loss_price"),
        "take_profit_price": monitoring_doc.get("take_profit_price"),
        "trailing_stop_percent": monitoring_doc.get("trailing_stop_percent")
    }


def prepare_chat_message_for_save(
    user_id: str,
    conversation_id: str,
    role: str,
    message: str,
    llm_model_version: Optional[str] = None,
    meta_json: Optional[Dict[str, Any]] = None,
    order_id: Optional[str] = None,
    type: str = "message"
) -> Dict[str, Any]:
    """
    Prepare chat message data for saving to CouchDB.
    
    Args:
        user_id: The user's unique identifier
        conversation_id: The conversation's unique identifier
        role: The role of the message sender
        message: The message content
        llm_model_version: The LLM model version used (optional)
        meta_json: Additional metadata as a dictionary (optional)
        order_id: Message order in the conversation (optional)
        type: Message type (default: 'message')
    
    Returns:
        Dict[str, Any]: Prepared document for CouchDB
    """
    return {
        "type": "chat_message",
        "user_id": user_id,
        "conversation_id": conversation_id,
        "timestamp": datetime.now().isoformat(),
        "role": role,
        "message": message,
        "llm_model_version": llm_model_version,
        "meta_json": meta_json or {},
        "order_id": order_id,
        "message_type": type
    }


def prepare_summary_for_save(
    user_id: str,
    conversation_id: str,
    summary: str,
    llm_model_version: Optional[str] = None,
    meta_json: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Prepare summary data for saving to CouchDB.
    
    Args:
        user_id: The user's unique identifier
        conversation_id: The conversation's unique identifier
        summary: The summary text content
        llm_model_version: The LLM model version used (optional)
        meta_json: Additional metadata as a dictionary (optional)
    
    Returns:
        Dict[str, Any]: Prepared document for CouchDB
    """
    return {
        "type": "summary",
        "user_id": user_id,
        "conversation_id": conversation_id,
        "timestamp": datetime.now().isoformat(),
        "summary": summary,
        "llm_model_version": llm_model_version,
        "meta_json": meta_json or {}
    }


def prepare_order_for_save(order_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Prepare order data for saving to CouchDB.
    
    Args:
        order_data: Order data dictionary
    
    Returns:
        Dict[str, Any]: Prepared document for CouchDB
    """
    doc = {
        "type": "order",
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat()
    }
    doc.update(order_data)
    return doc


def prepare_monitoring_for_save(monitoring_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Prepare monitoring data for saving to CouchDB.
    
    Args:
        monitoring_data: Monitoring data dictionary
    
    Returns:
        Dict[str, Any]: Prepared document for CouchDB
    """
    doc = {
        "type": "monitoring_instance",
        "created_at": datetime.now().isoformat()
    }
    doc.update(monitoring_data)
    return doc


# Legacy function names for backward compatibility
def chat_history_tuple_to_dict(chat_docs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Convert list of chat documents to API format (backward compatibility)."""
    return [chat_history_dict_to_response(doc) for doc in chat_docs]


def orders_tuple_to_dict(order_docs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Convert list of order documents to API format (backward compatibility)."""
    return [orders_dict_to_response(doc) for doc in order_docs]


def monitoring_tuple_to_dict(monitoring_docs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Convert list of monitoring documents to API format (backward compatibility)."""
    return [monitoring_dict_to_response(doc) for doc in monitoring_docs] 