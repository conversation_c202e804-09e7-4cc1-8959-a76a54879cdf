"""API routes using real CouchDB for the backend module."""

from typing import List, Optional, Dict
from uuid import UUID
import sys
import os
import uuid
import json
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Query, Header, WebSocket, WebSocketDisconnect, status
from starlette.status import HTTP_401_UNAUTHORIZED, HTTP_403_FORBIDDEN, HTTP_500_INTERNAL_SERVER_ERROR
from fastapi.responses import J<PERSON><PERSON>esponse
import logging

from ..models.schemas import (
    # New schemas
    NewChatHistoryRequest,
    NewChatHistoryResponse,
    ChatHistoryMessage,
    ChatHistoryAction,
    ChatHistoryMessageType,
    BrokerName,
    ConversationType,
    # Orders schemas
    OrdersRequest,
    OrdersResponse,
    OrderItem,
    OrderStatus,
    # Monitoring schemas
    MonitoringInstancesRequest,
    MonitoringInstancesResponse,
    MonitoringInstanceItem,
    # WebSocket schemas
    WebSocketChatRequest,
    WebSocketChatResponse,
    WebSocketAction,
    WebSocketMessageType,
    WebSocketResponseMessageType
)

# Import proper services and connection manager
from ..services.connection_manager import connection_manager
from ..services.websocket_service import websocket_chat_service
# Import CouchDB-compatible functions from data_layer_v3
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'data_layer_v3'))
from sql_queries import (
    extract_chat_history,
    fetch_orders_by_user,
    fetch_monitoring_by_user
)
from ..utils.data_mapper import (
    chat_history_dict_to_response,
    orders_dict_to_response,
    monitoring_dict_to_response
)

# Create router
router = APIRouter(prefix="/api/v1", tags=["api"])


def get_current_user_id(authorization: Optional[str] = Header(None)) -> str:
    """Get current user ID from Authorization header (simplified for testing)."""
    # For testing, just return a test user ID
    return "test-user-123"


@router.post("/chatHistory", response_model=NewChatHistoryResponse)
async def get_chat_history(
    request: NewChatHistoryRequest,
    current_user_id: str = Depends(get_current_user_id)
) -> NewChatHistoryResponse:
    """Get chat history for a specific conversation using CouchDB."""
    try:
        # Use connection manager to get database connection (compatibility)
        with connection_manager.get_transaction() as db:
            # Use CouchDB function to extract chat history (cursor param for compatibility)
            try:
                messages = extract_chat_history(request.user_id, request.conversation_id)
            except Exception as db_error:
                logging.error(f"CouchDB connection/query error in chatHistory: {db_error}")
                # Return empty response if database is unavailable
                return NewChatHistoryResponse(
                    user_id=request.user_id,
                    conversation_id=request.conversation_id,
                    type=request.type,
                    brokerName=request.brokerName,
                    history=[]
                )
            
            # Convert messages to response format
            chat_messages = []
            for msg in messages:
                # Skip invalid messages
                if not msg or not isinstance(msg, dict):
                    continue
                
                # Get meta_json for actions and primitives
                meta_json = msg.get("meta_json", {})
                
                # Parse actions from meta_json
                actions = []
                actions_data = meta_json.get("actions", [])
                if isinstance(actions_data, list):
                    for action in actions_data:
                        if not isinstance(action, dict):
                            continue
                        
                        # Map action type properly
                        action_type = action.get("type", "chat").lower()
                        if action_type in ["orders", "monitoring", "chat"]:
                            conv_type = ConversationType(action_type)
                        else:
                            conv_type = ConversationType.CHAT  # Default fallback
                        
                        actions.append(ChatHistoryAction(
                            description=action.get("description", ""),
                            type=conv_type,
                            message=action.get("message", "")
                        ))
                
                # Create chat message using data from CouchDB
                # Map message type properly based on available enum values
                message_type = msg.get("message_type", msg.get("type", "order_confirmation")).lower()
                if message_type == "order_planning" or "planning" in message_type:
                    msg_type = ChatHistoryMessageType.ORDER_PLANNING
                elif message_type == "orders" or "order" in message_type:
                    if "execution" in message_type:
                        msg_type = ChatHistoryMessageType.ORDER_EXECUTION
                    else:
                        msg_type = ChatHistoryMessageType.ORDER_CONFIRMATION
                elif message_type == "monitoring" or "monitor" in message_type:
                    msg_type = ChatHistoryMessageType.MONITOR_ORDER
                else:
                    msg_type = ChatHistoryMessageType.ORDER_CONFIRMATION  # Default fallback
                
                chat_message = ChatHistoryMessage(
                    textMessage=msg.get("message", ""),
                    messageType=msg_type,
                    primitives=meta_json.get("primitives", []),
                    sender=msg.get("sender", "system"),
                    actions=actions
                )
                chat_messages.append(chat_message)
            
            return NewChatHistoryResponse(
                user_id=request.user_id,
                conversation_id=request.conversation_id,
                type=request.type,
                brokerName=request.brokerName,
                history=chat_messages
            )
        
    except Exception as e:
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get chat history: {str(e)}"
        )


@router.post("/orders", response_model=OrdersResponse)
async def get_orders(
    request: OrdersRequest,
    current_user_id: str = Depends(get_current_user_id)
) -> OrdersResponse:
    """Get orders for a user using CouchDB."""
    try:
        # Use connection manager to get database connection
        with connection_manager.get_transaction() as db:
            # Use proper function to fetch orders
            try:
                orders = fetch_orders_by_user(None, request.user_id, request.broker, request.status)
            except Exception as db_error:
                logging.error(f"CouchDB connection/query error in orders: {db_error}")
                # Return empty response if database is unavailable
                return OrdersResponse(
                    orders=[]
                )
            
            # Convert to OrderItem format using data mapper
            order_items = []
            for order in orders:
                # Use data mapper to convert CouchDB document to API format
                order_dict = orders_dict_to_response(order)
                
                # Create OrderItem with proper type conversion and field mapping
                try:
                    # Handle potential None values for numeric fields
                    quantity_val = order_dict.get("quantity")
                    quantity = int(quantity_val) if quantity_val is not None else 0
                    
                    price_val = order_dict.get("price")
                    price = float(price_val) if price_val is not None else 0.0
                    
                    order_item = OrderItem(
                        order_id=str(order_dict.get("order_id", "")),
                        broker=str(order_dict.get("broker_id", "")),
                        symbol=str(order_dict.get("symbol", "")),
                        quantity=quantity,
                        price=price,
                        status=str(order_dict.get("status", "")),
                        timestamp=str(order_dict.get("created_at", ""))
                    )
                    order_items.append(order_item)
                except (ValueError, TypeError) as e:
                    logging.warning(f"Skipping invalid order data: {e}, order_dict: {order_dict}")
                    continue
            
            return OrdersResponse(
                orders=order_items
            )
        
    except Exception as e:
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get orders: {str(e)}"
        )


@router.post("/monitoring/instances", response_model=MonitoringInstancesResponse)
async def get_monitoring_instances(
    request: MonitoringInstancesRequest,
    current_user_id: str = Depends(get_current_user_id)
) -> MonitoringInstancesResponse:
    """Get monitoring instances for a user using CouchDB."""
    try:
        # Use connection manager to get database connection
        with connection_manager.get_transaction() as db:
            # Use proper function to fetch monitoring instances
            try:
                monitoring_instances = fetch_monitoring_by_user(None, request.user_id)
            except Exception as db_error:
                logging.error(f"CouchDB connection/query error in monitoring: {db_error}")
                # Return empty response if database is unavailable
                return MonitoringInstancesResponse(
                    monitoring_instances=[]
                )
            
            # Convert to MonitoringInstanceItem format using data mapper
            instance_items = []
            for instance in monitoring_instances:
                # Use data mapper to convert CouchDB document to API format
                instance_dict = monitoring_dict_to_response(instance)
                
                # Create MonitoringInstanceItem with proper type conversion and field mapping
                try:
                    instance_item = MonitoringInstanceItem(
                        instance_id=str(instance_dict.get("monitoring_id", "")),
                        broker=str(instance_dict.get("broker_id", "")),
                        name=str(instance_dict.get("desc", f"Monitoring Instance {instance_dict.get('monitoring_id', 'Unknown')}")),
                        target=str(instance_dict.get("symbol", "")),
                        status=str(instance_dict.get("status", "")),
                        created_at=str(instance_dict.get("created_at", ""))
                    )
                    instance_items.append(instance_item)
                except (ValueError, TypeError) as e:
                    logging.warning(f"Skipping invalid monitoring instance data: {e}, instance_dict: {instance_dict}")
                    continue
            
            return MonitoringInstancesResponse(
                monitoring_instances=instance_items
            )
        
    except Exception as e:
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get monitoring instances: {str(e)}"
        )


# WebSocket endpoint for real-time chat
@router.websocket("/ws/chat")
async def websocket_chat_endpoint(websocket: WebSocket, user_id: str = Query(...), token: Optional[str] = Query(None)):
    """WebSocket endpoint for real-time chat using proper WebSocket service."""
    
    print(f"🌐 [ROUTE] WebSocket endpoint hit! user_id={user_id}, token={'***' if token else 'None'}")
    logging.info(f"🌐 [ROUTE] WebSocket endpoint hit! user_id={user_id}, token={'***' if token else 'None'}")
    
    try:
        # Connect user to WebSocket service
        await websocket_chat_service.connect(websocket, user_id, token)
        
        while True:
            # Receive message
            data = await websocket.receive_text()
            
            # Process message using WebSocket service (with Mock LLM and proper database integration)
            success = await websocket_chat_service.handle_message(websocket, data)
            
            if not success:
                break
            
    except WebSocketDisconnect:
        print(f"WebSocket disconnected for user {user_id}")
    except Exception as e:
        print(f"WebSocket error for user {user_id}: {e}")
    finally:
        # Disconnect user from WebSocket service
        websocket_chat_service.disconnect(websocket) 