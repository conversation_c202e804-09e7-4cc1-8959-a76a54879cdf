"""Authentication API routes for user management."""

from typing import Optional, Dict, Any
from fastapi import APIRouter, HTTPException, status, Request
from pydantic import BaseModel, Field
import logging
import sys
import os

# Import Firebase service and database functions
from ..services.firebase_service import verify_firebase_token
from ..middleware.auth_middleware import firebase_auth_required
# Import CouchDB-compatible functions from data_layer_v3
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'data_layer_v3'))
from sql_queries import (
    save_user_profile_simple,
    get_user_by_auth_uid_simple,
    check_user_exists_simple,
    create_user_session_mapping_simple,
    get_user_id_by_auth_uid_simple
)

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/v1/auth", tags=["authentication"])


# Request/Response Models
class SignupRequest(BaseModel):
    """Request model for user signup."""
    firebase_token: str = Field(..., description="Firebase ID token")
    name: str = Field(..., min_length=1, max_length=100, description="User's full name")
    phone: Optional[str] = Field(None, description="User's phone number")


class SignupResponse(BaseModel):
    """Response model for user signup."""
    success: bool
    message: str
    user_id: Optional[str]  # None for new users, populated on first WebSocket message
    firebase_uid: str


class ProfileRequest(BaseModel):
    """Request model for user profile updates."""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    phone: Optional[str] = Field(None)


class ProfileResponse(BaseModel):
    """Response model for user profile."""
    firebase_uid: str
    name: str
    phone: Optional[str]
    user_id: str


class CheckUserRequest(BaseModel):
    """Request model for checking if user exists."""
    firebase_token: str = Field(..., description="Firebase ID token")


class CheckUserResponse(BaseModel):
    """Response model for user existence check."""
    exists: bool
    user_id: Optional[str] = None
    firebase_uid: str


@router.post("/signup", response_model=SignupResponse)
async def signup_user(request: SignupRequest) -> SignupResponse:
    """
    Create a new user profile after Firebase authentication.
    This is called when a new user completes the name input step.
    """
    try:
        # Verify Firebase token
        logger.info(f"🔍 [AUTH-CREATE] About to verify Firebase token: {request.firebase_token[:50]}...")
        token_claims = verify_firebase_token(request.firebase_token)
        logger.info(f"🔍 [AUTH-CREATE] Token verification result: {token_claims is not None}")
        if not token_claims:
            logger.error("🔍 [AUTH-CREATE] Firebase token verification failed - token_claims is None")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid Firebase token"
            )
        logger.info(f"🔍 [AUTH-CREATE] Firebase token verified successfully for user: {token_claims.get('uid', 'unknown')}")
        
        firebase_uid = token_claims['firebase_uid']
        
        # Check if user already exists
        if check_user_exists_simple(firebase_uid):
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="User already exists"
            )
        
        # Save user profile to database
        user_doc_id = save_user_profile_simple(firebase_uid, request.name, request.phone)
        
        # Note: Session mapping will be created later when user sends first message via WebSocket
        logger.info(f"New user profile created: firebase_uid={firebase_uid}")
        
        return SignupResponse(
            success=True,
            message="User created successfully",
            user_id=None,  # No user_id until first WebSocket message
            firebase_uid=firebase_uid
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create user"
        )


@router.post("/check-user", response_model=CheckUserResponse)
async def check_user_exists(request: CheckUserRequest) -> CheckUserResponse:
    """
    Check if a user exists in the system after Firebase authentication.
    This is called during login to determine if name input step should be skipped.
    """
    try:
        # Verify Firebase token
        logger.info(f"🔍 [AUTH-CHECK] About to verify Firebase token: {request.firebase_token[:50]}...")
        token_claims = verify_firebase_token(request.firebase_token)
        logger.info(f"🔍 [AUTH-CHECK] Token verification result: {token_claims is not None}")
        if not token_claims:
            logger.error("🔍 [AUTH-CHECK] Firebase token verification failed - token_claims is None")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid Firebase token"
            )
        logger.info(f"🔍 [AUTH-CHECK] Firebase token verified successfully for user: {token_claims.get('uid', 'unknown')}")
        
        firebase_uid = token_claims['firebase_uid']
        
        # Check if user exists
        exists = check_user_exists_simple(firebase_uid)
        user_id = None
        
        if exists:
            # Get or create session mapping
            user_id = get_user_id_by_auth_uid_simple(firebase_uid)
            if not user_id:
                # Create new session mapping if it doesn't exist
                user_id = create_user_session_mapping_simple(firebase_uid)
        
        return CheckUserResponse(
            exists=exists,
            user_id=user_id,
            firebase_uid=firebase_uid
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error checking user existence: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check user"
        )


@router.get("/profile", response_model=ProfileResponse)
@firebase_auth_required
async def get_user_profile(http_request: Request) -> ProfileResponse:
    """Get the current user's profile information."""
    try:
        # Get authenticated user info from middleware
        user_info = http_request.state.user
        firebase_uid = user_info['firebase_uid']
        session_user_id = user_info['user_id']
        
        # Get user profile from database
        user_profile = get_user_by_auth_uid_simple(firebase_uid)
        if not user_profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User profile not found"
            )
        
        return ProfileResponse(
            firebase_uid=firebase_uid,
            name=user_profile.get('name', ''),
            phone=user_profile.get('phone'),
            user_id=session_user_id
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user profile: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user profile"
        )


@router.put("/profile", response_model=ProfileResponse)
@firebase_auth_required
async def update_user_profile(http_request: Request, request: ProfileRequest) -> ProfileResponse:
    """Update the current user's profile information."""
    try:
        # Get authenticated user info from middleware
        user_info = http_request.state.user
        firebase_uid = user_info['firebase_uid']
        session_user_id = user_info['user_id']
        
        # Get current profile
        current_profile = get_user_by_auth_uid_simple(firebase_uid)
        if not current_profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User profile not found"
            )
        
        # Update profile with new values (keep existing if not provided)
        updated_name = request.name if request.name is not None else current_profile.get('name', '')
        updated_phone = request.phone if request.phone is not None else current_profile.get('phone')
        
        # Save updated profile
        save_user_profile_simple(firebase_uid, updated_name, updated_phone)
        
        logger.info(f"User profile updated: firebase_uid={firebase_uid}")
        
        return ProfileResponse(
            firebase_uid=firebase_uid,
            name=updated_name,
            phone=updated_phone,
            user_id=session_user_id
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user profile: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user profile"
        ) 