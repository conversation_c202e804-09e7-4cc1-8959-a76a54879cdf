#!/usr/bin/env python3
"""
Environment Configuration for Real Database Integration

This module sets up environment variables for connecting to the real CouchDB
database by loading from the data_layer_v3 .env file.
"""

import os
from pathlib import Path
from dotenv import load_dotenv

def load_env_from_data_layer():
    """Load environment variables from data_layer_v3 .env file."""
    # Get the path to the data_layer_v3 .env file
    current_dir = Path(__file__).parent.parent  # backend_api_module
    data_layer_env_path = current_dir.parent / "data_layer_v3" / ".env"
    
    if data_layer_env_path.exists():
        with open(data_layer_env_path, 'r') as f:
            for line in f:
                line = line.strip()
                # Skip comments and empty lines
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    
                    # Remove quotes if present
                    if value.startswith('"') and value.endswith('"'):
                        value = value[1:-1]
                    elif value.startswith("'") and value.endswith("'"):
                        value = value[1:-1]
                    
                    # Only set if not already set in environment
                    if not os.getenv(key):
                        os.environ[key] = value
        print(f"✓ Loaded environment variables from {data_layer_env_path}")
    else:
        print(f"⚠ Warning: {data_layer_env_path} not found")

def setup_real_db_environment():
    """Setup environment variables for real database connections."""
    # Load .env from backend_api_module directory only
    backend_env_path = os.path.join(os.path.dirname(__file__), '..', '.env')
    
    if os.path.exists(backend_env_path):
        load_dotenv(backend_env_path)
        print(f"✓ Loaded environment variables from {backend_env_path}")
    else:
        print(f"⚠️  Environment file not found at {backend_env_path}")
    
    # Load CouchDB environment variables from data_layer_v3/.env
    load_env_from_data_layer()
    
    # Setup Firebase service account path if file exists
    backend_dir = os.path.join(os.path.dirname(__file__), '..')
    firebase_service_account_path = os.path.join(backend_dir, 'firebase-service-account.json')
    
    if os.path.exists(firebase_service_account_path):
        # Convert to absolute path
        abs_firebase_path = os.path.abspath(firebase_service_account_path)
        os.environ['FIREBASE_SERVICE_ACCOUNT_PATH'] = abs_firebase_path
        print(f"✓ Set FIREBASE_SERVICE_ACCOUNT_PATH to {abs_firebase_path}")
        
        # Read project ID from service account file and set environment variables
        try:
            import json
            with open(abs_firebase_path, 'r') as f:
                service_account_info = json.load(f)
                project_id = service_account_info.get('project_id')
                if project_id:
                    os.environ['GOOGLE_CLOUD_PROJECT'] = project_id
                    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = abs_firebase_path
                    print(f"✓ Set GOOGLE_CLOUD_PROJECT to {project_id}")
                    print(f"✓ Set GOOGLE_APPLICATION_CREDENTIALS to {abs_firebase_path}")
                else:
                    print(f"⚠️  Service account file missing project_id")
        except Exception as e:
            print(f"⚠️  Error reading service account file: {e}")
    else:
        print(f"⚠️  Firebase service account file not found at {firebase_service_account_path}")
    
    print("Environment variables loaded successfully.")
    
    # CouchDB Configuration - use environment variables or defaults
    # Note: COUCHDB_USERNAME and COUCHDB_PASSWORD should be set in .env file
    if not os.getenv("COUCHDB_HOST"):
        os.environ["COUCHDB_HOST"] = "localhost"
    if not os.getenv("COUCHDB_PORT"):
        os.environ["COUCHDB_PORT"] = "5984"
    if not os.getenv("COUCHDB_DATABASE"):
        os.environ["COUCHDB_DATABASE"] = "aagmanai"
    if not os.getenv("COUCHDB_USE_SSL"):
        os.environ["COUCHDB_USE_SSL"] = "false"

    # PouchDB Configuration
    os.environ["POUCHDB_NAME"] = "aagmanai_local"
    os.environ["POUCHDB_ADAPTER"] = "idb"

    # Testing Configuration
    if not os.getenv("TESTING_MODE"):
        os.environ["TESTING_MODE"] = "false"

    # Connection Settings
    os.environ["DB_TIMEOUT"] = "30"
    os.environ["DB_MAX_RETRIES"] = "3"
    os.environ["DB_RETRY_DELAY"] = "1.0"
    os.environ["DB_POOL_SIZE"] = "10"

    # Sync Settings
    os.environ["SYNC_ENABLED"] = "true"
    os.environ["SYNC_INTERVAL"] = "5000"
    os.environ["SYNC_LIVE"] = "true"
    os.environ["SYNC_RETRY"] = "true"

    # Schema Settings
    os.environ["SCHEMA_VALIDATION_ENABLED"] = "true"
    os.environ["AUTO_CREATE_DESIGN_DOCS"] = "true"

if __name__ == "__main__":
    setup_real_db_environment()
    print("✓ Real database environment configured")
    print(f"  CouchDB: {os.environ['COUCHDB_HOST']}:{os.environ['COUCHDB_PORT']}")
    print(f"  CouchDB Username: {os.environ.get('COUCHDB_USERNAME', 'NOT SET')}")
    print(f"  CouchDB Database: {os.environ.get('COUCHDB_DATABASE', 'NOT SET')}")
    print(f"  Testing Mode: {os.environ.get('TESTING_MODE', 'false')}")
    print("  CouchDB-only configuration complete") 