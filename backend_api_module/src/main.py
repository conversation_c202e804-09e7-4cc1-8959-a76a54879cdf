"""Main FastAPI application."""

import structlog
from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
import os
from contextlib import asynccontextmanager

from .api.routes import router
from .api.auth_routes import router as auth_router
from .utils.config import get_settings

# Ensure environment variables for CouchDB are set
from src.env_config import setup_real_db_environment
setup_real_db_environment()

# Initialize Firebase service during startup
print("🔍 About to import firebase_service from .services.firebase_service")
try:
    from .services.firebase_service import firebase_auth_service, verify_firebase_configuration
    print(f"✅ Import successful - firebase_auth_service type: {type(firebase_auth_service)}")
    print(f"✅ firebase_auth_service._app: {firebase_auth_service._app}")
    print(f"🔥 Firebase service initialized: {firebase_auth_service._app is not None}")
    
    # Verify Firebase configuration
    if verify_firebase_configuration():
        print("✅ Firebase configuration verified successfully")
    else:
        print("❌ Firebase configuration verification failed")
        raise Exception("Firebase configuration is invalid")
        
except Exception as e:
    print(f"❌ Import failed: {e}")
    import traceback
    print(f"❌ Import traceback: {traceback.format_exc()}")
    raise

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

# Get settings
settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifespan context manager for FastAPI application."""
    # Startup
    logger.info("Starting backend API module", version=settings.app_version, environment=settings.app_env)
    
    # Initialize Firebase in production mode
    if settings.app_env == "production":
        try:
            from firebase_admin import initialize_app, credentials
            if settings.google_application_credentials:
                cred = credentials.Certificate(settings.google_application_credentials)
                initialize_app(cred)
                logger.info("Firebase Admin SDK initialized with service account credentials")
            else:
                initialize_app()
                logger.info("Firebase Admin SDK initialized with default credentials")
        except ImportError:
            logger.error("firebase-admin is required for production environment")
            raise
        except Exception as e:
            logger.error("Failed to initialize Firebase Admin SDK", error=str(e))
            raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down backend API module")


# Create FastAPI app
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="Backend API module for Smart Agent trading platform",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add CORS middleware with specific frontend origin
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # Vite dev server
        "http://localhost:5173",  # Vite dev server alternative
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5173",
        "http://localhost:8000",  # Backend server
        "http://127.0.0.1:8000",
        "*"  # Allow all origins for development
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(router)
app.include_router(auth_router)

# Mount static files for the frontend
frontend_path = os.path.join(os.path.dirname(__file__), '..', '..', 'frontend', 'dist')
if os.path.exists(frontend_path):
    app.mount("/static", StaticFiles(directory=frontend_path), name="static")
    logger.info(f"Static files mounted at /static from {frontend_path}")
else:
    logger.warning(f"Frontend directory not found at {frontend_path}")


@app.get("/")
async def root():
    """Root endpoint - serve the frontend HTML file."""
    frontend_index = os.path.join(frontend_path, 'index.html')
    if os.path.exists(frontend_index):
        return FileResponse(frontend_index)
    else:
        return {
            "message": "Smart Agent Backend API",
            "version": settings.app_version,
            "status": "running",
            "note": "Frontend not found, serving API only"
        }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return JSONResponse(
        content={
            "status": "healthy",
            "service": "backend-api-module",
            "version": "1.0.0"
        },
        status_code=200
    )


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "src.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
