#!/usr/bin/env python3
"""
Test runner script for the backend API module.
Provides a convenient way to run different categories of tests.
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def check_environment():
    """Check if the testing environment is properly set up."""
    print("🔍 Checking testing environment...")
    
    # Check if we're in the right directory
    if not os.path.exists("src/main.py"):
        print("❌ Error: src/main.py not found. Please run this script from the backend_api_module directory.")
        return False
    
    # Check if virtual environment is activated
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  Warning: Virtual environment not detected. Consider activating one.")
    
    # Check required packages
    required_packages = ['pytest', 'pytest-asyncio', 'pytest-cov', 'psutil']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing required packages: {', '.join(missing_packages)}")
        print("💡 Install with: pip install " + " ".join(missing_packages))
        return False
    
    # Check CouchDB connection
    try:
        from src.services.connection_manager import ConnectionManager
        manager = ConnectionManager()
        with manager.get_transaction() as conn:
            # Try to access the database
            info = conn.info()
            print(f"✅ CouchDB connection successful: {info.get('db_name', 'unknown')}")
    except Exception as e:
        print(f"❌ CouchDB connection failed: {e}")
        return False
    
    print("✅ Environment check passed!")
    return True

def run_tests(category=None, verbose=False, coverage=False, parallel=False):
    """Run the specified test category."""
    
    # Set environment variables for testing
    os.environ['MOCK_LLM_RESPONSES'] = 'true'
    os.environ['MOCK_LLM_RESPONSE_TYPE'] = 'orders' # or 'monitoring'
    
    # Base pytest command
    cmd = ['python', '-m', 'pytest']
    
    # Add category filter
    if category:
        if category == 'all':
            cmd.extend(['tests/'])
        elif category == 'enhanced_performance':
            cmd.extend(['tests/performance/test_enhanced_performance.py'])
        elif category == 'enhanced_security':
            cmd.extend(['tests/security/test_enhanced_security.py'])
        else:
            cmd.extend([f'tests/{category}/'])
    
    # Add options
    if verbose:
        cmd.extend(['-v', '--tb=short'])
    else:
        cmd.extend(['--tb=short'])
    
    if coverage:
        cmd.extend(['--cov=src', '--cov-report=term-missing', '--cov-report=html'])
    
    if parallel:
        cmd.extend(['-n', 'auto'])
    
    # Add timeout for long-running tests
    cmd.extend(['--timeout=60', '--timeout-method=thread'])
    
    print(f"🚀 Running tests with command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=False)
        return result.returncode == 0
    except KeyboardInterrupt:
        print("\n⏹️  Test execution interrupted by user")
        return False
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Run backend API tests')
    parser.add_argument(
        '--category', '-c',
        choices=['unit', 'integration', 'performance', 'security', 'edge_cases', 'concurrency', 'enhanced_performance', 'enhanced_security', 'all'],
        default='all',
        help='Test category to run'
    )
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Verbose output'
    )
    parser.add_argument(
        '--coverage', '--cov',
        action='store_true',
        help='Generate coverage report'
    )
    parser.add_argument(
        '--parallel', '-p',
        action='store_true',
        help='Run tests in parallel'
    )
    parser.add_argument(
        '--check-env',
        action='store_true',
        help='Check testing environment only'
    )
    
    args = parser.parse_args()
    
    if args.check_env:
        success = check_environment()
        sys.exit(0 if success else 1)
    
    # Always check environment before running tests
    if not check_environment():
        print("❌ Environment check failed. Please fix the issues above and try again.")
        sys.exit(1)
    
    # Run tests
    success = run_tests(
        category=args.category,
        verbose=args.verbose,
        coverage=args.coverage,
        parallel=args.parallel
    )
    
    if success:
        print("✅ All tests passed!")
        sys.exit(0)
    else:
        print("❌ Some tests failed!")
        sys.exit(1)

if __name__ == '__main__':
    main() 