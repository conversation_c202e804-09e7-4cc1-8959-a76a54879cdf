"""Unit tests for connection manager using real CouchDB."""

import pytest
import os
from couchdb import Server, Database

from src.services.connection_manager import ConnectionManager


class TestConnectionManagerRealCouchDB:
    """Test cases for ConnectionManager class using real CouchDB."""

    def test_init(self):
        """Test ConnectionManager initialization."""
        manager = ConnectionManager()
        
        assert manager.server is None
        assert manager.database is None
        assert manager._connected is False

    def test_get_couchdb_connection_url_with_credentials(self):
        """Test getting CouchDB connection URL with credentials."""
        with pytest.MonkeyPatch().context() as m:
            m.setenv('COUCHDB_HOST', 'test-host')
            m.setenv('COUCHDB_PORT', '5984')
            m.setenv('COUCHDB_USERNAME', 'test_user')
            m.setenv('COUCHDB_PASSWORD', 'test_password')
            
            manager = ConnectionManager()
            url = manager._get_couchdb_connection_url()
            
            assert url == "*********************************************/"

    def test_get_couchdb_connection_url_default_values(self):
        """Test getting CouchDB connection URL with default values."""
        with pytest.MonkeyPatch().context() as m:
            m.setenv('COUCHDB_USERNAME', 'test_user')
            m.setenv('COUCHDB_PASSWORD', 'test_password')
            
            manager = ConnectionManager()
            url = manager._get_couchdb_connection_url()
            
            assert url == "*********************************************/"

    def test_get_couchdb_connection_url_missing_username(self):
        """Test getting CouchDB connection URL with missing username."""
        with pytest.MonkeyPatch().context() as m:
            m.setenv('COUCHDB_PASSWORD', 'test_password')
            m.delenv('COUCHDB_USERNAME', raising=False)
            
            manager = ConnectionManager()
            
            with pytest.raises(ValueError, match="COUCHDB_USERNAME must be set"):
                manager._get_couchdb_connection_url()

    def test_get_couchdb_connection_url_missing_password(self):
        """Test getting CouchDB connection URL with missing password."""
        with pytest.MonkeyPatch().context() as m:
            m.setenv('COUCHDB_USERNAME', 'test_user')
            m.delenv('COUCHDB_PASSWORD', raising=False)
            
            manager = ConnectionManager()
            
            with pytest.raises(ValueError, match="COUCHDB_PASSWORD must be set"):
                manager._get_couchdb_connection_url()

    def test_disconnect(self):
        """Test disconnecting from CouchDB."""
        manager = ConnectionManager()
        manager._connected = True
        manager.server = "mock_server"
        manager.database = "mock_database"
        
        manager.disconnect()
        
        assert manager._connected is False
        assert manager.server is None
        assert manager.database is None

    def test_is_connected_true(self):
        """Test is_connected when connected."""
        manager = ConnectionManager()
        manager._connected = True
        
        assert manager.is_connected() is True

    def test_is_connected_false(self):
        """Test is_connected when not connected."""
        manager = ConnectionManager()
        manager._connected = False
        
        assert manager.is_connected() is False

    def test_connect_already_connected(self):
        """Test connecting when already connected."""
        manager = ConnectionManager()
        manager._connected = True
        manager.database = "existing_database"
        
        result = manager.connect()
        
        assert result == "existing_database"

    def test_environment_variable_handling(self):
        """Test environment variable handling."""
        with pytest.MonkeyPatch().context() as m:
            m.setenv('COUCHDB_HOST', 'custom-host')
            m.setenv('COUCHDB_PORT', '5985')
            m.setenv('COUCHDB_USERNAME', 'custom_user')
            m.setenv('COUCHDB_PASSWORD', 'custom_password')
            
            manager = ConnectionManager()
            url = manager._get_couchdb_connection_url()
            
            assert url == "***************************************************/"

    def test_transaction_without_connection(self):
        """Test getting transaction without connection."""
        manager = ConnectionManager()
        manager._connected = False
        
        # The get_transaction method will try to connect if not connected
        # So we need to mock the connect method to fail
        with pytest.MonkeyPatch().context() as m:
            m.setenv('COUCHDB_HOST', 'invalid-host')
            m.setenv('COUCHDB_PORT', '9999')
            m.setenv('COUCHDB_USERNAME', 'invalid')
            m.setenv('COUCHDB_PASSWORD', 'invalid')
            
            with pytest.raises(Exception):
                with manager.get_transaction():
                    pass


class TestConnectionManagerRealCouchDBIntegration:
    """Integration tests for ConnectionManager using real CouchDB."""

    def test_real_couchdb_connection_cycle(self, real_couchdb_connection):
        """Test full connection cycle with real CouchDB."""
        # This test uses the real_couchdb_connection fixture
        # which establishes a real connection to CouchDB
        
        # Verify we can access the database
        assert real_couchdb_connection is not None
        
        # Test basic operations
        test_doc = {
            "_id": "test_connection_manager",
            "type": "test",
            "message": "Testing connection manager"
        }
        
        # Save document
        real_couchdb_connection.save(test_doc)
        
        # Retrieve document
        retrieved_doc = real_couchdb_connection.get("test_connection_manager")
        assert retrieved_doc["message"] == "Testing connection manager"
        
        # Delete document
        real_couchdb_connection.delete(retrieved_doc)
        
        # Verify deletion - document should not exist
        try:
            real_couchdb_connection.get("test_connection_manager")
            assert False, "Document should have been deleted"
        except Exception:
            # Expected - document was deleted
            pass

    def test_connection_manager_with_real_couchdb(self):
        """Test ConnectionManager with real CouchDB connection."""
        # Set up environment variables for real CouchDB
        with pytest.MonkeyPatch().context() as m:
            m.setenv('COUCHDB_USERNAME', 'admin')
            m.setenv('COUCHDB_PASSWORD', '123')
            m.setenv('COUCHDB_HOST', 'localhost')
            m.setenv('COUCHDB_PORT', '5984')
            m.setenv('COUCHDB_DATABASE', 'aagmanai')
            
            manager = ConnectionManager()
            
            # Test connection
            try:
                database = manager.connect()
                assert database is not None
                assert manager.is_connected() is True
                
                # Test transaction context
                with manager.get_transaction() as db:
                    assert db is not None
                    
                    # Test basic operation
                    test_doc = {
                        "_id": "test_manager_real",
                        "type": "test",
                        "message": "Testing real connection"
                    }
                    
                    db.save(test_doc)
                    retrieved = db.get("test_manager_real")
                    assert retrieved["message"] == "Testing real connection"
                    
                    # Cleanup
                    db.delete(retrieved)
                
                # Test disconnect
                manager.disconnect()
                assert manager.is_connected() is False
                
            except Exception as e:
                # If CouchDB is not available, skip the test
                pytest.skip(f"CouchDB not available: {e}") 