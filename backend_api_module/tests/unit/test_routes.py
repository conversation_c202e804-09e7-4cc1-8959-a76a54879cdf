import unittest
from fastapi.testclient import TestClient
from src.main import app

class TestRoutes(unittest.TestCase):

    def setUp(self):
        self.client = TestClient(app)

    def test_get_chat_history_success(self):
        """Test get_chat_history with valid request"""
        request_data = {
            "user_id": "test-user-123",
            "conversation_id": "test-conversation-123",
            "type": "chat",
            "brokerName": "zerodha"
        }
        response = self.client.post("/api/v1/chatHistory", json=request_data)
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn("user_id", data)
        self.assertIn("conversation_id", data)
        self.assertIn("history", data)

    def test_get_chat_history_missing_parameters(self):
        """Test get_chat_history with missing required parameters"""
        request_data = {
            "user_id": "test-user-123"
            # Missing conversation_id, type, brokerName
        }
        response = self.client.post("/api/v1/chatHistory", json=request_data)
        self.assertEqual(response.status_code, 422)  # Validation error

    def test_get_orders_success(self):
        """Test get_orders with valid request"""
        request_data = {
            "user_id": "test-user-123",
            "broker": "zerodha",
            "status": "executed"
        }
        response = self.client.post("/api/v1/orders", json=request_data)
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn("orders", data)  # OrdersResponse has 'orders' field

    def test_get_orders_missing_parameters(self):
        """Test get_orders with missing required parameters"""
        request_data = {
            "user_id": "test-user-123"
            # Missing broker, status (both are optional in schema)
        }
        response = self.client.post("/api/v1/orders", json=request_data)
        self.assertEqual(response.status_code, 200)  # Both broker and status are optional

    def test_get_monitoring_instances_success(self):
        """Test get_monitoring_instances with valid request"""
        request_data = {
            "user_id": "test-user-123"
        }
        response = self.client.post("/api/v1/monitoring/instances", json=request_data)
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn("monitoring_instances", data)  # MonitoringInstancesResponse has 'monitoring_instances' field

    def test_get_monitoring_instances_missing_parameters(self):
        """Test get_monitoring_instances with missing required parameters"""
        request_data = {
            # Missing user_id (required)
        }
        response = self.client.post("/api/v1/monitoring/instances", json=request_data)
        self.assertEqual(response.status_code, 422)  # Validation error for missing user_id

if __name__ == '__main__':
    unittest.main()
