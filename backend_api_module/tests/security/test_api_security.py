"""Security tests for API endpoints using real CouchDB data."""

import pytest
import json
import uuid
from datetime import datetime
from fastapi.testclient import TestClient

from src.models.schemas import BrokerName, ConversationType, OrderStatus


class TestInputValidation:
    """Test input validation and sanitization with real CouchDB."""

    def test_sql_injection_chat_history(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Test SQL injection attempts in chat history endpoint with real CouchDB."""
        # Create test data in CouchDB
        message = {
            "_id": f"chat_security_{uuid.uuid4().hex[:8]}",
            "type": "chat_message",
            "user_id": test_user_id,
            "conversation_id": "test-conversation-456",
            "timestamp": datetime.now().isoformat(),
            "role": "user",
            "message": "Normal message",
            "meta_json": {"broker_name": "zerodha"},
            "message_type": "chat"
        }
        real_couchdb_connection.save(message)

        # Test SQL injection attempts
        sql_injection_payloads = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "'; INSERT INTO users VALUES ('hacker', 'password'); --",
            "' UNION SELECT * FROM users --",
            "'; UPDATE users SET password='hacked' --"
        ]

        for payload in sql_injection_payloads:
            request_data = {
                "user_id": payload,
                "conversation_id": "test-conversation-456",
                "type": "chat",
                "brokerName": "zerodha"
            }

            response = test_client.post("/api/v1/chatHistory", json=request_data)

            # Should not crash or expose data
            assert response.status_code in [200, 422]  # Either success with empty data or validation error

        # Cleanup
        try:
            doc = real_couchdb_connection.get(message["_id"])
            real_couchdb_connection.delete(doc)
        except:
            pass

    def test_sql_injection_orders(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Test SQL injection attempts in orders endpoint with real CouchDB."""
        # Create test data in CouchDB
        order = {
            "_id": f"order_security_{uuid.uuid4().hex[:8]}",
            "type": "order",
            "order_id": f"ORD{uuid.uuid4().hex[:6].upper()}",
            "user_id": test_user_id,
            "broker_id": "zerodha",
            "symbol": "RELIANCE",
            "quantity": 100,
            "price": 2500.0,
            "status": "executed",
            "created_at": datetime.now().isoformat()
        }
        real_couchdb_connection.save(order)

        # Test SQL injection attempts
        sql_injection_payloads = [
            "'; DROP TABLE orders; --",
            "' OR '1'='1",
            "'; INSERT INTO orders VALUES ('hacker', 'FAKE', 1000); --",
            "' UNION SELECT * FROM orders --"
        ]

        for payload in sql_injection_payloads:
            request_data = {
                "user_id": payload,
                "broker": "zerodha",
                "status": None
            }

            response = test_client.post("/api/v1/orders", json=request_data)

            # Should not crash or expose data
            assert response.status_code in [200, 422]  # Either success with empty data or validation error

        # Cleanup
        try:
            doc = real_couchdb_connection.get(order["_id"])
            real_couchdb_connection.delete(doc)
        except:
            pass

    def test_xss_injection_chat_history(self, test_client: TestClient, real_couchdb_connection, test_user_id, test_conversation_id):
        """Test XSS injection attempts in chat history endpoint with real CouchDB."""
        # Create test data with XSS payload in CouchDB
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "javascript:alert('XSS')",
            "<img src=x onerror=alert('XSS')>",
            "';alert('XSS');//",
            "<svg onload=alert('XSS')>"
        ]

        for payload in xss_payloads:
            message = {
                "_id": f"chat_xss_{uuid.uuid4().hex[:8]}",
                "type": "chat_message",
                "user_id": test_user_id,
                "conversation_id": test_conversation_id,
                "timestamp": datetime.now().isoformat(),
                "role": "user",
                "message": payload,
                "meta_json": {"broker_name": "zerodha"},
                "message_type": "chat"
            }
            real_couchdb_connection.save(message)

            request_data = {
                "user_id": test_user_id,
                "conversation_id": test_conversation_id,
                "type": "chat",
                "brokerName": "zerodha"
            }

            response = test_client.post("/api/v1/chatHistory", json=request_data)

            assert response.status_code == 200
            data = response.json()

            # Check if XSS payload is properly escaped or sanitized
            if len(data["history"]) > 0:
                for history_item in data["history"]:
                    if "textMessage" in history_item:
                        # The message should be properly escaped
                        assert "<script>" not in history_item["textMessage"]
                        assert "javascript:" not in history_item["textMessage"]
                        assert "onerror=" not in history_item["textMessage"]
                        assert "onload=" not in history_item["textMessage"]

            # Cleanup
            try:
                doc = real_couchdb_connection.get(message["_id"])
                real_couchdb_connection.delete(doc)
            except:
                pass

    def test_no_sql_injection_couchdb(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Test NoSQL injection attempts against CouchDB with real data."""
        # Create test data in CouchDB
        message = {
            "_id": f"chat_nosql_{uuid.uuid4().hex[:8]}",
            "type": "chat_message",
            "user_id": test_user_id,
            "conversation_id": "test-conversation-456",
            "timestamp": datetime.now().isoformat(),
            "role": "user",
            "message": "Normal message",
            "meta_json": {"broker_name": "zerodha"},
            "message_type": "chat"
        }
        real_couchdb_connection.save(message)

        # Test NoSQL injection attempts
        nosql_injection_payloads = [
            {"$ne": ""},  # MongoDB-style injection
            {"$gt": ""},
            {"$where": "1==1"},
            {"$regex": ".*"},
            {"$exists": True}
        ]

        for payload in nosql_injection_payloads:
            request_data = {
                "user_id": payload,
                "conversation_id": "test-conversation-456",
                "type": "chat",
                "brokerName": "zerodha"
            }

            response = test_client.post("/api/v1/chatHistory", json=request_data)

            # Should handle gracefully
            assert response.status_code in [200, 422]

        # Cleanup
        try:
            doc = real_couchdb_connection.get(message["_id"])
            real_couchdb_connection.delete(doc)
        except:
            pass


class TestAuthenticationSecurity:
    """Test authentication and authorization security with real CouchDB."""

    def test_unauthorized_access_chat_history(self, test_client: TestClient, real_couchdb_connection, test_conversation_id):
        """Test unauthorized access to chat history with real CouchDB."""
        # Create test data for a specific user
        authorized_user = "authorized-user-123"
        message = {
            "_id": f"chat_auth_{uuid.uuid4().hex[:8]}",
            "type": "chat_message",
            "user_id": authorized_user,
            "conversation_id": test_conversation_id,
            "timestamp": datetime.now().isoformat(),
            "role": "user",
            "message": "Private message",
            "meta_json": {"broker_name": "zerodha"},
            "message_type": "chat"
        }
        real_couchdb_connection.save(message)

        # Try to access with different unauthorized users
        unauthorized_users = [
            "unauthorized-user-456",
            "hacker-user-789",
            "test-user-999"
        ]

        for unauthorized_user in unauthorized_users:
            request_data = {
                "user_id": unauthorized_user,
                "conversation_id": test_conversation_id,
                "type": "chat",
                "brokerName": "zerodha"
            }

            response = test_client.post("/api/v1/chatHistory", json=request_data)

            assert response.status_code == 200
            data = response.json()

            # Should not return data for unauthorized user
            assert data["user_id"] == unauthorized_user
            assert len(data["history"]) == 0  # No access to other user's data

        # Cleanup
        try:
            doc = real_couchdb_connection.get(message["_id"])
            real_couchdb_connection.delete(doc)
        except:
            pass

    def test_unauthorized_access_orders(self, test_client: TestClient, real_couchdb_connection):
        """Test unauthorized access to orders with real CouchDB."""
        # Create test data for a specific user
        authorized_user = "authorized-user-123"
        order = {
            "_id": f"order_auth_{uuid.uuid4().hex[:8]}",
            "type": "order",
            "order_id": f"ORD{uuid.uuid4().hex[:6].upper()}",
            "user_id": authorized_user,
            "broker_id": "zerodha",
            "symbol": "RELIANCE",
            "quantity": 100,
            "price": 2500.0,
            "status": "executed",
            "created_at": datetime.now().isoformat()
        }
        real_couchdb_connection.save(order)

        # Try to access with unauthorized user
        unauthorized_user = "unauthorized-user-456"
        request_data = {
            "user_id": unauthorized_user,
            "broker": "zerodha",
            "status": None
        }

        response = test_client.post("/api/v1/orders", json=request_data)

        assert response.status_code == 200
        data = response.json()

        # Should not return data for unauthorized user
        assert len(data["orders"]) == 0  # No access to other user's orders

        # Cleanup
        try:
            doc = real_couchdb_connection.get(order["_id"])
            real_couchdb_connection.delete(doc)
        except:
            pass

    def test_unauthorized_access_monitoring(self, test_client: TestClient, real_couchdb_connection):
        """Test unauthorized access to monitoring instances with real CouchDB."""
        # Create test data for a specific user
        authorized_user = "authorized-user-123"
        instance = {
            "_id": f"monitor_auth_{uuid.uuid4().hex[:8]}",
            "type": "monitoring_instance",
            "monitoring_id": f"MON{uuid.uuid4().hex[:6].upper()}",
            "user_id": authorized_user,
            "broker_id": "zerodha",
            "symbol": "RELIANCE",
            "status": "active",
            "created_at": datetime.now().isoformat(),
            "desc": "Private monitoring"
        }
        real_couchdb_connection.save(instance)

        # Try to access with unauthorized user
        unauthorized_user = "unauthorized-user-456"
        request_data = {
            "user_id": unauthorized_user
        }

        response = test_client.post("/api/v1/monitoring/instances", json=request_data)

        assert response.status_code == 200
        data = response.json()

        # Should not return data for unauthorized user
        assert len(data["monitoring_instances"]) == 0  # No access to other user's monitoring

        # Cleanup
        try:
            doc = real_couchdb_connection.get(instance["_id"])
            real_couchdb_connection.delete(doc)
        except:
            pass

    def test_token_manipulation(self, test_client: TestClient, real_couchdb_connection, test_user_id, test_conversation_id):
        """Test token manipulation attempts with real CouchDB."""
        # Create test data
        message = {
            "_id": f"chat_token_{uuid.uuid4().hex[:8]}",
            "type": "chat_message",
            "user_id": test_user_id,
            "conversation_id": test_conversation_id,
            "timestamp": datetime.now().isoformat(),
            "role": "user",
            "message": "Test message",
            "meta_json": {"broker_name": "zerodha"},
            "message_type": "chat"
        }
        real_couchdb_connection.save(message)

        # Test various token manipulation attempts
        malicious_tokens = [
            "Bearer null",
            "Bearer undefined",
            "Bearer ../../etc/passwd",
            "Bearer <script>alert('token')</script>",
            "Bearer ' OR '1'='1",
            "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
        ]

        for token in malicious_tokens:
            headers = {"Authorization": token}
            request_data = {
                "user_id": test_user_id,
                "conversation_id": test_conversation_id,
                "type": "chat",
                "brokerName": "zerodha"
            }

            response = test_client.post("/api/v1/chatHistory", json=request_data, headers=headers)

            # Should handle gracefully (simplified auth for testing)
            assert response.status_code == 200

        # Cleanup
        try:
            doc = real_couchdb_connection.get(message["_id"])
            real_couchdb_connection.delete(doc)
        except:
            pass


class TestDataExposure:
    """Test data exposure vulnerabilities with real CouchDB."""

    def test_sensitive_data_exposure_chat_history(self, test_client: TestClient, real_couchdb_connection, test_user_id, test_conversation_id):
        """Test for sensitive data exposure in chat history with real CouchDB."""
        # Create test data with potentially sensitive information
        sensitive_message = {
            "_id": f"chat_sensitive_{uuid.uuid4().hex[:8]}",
            "type": "chat_message",
            "user_id": test_user_id,
            "conversation_id": test_conversation_id,
            "timestamp": datetime.now().isoformat(),
            "role": "user",
            "message": "My password is 123456 and my credit card is **************-1111",
            "meta_json": {
                "broker_name": "zerodha",
                "internal_id": "secret_internal_123",
                "debug_info": "sensitive_debug_data"
            },
            "message_type": "chat"
        }
        real_couchdb_connection.save(sensitive_message)

        request_data = {
            "user_id": test_user_id,
            "conversation_id": test_conversation_id,
            "type": "chat",
            "brokerName": "zerodha"
        }

        response = test_client.post("/api/v1/chatHistory", json=request_data)

        assert response.status_code == 200
        data = response.json()

        # Check if sensitive data is properly handled
        if len(data["history"]) > 0:
            for history_item in data["history"]:
                if "textMessage" in history_item:
                    # Should not expose internal IDs or debug info in response
                    assert "secret_internal_123" not in str(history_item)
                    assert "sensitive_debug_data" not in str(history_item)

        # Cleanup
        try:
            doc = real_couchdb_connection.get(sensitive_message["_id"])
            real_couchdb_connection.delete(doc)
        except:
            pass

    def test_data_leakage_orders(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Test for data leakage in orders endpoint with real CouchDB."""
        # Create test data with internal information
        order = {
            "_id": f"order_leak_{uuid.uuid4().hex[:8]}",
            "type": "order",
            "order_id": f"ORD{uuid.uuid4().hex[:6].upper()}",
            "user_id": test_user_id,
            "broker_id": "zerodha",
            "symbol": "RELIANCE",
            "quantity": 100,
            "price": 2500.0,
            "status": "executed",
            "created_at": datetime.now().isoformat(),
            "internal_notes": "Confidential trading strategy",
            "risk_score": 0.85,
            "compliance_flags": ["high_risk", "manual_review"]
        }
        real_couchdb_connection.save(order)

        request_data = {
            "user_id": test_user_id,
            "broker": "zerodha",
            "status": None
        }

        response = test_client.post("/api/v1/orders", json=request_data)

        assert response.status_code == 200
        data = response.json()

        # Check if internal data is not exposed
        if len(data["orders"]) > 0:
            for order_data in data["orders"]:
                # Should not expose internal fields
                assert "internal_notes" not in order_data
                assert "risk_score" not in order_data
                assert "compliance_flags" not in order_data

        # Cleanup
        try:
            doc = real_couchdb_connection.get(order["_id"])
            real_couchdb_connection.delete(doc)
        except:
            pass

    def test_error_information_disclosure(self, test_client: TestClient, real_couchdb_connection):
        """Test for error information disclosure with real CouchDB."""
        # Test with malformed requests to see if sensitive error information is exposed
        malformed_requests = [
            {"invalid_field": "test"},  # Missing required fields
            {"user_id": None, "conversation_id": None},  # Null values
            {"user_id": "", "conversation_id": ""},  # Empty strings
            {"user_id": "a" * 10000, "conversation_id": "b" * 10000},  # Very long strings
        ]

        for request_data in malformed_requests:
            response = test_client.post("/api/v1/chatHistory", json=request_data)

            # Should not expose internal error details
            if response.status_code != 200:
                error_data = response.json()
                # Should not contain sensitive information
                assert "database" not in str(error_data).lower()
                assert "couchdb" not in str(error_data).lower()
                assert "connection" not in str(error_data).lower()
                assert "password" not in str(error_data).lower()
                assert "username" not in str(error_data).lower()


class TestRateLimiting:
    """Test rate limiting and DoS protection with real CouchDB."""

    def test_rapid_requests_chat_history(self, test_client: TestClient, real_couchdb_connection, test_user_id, test_conversation_id):
        """Test rapid requests to chat history endpoint with real CouchDB."""
        # Create test data
        message = {
            "_id": f"chat_rate_{uuid.uuid4().hex[:8]}",
            "type": "chat_message",
            "user_id": test_user_id,
            "conversation_id": test_conversation_id,
            "timestamp": datetime.now().isoformat(),
            "role": "user",
            "message": "Rate limit test",
            "meta_json": {"broker_name": "zerodha"},
            "message_type": "chat"
        }
        real_couchdb_connection.save(message)

        request_data = {
            "user_id": test_user_id,
            "conversation_id": test_conversation_id,
            "type": "chat",
            "brokerName": "zerodha"
        }

        # Make 50 rapid requests
        responses = []
        for _ in range(50):
            response = test_client.post("/api/v1/chatHistory", json=request_data)
            responses.append(response)

        # All requests should succeed (no rate limiting implemented in current version)
        success_count = sum(1 for r in responses if r.status_code == 200)
        assert success_count == 50

        # Cleanup
        try:
            doc = real_couchdb_connection.get(message["_id"])
            real_couchdb_connection.delete(doc)
        except:
            pass

    def test_large_payload_orders(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Test large payload handling in orders endpoint with real CouchDB."""
        # Create test data
        order = {
            "_id": f"order_large_{uuid.uuid4().hex[:8]}",
            "type": "order",
            "order_id": f"ORD{uuid.uuid4().hex[:6].upper()}",
            "user_id": test_user_id,
            "broker_id": "zerodha",
            "symbol": "RELIANCE",
            "quantity": 100,
            "price": 2500.0,
            "status": "executed",
            "created_at": datetime.now().isoformat()
        }
        real_couchdb_connection.save(order)

        # Test with large payload
        large_payload = {
            "user_id": test_user_id,
            "broker": "zerodha",
            "status": None,
            "extra_data": "x" * 1000000  # 1MB of extra data
        }

        response = test_client.post("/api/v1/orders", json=large_payload)

        # Should handle large payload gracefully
        assert response.status_code in [200, 413, 422]  # Success, payload too large, or validation error

        # Cleanup
        try:
            doc = real_couchdb_connection.get(order["_id"])
            real_couchdb_connection.delete(doc)
        except:
            pass


class TestWebSocketSecurity:
    """Test WebSocket security with real CouchDB."""

    @pytest.mark.asyncio
    async def test_websocket_authentication_bypass(self, test_client: TestClient, websocket_service):
        """Test WebSocket authentication bypass attempts with real CouchDB."""
        # Test connection without user_id parameter
        with pytest.raises(Exception):  # Should fail without user_id
            with test_client.websocket_connect("/api/v1/ws/chat") as websocket:
                pass

    @pytest.mark.asyncio
    async def test_websocket_message_injection(self, test_client: TestClient, websocket_service, test_user_id, test_conversation_id):
        """Test WebSocket message injection attempts with real CouchDB."""
        with test_client.websocket_connect(f"/api/v1/ws/chat?user_id={test_user_id}") as websocket:
            # Test malicious message payloads
            malicious_messages = [
                {"malicious": "payload"},
                {"user_id": "different_user", "message": "hack attempt"},
                {"conversation_id": "unauthorized_conversation"},
                {"brokerName": "invalid_broker"},
                {"typeOfMessage": "invalid_type"}
            ]

            for malicious_msg in malicious_messages:
                websocket.send_text(json.dumps(malicious_msg))
                
                # Should handle gracefully
                response = websocket.receive_text()
                response_data = json.loads(response)
                
                # Should not crash or expose sensitive data
                assert "error" in response_data or "status" in response_data

    @pytest.mark.asyncio
    async def test_websocket_connection_flooding(self, test_client: TestClient, websocket_service):
        """Test WebSocket connection flooding with real CouchDB."""
        # Try to create many WebSocket connections
        connections = []
        try:
            for i in range(10):
                websocket = test_client.websocket_connect(f"/api/v1/ws/chat?user_id=flood_user_{i}")
                connections.append(websocket)
                websocket.__enter__()
        except Exception as e:
            # Should handle connection limits gracefully
            assert "connection" in str(e).lower() or "limit" in str(e).lower()
        finally:
            # Cleanup connections
            for conn in connections:
                try:
                    conn.__exit__(None, None, None)
                except:
                    pass


class TestDataIntegrity:
    """Test data integrity and consistency with real CouchDB."""

    def test_data_consistency_chat_history(self, test_client: TestClient, real_couchdb_connection, test_user_id, test_conversation_id):
        """Test data consistency in chat history with real CouchDB."""
        # Create multiple messages in CouchDB
        messages = []
        for i in range(5):
            message = {
                "_id": f"chat_consistency_{uuid.uuid4().hex[:8]}",
                "type": "chat_message",
                "user_id": test_user_id,
                "conversation_id": test_conversation_id,
                "timestamp": datetime.now().isoformat(),
                "role": "user" if i % 2 == 0 else "system",
                "message": f"Consistency test message {i}",
                "meta_json": {"broker_name": "zerodha"},
                "message_type": "chat"
            }
            messages.append(message)
            real_couchdb_connection.save(message)

        # Make multiple requests and verify consistency
        request_data = {
            "user_id": test_user_id,
            "conversation_id": test_conversation_id,
            "type": "chat",
            "brokerName": "zerodha"
        }

        responses = []
        for _ in range(3):
            response = test_client.post("/api/v1/chatHistory", json=request_data)
            responses.append(response.json())

        # All responses should be consistent
        for i in range(1, len(responses)):
            assert len(responses[i]["history"]) == len(responses[i-1]["history"])
            assert responses[i]["user_id"] == responses[i-1]["user_id"]
            assert responses[i]["conversation_id"] == responses[i-1]["conversation_id"]

        # Cleanup
        for message in messages:
            try:
                doc = real_couchdb_connection.get(message["_id"])
                real_couchdb_connection.delete(doc)
            except:
                pass

    def test_data_validation_orders(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Test data validation in orders endpoint with real CouchDB."""
        # Create test order with various data types in CouchDB
        order = {
            "_id": f"order_validation_{uuid.uuid4().hex[:8]}",
            "type": "order",
            "order_id": f"ORD{uuid.uuid4().hex[:6].upper()}",
            "user_id": test_user_id,
            "broker_id": "zerodha",
            "symbol": "RELIANCE",
            "quantity": 100,
            "price": 2500.0,
            "status": "executed",
            "created_at": datetime.now().isoformat(),
            "extra_fields": {
                "nested_data": {"deep": "value"},
                "array_data": [1, 2, 3, 4, 5],
                "null_value": None,
                "boolean_value": True
            }
        }
        real_couchdb_connection.save(order)

        request_data = {
            "user_id": test_user_id,
            "broker": "zerodha",
            "status": None
        }

        response = test_client.post("/api/v1/orders", json=request_data)

        assert response.status_code == 200
        data = response.json()

        # Verify data is properly validated and transformed
        if len(data["orders"]) > 0:
            for order_data in data["orders"]:
                # Required fields should be present and properly typed
                assert "order_id" in order_data
                assert "broker" in order_data
                assert "symbol" in order_data
                assert "quantity" in order_data
                assert "price" in order_data
                assert "status" in order_data
                assert "timestamp" in order_data

                # Data types should be correct
                assert isinstance(order_data["quantity"], int)
                assert isinstance(order_data["price"], (int, float))
                assert isinstance(order_data["status"], str)

        # Cleanup
        try:
            doc = real_couchdb_connection.get(order["_id"])
            real_couchdb_connection.delete(doc)
        except:
            pass 