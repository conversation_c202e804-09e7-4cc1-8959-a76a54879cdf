"""Unit-tests verifying that Lang<PERSON> instrumentation is wired correctly.

These tests **do not** talk to the real Langfuse backend – we monkey-patch the
client so the test-suite stays fast, offline and free of secrets.
"""

import asyncio
import json
from types import SimpleNamespace
from unittest.mock import AsyncMock, patch

import pytest

from src.services.websocket_service import WebSocketChatService

# ---------------------------------------------------------------------------
# Helpers
# ---------------------------------------------------------------------------

class FakeSpan:
    """A minimal fake that records update / event calls."""

    def __init__(self, name):
        self.name = name
        self.updates = []  # list of dicts passed to update()
        self.trace_updates = []
        self.events = []

    # context-manager hooks
    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc, tb):
        # no special error handling – tests inspect stored data
        return False

    # Langfuse SDK public methods we rely on
    def update(self, **kwargs):
        self.updates.append(kwargs)

    def update_trace(self, **kwargs):
        self.trace_updates.append(kwargs)

    def create_event(self, name: str, metadata=None):
        self.events.append((name, metadata))

# ---------------------------------------------------------------------------
# Fixtures
# ---------------------------------------------------------------------------

@pytest.fixture()
def fake_langfuse(monkeypatch):
    """Patch langfuse.start_as_current_span to return FakeSpan instances."""

    import logic.observability as observability_module

    spans = []

    def _fake_start_as_current_span(*, name, **kwargs):
        span = FakeSpan(name)
        spans.append(span)
        return span  # works as context manager

    monkeypatch.setattr(observability_module.langfuse, "start_as_current_span", _fake_start_as_current_span)

    return spans  # let tests introspect created spans


# ---------------------------------------------------------------------------
# Test cases
# ---------------------------------------------------------------------------

@pytest.mark.asyncio
async def test_handle_message_creates_dynamic_span(fake_langfuse, monkeypatch):
    """`handle_message` should create a span named according to the function (`handle_message`) and
    propagate user/session IDs to trace-level update."""

    # ---- Prepare WebSocket stub ------------------------------------------------
    class _StubWS(SimpleNamespace):
        async def send_text(self, text):
            self.last_sent = text

    ws = _StubWS()
    svc = WebSocketChatService()

    # Insert minimal connection metadata so code path is simpler
    svc.connection_metadata[ws] = {"user_id": "u-1"}

    # ---- Patch heavy sub-calls so we don't hit DB or LLM -----------------------
    monkeypatch.setattr(svc, "_process_message", AsyncMock(return_value=(
        # Fake WebSocketChatResponse (we only need .dict() & .primitives)
        SimpleNamespace(
            dict=lambda: {"textMessage": "hi", "messageType": "order_planning", "primitives": []},
            primitives=[],
            messageType=SimpleNamespace(value="order_planning")
        ),
        None)),
    )
    monkeypatch.setattr(svc, "_store_message", AsyncMock())
    monkeypatch.setattr(svc, "_store_system_response", AsyncMock())

    # ---- Execute --------------------------------------------------------------
    payload = json.dumps({
        "user_id": "u-1",
        "conversation_id": "c-1",
        "brokerName": "zerodha",
        "message": "hi",
        "typeOfMessage": "chat",
        "modelId": "mock-1",
        "sender": "user",
        "recent_messages": ["older"]
    })

    ok = await svc.handle_message(ws, payload)
    assert ok is True

    # ---- Assertions -----------------------------------------------------------
    # One span should have been created
    assert len(fake_langfuse) == 1
    span = fake_langfuse[0]
    assert span.name == "handle_message"

    # The first update() call should contain accurate recent_messages_len
    input_payload = span.updates[0]["input"]
    assert input_payload["recent_messages_len"] == 1

    # Trace-level update should set semantic meaning in keys (session_id = conversation_id)  
    assert {"user_id": "unknown", "session_id": "conv-1"} in span.trace_updates


@pytest.mark.asyncio
async def test_primitive_event_emitted(fake_langfuse, monkeypatch):
    """Ensure a primitive_requested event is created when primitives exist."""

    class _StubWS(SimpleNamespace):
        async def send_text(self, text):
            self.last_sent = text

    ws = _StubWS()
    svc = WebSocketChatService()
    svc.connection_metadata[ws] = {"user_id": "u-2"}

    primitive = {"action": "buy", "symbol": "AAPL"}

    monkeypatch.setattr(svc, "_process_message", AsyncMock(return_value=(
        SimpleNamespace(
            dict=lambda: {"textMessage": "hi", "messageType": "order_planning", "primitives": [primitive]},
            primitives=[primitive],
            messageType=SimpleNamespace(value="order_planning")
        ),
        None)),
    )
    monkeypatch.setattr(svc, "_store_message", AsyncMock())
    monkeypatch.setattr(svc, "_store_system_response", AsyncMock())

    payload = json.dumps({
        "user_id": "u-2",
        "conversation_id": "c-2",
        "brokerName": "zerodha",
        "message": "hi",
        "typeOfMessage": "chat",
        "modelId": "mock-1",
        "sender": "user",
        "recent_messages": []
    })

    await svc.handle_message(ws, payload)

    span = fake_langfuse[0]
    assert ("primitive_requested", primitive) in span.events
