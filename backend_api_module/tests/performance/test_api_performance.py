"""Performance tests for API endpoints using real CouchDB data."""

import pytest
import time
import asyncio
import concurrent.futures
import uuid
from typing import List, Dict, Any
from datetime import datetime
from fastapi.testclient import TestClient

from src.models.schemas import BrokerName, ConversationType, OrderStatus


class TestAPIPerformance:
    """Performance tests for API endpoints using real CouchDB."""

    def test_chat_history_response_time_small_dataset(self, test_client: TestClient, real_couchdb_connection, test_user_id, test_conversation_id):
        """Test chat history endpoint response time with small dataset in CouchDB."""
        # Create small dataset (10 messages) in CouchDB
        small_dataset = []
        for i in range(10):
            message = {
                "_id": f"chat_small_{uuid.uuid4().hex[:8]}",
                "type": "chat_message",
                "user_id": test_user_id,
                "conversation_id": test_conversation_id,
                "timestamp": datetime.now().isoformat(),
                "role": "user" if i % 2 == 0 else "system",
                "message": f"Small dataset message {i}",
                "meta_json": {"broker_name": "zerodha"},
                "message_type": "chat"
            }
            small_dataset.append(message)
            real_couchdb_connection.save(message)

        request_data = {
            "user_id": test_user_id,
            "conversation_id": test_conversation_id,
            "type": "chat",
            "brokerName": "zerodha"
        }

        # Measure response time
        start_time = time.time()
        response = test_client.post("/api/v1/chatHistory", json=request_data)
        end_time = time.time()

        response_time = end_time - start_time
        
        assert response.status_code == 200
        assert response_time < 1.0  # Should respond within 1 second for small dataset
        
        data = response.json()
        assert len(data["history"]) == 10

        # Cleanup
        for message in small_dataset:
            try:
                doc = real_couchdb_connection.get(message["_id"])
                real_couchdb_connection.delete(doc)
            except:
                pass

    def test_chat_history_response_time_large_dataset(self, test_client: TestClient, large_test_dataset, test_user_id, test_conversation_id):
        """Test chat history endpoint response time with large dataset in CouchDB."""
        request_data = {
            "user_id": test_user_id,
            "conversation_id": test_conversation_id,
            "type": "chat",
            "brokerName": "zerodha"
        }

        # Measure response time
        start_time = time.time()
        response = test_client.post("/api/v1/chatHistory", json=request_data)
        end_time = time.time()

        response_time = end_time - start_time
        
        assert response.status_code == 200
        assert response_time < 5.0  # Should respond within 5 seconds for large dataset
        
        data = response.json()
        assert len(data["history"]) == 1000

    def test_orders_response_time_small_dataset(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Test orders endpoint response time with small dataset in CouchDB."""
        # Create small dataset (50 orders) in CouchDB
        small_dataset = []
        for i in range(50):
            order = {
                "_id": f"order_small_{uuid.uuid4().hex[:8]}",
                "type": "order",
                "order_id": f"ORD{uuid.uuid4().hex[:6].upper()}",
                "user_id": test_user_id,
                "broker_id": "zerodha",
                "symbol": f"STOCK_{i % 5}",
                "quantity": 100 + i,
                "price": 1000.0 + i,
                "status": "executed" if i % 3 == 0 else "pending",
                "created_at": datetime.now().isoformat()
            }
            small_dataset.append(order)
            real_couchdb_connection.save(order)

        request_data = {
            "user_id": test_user_id,
            "broker": "zerodha",
            "status": None
        }

        # Measure response time
        start_time = time.time()
        response = test_client.post("/api/v1/orders", json=request_data)
        end_time = time.time()

        response_time = end_time - start_time
        
        assert response.status_code == 200
        assert response_time < 1.0  # Should respond within 1 second for small dataset
        
        data = response.json()
        assert len(data["orders"]) == 50

        # Cleanup
        for order in small_dataset:
            try:
                doc = real_couchdb_connection.get(order["_id"])
                real_couchdb_connection.delete(doc)
            except:
                pass

    def test_orders_response_time_large_dataset(self, test_client: TestClient, large_test_dataset, test_user_id):
        """Test orders endpoint response time with large dataset in CouchDB."""
        request_data = {
            "user_id": test_user_id,
            "broker": "zerodha",
            "status": None
        }

        # Measure response time
        start_time = time.time()
        response = test_client.post("/api/v1/orders", json=request_data)
        end_time = time.time()

        response_time = end_time - start_time
        
        assert response.status_code == 200
        assert response_time < 3.0  # Should respond within 3 seconds for large dataset
        
        data = response.json()
        assert len(data["orders"]) == 500

    def test_monitoring_response_time_small_dataset(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Test monitoring endpoint response time with small dataset in CouchDB."""
        # Create small dataset (20 monitoring instances) in CouchDB
        small_dataset = []
        for i in range(20):
            instance = {
                "_id": f"monitor_small_{uuid.uuid4().hex[:8]}",
                "type": "monitoring_instance",
                "monitoring_id": f"MON{uuid.uuid4().hex[:6].upper()}",
                "user_id": test_user_id,
                "broker_id": "zerodha",
                "symbol": f"STOCK_{i % 5}",
                "status": "active" if i % 2 == 0 else "inactive",
                "created_at": datetime.now().isoformat(),
                "desc": f"Small monitoring {i}"
            }
            small_dataset.append(instance)
            real_couchdb_connection.save(instance)

        request_data = {
            "user_id": test_user_id
        }

        # Measure response time
        start_time = time.time()
        response = test_client.post("/api/v1/monitoring/instances", json=request_data)
        end_time = time.time()

        response_time = end_time - start_time
        
        assert response.status_code == 200
        assert response_time < 1.0  # Should respond within 1 second for small dataset
        
        data = response.json()
        assert len(data["monitoring_instances"]) == 20

        # Cleanup
        for instance in small_dataset:
            try:
                doc = real_couchdb_connection.get(instance["_id"])
                real_couchdb_connection.delete(doc)
            except:
                pass

    def test_monitoring_response_time_large_dataset(self, test_client: TestClient, large_test_dataset, test_user_id):
        """Test monitoring endpoint response time with large dataset in CouchDB."""
        request_data = {
            "user_id": test_user_id
        }

        # Measure response time
        start_time = time.time()
        response = test_client.post("/api/v1/monitoring/instances", json=request_data)
        end_time = time.time()

        response_time = end_time - start_time
        
        assert response.status_code == 200
        assert response_time < 2.0  # Should respond within 2 seconds for large dataset
        
        data = response.json()
        assert len(data["monitoring_instances"]) == 200


class TestConcurrentRequests:
    """Test API performance under concurrent load using real CouchDB."""

    def test_concurrent_chat_history_requests(self, test_client: TestClient, real_couchdb_connection, test_conversation_id):
        """Test concurrent chat history requests with real CouchDB data."""
        # Create test data for multiple users
        user_ids = [f"user_concurrent_{i}" for i in range(10)]
        
        # Create chat messages for each user
        for user_id in user_ids:
            message = {
                "_id": f"chat_concurrent_{uuid.uuid4().hex[:8]}",
                "type": "chat_message",
                "user_id": user_id,
                "conversation_id": test_conversation_id,
                "timestamp": datetime.now().isoformat(),
                "role": "user",
                "message": f"Concurrent test message for {user_id}",
                "meta_json": {"broker_name": "zerodha"},
                "message_type": "chat"
            }
            real_couchdb_connection.save(message)

        def make_request(user_id: str) -> Dict[str, Any]:
            """Make a single request."""
            request_data = {
                "user_id": user_id,
                "conversation_id": test_conversation_id,
                "type": "chat",
                "brokerName": "zerodha"
            }
            
            start_time = time.time()
            response = test_client.post("/api/v1/chatHistory", json=request_data)
            end_time = time.time()
            
            return {
                "status_code": response.status_code,
                "response_time": end_time - start_time,
                "data": response.json() if response.status_code == 200 else None
            }

        # Make 10 concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [
                executor.submit(make_request, user_id)
                for user_id in user_ids
            ]
            
            results = [future.result() for future in concurrent.futures.as_completed(futures)]

        # Verify all requests succeeded
        assert len(results) == 10
        for result in results:
            assert result["status_code"] == 200
            assert result["response_time"] < 2.0  # Each request should complete within 2 seconds
            assert result["data"] is not None
            assert len(result["data"]["history"]) >= 1

        # Cleanup
        for user_id in user_ids:
            try:
                # Find and delete test messages
                view_result = real_couchdb_connection.view('_all_docs', startkey=f"chat_concurrent_", endkey=f"chat_concurrent_\ufff0")
                for row in view_result:
                    doc = real_couchdb_connection.get(row['id'])
                    if doc.get('user_id') == user_id:
                        real_couchdb_connection.delete(doc)
            except:
                pass

    def test_concurrent_orders_requests(self, test_client: TestClient, real_couchdb_connection):
        """Test concurrent orders requests with real CouchDB data."""
        # Create test data for multiple users
        user_ids = [f"user_orders_{i}" for i in range(15)]
        
        # Create orders for each user
        for user_id in user_ids:
            order = {
                "_id": f"order_concurrent_{uuid.uuid4().hex[:8]}",
                "type": "order",
                "order_id": f"ORD{uuid.uuid4().hex[:6].upper()}",
                "user_id": user_id,
                "broker_id": "zerodha",
                "symbol": f"STOCK_{hash(user_id) % 10}",
                "quantity": 100,
                "price": 1000.0,
                "status": "executed",
                "created_at": datetime.now().isoformat()
            }
            real_couchdb_connection.save(order)

        def make_request(user_id: str) -> Dict[str, Any]:
            """Make a single request."""
            request_data = {
                "user_id": user_id,
                "broker": "zerodha",
                "status": None
            }
            
            start_time = time.time()
            response = test_client.post("/api/v1/orders", json=request_data)
            end_time = time.time()
            
            return {
                "status_code": response.status_code,
                "response_time": end_time - start_time,
                "data": response.json() if response.status_code == 200 else None
            }

        # Make 15 concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=15) as executor:
            futures = [
                executor.submit(make_request, user_id)
                for user_id in user_ids
            ]
            
            results = [future.result() for future in concurrent.futures.as_completed(futures)]

        # Verify all requests succeeded
        assert len(results) == 15
        for result in results:
            assert result["status_code"] == 200
            assert result["response_time"] < 3.0  # Each request should complete within 3 seconds
            assert result["data"] is not None
            assert len(result["data"]["orders"]) >= 1

        # Cleanup
        for user_id in user_ids:
            try:
                # Find and delete test orders
                view_result = real_couchdb_connection.view('_all_docs', startkey=f"order_concurrent_", endkey=f"order_concurrent_\ufff0")
                for row in view_result:
                    doc = real_couchdb_connection.get(row['id'])
                    if doc.get('user_id') == user_id:
                        real_couchdb_connection.delete(doc)
            except:
                pass

    def test_concurrent_monitoring_requests(self, test_client: TestClient, real_couchdb_connection):
        """Test concurrent monitoring requests with real CouchDB data."""
        # Create test data for multiple users
        user_ids = [f"user_monitor_{i}" for i in range(12)]
        
        # Create monitoring instances for each user
        for user_id in user_ids:
            instance = {
                "_id": f"monitor_concurrent_{uuid.uuid4().hex[:8]}",
                "type": "monitoring_instance",
                "monitoring_id": f"MON{uuid.uuid4().hex[:6].upper()}",
                "user_id": user_id,
                "broker_id": "zerodha",
                "symbol": f"STOCK_{hash(user_id) % 10}",
                "status": "active",
                "created_at": datetime.now().isoformat(),
                "desc": f"Concurrent monitoring for {user_id}"
            }
            real_couchdb_connection.save(instance)

        def make_request(user_id: str) -> Dict[str, Any]:
            """Make a single request."""
            request_data = {
                "user_id": user_id
            }
            
            start_time = time.time()
            response = test_client.post("/api/v1/monitoring/instances", json=request_data)
            end_time = time.time()
            
            return {
                "status_code": response.status_code,
                "response_time": end_time - start_time,
                "data": response.json() if response.status_code == 200 else None
            }

        # Make 12 concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=12) as executor:
            futures = [
                executor.submit(make_request, user_id)
                for user_id in user_ids
            ]
            
            results = [future.result() for future in concurrent.futures.as_completed(futures)]

        # Verify all requests succeeded
        assert len(results) == 12
        for result in results:
            assert result["status_code"] == 200
            assert result["response_time"] < 3.0  # Each request should complete within 3 seconds
            assert result["data"] is not None
            assert len(result["data"]["monitoring_instances"]) >= 1

        # Cleanup
        for user_id in user_ids:
            try:
                # Find and delete test monitoring instances
                view_result = real_couchdb_connection.view('_all_docs', startkey=f"monitor_concurrent_", endkey=f"monitor_concurrent_\ufff0")
                for row in view_result:
                    doc = real_couchdb_connection.get(row['id'])
                    if doc.get('user_id') == user_id:
                        real_couchdb_connection.delete(doc)
            except:
                pass


class TestMemoryUsage:
    """Test API memory usage under load with real CouchDB."""

    def test_memory_usage_large_chat_history(self, test_client: TestClient, large_test_dataset, test_user_id, test_conversation_id):
        """Test memory usage with large chat history dataset in CouchDB."""
        import psutil
        import os
        
        request_data = {
            "user_id": test_user_id,
            "conversation_id": test_conversation_id,
            "type": "chat",
            "brokerName": "zerodha"
        }

        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        # Make request
        response = test_client.post("/api/v1/chatHistory", json=request_data)
        
        # Get memory usage after request
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory

        assert response.status_code == 200
        assert memory_increase < 100  # Memory increase should be less than 100MB for 1000 messages

    def test_memory_usage_large_orders(self, test_client: TestClient, large_test_dataset, test_user_id):
        """Test memory usage with large orders dataset in CouchDB."""
        import psutil
        import os
        
        request_data = {
            "user_id": test_user_id,
            "broker": "zerodha",
            "status": None
        }

        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        # Make request
        response = test_client.post("/api/v1/orders", json=request_data)
        
        # Get memory usage after request
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory

        assert response.status_code == 200
        assert memory_increase < 50  # Memory increase should be less than 50MB for 500 orders


class TestScalability:
    """Test API scalability characteristics with real CouchDB."""

    def test_response_time_scaling_chat_history(self, test_client: TestClient, real_couchdb_connection, test_user_id, test_conversation_id):
        """Test how response time scales with dataset size for chat history."""
        dataset_sizes = [10, 50, 100, 500, 1000]
        response_times = []

        for size in dataset_sizes:
            # Create dataset of specified size in CouchDB
            messages = []
            for i in range(size):
                message = {
                    "_id": f"chat_scale_{uuid.uuid4().hex[:8]}",
                    "type": "chat_message",
                    "user_id": test_user_id,
                    "conversation_id": test_conversation_id,
                    "timestamp": datetime.now().isoformat(),
                    "role": "user" if i % 2 == 0 else "system",
                    "message": f"Scaling test message {i}",
                    "meta_json": {"broker_name": "zerodha"},
                    "message_type": "chat"
                }
                messages.append(message)
                real_couchdb_connection.save(message)

            request_data = {
                "user_id": test_user_id,
                "conversation_id": test_conversation_id,
                "type": "chat",
                "brokerName": "zerodha"
            }

            # Measure response time
            start_time = time.time()
            response = test_client.post("/api/v1/chatHistory", json=request_data)
            end_time = time.time()

            response_time = end_time - start_time
            response_times.append(response_time)

            assert response.status_code == 200

            # Cleanup this dataset
            for message in messages:
                try:
                    doc = real_couchdb_connection.get(message["_id"])
                    real_couchdb_connection.delete(doc)
                except:
                    pass

        # Verify response time scales reasonably (not exponentially)
        for i in range(1, len(response_times)):
            # Response time should not increase more than 10x for 100x increase in data
            if dataset_sizes[i] / dataset_sizes[i-1] == 10:
                assert response_times[i] / response_times[i-1] < 10

    def test_concurrent_user_scaling(self, test_client: TestClient, real_couchdb_connection, test_conversation_id):
        """Test how the API handles increasing number of concurrent users."""
        # Create test data for multiple users
        concurrent_users = [5, 10, 20, 50]
        avg_response_times = []

        for num_users in concurrent_users:
            user_ids = [f"user_scale_{i}" for i in range(num_users)]
            
            # Create chat messages for each user
            for user_id in user_ids:
                message = {
                    "_id": f"chat_scale_{uuid.uuid4().hex[:8]}",
                    "type": "chat_message",
                    "user_id": user_id,
                    "conversation_id": test_conversation_id,
                    "timestamp": datetime.now().isoformat(),
                    "role": "user",
                    "message": f"Scaling test message for {user_id}",
                    "meta_json": {"broker_name": "zerodha"},
                    "message_type": "chat"
                }
                real_couchdb_connection.save(message)

            def make_request(user_id: str) -> Dict[str, Any]:
                """Make a single request."""
                request_data = {
                    "user_id": user_id,
                    "conversation_id": test_conversation_id,
                    "type": "chat",
                    "brokerName": "zerodha"
                }
                
                start_time = time.time()
                response = test_client.post("/api/v1/chatHistory", json=request_data)
                end_time = time.time()
                
                return {
                    "status_code": response.status_code,
                    "response_time": end_time - start_time
                }

            with concurrent.futures.ThreadPoolExecutor(max_workers=num_users) as executor:
                futures = [
                    executor.submit(make_request, user_id)
                    for user_id in user_ids
                ]
                
                results = [future.result() for future in concurrent.futures.as_completed(futures)]

            # Calculate average response time
            avg_response_time = sum(r["response_time"] for r in results) / len(results)
            avg_response_times.append(avg_response_time)

            # Verify all requests succeeded
            for result in results:
                assert result["status_code"] == 200

            # Cleanup this batch
            for user_id in user_ids:
                try:
                    # Find and delete test messages
                    view_result = real_couchdb_connection.view('_all_docs', startkey=f"chat_scale_", endkey=f"chat_scale_\ufff0")
                    for row in view_result:
                        doc = real_couchdb_connection.get(row['id'])
                        if doc.get('user_id') == user_id:
                            real_couchdb_connection.delete(doc)
                except:
                    pass

        # Verify response time doesn't degrade too much with more concurrent users
        for i in range(1, len(avg_response_times)):
            # Response time should not increase more than 3x for 10x increase in users
            if concurrent_users[i] / concurrent_users[i-1] == 10:
                assert avg_response_times[i] / avg_response_times[i-1] < 3 