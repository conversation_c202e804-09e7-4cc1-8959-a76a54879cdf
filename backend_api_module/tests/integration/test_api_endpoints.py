"""Integration tests for API endpoints using real CouchDB data."""

import pytest
import json
import time
import uuid
from datetime import datetime
from fastapi.testclient import TestClient

from src.models.schemas import (
    NewChatHistoryRequest,
    OrdersRequest,
    MonitoringInstancesRequest,
    BrokerName,
    ConversationType,
    OrderStatus
)


class TestChatHistoryEndpoint:
    """Integration tests for /chatHistory endpoint using real CouchDB."""

    def test_get_chat_history_success(self, test_client: TestClient, real_chat_history_data, sample_chat_history_request):
        """Test successful chat history retrieval with real CouchDB data."""
        request_data = {
            "user_id": sample_chat_history_request.user_id,
            "conversation_id": sample_chat_history_request.conversation_id,
            "type": sample_chat_history_request.type.value,
            "brokerName": sample_chat_history_request.brokerName.value
        }

        response = test_client.post("/api/v1/chatHistory", json=request_data)

        assert response.status_code == 200
        data = response.json()
        
        assert data["user_id"] == sample_chat_history_request.user_id
        assert data["conversation_id"] == sample_chat_history_request.conversation_id
        assert data["type"] == sample_chat_history_request.type.value
        assert data["brokerName"] == sample_chat_history_request.brokerName.value
        assert len(data["history"]) == 2
        
        # Verify first message
        first_message = data["history"][0]
        assert first_message["textMessage"] == "Hello, I want to place an order"
        assert first_message["messageType"] == "order_confirmation"
        assert first_message["sender"] == "user"
        assert len(first_message["actions"]) == 1
        assert first_message["actions"][0]["description"] == "Place Order"

        # Verify second message
        second_message = data["history"][1]
        assert second_message["textMessage"] == "I'll help you place an order. What would you like to buy?"
        assert second_message["messageType"] == "order_confirmation"
        assert second_message["sender"] == "system"
        assert len(second_message["actions"]) == 1
        assert second_message["actions"][0]["description"] == "Buy Stock"

    def test_get_chat_history_empty_response(self, test_client: TestClient, test_user_id, test_conversation_id):
        """Test chat history retrieval with empty response (no data in CouchDB)."""
        request_data = {
            "user_id": test_user_id,
            "conversation_id": test_conversation_id,
            "type": "chat",
            "brokerName": "zerodha"
        }

        response = test_client.post("/api/v1/chatHistory", json=request_data)

        assert response.status_code == 200
        data = response.json()
        
        assert data["user_id"] == test_user_id
        assert data["conversation_id"] == test_conversation_id
        assert data["type"] == "chat"
        assert data["brokerName"] == "zerodha"
        assert len(data["history"]) == 0

    def test_get_chat_history_invalid_request(self, test_client: TestClient):
        """Test chat history retrieval with invalid request."""
        # Missing required fields
        request_data = {
            "user_id": "test-user-123"
            # Missing conversation_id, type, brokerName
        }

        response = test_client.post("/api/v1/chatHistory", json=request_data)

        assert response.status_code == 422  # Validation error

    def test_get_chat_history_invalid_broker(self, test_client: TestClient, test_user_id, test_conversation_id):
        """Test chat history retrieval with invalid broker."""
        request_data = {
            "user_id": test_user_id,
            "conversation_id": test_conversation_id,
            "type": "chat",
            "brokerName": "invalid_broker"
        }

        response = test_client.post("/api/v1/chatHistory", json=request_data)

        assert response.status_code == 422  # Validation error

    def test_get_chat_history_invalid_conversation_type(self, test_client: TestClient, test_user_id, test_conversation_id):
        """Test chat history retrieval with invalid conversation type."""
        request_data = {
            "user_id": test_user_id,
            "conversation_id": test_conversation_id,
            "type": "invalid_type",
            "brokerName": "zerodha"
        }

        response = test_client.post("/api/v1/chatHistory", json=request_data)

        assert response.status_code == 422  # Validation error


class TestOrdersEndpoint:
    """Integration tests for /orders endpoint using real CouchDB."""

    def test_get_orders_success(self, test_client: TestClient, real_orders_data, sample_orders_request):
        """Test successful orders retrieval with real CouchDB data."""
        request_data = {
            "user_id": sample_orders_request.user_id,
            "broker": sample_orders_request.broker.value if sample_orders_request.broker else None,
            "status": sample_orders_request.status.value if sample_orders_request.status else None
        }

        response = test_client.post("/api/v1/orders", json=request_data)

        assert response.status_code == 200
        data = response.json()
        
        assert len(data["orders"]) == 2
        
        # Verify we have exactly 2 orders
        assert len(data["orders"]) == 2
        
        # Verify both orders exist with expected values (order-independent)
        orders = data["orders"]
        quantities = [order["quantity"] for order in orders]
        prices = [order["price"] for order in orders]
        statuses = [order["status"] for order in orders]
        symbols = [order["symbol"] for order in orders]
        
        # Check that both expected quantities exist
        assert 50 in quantities
        assert 100 in quantities
        
        # Check that both expected prices exist
        assert 2500.0 in prices
        assert 3500.0 in prices
        
        # Check that both expected statuses exist
        assert "executed" in statuses
        assert "pending" in statuses
        
        # Check that both expected symbols exist
        assert "RELIANCE" in symbols
        assert "TCS" in symbols
        
        # Verify all orders have required fields
        for order in orders:
            assert order["order_id"] is not None
            assert order["broker"] == "zerodha"
            assert order["timestamp"] is not None

    def test_get_orders_with_filters(self, test_client: TestClient, real_orders_data, test_user_id):
        """Test orders retrieval with filters using real CouchDB data."""
        request_data = {
            "user_id": test_user_id,
            "broker": "zerodha",
            "status": "executed"
        }

        response = test_client.post("/api/v1/orders", json=request_data)

        assert response.status_code == 200
        data = response.json()
        
        # Should return at least one executed order
        assert len(data["orders"]) >= 1
        for order in data["orders"]:
            assert order["status"] == "executed"

    def test_get_orders_empty_response(self, test_client: TestClient, test_user_id):
        """Test orders retrieval with empty response (no data in CouchDB)."""
        request_data = {
            "user_id": test_user_id,
            "broker": "zerodha",
            "status": None
        }

        response = test_client.post("/api/v1/orders", json=request_data)

        assert response.status_code == 200
        data = response.json()
        
        assert len(data["orders"]) == 0

    def test_get_orders_invalid_request(self, test_client: TestClient):
        """Test orders retrieval with invalid request."""
        # Missing required fields
        request_data = {
            "broker": "zerodha"
            # Missing user_id
        }

        response = test_client.post("/api/v1/orders", json=request_data)

        assert response.status_code == 422  # Validation error

    def test_get_orders_invalid_broker(self, test_client: TestClient, test_user_id):
        """Test orders retrieval with invalid broker."""
        request_data = {
            "user_id": test_user_id,
            "broker": "invalid_broker",
            "status": None
        }

        response = test_client.post("/api/v1/orders", json=request_data)

        assert response.status_code == 422  # Validation error

    def test_get_orders_invalid_status(self, test_client: TestClient, test_user_id):
        """Test orders retrieval with invalid status."""
        request_data = {
            "user_id": test_user_id,
            "broker": "zerodha",
            "status": "invalid_status"
        }

        response = test_client.post("/api/v1/orders", json=request_data)

        assert response.status_code == 422  # Validation error


class TestMonitoringEndpoint:
    """Integration tests for /monitoring/instances endpoint using real CouchDB."""

    def test_get_monitoring_instances_success(self, test_client: TestClient, real_monitoring_data, sample_monitoring_request):
        """Test successful monitoring instances retrieval with real CouchDB data."""
        request_data = {
            "user_id": sample_monitoring_request.user_id
        }

        response = test_client.post("/api/v1/monitoring/instances", json=request_data)

        assert response.status_code == 200
        data = response.json()
        
        assert len(data["monitoring_instances"]) == 2
        
        # Verify both monitoring instances exist with expected values (order-independent)
        instances = data["monitoring_instances"]
        statuses = [instance["status"] for instance in instances]
        targets = [instance["target"] for instance in instances]
        names = [instance["name"] for instance in instances]
        
        # Check that both expected statuses exist
        assert "active" in statuses
        assert "inactive" in statuses
        
        # Check that both expected targets exist
        assert "RELIANCE" in targets
        assert "TCS" in targets
        
        # Check that both expected names exist
        assert "Price Alert for RELIANCE" in names
        assert "Portfolio Monitoring for TCS" in names
        
        # Verify all instances have required fields
        for instance in instances:
            assert instance["instance_id"] is not None
            assert instance["broker"] == "zerodha"
            assert instance["created_at"] is not None

    def test_get_monitoring_instances_empty_response(self, test_client: TestClient, test_user_id):
        """Test monitoring instances retrieval with empty response (no data in CouchDB)."""
        request_data = {
            "user_id": test_user_id
        }

        response = test_client.post("/api/v1/monitoring/instances", json=request_data)

        assert response.status_code == 200
        data = response.json()
        
        assert len(data["monitoring_instances"]) == 0

    def test_get_monitoring_instances_invalid_request(self, test_client: TestClient):
        """Test monitoring instances retrieval with invalid request."""
        # Missing required fields
        request_data = {}

        response = test_client.post("/api/v1/monitoring/instances", json=request_data)

        assert response.status_code == 422  # Validation error


class TestWebSocketEndpoint:
    """Integration tests for WebSocket endpoint using real CouchDB."""

    @pytest.mark.asyncio
    async def test_websocket_connection_success(self, test_client: TestClient):
        """Test successful WebSocket connection."""
        # Import the global service instance that the endpoint actually uses
        from src.services.websocket_service import websocket_chat_service
        
        with test_client.websocket_connect("/api/v1/ws/chat?user_id=test-user-123") as websocket:
            # Verify connection was established in the global service
            assert websocket_chat_service.active_connections.get("test-user-123") is not None

    @pytest.mark.asyncio
    async def test_websocket_message_handling(self, test_client: TestClient, websocket_service, test_user_id, test_conversation_id):
        """Test WebSocket message handling with real CouchDB."""
        with test_client.websocket_connect(f"/api/v1/ws/chat?user_id={test_user_id}") as websocket:
            # Send a test message
            test_message = {
                "user_id": test_user_id,
                "conversation_id": test_conversation_id,
                "brokerName": "zerodha",
                "message": "Hello, I want to place an order",
                "typeOfMessage": "chat",
                "modelId": "gpt-4",
                "sender": "user"
            }
            
            websocket.send_text(json.dumps(test_message))
            
            # Wait for response
            response = websocket.receive_text()
            response_data = json.loads(response)
            
            # Verify response structure
            assert "textMessage" in response_data
            assert "messageType" in response_data
            assert "sender" in response_data
            assert "actions" in response_data
            assert response_data["user_id"] == test_user_id
            assert response_data["conversation_id"] == test_conversation_id

    @pytest.mark.asyncio
    async def test_websocket_invalid_message(self, test_client: TestClient, websocket_service, test_user_id):
        """Test WebSocket with invalid message format."""
        with test_client.websocket_connect(f"/api/v1/ws/chat?user_id={test_user_id}") as websocket:
            # Send invalid JSON
            websocket.send_text("invalid json")
            
            # Should receive error response
            response = websocket.receive_text()
            response_data = json.loads(response)
            
            assert "error" in response_data
            assert response_data["status"] == "error"

    @pytest.mark.asyncio
    async def test_websocket_missing_user_id(self, test_client: TestClient):
        """Test WebSocket connection without user_id parameter."""
        with pytest.raises(Exception):  # Should fail without user_id
            with test_client.websocket_connect("/api/v1/ws/chat") as websocket:
                pass


class TestAuthentication:
    """Integration tests for authentication using real CouchDB."""

    def test_get_current_user_id_with_header(self, test_client: TestClient, test_user_id, test_conversation_id):
        """Test user ID extraction with Authorization header."""
        headers = {"Authorization": "Bearer test-token"}
        
        # Test with any endpoint that uses authentication
        request_data = {
            "user_id": test_user_id,
            "conversation_id": test_conversation_id,
            "type": "chat",
            "brokerName": "zerodha"
        }
        
        response = test_client.post("/api/v1/chatHistory", json=request_data, headers=headers)
        
        # Should work regardless of header value (simplified auth for testing)
        assert response.status_code == 200

    def test_get_current_user_id_without_header(self, test_client: TestClient, test_user_id, test_conversation_id):
        """Test user ID extraction without Authorization header."""
        request_data = {
            "user_id": test_user_id,
            "conversation_id": test_conversation_id,
            "type": "chat",
            "brokerName": "zerodha"
        }
        
        response = test_client.post("/api/v1/chatHistory", json=request_data)
        
        # Should work without header (simplified auth for testing)
        assert response.status_code == 200


class TestErrorHandling:
    """Integration tests for error handling with real CouchDB."""

    def test_database_connection_error_handling(self, test_client: TestClient, test_user_id, test_conversation_id):
        """Test handling of database connection errors."""
        # This test would require temporarily breaking the CouchDB connection
        # For now, we'll test with valid data and ensure graceful handling
        
        request_data = {
            "user_id": test_user_id,
            "conversation_id": test_conversation_id,
            "type": "chat",
            "brokerName": "zerodha"
        }

        response = test_client.post("/api/v1/chatHistory", json=request_data)

        # Should handle gracefully even if no data exists
        assert response.status_code == 200
        data = response.json()
        assert data["user_id"] == test_user_id
        assert data["conversation_id"] == test_conversation_id

    def test_data_validation_error_handling(self, test_client: TestClient, real_orders_data, test_user_id):
        """Test handling of data validation errors with real CouchDB data."""
        request_data = {
            "user_id": test_user_id,
            "broker": "zerodha",
            "status": None
        }

        response = test_client.post("/api/v1/orders", json=request_data)

        # Should handle validation errors gracefully
        assert response.status_code == 200
        data = response.json()
        # Should return valid orders from CouchDB
        assert len(data["orders"]) >= 0


class TestDataFlow:
    """Integration tests for data flow through the system using real CouchDB."""

    def test_complete_data_flow_chat_history(self, test_client: TestClient, real_chat_history_data, sample_chat_history_request):
        """Test complete data flow for chat history endpoint with real CouchDB."""
        request_data = {
            "user_id": sample_chat_history_request.user_id,
            "conversation_id": sample_chat_history_request.conversation_id,
            "type": sample_chat_history_request.type.value,
            "brokerName": sample_chat_history_request.brokerName.value
        }

        response = test_client.post("/api/v1/chatHistory", json=request_data)

        # Verify response
        assert response.status_code == 200
        data = response.json()
        
        # Verify data transformation
        assert data["user_id"] == request_data["user_id"]
        assert data["conversation_id"] == request_data["conversation_id"]
        assert data["type"] == request_data["type"]
        assert data["brokerName"] == request_data["brokerName"]
        
        # Verify data from CouchDB
        assert len(data["history"]) == 2

    def test_complete_data_flow_orders(self, test_client: TestClient, real_orders_data, sample_orders_request):
        """Test complete data flow for orders endpoint with real CouchDB."""
        request_data = {
            "user_id": sample_orders_request.user_id,
            "broker": sample_orders_request.broker.value if sample_orders_request.broker else None,
            "status": sample_orders_request.status.value if sample_orders_request.status else None
        }

        response = test_client.post("/api/v1/orders", json=request_data)

        # Verify response
        assert response.status_code == 200
        data = response.json()
        
        # Verify data transformation
        assert len(data["orders"]) == 2
        
        # Verify data from CouchDB
        for order in data["orders"]:
            assert order["order_id"] is not None
            assert order["broker"] == "zerodha"
            assert order["symbol"] in ["RELIANCE", "TCS"]
            assert order["quantity"] > 0
            assert order["price"] > 0

    def test_complete_data_flow_monitoring(self, test_client: TestClient, real_monitoring_data, sample_monitoring_request):
        """Test complete data flow for monitoring endpoint with real CouchDB."""
        request_data = {
            "user_id": sample_monitoring_request.user_id
        }

        response = test_client.post("/api/v1/monitoring/instances", json=request_data)

        # Verify response
        assert response.status_code == 200
        data = response.json()
        
        # Verify data transformation
        assert len(data["monitoring_instances"]) == 2
        
        # Verify data from CouchDB
        for instance in data["monitoring_instances"]:
            assert instance["instance_id"] is not None
            assert instance["broker"] == "zerodha"
            assert instance["name"] is not None
            assert instance["target"] in ["RELIANCE", "TCS"]
            assert instance["status"] in ["active", "inactive"]


class TestRealWorldScenarios:
    """Integration tests for real-world scenarios using real CouchDB."""

    def test_multiple_users_same_conversation(self, test_client: TestClient, real_couchdb_connection, test_conversation_id):
        """Test multiple users accessing the same conversation."""
        # Create multiple users with same conversation
        user_ids = [f"user_{i}" for i in range(3)]
        
        # Create chat messages for each user in the same conversation
        for user_id in user_ids:
            message = {
                "_id": f"chat_multi_{user_id}_{uuid.uuid4().hex[:8]}",
                "type": "chat_message",
                "user_id": user_id,
                "conversation_id": test_conversation_id,
                "timestamp": datetime.now().isoformat(),
                "role": "user",
                "message": f"Message from {user_id}",
                "meta_json": {"broker_name": "zerodha"},
                "message_type": "chat"
            }
            real_couchdb_connection.save(message)
        
        # Test each user can access the conversation
        for user_id in user_ids:
            request_data = {
                "user_id": user_id,
                "conversation_id": test_conversation_id,
                "type": "chat",
                "brokerName": "zerodha"
            }
            
            response = test_client.post("/api/v1/chatHistory", json=request_data)
            assert response.status_code == 200
            
            data = response.json()
            assert data["user_id"] == user_id
            assert data["conversation_id"] == test_conversation_id
            assert len(data["history"]) >= 1
        
        # Cleanup
        for user_id in user_ids:
            try:
                doc = real_couchdb_connection.get(f"chat_multi_{user_id}_{uuid.uuid4().hex[:8]}")
                real_couchdb_connection.delete(doc)
            except:
                pass

    def test_large_conversation_history(self, test_client: TestClient, real_couchdb_connection, test_user_id, test_conversation_id):
        """Test handling of large conversation history."""
        # Create 100 messages in the conversation
        messages = []
        for i in range(100):
            message = {
                "_id": f"chat_large_{uuid.uuid4().hex[:8]}",
                "type": "chat_message",
                "user_id": test_user_id,
                "conversation_id": test_conversation_id,
                "timestamp": datetime.now().isoformat(),
                "role": "user" if i % 2 == 0 else "system",
                "message": f"Large conversation message {i}",
                "meta_json": {"broker_name": "zerodha"},
                "message_type": "chat"
            }
            messages.append(message)
            real_couchdb_connection.save(message)
        
        # Test retrieval
        request_data = {
            "user_id": test_user_id,
            "conversation_id": test_conversation_id,
            "type": "chat",
            "brokerName": "zerodha"
        }
        
        start_time = time.time()
        response = test_client.post("/api/v1/chatHistory", json=request_data)
        end_time = time.time()
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["history"]) == 100
        
        # Should complete within reasonable time
        assert end_time - start_time < 5.0
        
        # Cleanup
        for message in messages:
            try:
                doc = real_couchdb_connection.get(message["_id"])
                real_couchdb_connection.delete(doc)
            except:
                pass

    def test_mixed_data_types(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Test handling of mixed data types in CouchDB."""
        # Create orders with valid statuses only (as per OrderStatus enum)
        orders = []
        valid_statuses = ["executed", "pending", "cancelled"]
        
        for i, status in enumerate(valid_statuses):
            order = {
                "_id": f"order_mixed_{uuid.uuid4().hex[:8]}",
                "type": "order",
                "order_id": f"ORD{uuid.uuid4().hex[:6].upper()}",
                "user_id": test_user_id,
                "broker_id": "zerodha",
                "symbol": f"STOCK_{i}",
                "quantity": 100 + i,
                "price": 1000.0 + i,
                "status": status,
                "created_at": datetime.now().isoformat()
            }
            orders.append(order)
            real_couchdb_connection.save(order)
        
        # Test retrieval with valid status filters
        for status in valid_statuses:
            request_data = {
                "user_id": test_user_id,
                "broker": "zerodha",
                "status": status
            }
            
            response = test_client.post("/api/v1/orders", json=request_data)
            assert response.status_code == 200
            
            data = response.json()
            for order in data["orders"]:
                assert order["status"] == status
        
        # Test handling of invalid status (should return 422 - this is correct behavior)
        invalid_request_data = {
            "user_id": test_user_id,
            "broker": "zerodha",
            "status": "invalid_status"
        }
        
        response = test_client.post("/api/v1/orders", json=invalid_request_data)
        assert response.status_code == 422  # Validation error for invalid status
        
        # Cleanup
        for order in orders:
            try:
                doc = real_couchdb_connection.get(order["_id"])
                real_couchdb_connection.delete(doc)
            except:
                pass 