# Backend API Module Environment Configuration
# Copy this file to .env and update with your actual values

# Application Environment
APP_ENV=local  # Options: local, production

# Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=false
LOG_LEVEL=INFO

# JWT Configuration (for local development)
JWT_SECRET_KEY=your-secret-key-change-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# Firebase Configuration (for production)
# GOOGLE_APPLICATION_CREDENTIALS=/path/to/your/service-account-key.json

# LLM Configuration
# OpenAI API Key (required for OpenAI models like gpt-4, gpt-3.5-turbo)
OPENAI_API_KEY=your_openai_api_key_here

# Gemini API Key (optional, for Google Gemini models)
# GEMINI_API_KEY=your_gemini_api_key_here



# Data Layer Configuration
DATA_LAYER_MODULE=data_layer_v3

# WebSocket Configuration
WEBSOCKET_PING_INTERVAL=20
WEBSOCKET_PING_TIMEOUT=20

# Note: CouchDB configuration is loaded from data_layer_v3/.env file
# The following variables are set automatically by env_config.py:
# - COUCHDB_HOST
# - COUCHDB_PORT
# - COUCHDB_USERNAME
# - COUCHDB_PASSWORD
# - COUCHDB_DATABASE
# - COUCHDB_USE_SSL

# Langfuse Configuration (for observability)
# Get these from https://cloud.langfuse.com
LANGFUSE_PUBLIC_KEY=your_langfuse_public_key_here
LANGFUSE_SECRET_KEY=your_langfuse_secret_key_here
LANGFUSE_HOST=https://cloud.langfuse.com 