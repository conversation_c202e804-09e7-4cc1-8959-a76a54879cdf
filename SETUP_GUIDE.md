# Smart Agent Firebase Authentication Setup Guide

This guide provides step-by-step instructions for setting up Firebase Authentication in the Smart Agent application for both local development and production environments.

## Overview

The Smart Agent application now supports environment-specific authentication:

- **Local Development**: Mock authentication for seamless development
- **Production**: Full Firebase Authentication with token validation

## Backend Setup

### 1. Environment Configuration

The backend uses the `APP_ENV` environment variable to control authentication behavior.

#### Local Development Setup

```bash
# Set environment to local development
export APP_ENV=local

# Optional: Set JWT settings (not used in new system but kept for compatibility)
export JWT_SECRET_KEY="your-secret-key-for-local-dev"
export JWT_ALGORITHM="HS256"
export JWT_ACCESS_TOKEN_EXPIRE_MINUTES="30"
```

#### Production Setup

```bash
# Set environment to production
export APP_ENV=production

# Set Firebase service account credentials path
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/service-account-key.json"
```

### 2. Firebase Project Setup (Production Only)

1. **Create a Firebase project**:
   - Go to https://console.firebase.google.com/
   - Click "Create a project"
   - Follow the setup wizard

2. **Enable Authentication**:
   - In the Firebase console, go to Authentication > Sign-in method
   - Enable Email/Password authentication
   - Optionally enable Google Sign-in or other providers

3. **Generate Service Account Credentials**:
   - Go to Project Settings > Service Accounts
   - Click "Generate new private key"
   - Save the JSON file securely
   - Set the path in `GOOGLE_APPLICATION_CREDENTIALS`

### 3. Install Dependencies

```bash
# Navigate to backend directory
cd backend_api_module

# Install dependencies (includes firebase-admin)
pip install -r requirements.txt
```

### 4. Start the Backend Server

#### Local Development
```bash
export APP_ENV=local
python -m uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
```

#### Production
```bash
export APP_ENV=production
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/service-account-key.json"
python -m uvicorn src.main:app --host 0.0.0.0 --port 8000
```

## Frontend Setup

### 1. Environment Configuration

The frontend uses `VITE_APP_ENV` to control authentication behavior.

#### Local Development Setup

Create `.env.local`:
```bash
VITE_APP_ENV=local
VITE_API_BASE_URL=http://localhost:8000

# Firebase config (not used in local mode but required for compilation)
VITE_FIREBASE_API_KEY=your-api-key
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=*********
VITE_FIREBASE_APP_ID=your-app-id
```

#### Production Setup

Create `.env.production`:
```bash
VITE_APP_ENV=production
VITE_API_BASE_URL=https://your-api-domain.com

# Firebase config (required for production)
VITE_FIREBASE_API_KEY=your-production-api-key
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=*********
VITE_FIREBASE_APP_ID=your-app-id
```

### 2. Install Dependencies

```bash
# Navigate to frontend directory
cd frontend-example

# Install dependencies
npm install
```

### 3. Start the Frontend

```bash
npm run dev
```

The application will be available at `http://localhost:3000`.

## Authentication Flow

### Local Development Mode

1. **Backend**: Authentication is completely bypassed
2. **Frontend**: No authentication required
3. **API Calls**: Made without authentication headers
4. **User**: Mock user is automatically provided

### Production Mode

1. **Backend**: Validates Firebase ID tokens
2. **Frontend**: User signs in with Firebase
3. **API Calls**: Firebase ID token is automatically attached
4. **User**: Real user information from Firebase

## Testing the Setup

### Local Development Testing

1. **Start Backend**:
   ```bash
   export APP_ENV=local
   python -m uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
   ```

2. **Start Frontend**:
   ```bash
   cd frontend-example
   npm run dev
   ```

3. **Test API Calls**:
   - Visit http://localhost:3000
   - Authentication should be bypassed
   - API calls should work without authentication

### Production Testing

1. **Configure Firebase**:
   - Set up Firebase project
   - Configure service account credentials
   - Update environment variables

2. **Start Backend**:
   ```bash
   export APP_ENV=production
   export GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/service-account-key.json"
   python -m uvicorn src.main:app --host 0.0.0.0 --port 8000
   ```

3. **Start Frontend**:
   ```bash
   cd frontend-example
   npm run dev
   ```

4. **Test Authentication**:
   - Visit http://localhost:3000
   - Sign in with Firebase
   - API calls should include authentication tokens

## API Testing

### Test Authentication Endpoints

#### Local Development
```bash
# No authentication required
curl -X GET "http://localhost:8000/api/v1/orders?user_id=test&broker_name=test"
```

#### Production
```bash
# Get Firebase ID token (you'll need to implement this)
TOKEN="your-firebase-id-token"

# Test with authentication
curl -X GET "http://localhost:8000/api/v1/orders?user_id=test&broker_name=test" \
  -H "Authorization: Bearer $TOKEN"
```

### Test WebSocket Connections

#### Local Development
```javascript
// No authentication required
const ws = new WebSocket('ws://localhost:8000/ws/chat');
```

#### Production
```javascript
// Authentication required
const token = await firebase.auth().currentUser.getIdToken();
const ws = new WebSocket(`ws://localhost:8000/ws/chat?token=${token}`);
```

## Troubleshooting

### Common Issues

1. **Firebase not initialized**:
   - Check that `GOOGLE_APPLICATION_CREDENTIALS` is set correctly
   - Verify the service account JSON file exists and is valid

2. **Import errors**:
   - Ensure `firebase-admin` is installed: `pip install firebase-admin`
   - Check that all dependencies are installed

3. **Authentication errors**:
   - Verify environment variables are set correctly
   - Check that Firebase project is configured properly

4. **CORS issues**:
   - Ensure CORS middleware is configured in backend
   - Check that frontend is making requests to correct backend URL

### Debug Mode

Enable debug logging:

**Backend**:
```bash
export LOG_LEVEL=DEBUG
```

**Frontend**:
```bash
VITE_DEBUG=true
```

### Environment Variable Checklist

#### Backend (Local Development)
- [ ] `APP_ENV=local`
- [ ] `JWT_SECRET_KEY` (optional)
- [ ] `JWT_ALGORITHM` (optional)
- [ ] `JWT_ACCESS_TOKEN_EXPIRE_MINUTES` (optional)

#### Backend (Production)
- [ ] `APP_ENV=production`
- [ ] `GOOGLE_APPLICATION_CREDENTIALS`

#### Frontend (Local Development)
- [ ] `VITE_APP_ENV=local`
- [ ] `VITE_API_BASE_URL`
- [ ] Firebase config (for compilation)

#### Frontend (Production)
- [ ] `VITE_APP_ENV=production`
- [ ] `VITE_API_BASE_URL`
- [ ] All Firebase config variables

## Security Considerations

### Production Security

1. **Service Account Security**:
   - Store service account JSON securely
   - Use environment variables, not hardcoded paths
   - Rotate credentials regularly

2. **Firebase Security Rules**:
   - Configure Firebase Security Rules
   - Restrict access to authenticated users only

3. **CORS Configuration**:
   - Configure CORS to allow only trusted domains
   - Use HTTPS in production

4. **Environment Variables**:
   - Never commit sensitive credentials to version control
   - Use secure secret management in production

### Local Development Security

1. **Mock Authentication**:
   - Mock authentication is safe for local development
   - No real credentials are used
   - API calls work without authentication

## Migration from JWT to Firebase

If you're migrating from the old JWT system:

1. **Update Environment Variables**:
   - Set `APP_ENV=production` for Firebase
   - Set `APP_ENV=local` for mock authentication

2. **Update Frontend**:
   - Remove JWT token handling
   - Implement Firebase authentication
   - Update API client to use Firebase tokens

3. **Test Thoroughly**:
   - Test both local and production modes
   - Verify all endpoints work correctly
   - Check WebSocket connections

## Next Steps

1. **Configure Firebase Project**: Set up your Firebase project with authentication
2. **Update Environment Variables**: Configure all required environment variables
3. **Test Both Modes**: Verify local and production authentication work correctly
4. **Deploy**: Deploy with proper environment configuration
5. **Monitor**: Monitor authentication logs and errors in production

For additional help, refer to:
- [Firebase Documentation](https://firebase.google.com/docs)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Vite Documentation](https://vitejs.dev/) 