# PouchDB Operations Guide

## 📋 **Overview**

This guide defines the expected schemas and examples for all PouchDB operations in the Smart Agent system, covering execution requests, orders, and monitoring data.

---

## 🔧 **Execution Request Schema**

### **Base Schema**

```typescript
interface PrimitiveAction {
  action: string; // Required: Action type (e.g., "BUY", "SELL", "MONITORPROFIT")
  arguments: Record<string, any>; // Required: Action-specific parameters
  human_friendly_explanation?: string; // Optional: Human-readable description
  need_more_info?: string[]; // Optional: Missing information needed
  clarification?: string; // Optional: Additional clarification
}

interface ExecutionRequest {
  _id: string; // Required: Unique document ID
  _rev?: string; // Optional: Revision (managed by PouchDB)
  type: "execution_request"; // Required: Document type identifier
  firebase_uid: string; // Required: User identifier
  created_at: string; // Required: ISO timestamp
  updated_at: string; // Required: ISO timestamp
  status: "pending" | "processing" | "completed" | "failed";
  primitives: PrimitiveAction[]; // Required: Array of action objects
  tabId?: number; // Optional: Browser tab ID for execution
  siteId?: string; // Optional: Target site identifier
  automationMode?: "currentTab" | "background"; // Optional: Execution mode
  metadata?: {
    // Optional: Additional context
    source: string; // Source of the request
    sessionId?: string; // Session identifier
    conversationId?: string; // Conversation identifier
  };
}
```

### **Example: Basic Execution Request**

```json
{
  "_id": "exec_1705123456789_abc123",
  "type": "execution_request",
  "firebase_uid": "test_user_1234567890abcdef",
  "created_at": "2024-01-13T10:30:45.789Z",
  "updated_at": "2024-01-13T10:30:45.789Z",
  "status": "pending",
  "primitives": [
    {
      "action": "BUY",
      "arguments": {
        "symbol": "RELIANCE",
        "exchange": "NSE",
        "quantity": 100,
        "productType": "CNC"
      },
      "human_friendly_explanation": "Will place a Market Buy Order for 100 shares of RELIANCE in NSE under Delivery (CNC).",
      "need_more_info": [],
      "clarification": ""
    }
  ],
  "tabId": 123456789,
  "siteId": "kiteByZerodha",
  "automationMode": "currentTab",
  "metadata": {
    "source": "websocket",
    "sessionId": "session_abc123",
    "conversationId": "conv_def456"
  }
}
```

### **Example: Complex Multi-Action Execution**

```json
{
  "_id": "exec_1705123500000_xyz789",
  "type": "execution_request",
  "firebase_uid": "test_user_1234567890abcdef",
  "created_at": "2024-01-13T10:31:40.000Z",
  "updated_at": "2024-01-13T10:31:40.000Z",
  "status": "pending",
  "primitives": [
    {
      "action": "BUY",
      "arguments": {
        "symbol": "TCS",
        "exchange": "NSE",
        "quantity": 50,
        "price": 3500,
        "productType": "CNC",
        "orderType": "LIMIT"
      },
      "human_friendly_explanation": "Will place a Limit Buy Order for 50 shares of TCS at ₹3500 in NSE under Delivery (CNC).",
      "need_more_info": [],
      "clarification": ""
    },
    {
      "action": "SELL",
      "arguments": {
        "symbol": "INFY",
        "exchange": "NSE",
        "quantity": 25,
        "triggerPrice": 1800,
        "orderType": "SL"
      },
      "human_friendly_explanation": "Will place a Stop Loss Order for 25 shares of INFY at trigger price ₹1800 in NSE.",
      "need_more_info": [],
      "clarification": ""
    },
    {
      "action": "MONITORPROFIT",
      "arguments": {
        "symbol": "HDFC",
        "targetPercent": 5,
        "type": "PROFIT_TARGET"
      },
      "human_friendly_explanation": "Will monitor profit target of 5% on existing HDFC positions.",
      "need_more_info": [],
      "clarification": ""
    }
  ],
  "automationMode": "background",
  "metadata": {
    "source": "rule_engine",
    "sessionId": "session_rule_001"
  }
}
```

### **Available Primitive Actions**

Based on the executor constants, here are all supported primitive actions:

| Action                         | Description               | Required Arguments                                                       |
| ------------------------------ | ------------------------- | ------------------------------------------------------------------------ |
| `BUY`                          | Market buy order          | `symbol`, `exchange`, `quantity`, `productType`                          |
| `SELL`                         | Market sell order         | `symbol`, `exchange`, `quantity`, `productType`                          |
| `PlaceBuyLimitOrder`           | Limit buy order           | `symbol`, `exchange`, `quantity`, `price`, `productType`                 |
| `PlaceSellLimitOrder`          | Limit sell order          | `symbol`, `exchange`, `quantity`, `price`, `productType`                 |
| `PlaceBuyStopLossMarketOrder`  | Buy stop-loss market      | `symbol`, `exchange`, `quantity`, `triggerPrice`, `productType`          |
| `PlaceSellStopLossMarketOrder` | Sell stop-loss market     | `symbol`, `exchange`, `quantity`, `triggerPrice`, `productType`          |
| `PlaceBuyStopLossLimitOrder`   | Buy stop-loss limit       | `symbol`, `exchange`, `quantity`, `triggerPrice`, `price`, `productType` |
| `PlaceSellStopLossLimitOrder`  | Sell stop-loss limit      | `symbol`, `exchange`, `quantity`, `triggerPrice`, `price`, `productType` |
| `MONITORPROFIT`                | Monitor profit targets    | `symbol`, `targetAmount`, `targetPercent`, `monitorInterval`             |
| `CancelOrder`                  | Cancel existing order     | `orderId`, `symbol`                                                      |
| `GetPortfolioStats`            | Get portfolio statistics  | None                                                                     |
| `GetOpenPositionPnL`           | Get position P&L          | `symbol` (optional)                                                      |
| `SelectOrderByCriteria`        | Select orders by criteria | `criteria` object                                                        |

### **Common Argument Values**

- **productType**: `"CNC"` (Delivery), `"MIS"` (Intraday), `"NRML"` (Normal)
- **exchange**: `"NSE"`, `"BSE"`
- **orderType**: `"MARKET"`, `"LIMIT"`, `"SL"` (Stop Loss), `"SL-M"` (Stop Loss Market)

---

## 📊 **Orders Schema**

### **Base Schema**

```typescript
interface Order {
  _id: string; // Required: Unique document ID (pattern: "order_${executionRequestId}_${actionId}")
  _rev?: string; // Optional: Revision (managed by PouchDB)
  type: "order_result"; // Required: Document type identifier
  firebase_uid: string; // Required: User identifier
  created_at: string; // Required: ISO timestamp
  updated_at: string; // Required: ISO timestamp

  // Tracking IDs (PouchDB-specific)
  execution_request_id: string; // Required: Reference to execution request
  action_id: string; // Required: Action index within execution request

  // Frontend-compatible fields
  id: string; // Required: Same as _id for frontend compatibility
  symbol: string; // Required: Trading symbol (e.g., "RELIANCE")
  tradeType: "BUY" | "SELL"; // Required: Order direction
  quantity: string; // Required: Number of shares (as string for frontend)
  price: string; // Required: Price (as string for frontend)
  status: "pending" | "executed" | "cancelled"; // Required: Order status
  timestamp: string; // Required: ISO timestamp
  broker: string; // Required: Broker name (e.g., "zerodha")
  product: string; // Required: Product type ("CNC", "MIS", "NRML")
  orderType: "MARKET" | "LIMIT"; // Required: Order variety

  // Additional data
  broker_order_id?: string; // Optional: Broker-assigned order ID
  execution_details?: Record<string, any>; // Optional: Detailed execution results
}
```

### **Example: Market Buy Order**

```json
{
  "_id": "order_exec_1705123456789_abc123_0",
  "type": "order_result",
  "firebase_uid": "test_user_1234567890abcdef",
  "created_at": "2024-01-13T10:33:20.000Z",
  "updated_at": "2024-01-13T10:33:25.500Z",
  "symbol": "RELIANCE",
  "exchange": "NSE",
  "quantity": 100,
  "order_type": "BUY",
  "product_type": "CNC",
  "order_variety": "MARKET",
  "status": "COMPLETE",
  "order_id": "240113000123456",
  "executed_quantity": 100,
  "executed_price": 2847.5,
  "broker": "zerodha",
  "execution_request_id": "exec_1705123456789_abc123"
}
```

### **Example: Limit Sell Order with Stop Loss**

```json
{
  "_id": "order_1705123700000_sell_tcs_limit",
  "type": "order",
  "firebase_uid": "test_user_1234567890abcdef",
  "created_at": "2024-01-13T10:35:00.000Z",
  "updated_at": "2024-01-13T10:35:00.000Z",
  "symbol": "TCS",
  "exchange": "NSE",
  "quantity": 25,
  "price": 3600.0,
  "trigger_price": 3550.0,
  "order_type": "SELL",
  "product_type": "CNC",
  "order_variety": "SL",
  "status": "OPEN",
  "order_id": "240113000123457",
  "broker": "zerodha",
  "execution_request_id": "exec_1705123500000_xyz789"
}
```

### **Example: Failed Order**

```json
{
  "_id": "order_1705123800000_buy_infy_failed",
  "type": "order",
  "firebase_uid": "test_user_1234567890abcdef",
  "created_at": "2024-01-13T10:36:40.000Z",
  "updated_at": "2024-01-13T10:36:42.100Z",
  "symbol": "INFY",
  "exchange": "NSE",
  "quantity": 50,
  "price": 1500.0,
  "order_type": "BUY",
  "product_type": "MIS",
  "order_variety": "LIMIT",
  "status": "REJECTED",
  "broker": "zerodha",
  "error_message": "Insufficient funds. Available margin: ₹45,000, Required: ₹75,000"
}
```

---

## 📈 **Monitoring Schema**

### **Base Schema**

```typescript
interface MonitoringAlert {
  _id: string; // Required: Unique document ID (pattern: "monitoring_${executionRequestId}_${actionId}")
  _rev?: string; // Optional: Revision (managed by PouchDB)
  type: "monitoring_alert"; // Required: Document type identifier
  firebase_uid: string; // Required: User identifier
  created_at: string; // Required: ISO timestamp
  updated_at: string; // Required: ISO timestamp

  // Tracking IDs (PouchDB-specific)
  execution_request_id: string; // Required: Reference to execution request
  action_id: string; // Required: Action index within execution request

  // Frontend-compatible fields
  id: string; // Required: Same as _id for frontend compatibility
  description: string; // Required: Human-readable alert description
  symbol: string; // Required: Trading symbol
  triggerPrice: string; // Required: Target price (as string for frontend)
  currentPrice: string; // Required: Current market price (as string)
  progress: string; // Required: Progress indicator (e.g., "0/100")
  progressPercent: number; // Required: Progress percentage (0-100)
  status: "pending" | "triggered" | "stopped"; // Required: Alert status
  orderType: string; // Required: Order type when triggered
  stopLoss: string; // Required: Stop loss price (as string)
  product: string; // Required: Product type ("CNC", "MIS", "NRML")

  // Original condition data (for updates)
  condition?: {
    symbol: string;
    operator: string;
    value: number;
    field?: string;
  };
  onTrigger?: {
    action?: string;
    arguments?: Record<string, any>;
  };
}
```

### **Example: Profit Target Monitor**

```json
{
  "_id": "monitor_1705123900000_reliance_profit",
  "type": "monitoring_alert",
  "firebase_uid": "test_user_1234567890abcdef",
  "created_at": "2024-01-13T10:38:20.000Z",
  "updated_at": "2024-01-13T10:38:20.000Z",
  "alert_type": "PROFIT_TARGET",
  "symbol": "RELIANCE",
  "exchange": "NSE",
  "trigger_condition": {
    "operator": ">=",
    "value": 5.0,
    "field": "profit_percentage"
  },
  "current_value": 2.3,
  "status": "ACTIVE",
  "monitor_interval_seconds": 30,
  "expiry_date": "2024-01-14T10:38:20.000Z",
  "on_trigger": {
    "actions": [
      "Will place a Market Sell Order for 100 shares of RELIANCE to book profits."
    ],
    "execution_mode": "auto"
  }
}
```

### **Example: Stop Loss Alert**

```json
{
  "_id": "monitor_1705124000000_tcs_stoploss",
  "type": "monitoring_alert",
  "firebase_uid": "test_user_1234567890abcdef",
  "created_at": "2024-01-13T10:40:00.000Z",
  "updated_at": "2024-01-13T10:45:30.000Z",
  "alert_type": "STOP_LOSS",
  "symbol": "TCS",
  "exchange": "NSE",
  "trigger_condition": {
    "operator": "<=",
    "value": 3400.0,
    "field": "price"
  },
  "current_value": 3420.5,
  "status": "ACTIVE",
  "monitor_interval_seconds": 15,
  "on_trigger": {
    "actions": [
      "Will place a Market Sell Order for 50 shares of TCS to limit losses."
    ],
    "execution_mode": "auto"
  }
}
```

### **Example: Triggered Price Alert**

```json
{
  "_id": "monitor_1705124100000_infy_price_triggered",
  "type": "monitoring_alert",
  "firebase_uid": "test_user_1234567890abcdef",
  "created_at": "2024-01-13T10:41:40.000Z",
  "updated_at": "2024-01-13T10:47:15.500Z",
  "alert_type": "PRICE_ALERT",
  "symbol": "INFY",
  "exchange": "NSE",
  "trigger_condition": {
    "operator": ">=",
    "value": 1850.0,
    "field": "price"
  },
  "current_value": 1852.75,
  "status": "TRIGGERED",
  "monitor_interval_seconds": 60,
  "triggered_at": "2024-01-13T10:47:15.500Z",
  "triggered_value": 1852.75,
  "on_trigger": {
    "actions": ["Send notification: INFY has reached target price of ₹1850"],
    "execution_mode": "manual"
  },
  "execution_result": {
    "success": true,
    "message": "Notification sent successfully"
  }
}
```

---

## 🔄 **Document Relationships**

### **Execution → Orders Relationship**

```json
{
  "execution_request": {
    "_id": "exec_1705123456789_abc123",
    "primitives": ["Buy 100 RELIANCE shares"]
  },
  "generated_orders": [
    {
      "_id": "order_1705123600000_buy_reliance",
      "execution_request_id": "exec_1705123456789_abc123",
      "symbol": "RELIANCE",
      "order_type": "BUY"
    }
  ]
}
```

### **Orders → Monitoring Relationship**

```json
{
  "order": {
    "_id": "order_1705123600000_buy_reliance",
    "symbol": "RELIANCE",
    "status": "COMPLETE"
  },
  "monitoring_alert": {
    "_id": "monitor_1705123900000_reliance_profit",
    "symbol": "RELIANCE",
    "alert_type": "PROFIT_TARGET",
    "reference_order_id": "order_1705123600000_buy_reliance"
  }
}
```

---

## 🔍 **Query Examples**

### **Find All Pending Executions for User**

```javascript
const result = await localDB.allDocs({
  include_docs: true,
  startkey: "exec_",
  endkey: "exec_\uffff",
});

const pendingExecutions = result.rows
  .map((row) => row.doc)
  .filter((doc) => doc.firebase_uid === userId && doc.status === "pending");
```

### **Find All Active Monitoring Alerts**

```javascript
const result = await localDB.allDocs({
  include_docs: true,
  startkey: "monitor_",
  endkey: "monitor_\uffff",
});

const activeAlerts = result.rows
  .map((row) => row.doc)
  .filter((doc) => doc.firebase_uid === userId && doc.status === "ACTIVE");
```

### **Find Orders by Symbol**

```javascript
const result = await localDB.allDocs({
  include_docs: true,
  startkey: "order_",
  endkey: "order_\uffff",
});

const relianceOrders = result.rows
  .map((row) => row.doc)
  .filter((doc) => doc.firebase_uid === userId && doc.symbol === "RELIANCE");
```

---

## 🏷️ **Document ID Patterns**

### **Actual ID Generation Logic**

Based on the executor code, here are the **real** ID patterns used:

```javascript
// Execution Requests (from ExecutionPouchDBSyncService.ts)
`exec_${Date.now()}_${Math.random()
  .toString(36)
  .substr(2, 9)}`// Example: "exec_1705123456789_abc123def"

// Order Results (from executor/background.js)
`order_${executionRequestId}_${actionId}`// Example: "order_exec_1705123456789_abc123def_0"

// Monitoring Alerts (from executor/background.js)
`monitoring_${executionRequestId}_${actionId}`// Example: "monitoring_exec_1705123456789_abc123def_1"

// Temporary Graph IDs (for rule engine execution)
`temp_${Date.now()}_uuid`;
// Example: "temp_1705123456789_uuid"
```

### **ID Component Breakdown**

- **`executionRequestId`**: The full execution request ID (e.g., "exec_1705123456789_abc123def")
- **`actionId`**: Zero-based index of the primitive action (e.g., "0", "1", "2")
- **`timestamp`**: `Date.now()` in milliseconds (e.g., 1705123456789)
- **`randomId`**: 9-character random string using base36 (e.g., "abc123def")

### **🔗 ID Generation Rules & Flexibility**

#### **✅ Execution Request ID = Flexible**

The execution request ID can be **any format** you choose:

```javascript
// Standard generated format (current default)
_id: `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
// Example: "exec_1705123456789_abc123def"

// Custom formats (all valid)
_id: "exec_2024_user_action_123";
_id: "execution_abc123def";
_id: "my_custom_exec_id_456";
_id: "550e8400-e29b-41d4-a716-************"; // UUID
```

#### **❌ Child IDs = Must Follow Pattern**

Once you have an execution request ID, **child documents MUST follow the hardcoded pattern**:

```javascript
// From executor/background.js - CANNOT be changed without code updates
orderDoc._id = `order_${executionRequestId}_${actionId}`;
monitoringDoc._id = `monitoring_${executionRequestId}_${actionId}`;
```

#### **📋 Real Examples**

**Scenario 1: Standard Generated ID**

```javascript
executionRequestId = "exec_1705123456789_abc123def";
// Child IDs:
orderId = "order_exec_1705123456789_abc123def_0";
monitoringId = "monitoring_exec_1705123456789_abc123def_1";
```

**Scenario 2: Custom Execution ID**

```javascript
executionRequestId = "my_custom_execution_2024_jan_15";
// Child IDs:
orderId = "order_my_custom_execution_2024_jan_15_0";
monitoringId = "monitoring_my_custom_execution_2024_jan_15_1";
```

**Scenario 3: UUID-based Execution ID**

```javascript
executionRequestId = "550e8400-e29b-41d4-a716-************";
// Child IDs:
orderId = "order_550e8400-e29b-41d4-a716-************_0";
monitoringId = "monitoring_550e8400-e29b-41d4-a716-************_1";
```

#### **⚠️ Critical Constraint**

The executor code **hardcodes** the relationship pattern for tracking. You can customize the execution request ID format, but the child ID patterns are **fixed** without code changes.

### **Timestamp Format**

```javascript
// Use Unix timestamp in milliseconds
const timestamp = Date.now();
// Example: 1705123456789

// For human-readable timestamps in documents
const isoTimestamp = new Date().toISOString();
// Example: "2024-01-13T10:30:45.789Z"
```

---

## ⚡ **Best Practices**

### **1. Document Validation**

```javascript
function validateExecutionRequest(doc) {
  const required = ["_id", "type", "firebase_uid", "created_at", "primitives"];
  return required.every((field) => doc[field] !== undefined);
}
```

### **2. Consistent Timestamps**

```javascript
const now = new Date().toISOString();
const doc = {
  created_at: now,
  updated_at: now,
};
```

### **3. Status Management**

```javascript
// Always update 'updated_at' when changing status
function updateStatus(doc, newStatus) {
  doc.status = newStatus;
  doc.updated_at = new Date().toISOString();
  return doc;
}
```

### **4. Error Handling**

```javascript
try {
  await localDB.put(document);
} catch (error) {
  if (error.status === 409) {
    // Document conflict - handle revision mismatch
    console.log("Document conflict, retrying...");
  }
}
```

---

**Document Version**: 1.0  
**Last Updated**: January 2025  
**Status**: Reference Guide
