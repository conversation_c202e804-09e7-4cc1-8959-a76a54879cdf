# Byte-compiled / optimized / DLL files
__pycache__/
*.pyc
*.pyd
*.pyo
*.egg
*.egg-info/

# Distribution / packaging
build/
dist/
*.whl
*.tar.gz
*.zip
*.egg-info/  # Already covered above, but good to reiterate

# Virtual environment
# This is crucial! Never commit your virtual environment.
# Common names for virtual environments:
venv/
.venv/
env/
.env/
.Python/
# If you use Poetry, you might also have:
.venv/
# If you use Pipenv, the virtual environment is often outside the project,
# but if it's inside:
.venv/

# Environment variables (sensitive information)
# This is extremely important! Never commit sensitive data.
.env*
*.env
# If you use a specific settings file for local development:
local_settings.py

# Editors / IDEs
# Ignore files generated by popular IDEs and text editors
.vscode/           # Visual Studio Code
.idea/             # PyCharm, IntelliJ IDEA
*.sublime-project  # Sublime Text
*.sublime-workspace
.DS_Store
*.DS_Store         # macOS specific
Thumbs.db          # Windows specific
ehthumbs.db
.history/          # Some editors (e.g., VS Code) create this for local history

# Testing and coverage
.pytest_cache/
.tox/
htmlcov/
.coverage
.coverage.*
# For pytest, some test run files
.nox/
.pytest_cache/

# Sphinx documentation
docs/_build/
# You might want to ignore the entire _build directory for docs.

# Jupyter Notebook / IPython
.ipynb_checkpoints/
*.ipynb_checkpoints/
# For Jupyter, you might also want to ignore large data outputs
# For example, if you have a `data` directory with large generated files:
# data/*.pkl
# data/*.csv
# data/*.json

# Logs and databases
*.log
*.sqlite3
*.db
# For Django projects, you might see:
db.sqlite3

# Temporary files
*.tmp
*~
~*

# Misc
.cache/
# Other common files that might be generated:
.python-version  # For pyenv
.project
.pydevproject

# You can add specific ignore patterns for frameworks like Django or Flask here:

# Django specific
# staticfiles/  # If you collect static files locally and don't want to commit them
# media/        # User-uploaded media files
# You might have specific uploads directories like:
# uploads/

# Flask specific
# instance/ # Folder for instance-specific files that shouldn't be versioned

# Black/Flake8 cache
.blackd/
.flake8_cache/

node_modules/
server/node_modules/*
*firebase*.json

node_modules/

# Chrome extension development
.chrome-profile/

# Generated executor files (built by npm run build:executor)
executor/ui/index.html
executor/ui/index.js
executor/ui/index.css
executor/ui/*.svg
executor/ui/websocket.worker-*.js
executor/ui/build-timestamp.txt
executor/ui/dev-reload.js

# build in extention
executor/lib/json-rule-engine-bundle.bundle.js
executor/lib/json-rule-engine-bundle.bundle.js.LICENSE.txt