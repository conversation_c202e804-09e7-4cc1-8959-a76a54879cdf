#!/usr/bin/env python3
"""
Simple WebSocket End-to-End Test
Tests the basic chat flow: connect -> send message -> receive response -> verify storage
"""

import asyncio
import websockets
import json
import requests
import time

# Test configuration
WEBSOCKET_URL = "ws://localhost:8000/api/v1/ws/chat"
HTTP_BASE_URL = "http://localhost:8000/api/v1"
TEST_USER_ID = "test-user-websocket-001"
TEST_CONVERSATION_ID = "test-conv-websocket-001"

async def test_websocket_end_to_end():
    """Test complete WebSocket flow: connect -> chat -> verify storage"""
    
    print("🚀 Starting WebSocket End-to-End Test")
    print("=" * 50)
    
    try:
        # Step 1: Connect to WebSocket
        print(f"📡 Connecting to WebSocket: {WEBSOCKET_URL}")
        uri = f"{WEBSOCKET_URL}?user_id={TEST_USER_ID}"
        
        async with websockets.connect(uri) as websocket:
            print(f"✅ Connected successfully as user: {TEST_USER_ID}")
            
            # Step 2: Send a chat message
            test_message = {
                "user_id": TEST_USER_ID,
                "conversation_id": TEST_CONVERSATION_ID,
                "message": "Hello! I want to buy some RELIANCE stocks through Zerodha",
                "sender": "user",
                "typeOfMessage": "orders",
                "brokerName": "zerodha",
                "modelId": "test-model-v1"
            }
            
            print(f"📤 Sending message: {test_message['message']}")
            await websocket.send(json.dumps(test_message))
            
            # Step 3: Receive response
            print("⏳ Waiting for response...")
            response = await websocket.recv()
            response_data = json.loads(response)
            
            print("📥 Received response:")
            print(f"   Text: {response_data.get('textMessage', 'N/A')}")
            print(f"   Type: {response_data.get('messageType', 'N/A')}")
            print(f"   Sender: {response_data.get('sender', 'N/A')}")
            print(f"   Actions: {len(response_data.get('actions', []))} actions")
            
            # Step 4: Wait a moment for database storage to complete
            print("⏳ Waiting for database storage to complete...")
            await asyncio.sleep(2)
            
        print("🔌 WebSocket connection closed")
        
        # Step 5: Verify data storage via HTTP API
        print("\n📊 Verifying data storage via HTTP API...")
        
        # Test chat history endpoint
        chat_history_payload = {
            "user_id": TEST_USER_ID,
            "conversation_id": TEST_CONVERSATION_ID,
            "type": "chat",
            "brokerName": "zerodha"
        }
        
        print(f"🔍 Checking chat history for conversation: {TEST_CONVERSATION_ID}")
        chat_response = requests.post(
            f"{HTTP_BASE_URL}/chatHistory",
            json=chat_history_payload,
            headers={"Content-Type": "application/json"}
        )
        
        if chat_response.status_code == 200:
            chat_data = chat_response.json()
            history = chat_data.get("history", [])
            print(f"✅ Chat history retrieved: {len(history)} messages")
            
            # Show the messages
            for i, msg in enumerate(history):
                print(f"   Message {i+1}: [{msg.get('sender', 'unknown')}] {msg.get('textMessage', 'N/A')}")
        else:
            print(f"❌ Failed to retrieve chat history: {chat_response.status_code}")
            print(f"   Error: {chat_response.text}")
        
        # Step 6: Test summary generation (send more messages to trigger summary)
        print(f"\n🔄 Testing summary generation...")
        
        # Send multiple messages to trigger summary
        async with websockets.connect(uri) as websocket:
            for i in range(3):
                message = {
                    "user_id": TEST_USER_ID,
                    "conversation_id": TEST_CONVERSATION_ID,
                    "message": f"Follow-up message {i+1}: What about my portfolio performance?",
                    "sender": "user",
                    "typeOfMessage": "monitoring",
                    "brokerName": "zerodha",
                    "modelId": "test-model-v1"
                }
                
                await websocket.send(json.dumps(message))
                response = await websocket.recv()
                print(f"   Sent message {i+1}, got response")
                await asyncio.sleep(0.5)  # Small delay between messages
        
        # Wait for summary processing
        await asyncio.sleep(2)
        
        # Check if summary was generated (would need summary endpoint to verify)
        print("✅ Summary generation test completed (check server logs for summary storage)")
        
        print("\n🎉 WebSocket End-to-End Test Completed Successfully!")
        print("=" * 50)
        
        return True
        
    except websockets.exceptions.ConnectionClosed as e:
        print(f"❌ WebSocket connection closed unexpectedly: {e}")
        return False
    except websockets.exceptions.InvalidURI as e:
        print(f"❌ Invalid WebSocket URI: {e}")
        return False
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_server_connectivity():
    """Test if server is running"""
    try:
        response = requests.get(f"{HTTP_BASE_URL.replace('/api/v1', '')}/docs")
        return response.status_code == 200
    except:
        return False

async def main():
    """Main test function"""
    print("🔍 Checking server connectivity...")
    
    if not test_server_connectivity():
        print("❌ Server is not running! Please start the server first:")
        print("   cd backend_api_module && source venv/bin/activate && PYTHONPATH=. uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload")
        return
    
    print("✅ Server is running")
    
    # Run the WebSocket test
    success = await test_websocket_end_to_end()
    
    if success:
        print("\n🎯 TEST RESULT: SUCCESS ✅")
        print("WebSocket chat flow is working correctly!")
    else:
        print("\n💥 TEST RESULT: FAILED ❌")
        print("Issues found in WebSocket chat flow")

if __name__ == "__main__":
    asyncio.run(main()) 