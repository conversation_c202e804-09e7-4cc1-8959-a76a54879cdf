# Model Selection Feature Implementation

## Overview

This document describes the implementation of user-selectable AI models in the Smart Agent system. Users can now choose between different LLM providers and models based on their preferences and needs.

## Features Implemented

### 1. User-Level Model Selection

- Users can select their preferred AI model from a dropdown menu
- Model preferences are stored per user in the database
- Changes take effect immediately for new conversations
- Fallback to default model if selected model is unavailable

### 2. Available Models

#### Gemini Models

- **gemini-2.0-flash** (default) - Fast and cost-effective
- **gemini-2.0-exp** - Experimental features
- **gemini-1.5-flash** - Previous generation

#### OpenAI Models

- **gpt-4o-mini** (default) - Fast and cost-effective
- **gpt-4o** - More capable than mini
- **gpt-4** - Most capable but slower
- **gpt-3.5-turbo** - Fast and reliable

### 3. UI Components

#### Model Selector Dropdown

- Located in the menu dropdown
- Groups models by provider
- Shows model descriptions
- Updates in real-time

#### Current Model Indicator

- Displays current model in header
- Visual badge showing active model
- Updates when model is changed

## Technical Implementation

### Database Changes

#### New Table: `user_preferences`

```sql
CREATE TABLE user_preferences (
  user_id TEXT PRIMARY KEY,
  preferred_model TEXT DEFAULT 'gemini-2.0-flash',
  preferred_provider TEXT DEFAULT 'gemini',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### New Functions

- `getUserPreferences(db, userId)` - Get user's model preferences
- `updateUserPreferences(db, userId, preferences)` - Update user's preferences

### Backend API Endpoints

#### GET `/api/models`

Returns all available models grouped by provider.

#### GET `/api/user/model-preference`

Returns the current user's model preference.

#### POST `/api/user/model-preference`

Updates the user's model preference.

### Frontend Changes

#### Model Selection UI

- Added model selector dropdown to menu
- Added current model indicator in header
- Real-time updates when model is changed

#### JavaScript Functions

- `loadAvailableModels()` - Load and populate model selector
- `loadCurrentModelPreference()` - Load user's current preference
- `updateModelPreference(provider, model)` - Update user's preference
- `updateCurrentModelIndicator(modelName)` - Update UI indicator

### LLM Provider Updates

#### Dynamic Model Selection

- Updated `generateResponse()` and `generateResponseV2()` functions
- Added `modelName` parameter to both functions
- Fallback to default model if specified model is unavailable

#### Provider-Specific Changes

- **Gemini**: Uses `genAI.getGenerativeModel({ model: modelName })`
- **OpenAI**: Uses `model: modelName` in API calls

## Usage Instructions

### For Users

1. **Access Model Selection**

   - Click the menu button (three dots) in the top-right corner
   - Look for the "AI Model" dropdown in the menu

2. **Change Model**

   - Select your preferred model from the dropdown
   - The change takes effect immediately
   - You'll see a confirmation message

3. **View Current Model**
   - The current model is displayed as a blue badge next to "Smart FinAgent"
   - This updates automatically when you change models

### For Developers

#### Adding New Models

1. **Update `llm-config.js`**

   ```javascript
   const AVAILABLE_MODELS = {
     [LLM_PROVIDERS.GEMINI]: [
       // Add new model here
       { id: "new-model", name: "New Model", description: "Description" },
     ],
   };
   ```

2. **Update Provider Files**

   - Ensure the model is supported by the provider
   - Test the model with the provider's API

3. **Update Documentation**
   - Add model to this documentation
   - Update any relevant README files

#### Testing Model Selection

1. **Start the server**

   ```bash
   cd server
   node index.js
   ```

2. **Test API endpoints**

   ```bash
   # Get available models
   curl http://localhost:3000/api/models

   # Get user preference (requires auth)
   curl http://localhost:3000/api/user/model-preference
   ```

3. **Test UI**
   - Open the sidepanel
   - Try changing models via the dropdown
   - Verify the indicator updates

## Error Handling

### Fallback Strategy

- If user's preferred model is unavailable, falls back to default
- If provider is unavailable, falls back to system default
- Graceful degradation with user notification

### Error Messages

- Clear error messages for invalid model selections
- User-friendly notifications for API failures
- Console logging for debugging

## Security Considerations

### Authentication

- All model preference endpoints require authentication
- User can only modify their own preferences
- Server validates model availability before saving

### Validation

- Model names are validated against available models
- Provider names are validated against supported providers
- Input sanitization prevents injection attacks

## Performance Considerations

### Caching

- Model list is cached on frontend after first load
- User preferences are cached in session
- Minimal API calls for model changes

### Database

- Efficient queries with indexed user_id
- Minimal storage requirements for preferences
- No impact on existing chat functionality

## Future Enhancements

### Potential Features

1. **Model Comparison** - Side-by-side comparison of model outputs
2. **Cost Tracking** - Track usage costs per model
3. **Performance Metrics** - Show response times per model
4. **Model Recommendations** - Suggest models based on use case
5. **Batch Model Testing** - Test multiple models simultaneously

### Technical Improvements

1. **Model Switching** - Allow switching models mid-conversation
2. **Model Presets** - Save model configurations for different tasks
3. **API Key Management** - User-specific API keys for different providers
4. **Usage Analytics** - Track which models are most popular

## Troubleshooting

### Common Issues

1. **Model Not Available**

   - Check if the model is listed in `AVAILABLE_MODELS`
   - Verify the provider supports the model
   - Check API key permissions

2. **Preference Not Saving**

   - Verify user is authenticated
   - Check database connection
   - Review server logs for errors

3. **UI Not Updating**
   - Clear browser cache
   - Check JavaScript console for errors
   - Verify API responses

### Debug Commands

```bash
# Check database
sqlite3 database.sqlite "SELECT * FROM user_preferences;"

# Check server logs
tail -f server.log

# Test API directly
curl -X POST http://localhost:3000/api/user/model-preference \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"preferred_provider":"gemini","preferred_model":"gemini-2.0-flash"}'
```

## Conclusion

The model selection feature provides users with flexibility in choosing their preferred AI model while maintaining system reliability through fallback mechanisms. The implementation is secure, performant, and easily extensible for future enhancements.
