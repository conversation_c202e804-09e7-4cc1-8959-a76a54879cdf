# Smart Agent Admin Dashboard

This admin dashboard provides a comprehensive view of all data stored in the Smart Agent database, including users, sessions, messages, and detailed usage analytics.

## Features

- **Firebase Authentication**: Secure access using Firebase authentication with phone number validation
- **Database Statistics**: Overview of total users, sessions, messages, and active users
- **Usage Analytics**: Comprehensive LLM performance metrics and token usage tracking
- **User Management**: View all users with their message and session counts
- **Session Overview**: Browse all chat sessions with expandable message details
- **Message History**: View recent messages with integrated performance analytics
- **Real-time Refresh**: Update data with refresh buttons
- **Responsive Design**: Works on desktop and mobile devices
- **Performance Monitoring**: Track response times, token usage, and model performance

## Setup

### 1. Configure Authorized Phone Numbers

Edit `server/config.js` and add your authorized phone numbers:

```javascript
const AUTHORIZED_PHONES = [
  "+************", // Replace with your actual phone number
  "+**********", // Add more authorized numbers as needed
];
```

**Important**: The phone numbers must match the phone numbers used for Firebase authentication. Users can only access the admin dashboard if their Firebase account phone number is in this list.

### 2. Start the Server

```bash
cd server
npm install
npm start
```

### 3. Access the Admin Dashboard

Navigate to: `http://localhost:3000/admin`

## Usage

### Authentication

1. Click "Sign in with Google" to authenticate with Firebase
2. If you have a phone number associated with your Firebase account, it will be validated against the authorized list
3. If your phone number is authorized, you'll be redirected to the dashboard
4. If your phone number is not authorized, you'll see an access denied message

### Dashboard Sections

#### Statistics Overview

- **Total Users**: Number of registered users
- **Total Sessions**: Number of chat sessions created
- **Total Messages**: Number of messages exchanged
- **Active Users**: Users who have sent messages

#### Users Tab

- View all registered users
- See message and session counts per user
- View user registration dates

#### Sessions Tab

- Browse all chat sessions with expandable interface
- Click on any session to expand and view its messages
- See session titles and user information
- View message counts per session
- Check creation and update timestamps
- **Expandable Messages**: Each session can be expanded to show:
  - User messages and AI responses
  - Performance metrics (latency, tokens, model info)
  - Generation configuration details
  - Timestamps for each message

#### Messages & Analytics Tab

- **Usage Statistics Cards**: High-level performance metrics
  - Total API calls
  - Average latency
  - Total tokens consumed
  - Unique users
- **Message List**: View recent messages with integrated analytics
- **Performance Metrics**: Each message shows:
  - Response latency with color coding
  - Token usage (input → output → total)
  - Model information
  - Generation configuration
  - Performance indicators

### Performance Analytics

The dashboard provides comprehensive analytics for LLM usage:

#### Latency Tracking

- **Green**: < 1 second (fast)
- **Yellow**: 1-3 seconds (moderate)
- **Red**: > 3 seconds (slow)

#### Token Usage

- Input tokens consumed
- Output tokens generated
- Total tokens used
- Cost analysis capabilities

#### Model Information

- Model name (e.g., "gemini-2.0-flash")
- Generation configuration used
- Performance trends

### Security Features

- **Firebase Authentication**: Uses Firebase's secure authentication system
- **Phone Number Validation**: Only users with authorized phone numbers can access the dashboard
- **Token-based API**: All API calls use Firebase ID tokens for authentication
- **Automatic Token Refresh**: Tokens are automatically refreshed when needed

### API Endpoints

The admin dashboard uses the following API endpoints:

- `POST /api/admin/validate-firebase-user` - Validate Firebase user
- `GET /api/admin/stats` - Get database statistics
- `GET /api/admin/users` - Get all users
- `GET /api/admin/sessions` - Get all sessions
- `GET /api/admin/messages` - Get recent messages with usage analytics
- `GET /api/admin/usage` - Get messages with usage metadata
- `GET /api/admin/usage/stats` - Get usage statistics
- `GET /api/admin/usage/user/:userId` - Get user-specific usage data
- `GET /api/admin/session/:sessionId` - Get session messages (admin endpoint)

### Configuration

You can customize the admin dashboard by editing `server/config.js`:

```javascript
// Authorized phone numbers for admin access
const AUTHORIZED_PHONES = [
  "+************", // Replace with your actual phone number
  "+**********", // Add more authorized numbers as needed
];

// Database query limits
const QUERY_LIMITS = {
  defaultMessages: 100,
  maxMessages: 1000,
  defaultUsers: 50,
  maxUsers: 500,
};
```

## Troubleshooting

### Access Denied Error

- Ensure your phone number is added to `AUTHORIZED_PHONES` in `config.js`
- Verify that your Firebase account has a phone number associated with it
- Check that the phone number format matches exactly (e.g., `+************`)

### Firebase Authentication Issues

- Make sure you're signed in with the correct Google account
- Verify that your Firebase project is properly configured
- Check browser console for any Firebase-related errors

### Database Connection Issues

- Verify the SQLite database file exists
- Check database permissions
- Review server logs for error messages

### Token Expired

- Firebase tokens automatically refresh, but if you see token errors, try signing out and back in

### Session Expansion Issues

- If sessions don't expand, check the browser console for API errors
- Verify that the admin session endpoint is working correctly
- Ensure the database has the correct schema with usage metadata fields

## Security Notes

- Keep the `config.js` file secure and never commit it to public repositories
- Regularly update authorized phone numbers
- Monitor admin access logs
- Consider implementing additional security measures for production use
- Ensure your Firebase project has proper security rules configured

## Development

To modify the admin dashboard:

1. Edit `server/admin.html` for UI changes
2. Update `server/database.js` for new database queries
3. Modify `server/index.js` for new API endpoints
4. Update `server/config.js` for configuration changes

The dashboard uses:

- **Vue.js 3** for reactive UI
- **Tailwind CSS** for styling
- **Font Awesome** for icons
- **Firebase Auth** for authentication
- **Fetch API** for data requests

## Database Schema

The admin dashboard works with the enhanced database schema that includes usage metadata:

```sql
-- Messages table with usage metadata
CREATE TABLE messages (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id TEXT NOT NULL,
  user_message TEXT NOT NULL,
  llm_response TEXT NOT NULL,
  timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
  session_id TEXT,
  latency_ms INTEGER,
  generation_config TEXT,
  model_name TEXT,
  input_tokens INTEGER,
  output_tokens INTEGER,
  total_tokens INTEGER,
  FOREIGN KEY (session_id) REFERENCES sessions (id)
);
```

## Firebase Setup Requirements

For the admin dashboard to work properly, ensure your Firebase project has:

1. **Authentication enabled** with Google sign-in method
2. **Phone number authentication** enabled (if you want to use phone numbers for validation)
3. **Proper security rules** configured
4. **Service account key** properly configured in the server

## Related Documentation

- `README.md` - Main server documentation
- `USAGE_METADATA_README.md` - Detailed usage analytics documentation
