# Smart Agent CLI - LLM Testing Tool

A command-line interface for testing the `generateResponse` and `generateResponseV2` methods from both OpenAI and Gemini providers.

## Prerequisites

1. Make sure your `.env` file is configured with the appropriate API keys:

   - For OpenAI: `OPENAI_API_KEY`
   - For Gemini: `GEMINI_API_KEY`

2. Install dependencies:
   ```bash
   npm install
   ```

## Usage

### Quick Commands

```bash
# Show help
npm run cli -- help

# Test generateResponse with a message
npm run cli:test "Buy 100 shares of RELIANCE"

# Test generateResponseV2 with a message
npm run cli:test-v2 "Sell all TATASTEEL when profit exceeds 5000"

# Start interactive mode
npm run cli:interactive
```

### Direct CLI Usage

```bash
# Show help
node cli.js help

# Test generateResponse
node cli.js test "Buy 100 shares of RELIANCE"

# Test generateResponseV2
node cli.js test-v2 "Sell all TATASTEEL when profit exceeds 5000"

# Interactive mode
node cli.js interactive

# With options
node cli.js test "Buy 100 shares of RELIANCE" --verbose --pretty
```

### Options

- `--verbose, -v`: Show detailed response metadata (latency, tokens, etc.)
- `--pretty, -p`: Pretty print JSON responses
- `--help, -h`: Show help message

## Interactive Mode

Interactive mode allows you to test multiple messages without restarting the CLI:

```bash
npm run cli:interactive
```

### Interactive Commands

- `help`: Show available commands
- `exit` or `quit`: Exit interactive mode
- `test <message>`: Test generateResponse
- `test-v2 <message>`: Test generateResponseV2
- `verbose on/off`: Toggle verbose mode
- `pretty on/off`: Toggle pretty print mode

### Example Interactive Session

```
🎯 Interactive Mode - Gemini (gemini-2.0-flash)
Type 'help' for commands, 'exit' to quit

💬 Enter your message: Buy 100 shares of RELIANCE

🔄 Testing generateResponseV2...
📝 Message: "Buy 100 shares of RELIANCE"
🔧 Provider: Gemini (gemini-2.0-flash)

✅ Response generated successfully!
⏱️  Total time: 1234ms

🤖 Response:
[
  {
    "action": "Buy",
    "arguments": {
      "symbol": "RELIANCE",
      "exchange": "NSE"
    },
    "need_more_info": ["quantity", "productType"],
    "clarification": "Got it — you're placing a Buy Order for RELIANCE.\nPlease share the following details:\n1️⃣ Quantity\n2️⃣ Product type: Intraday or Delivery"
  }
]

💬 Enter your message: test-v2 Sell all TATASTEEL when profit exceeds 5000

🔄 Testing generateResponseV2...
📝 Message: "Sell all TATASTEEL when profit exceeds 5000"
🔧 Provider: Gemini (gemini-2.0-flash)

✅ Response generated successfully!
⏱️  Total time: 987ms

🤖 Response:
[
  {
    "action": "MonitorProfit",
    "arguments": {
      "symbol": "TATASTEEL",
      "amountToExceed": 5000,
      "timeout": null
    }
  },
  {
    "action": "SellAll",
    "arguments": {
      "symbol": "TATASTEEL",
      "exchange": "NSE"
    }
  }
]

💬 Enter your message: exit
👋 Goodbye!
```

## Examples

### Testing generateResponse

```bash
# Basic test
npm run cli:test "Buy 100 shares of RELIANCE"

# With verbose output
npm run cli:test "Sell all TATASTEEL" --verbose

# With pretty print
npm run cli:test "Place stop loss for INFY" --pretty
```

### Testing generateResponseV2

```bash
# Basic test
npm run cli:test-v2 "Buy 100 shares of RELIANCE"

# With all options
npm run cli:test-v2 "Sell all TATASTEEL when profit exceeds 5000" --verbose --pretty
```

## Provider Configuration

The CLI automatically detects and uses the configured LLM provider from your `llm-config.js` file. It supports:

- **OpenAI**: Uses GPT-4o-mini or GPT-4o models
- **Gemini**: Uses Gemini 2.0 Flash model

## Error Handling

The CLI provides user-friendly error messages for common issues:

- **API Key Missing**: Check your `.env` file
- **Rate Limits**: Wait and try again
- **Network Issues**: Check your internet connection
- **Invalid Responses**: The LLM response couldn't be parsed

## Troubleshooting

1. **"Failed to initialize LLM provider"**: Check your `.env` file and API keys
2. **"Invalid JSON payload"**: This might indicate an API format issue
3. **"Rate limit exceeded"**: Wait a few minutes before trying again
4. **"Network error"**: Check your internet connection

## Development

The CLI is built using Node.js's built-in `readline` module and doesn't require additional dependencies. It integrates with the existing LLM configuration system and can be easily extended for additional testing scenarios.
