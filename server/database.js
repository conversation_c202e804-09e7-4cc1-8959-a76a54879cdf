const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Database file path
const dbPath = path.join(__dirname, 'chat.db');

// Initialize database
function initializeDatabase() {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('Error opening database:', err.message);
        reject(err);
        return;
      }
      console.log('Connected to SQLite database.');

      // Create sessions table if it doesn't exist
      db.run(`
        CREATE TABLE IF NOT EXISTS sessions (
          id TEXT PRIMARY KEY,
          user_id TEXT NOT NULL,
          title TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `, (err) => {
        if (err) {
          console.error('Error creating sessions table:', err.message);
          reject(err);
          return;
        }
        console.log('Sessions table ready.');

        // Create users table if it doesn't exist
        db.run(`
          CREATE TABLE IF NOT EXISTS users (
            user_id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
          )
        `, (err) => {
          if (err) {
            console.error('Error creating users table:', err.message);
            reject(err);
            return;
          }
          console.log('Users table ready.');

          // Create messages table if it doesn't exist
          db.run(`
          CREATE TABLE IF NOT EXISTS messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT NOT NULL,
            user_message TEXT NOT NULL,
            llm_response TEXT NOT NULL,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            session_id TEXT,
            latency_ms INTEGER,
            generation_config TEXT,
            model_name TEXT,
            input_tokens INTEGER,
            output_tokens INTEGER,
            total_tokens INTEGER,
            FOREIGN KEY (session_id) REFERENCES sessions (id)
          )
        `, (err) => {
            if (err) {
              console.error('Error creating messages table:', err.message);
              reject(err);
              return;
            }
            console.log('Messages table ready.');

            // Run migration to add usage metadata columns if they don't exist
            migrateDatabase(db)
              .then(() => resolve(db))
              .catch(reject);
          });
        });
      });

      // Add user preferences table
      db.run(`
        CREATE TABLE IF NOT EXISTS user_preferences (
          user_id TEXT PRIMARY KEY,
          preferred_model TEXT DEFAULT 'gemini-2.0-flash',
          preferred_provider TEXT DEFAULT 'gemini',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `, (err) => {
        if (err) {
          console.error('Error creating user preferences table:', err.message);
          reject(err);
          return;
        }
        console.log('User preferences table ready.');
      });
    });
  });
}

// Migration function to add usage metadata columns to existing messages table
function migrateDatabase(db) {
  return new Promise((resolve, reject) => {
    // Check if usage metadata columns exist
    db.get("PRAGMA table_info(messages)", (err, rows) => {
      if (err) {
        console.error('Error checking table schema:', err.message);
        reject(err);
        return;
      }

      // Get all column names
      db.all("PRAGMA table_info(messages)", (err, columns) => {
        if (err) {
          console.error('Error getting table columns:', err.message);
          reject(err);
          return;
        }

        const columnNames = columns.map(col => col.name);
        const migrations = [];

        // Add missing columns
        if (!columnNames.includes('latency_ms')) {
          migrations.push("ALTER TABLE messages ADD COLUMN latency_ms INTEGER");
        }
        if (!columnNames.includes('generation_config')) {
          migrations.push("ALTER TABLE messages ADD COLUMN generation_config TEXT");
        }
        if (!columnNames.includes('model_name')) {
          migrations.push("ALTER TABLE messages ADD COLUMN model_name TEXT");
        }
        if (!columnNames.includes('input_tokens')) {
          migrations.push("ALTER TABLE messages ADD COLUMN input_tokens INTEGER");
        }
        if (!columnNames.includes('output_tokens')) {
          migrations.push("ALTER TABLE messages ADD COLUMN output_tokens INTEGER");
        }
        if (!columnNames.includes('total_tokens')) {
          migrations.push("ALTER TABLE messages ADD COLUMN total_tokens INTEGER");
        }

        // Execute migrations
        if (migrations.length === 0) {
          console.log('Database schema is up to date.');
          resolve();
          return;
        }

        console.log(`Running ${migrations.length} database migrations...`);

        let completed = 0;
        migrations.forEach((migration, index) => {
          db.run(migration, (err) => {
            if (err) {
              console.error(`Migration ${index + 1} failed:`, err.message);
              reject(err);
              return;
            }
            completed++;
            console.log(`Migration ${index + 1} completed.`);

            if (completed === migrations.length) {
              console.log('All migrations completed successfully.');
              resolve();
            }
          });
        });
      });
    });
  });
}

// Create or update session with title
function createOrUpdateSession(db, sessionId, userId, title) {
  return new Promise((resolve, reject) => {
    const sql = `
      INSERT OR REPLACE INTO sessions (id, user_id, title, updated_at)
      VALUES (?, ?, ?, CURRENT_TIMESTAMP)
    `;

    db.run(sql, [sessionId, userId, title], function (err) {
      if (err) {
        console.error('Error creating/updating session:', err.message);
        reject(err);
        return;
      }
      console.log(`Session ${sessionId} created/updated with title: ${title}`);
      resolve(this.lastID);
    });
  });
}

// Generate session title from first message
function generateSessionTitle(userMessage) {
  // Truncate message to 50 characters and add ellipsis if longer
  const truncatedMessage = userMessage.length > 50
    ? userMessage.substring(0, 50) + '...'
    : userMessage;

  // Get current date and time
  const now = new Date();
  const dateStr = now.toLocaleDateString();
  const timeStr = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

  return `${truncatedMessage} - ${dateStr} ${timeStr}`;
}

// Save a message and its response
function saveMessage(db, userId, userMessage, llmResponse, sessionId = null, metadata = null) {
  return new Promise((resolve, reject) => {
    // If no sessionId provided, create a new one
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // Check if this is the first message in the session
    const checkFirstMessageSql = `
      SELECT COUNT(*) as count FROM messages WHERE session_id = ?
    `;

    db.get(checkFirstMessageSql, [sessionId], (err, row) => {
      if (err) {
        console.error('Error checking first message:', err.message);
        reject(err);
        return;
      }

      const isFirstMessage = row.count === 0;

      // If this is the first message, create/update session with title
      if (isFirstMessage) {
        const sessionTitle = generateSessionTitle(userMessage);

        createOrUpdateSession(db, sessionId, userId, sessionTitle)
          .then(() => {
            // Now save the message
            saveMessageToDatabase(db, userId, userMessage, llmResponse, sessionId, metadata, resolve, reject);
          })
          .catch(reject);
      } else {
        // Just save the message
        saveMessageToDatabase(db, userId, userMessage, llmResponse, sessionId, metadata, resolve, reject);
      }
    });
  });
}

// Helper function to save message to database
function saveMessageToDatabase(db, userId, userMessage, llmResponse, sessionId, metadata, resolve, reject) {
  const sql = `
    INSERT INTO messages (
      user_id, user_message, llm_response, session_id, 
      latency_ms, generation_config, model_name, input_tokens, output_tokens, total_tokens
    )
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;

  const params = [
    userId,
    userMessage,
    llmResponse,
    sessionId,
    metadata?.latency_ms || null,
    metadata?.generation_config ? JSON.stringify(metadata.generation_config) : null,
    metadata?.model_name || null,
    metadata?.input_tokens || null,
    metadata?.output_tokens || null,
    metadata?.total_tokens || null
  ];

  db.run(sql, params, function (err) {
    if (err) {
      console.error('Error saving message:', err.message);
      reject(err);
      return;
    }
    console.log(`Message saved with ID: ${this.lastID}`);
    resolve({ messageId: this.lastID, sessionId: sessionId });
  });
}

// Get chat history for a user
function getChatHistory(db, userId, limit = 10) {
  return new Promise((resolve, reject) => {
    const sql = `
      SELECT * FROM messages 
      WHERE user_id = ? 
      ORDER BY timestamp DESC 
      LIMIT ?
    `;

    db.all(sql, [userId, limit], (err, rows) => {
      if (err) {
        console.error('Error getting chat history:', err.message);
        reject(err);
        return;
      }

      // Parse JSON responses back to objects
      const parsedRows = rows.map(row => ({
        ...row,
        llm_response: safeJsonParse(row.llm_response),
        generation_config: safeJsonParse(row.generation_config)
      }));

      resolve(parsedRows.reverse()); // Return in chronological order
    });
  });
}

// Get recent messages from a specific session
function getRecentSessionMessages(db, sessionId, userId, limit = 5) {
  return new Promise((resolve, reject) => {
    const sql = `
      SELECT user_message, llm_response 
      FROM messages 
      WHERE session_id = ? AND user_id = ? 
      ORDER BY timestamp DESC 
      LIMIT ?
    `;

    db.all(sql, [sessionId, userId, limit], (err, rows) => {
      if (err) {
        console.error('Error getting recent session messages:', err.message);
        reject(err);
        return;
      }

      // Parse JSON responses back to objects
      const parsedRows = rows.map(row => ({
        ...row,
        llm_response: safeJsonParse(row.llm_response)
      }));

      resolve(parsedRows.reverse()); // Return in chronological order
    });
  });
}

// Get recent messages from all sessions (legacy function - kept for backward compatibility)
function getRecentMessages(db, userId, limit = 5) {
  return new Promise((resolve, reject) => {
    const sql = `
      SELECT user_message, llm_response 
      FROM messages 
      WHERE user_id = ? 
      ORDER BY timestamp DESC 
      LIMIT ?
    `;

    db.all(sql, [userId, limit], (err, rows) => {
      if (err) {
        console.error('Error getting recent messages:', err.message);
        reject(err);
        return;
      }

      // Parse JSON responses back to objects
      const parsedRows = rows.map(row => ({
        ...row,
        llm_response: safeJsonParse(row.llm_response)
      }));

      resolve(parsedRows.reverse()); // Return in chronological order
    });
  });
}

// Get sessions list for a user
function getSessions(db, userId) {
  return new Promise((resolve, reject) => {
    const sql = `
      SELECT 
        s.id,
        s.title,
        s.created_at as timestamp,
        s.updated_at,
        COUNT(m.id) as messageCount
      FROM sessions s
      LEFT JOIN messages m ON s.id = m.session_id
      WHERE s.user_id = ?
      GROUP BY s.id
      ORDER BY s.updated_at DESC
    `;

    db.all(sql, [userId], (err, rows) => {
      if (err) {
        console.error('Error getting sessions:', err.message);
        reject(err);
        return;
      }

      // Format sessions with proper names
      const sessions = rows.map(row => ({
        id: row.id,
        name: row.title,
        timestamp: row.timestamp,
        updatedAt: row.updated_at,
        messageCount: row.messageCount
      }));

      resolve(sessions);
    });
  });
}

// Get messages for a specific session
function getSessionMessages(db, sessionId, userId) {
  return new Promise((resolve, reject) => {
    const sql = `
      SELECT 
        id,
        user_message, 
        llm_response, 
        timestamp,
        latency_ms,
        generation_config,
        model_name,
        input_tokens,
        output_tokens,
        total_tokens
      FROM messages 
      WHERE session_id = ? AND user_id = ?
      ORDER BY timestamp ASC
    `;

    db.all(sql, [sessionId, userId], (err, rows) => {
      if (err) {
        console.error('Error getting session messages:', err.message);
        reject(err);
        return;
      }

      // Parse JSON responses back to objects
      const parsedRows = rows.map(row => ({
        ...row,
        llm_response: safeJsonParse(row.llm_response),
        generation_config: safeJsonParse(row.generation_config)
      }));

      resolve(parsedRows);
    });
  });
}

// Get user by ID
function getUserById(db, userId) {
  return new Promise((resolve, reject) => {
    db.get('SELECT * FROM users WHERE user_id = ?', [userId], (err, row) => {
      if (err) {
        reject(err);
        return;
      }
      resolve(row);
    });
  });
}

// Create user
function createUser(db, userId, name) {
  return new Promise((resolve, reject) => {
    db.run('INSERT INTO users (user_id, name) VALUES (?, ?)', [userId, name], function (err) {
      if (err) {
        reject(err);
        return;
      }
      resolve({ userId, name });
    });
  });
}

// --- Admin Functions ---

// Get all users with their message counts
function getAllUsers(db) {
  return new Promise((resolve, reject) => {
    const sql = `
      SELECT 
        u.user_id,
        u.name,
        u.created_at,
        COUNT(m.id) as messageCount,
        COUNT(DISTINCT m.session_id) as sessionCount
      FROM users u
      LEFT JOIN messages m ON u.user_id = m.user_id
      GROUP BY u.user_id
      ORDER BY u.created_at DESC
    `;

    db.all(sql, [], (err, rows) => {
      if (err) {
        console.error('Error getting all users:', err.message);
        reject(err);
        return;
      }
      resolve(rows);
    });
  });
}

// Get all sessions with user info
function getAllSessions(db) {
  return new Promise((resolve, reject) => {
    const sql = `
      SELECT 
        s.id,
        s.user_id,
        u.name as user_name,
        s.title,
        s.created_at,
        s.updated_at,
        COUNT(m.id) as messageCount
      FROM sessions s
      LEFT JOIN users u ON s.user_id = u.user_id
      LEFT JOIN messages m ON s.id = m.session_id
      GROUP BY s.id
      ORDER BY s.updated_at DESC
    `;

    db.all(sql, [], (err, rows) => {
      if (err) {
        console.error('Error getting all sessions:', err.message);
        reject(err);
        return;
      }
      resolve(rows);
    });
  });
}

// Get all messages with user and session info
function getAllMessages(db, limit = 100) {
  return new Promise((resolve, reject) => {
    const sql = `
      SELECT 
        m.id,
        m.user_id,
        u.name as user_name,
        m.user_message,
        m.llm_response,
        m.timestamp,
        m.session_id,
        s.title as session_title,
        m.latency_ms,
        m.generation_config,
        m.model_name,
        m.input_tokens,
        m.output_tokens,
        m.total_tokens
      FROM messages m
      LEFT JOIN users u ON m.user_id = u.user_id
      LEFT JOIN sessions s ON m.session_id = s.id
      ORDER BY m.timestamp DESC
      LIMIT ?
    `;

    db.all(sql, [limit], (err, rows) => {
      if (err) {
        console.error('Error getting all messages:', err.message);
        reject(err);
        return;
      }

      // Parse JSON responses back to objects
      const parsedRows = rows.map(row => ({
        ...row,
        llm_response: safeJsonParse(row.llm_response),
        generation_config: safeJsonParse(row.generation_config)
      }));

      resolve(parsedRows);
    });
  });
}

// Get database statistics
function getDatabaseStats(db) {
  return new Promise((resolve, reject) => {
    const sql = `
      SELECT 
        (SELECT COUNT(*) FROM users) as totalUsers,
        (SELECT COUNT(*) FROM sessions) as totalSessions,
        (SELECT COUNT(*) FROM messages) as totalMessages,
        (SELECT COUNT(DISTINCT user_id) FROM messages) as activeUsers,
        (SELECT COUNT(DISTINCT session_id) FROM messages) as activeSessions
    `;

    db.get(sql, [], (err, row) => {
      if (err) {
        console.error('Error getting database stats:', err.message);
        reject(err);
        return;
      }
      resolve(row);
    });
  });
}

// Get all messages with usage metadata (for admin usage analytics)
function getMessagesWithUsage(db, limit = 100) {
  return new Promise((resolve, reject) => {
    const sql = `
      SELECT 
        m.id,
        m.user_id,
        u.name as user_name,
        m.session_id,
        s.title as session_title,
        m.latency_ms,
        m.generation_config,
        m.model_name,
        m.input_tokens,
        m.output_tokens,
        m.total_tokens,
        m.timestamp,
        m.user_message,
        m.llm_response
      FROM messages m
      LEFT JOIN users u ON m.user_id = u.user_id
      LEFT JOIN sessions s ON m.session_id = s.id
      WHERE m.latency_ms IS NOT NULL
      ORDER BY m.timestamp DESC
      LIMIT ?
    `;

    db.all(sql, [limit], (err, rows) => {
      if (err) {
        console.error('Error getting messages with usage:', err.message);
        reject(err);
        return;
      }

      // Parse JSON responses back to objects
      const parsedRows = rows.map(row => ({
        ...row,
        llm_response: safeJsonParse(row.llm_response),
        generation_config: safeJsonParse(row.generation_config)
      }));

      resolve(parsedRows);
    });
  });
}

// Get usage statistics
function getUsageStats(db) {
  return new Promise((resolve, reject) => {
    const sql = `
      SELECT 
        COUNT(*) as totalCalls,
        AVG(latency_ms) as avgLatency,
        MIN(latency_ms) as minLatency,
        MAX(latency_ms) as maxLatency,
        SUM(input_tokens) as totalInputTokens,
        SUM(output_tokens) as totalOutputTokens,
        SUM(total_tokens) as totalTokens,
        COUNT(DISTINCT user_id) as uniqueUsers,
        COUNT(DISTINCT DATE(timestamp)) as activeDays
      FROM messages
      WHERE latency_ms IS NOT NULL
    `;

    db.get(sql, [], (err, row) => {
      if (err) {
        console.error('Error getting usage stats:', err.message);
        reject(err);
        return;
      }
      resolve(row);
    });
  });
}

// Get usage metadata for a specific user
function getUserUsageMetadata(db, userId, limit = 50) {
  return new Promise((resolve, reject) => {
    const sql = `
      SELECT 
        m.id,
        m.session_id,
        s.title as session_title,
        m.latency_ms,
        m.generation_config,
        m.model_name,
        m.input_tokens,
        m.output_tokens,
        m.total_tokens,
        m.timestamp,
        m.user_message,
        m.llm_response
      FROM messages m
      LEFT JOIN sessions s ON m.session_id = s.id
      WHERE m.user_id = ? AND m.latency_ms IS NOT NULL
      ORDER BY m.timestamp DESC
      LIMIT ?
    `;

    db.all(sql, [userId, limit], (err, rows) => {
      if (err) {
        console.error('Error getting user usage metadata:', err.message);
        reject(err);
        return;
      }

      // Parse JSON responses back to objects
      const parsedRows = rows.map(row => ({
        ...row,
        llm_response: safeJsonParse(row.llm_response),
        generation_config: safeJsonParse(row.generation_config)
      }));

      resolve(parsedRows);
    });
  });
}

// --- Helper Functions ---

/**
 * Safely parse JSON string, returning null if parsing fails
 * @param {string} jsonString - The JSON string to parse
 * @returns {Object|null} - Parsed object or null if parsing fails
 */
function safeJsonParse(jsonString) {
  if (!jsonString) return null;

  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.warn('Failed to parse JSON string:', error.message);
    return null;
  }
}

/**
 * Get user preferences
 * @param {Object} db - Database connection
 * @param {string} userId - User ID
 * @returns {Promise<Object|null>} - User preferences or null if not found
 */
async function getUserPreferences(db, userId) {
  return new Promise((resolve, reject) => {
    db.get(
      'SELECT * FROM user_preferences WHERE user_id = ?',
      [userId],
      (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      }
    );
  });
}

/**
 * Create or update user preferences
 * @param {Object} db - Database connection
 * @param {string} userId - User ID
 * @param {Object} preferences - User preferences object
 * @returns {Promise<Object>} - Updated preferences
 */
async function updateUserPreferences(db, userId, preferences) {
  return new Promise((resolve, reject) => {
    const { preferred_model, preferred_provider } = preferences;

    db.run(
      `INSERT OR REPLACE INTO user_preferences 
       (user_id, preferred_model, preferred_provider, updated_at) 
       VALUES (?, ?, ?, CURRENT_TIMESTAMP)`,
      [userId, preferred_model, preferred_provider],
      function (err) {
        if (err) {
          reject(err);
        } else {
          resolve({
            user_id: userId,
            preferred_model,
            preferred_provider,
            updated_at: new Date().toISOString()
          });
        }
      }
    );
  });
}

module.exports = {
  initializeDatabase,
  saveMessage,
  getChatHistory,
  getRecentSessionMessages,
  getRecentMessages,
  getSessions,
  getSessionMessages,
  createOrUpdateSession,
  generateSessionTitle,
  getUserById,
  createUser,
  // Admin functions
  getAllUsers,
  getAllSessions,
  getAllMessages,
  getDatabaseStats,
  getMessagesWithUsage,
  getUsageStats,
  getUserUsageMetadata,
  // Helper functions
  safeJsonParse,
  getUserPreferences,
  updateUserPreferences
}; 