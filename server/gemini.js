const { GoogleGenerativeAI } = require('@google/generative-ai');
const { loadPrompt } = require('./prompt-loader');
require('dotenv').config();

// Initialize Gemini API
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

// Get Gemini model
const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

/**
 * Handle Gemini API errors and return user-friendly messages
 * @param {Error} error - The error object from Gemini API
 * @returns {string} - User-friendly error message
 */
function handleGeminiError(error) {
  console.error('Gemini API Error:', error);

  // Check for specific error types
  if (error.message && error.message.includes('503 Service Unavailable')) {
    return "I'm currently experiencing high traffic. Please try again in a few moments.";
  }

  if (error.message && error.message.includes('429')) {
    return "I've reached my rate limit. Please wait a moment before trying again.";
  }

  if (error.message && error.message.includes('400')) {
    return "I couldn't process your request. Please try rephrasing your message.";
  }

  if (error.message && error.message.includes('401') || error.message.includes('403')) {
    return "Authentication error. Please check your API configuration.";
  }

  if (error.message && error.message.includes('timeout')) {
    return "Request timed out. Please try again.";
  }

  if (error.message && error.message.includes('network')) {
    return "Network error. Please check your internet connection and try again.";
  }

  // Default error message
  return "I'm having trouble processing your request right now. Please try again later.";
}

/**
 * Generate response from Gemini API
 * @param {string} userMessage - The user's message
 * @param {Array} chatHistory - Previous messages for context
 * @param {string} modelName - Model name to use (optional)
 * @returns {Promise<Object>} - Object containing response and metadata
 */
async function generateResponse(userMessage, chatHistory = [], modelName = "gemini-2.0-flash") {
  const startTime = Date.now();

  try {
    // Load the system prompt from file (will prefer .md over .txt)
    const systemPrompt = await loadPrompt('trading-assistant-prompt');

    // Get the specified model or fall back to default
    const model = genAI.getGenerativeModel({ model: modelName });

    // Build conversation history for context
    let conversationHistory = [];

    if (chatHistory.length > 0) {
      // Add recent conversation context
      chatHistory.forEach((msg, index) => {
        console.log(`Adding message ${index}: user_message="${msg.user_message}", llm_response type=${typeof msg.llm_response}`);
        conversationHistory.push({
          role: "user",
          parts: [{ text: msg.user_message }]
        });
        // Only add model message if llm_response exists
        if (msg.llm_response) {
          const modelContent = typeof msg.llm_response === 'string' ? msg.llm_response : JSON.stringify(msg.llm_response);
          console.log(`Adding model message ${index}: content type=${typeof modelContent}`);
          conversationHistory.push({
            role: "model",
            parts: [{ text: modelContent }]
          });
        } else {
          console.log(`Skipping model message ${index}: llm_response is null/undefined`);
        }
      });
    }

    // Create the prompt with user message interpolated
    const promptWithUserMessage = systemPrompt + '\n\n' + userMessage;

    // Add the current user message with the full prompt
    conversationHistory.push({
      role: "user",
      parts: [{ text: promptWithUserMessage }]
    });

    // Validate all conversation history entries have valid text content
    conversationHistory = conversationHistory.filter(msg =>
      msg.parts && msg.parts.length > 0 &&
      msg.parts[0].text && typeof msg.parts[0].text === 'string'
    );
    console.log(`Final conversation history length: ${conversationHistory.length}`);

    // Define generation config
    const generationConfig = {
      maxOutputTokens: 1000,
      temperature: 0,
    };

    // Start a chat session
    const chat = model.startChat({
      history: conversationHistory.slice(0, -1), // All except the current message
      generationConfig: generationConfig,
    });

    // Send the current message and get response
    const result = await chat.sendMessage(promptWithUserMessage);
    const response = result.response;
    const text = response.text().replace("```json", "").replace("```", "").trim();

    // Calculate latency
    const endTime = Date.now();
    const latency = endTime - startTime;

    // Extract usage information
    const usageMetadata = result.response.usageMetadata;
    const inputTokens = usageMetadata?.promptTokenCount || null;
    const outputTokens = usageMetadata?.candidatesTokenCount || null;
    const totalTokens = usageMetadata?.totalTokenCount || null;

    console.log('Gemini response generated successfully');

    return {
      response: text,
      metadata: {
        latency_ms: latency,
        generation_config: generationConfig,
        model_name: modelName,
        input_tokens: inputTokens,
        output_tokens: outputTokens,
        total_tokens: totalTokens
      }
    };

  } catch (error) {
    console.error('Error generating Gemini response:', error);

    // Handle specific Gemini API errors
    const userFriendlyMessage = handleGeminiError(error);
    throw new Error(userFriendlyMessage);
  }
}

// ----- Send message and get response -----
async function generateResponseV2(userInput, chatHistory = [], modelName = "gemini-2.0-flash") {
  const startTime = Date.now();

  try {
    // Load the system prompt from file (will prefer .md over .txt)
    const systemPrompt = await loadPrompt('trading-assistant-prompt');

    // Get the specified model or fall back to default
    const model = genAI.getGenerativeModel({ model: modelName });

    // Build conversation history for context
    let conversationHistory = [
      {
        role: "user",
        parts: [{ text: systemPrompt }]
      }
    ];

    if (chatHistory.length > 0) {
      // Add recent conversation context
      chatHistory.forEach((msg, index) => {
        console.log(`Adding message ${index}: user_message="${msg.user_message}", llm_response type=${typeof msg.llm_response}`);
        if (!msg.user_message || typeof msg.user_message !== 'string' || msg.llm_response.primitives[0].action === "llmChat") {
          return;
        }
        conversationHistory.push({
          role: "user",
          parts: [{ text: msg.user_message }]
        });
        // Only add model message if llm_response exists
        if (msg.llm_response) {
          const modelContent = typeof msg.llm_response === 'string' ? msg.llm_response : JSON.stringify(msg.llm_response);
          console.log(`Adding model message ${index}: content type=${typeof modelContent}`);
          conversationHistory.push({
            role: "model",
            parts: [{ text: modelContent }]
          });
        } else {
          console.log(`Skipping model message ${index}: llm_response is null/undefined`);
        }
      });
    }

    // Add the current user message
    conversationHistory.push({
      role: "user",
      parts: [{ text: userInput }]
    });

    // Validate all conversation history entries have valid text content
    conversationHistory = conversationHistory.filter(msg =>
      msg.parts && msg.parts.length > 0 &&
      msg.parts[0].text && typeof msg.parts[0].text === 'string'
    );
    console.log(`Final conversation history length: ${conversationHistory.length}`);

    const response = await model.generateContent({
      contents: conversationHistory,
      generationConfig: {
        maxOutputTokens: 1000,
        temperature: 0,
      }
    });

    const assistantMsg = response.response.text();

    // Calculate latency
    const endTime = Date.now();
    const latency = endTime - startTime;

    // Extract usage information
    const usageMetadata = response.response.usageMetadata;
    const inputTokens = usageMetadata?.promptTokenCount || null;
    const outputTokens = usageMetadata?.candidatesTokenCount || null;
    const totalTokens = usageMetadata?.totalTokenCount || null;

    const cleanedResponse = assistantMsg.replace("```json", "").replace("```", "").trim();

    return {
      response: cleanedResponse,
      metadata: {
        latency_ms: latency,
        generation_config: {
          maxOutputTokens: 1000,
          temperature: 0,
        },
        model_name: modelName,
        input_tokens: inputTokens,
        output_tokens: outputTokens,
        total_tokens: totalTokens
      }
    };

  } catch (error) {
    console.error('Error generating Gemini response:', error);

    // Handle specific Gemini API errors
    const userFriendlyMessage = handleGeminiError(error);
    throw new Error(userFriendlyMessage);
  }
}

module.exports = {
  generateResponse,
  generateResponseV2
}; 