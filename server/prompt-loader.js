const fs = require('fs').promises;
const path = require('path');

// Cache for loaded prompts
const promptCache = new Map();

/**
 * Load a prompt from a file
 * @param {string} promptName - Name of the prompt file (without extension)
 * @param {string} promptsDir - Directory containing prompt files (default: '../prompts')
 * @returns {Promise<string>} - The loaded prompt content
 */
async function loadPrompt(promptName, promptsDir = '../prompts') {
  const cacheKey = `${promptsDir}/${promptName}`;

  // Check cache first
  if (promptCache.has(cacheKey)) {
    return promptCache.get(cacheKey);
  }

  try {
    // Try markdown file first, then fallback to txt
    let promptPath = path.join(__dirname, promptsDir, `${promptName}.md`);
    let promptContent;

    try {
      promptContent = await fs.readFile(promptPath, 'utf8');
      console.log(`Loaded markdown prompt: ${promptName}.md`);
    } catch (mdError) {
      // If markdown file doesn't exist, try txt file
      promptPath = path.join(__dirname, promptsDir, `${promptName}.txt`);
      promptContent = await fs.readFile(promptPath, 'utf8');
      console.log(`Loaded text prompt: ${promptName}.txt`);
    }

    // Cache the loaded prompt
    promptCache.set(cacheKey, promptContent);

    return promptContent;
  } catch (error) {
    console.error(`Error loading prompt ${promptName}:`, error);
    throw new Error(`Failed to load prompt: ${promptName} (tried both .md and .txt files)`);
  }
}

/**
 * Clear the prompt cache
 */
function clearPromptCache() {
  promptCache.clear();
  console.log('Prompt cache cleared');
}

/**
 * Get cache statistics
 * @returns {Object} - Cache statistics
 */
function getCacheStats() {
  return {
    size: promptCache.size,
    keys: Array.from(promptCache.keys())
  };
}

/**
 * Render a prompt with variables using {{var}} syntax
 * @param {string} prompt - The prompt template
 * @param {Object} variables - Key-value pairs for interpolation
 * @returns {string} - The rendered prompt
 */
function renderPrompt(prompt, variables) {
  return prompt.replace(/{{(\w+)}}/g, (match, key) => {
    return Object.prototype.hasOwnProperty.call(variables, key) ? variables[key] : match;
  });
}

module.exports = {
  loadPrompt,
  clearPromptCache,
  getCacheStats,
  renderPrompt
}; 