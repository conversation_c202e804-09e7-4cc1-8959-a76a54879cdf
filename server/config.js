// Configuration file for admin settings

// Authorized phone numbers for admin access
// Add your actual phone numbers here
const AUTHORIZED_PHONES = [
  '+919000784140',
  '+917400190682',
  '+917637834925',
  '+918769128326',
];

// Admin session settings
const ADMIN_SESSION_CONFIG = {
  maxAge: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production'
};

// Database query limits
const QUERY_LIMITS = {
  defaultMessages: 100,
  maxMessages: 1000,
  defaultUsers: 50,
  maxUsers: 500
};

module.exports = {
  AUTHORIZED_PHONES,
  ADMIN_SESSION_CONFIG,
  QUERY_LIMITS
}; 