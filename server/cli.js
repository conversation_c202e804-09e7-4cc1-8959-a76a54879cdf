#!/usr/bin/env node

require('dotenv').config(); // Load environment variables from .env file
const readline = require('readline');
const { validateProviderEnvironment, getCurrentProviderModule, getProviderInfo } = require('./llm-config');

// Import database functionality
const { initializeDatabase, saveMessage, getRecentMessages, getMessagesWithUsage, getUsageStats } = require('./database');

// Special CLI user ID for database operations
const CLI_USER_ID = 'cli_user';
const CLI_SESSION_ID = 'cli_session_' + Date.now();

// Global variables
let db;
let generateResponse;
let generateResponseV2;
let providerInfo;

// Initialize everything before starting CLI
async function initializeCLI() {
  console.log('🚀 Initializing Smart Agent CLI...\n');

  // Initialize database first
  try {
    console.log('📊 Initializing database...');
    db = await initializeDatabase();
    console.log('✅ Database initialized successfully\n');
  } catch (err) {
    console.error('❌ Failed to initialize database:', err);
    process.exit(1);
  }

  // Initialize LLM provider
  try {
    console.log('🤖 Initializing LLM provider...');
    validateProviderEnvironment();
    const llmModule = require(getCurrentProviderModule());
    generateResponse = llmModule.generateResponse;
    generateResponseV2 = llmModule.generateResponseV2;
    providerInfo = getProviderInfo();
    console.log(`✅ LLM provider initialized: ${providerInfo.name} (${providerInfo.model})\n`);
  } catch (error) {
    console.error('❌ Failed to initialize LLM provider:', error.message);
    process.exit(1);
  }

  console.log('🎯 CLI ready! Use "help" for available commands.\n');
}

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function printHelp() {
  console.log(`
🤖 Smart Agent CLI - LLM Testing Tool

Usage:
  node cli.js [command] [options]

Commands:
  test <message>           Test generateResponse with a message
  test-v2 <message>        Test generateResponseV2 with a message
  interactive              Start interactive mode
  history                  Show CLI user's chat history
  usage                    Show CLI user's usage metadata
  help                     Show this help message

Examples:
  node cli.js test "Buy 100 shares of RELIANCE"
  node cli.js test-v2 "Sell all TATASTEEL when profit exceeds 5000"
  node cli.js interactive
  node cli.js history
  node cli.js usage

Options:
  --verbose, -v           Show detailed response metadata
  --pretty, -p            Pretty print JSON responses
  --help, -h              Show help message

Features:
  ✅ Database Integration: All messages are saved to the database
  ✅ Context Awareness: Uses recent chat history for better responses
  ✅ Special CLI User: Uses unique CLI user ID for database operations
  ✅ Session Management: Maintains conversation context
  ✅ Chat History: View previous conversations with 'history' command
  ✅ Usage Tracking: Monitor API usage with 'usage' command

Environment:
  Make sure your .env file is configured with the appropriate API keys.
  Current provider: ${providerInfo.name} (${providerInfo.model})
  CLI User ID: ${CLI_USER_ID}
`);
}

function prettyPrintJSON(obj) {
  return JSON.stringify(obj, null, 2);
}

async function testGenerateResponse(message, options = {}) {
  console.log(`\n🔄 Testing generateResponse...`);
  console.log(`📝 Message: "${message}"`);
  console.log(`🔧 Provider: ${providerInfo.name} (${providerInfo.model})`);
  console.log(`👤 CLI User ID: ${CLI_USER_ID}`);

  try {
    // Get recent chat history for context
    let recentMessages = [];
    if (db) {
      try {
        recentMessages = await getRecentMessages(db, CLI_USER_ID, 5);
        if (recentMessages.length > 0) {
          console.log(`📚 Using ${recentMessages.length} recent messages for context`);
        }
      } catch (dbError) {
        console.warn(`⚠️  Failed to load chat history: ${dbError.message}`);
      }
    }

    const startTime = Date.now();
    const result = await generateResponse(message, recentMessages);
    const endTime = Date.now();

    console.log(`\n✅ Response generated successfully!`);
    console.log(`⏱️  Total time: ${endTime - startTime}ms`);

    if (options.verbose) {
      console.log(`\n📊 Metadata:`);
      console.log(prettyPrintJSON(result.metadata));
    }

    console.log(`\n🤖 Response:`);
    if (options.pretty) {
      try {
        const parsed = JSON.parse(result.response);
        console.log(prettyPrintJSON(parsed));
      } catch (e) {
        console.log(result.response);
      }
    } else {
      console.log(result.response);
    }

    // Save message and response to database
    if (db) {
      try {
        const saveResult = await saveMessage(db, CLI_USER_ID, message, result.response, CLI_SESSION_ID, result.metadata);
        console.log(`💾 Saved to database - Message ID: ${saveResult.messageId}, Session ID: ${saveResult.sessionId}`);
      } catch (dbError) {
        console.error(`❌ Failed to save to database: ${dbError.message}`);
      }
    }

  } catch (error) {
    console.error(`\n❌ Error: ${error.message}`);
    if (options.verbose) {
      console.error(`\n🔍 Full error:`, error);
    }
  }
}

async function testGenerateResponseV2(message, options = {}) {
  console.log(`\n🔄 Testing generateResponseV2...`);
  console.log(`📝 Message: "${message}"`);
  console.log(`🔧 Provider: ${providerInfo.name} (${providerInfo.model})`);
  console.log(`👤 CLI User ID: ${CLI_USER_ID}`);

  try {
    const startTime = Date.now();
    const result = await generateResponseV2(message);
    const endTime = Date.now();

    console.log(`\n✅ Response generated successfully!`);
    console.log(`⏱️  Total time: ${endTime - startTime}ms`);

    // Debug: Log the raw result structure
    if (options.verbose) {
      console.log(`\n🔍 Raw result type: ${typeof result}`);
      console.log(`🔍 Raw result:`, result);
    }

    // Handle different response formats
    let llmResponse, usageMetadata;
    if (typeof result === 'object' && result.response) {
      llmResponse = result.response;
      usageMetadata = result.metadata || null;
      if (options.verbose) {
        console.log(`📊 Extracted metadata:`, usageMetadata);
      }
    } else if (typeof result === 'object' && result.metadata) {
      // Handle case where result is an object with metadata but no response field
      llmResponse = JSON.stringify(result);
      usageMetadata = result.metadata;
      if (options.verbose) {
        console.log(`📊 Extracted metadata:`, usageMetadata);
      }
    } else {
      llmResponse = result;
      usageMetadata = null;
      if (options.verbose) {
        console.log(`⚠️  No metadata found in result`);
      }
    }

    console.log(`\n🤖 Response:`);
    if (options.pretty) {
      try {
        const parsed = JSON.parse(llmResponse);
        console.log(prettyPrintJSON(parsed));
      } catch (e) {
        console.log(llmResponse);
      }
    } else {
      console.log(llmResponse);
    }

    // Save message and response to database
    if (db) {
      try {
        console.log(`💾 Saving to database with metadata:`, usageMetadata);
        const saveResult = await saveMessage(db, CLI_USER_ID, message, llmResponse, CLI_SESSION_ID, usageMetadata);
        console.log(`💾 Saved to database - Message ID: ${saveResult.messageId}, Session ID: ${saveResult.sessionId}`);
      } catch (dbError) {
        console.error(`❌ Failed to save to database: ${dbError.message}`);
        if (options.verbose) {
          console.error(`🔍 Full database error:`, dbError);
        }
      }
    }

  } catch (error) {
    console.error(`\n❌ Error: ${error.message}`);
    if (options.verbose) {
      console.error(`\n🔍 Full error:`, error);
    }
  }
}

async function interactiveMode() {
  console.log(`\n${'='.repeat(50)}`);
  console.log(`🎯 Interactive Mode - ${providerInfo.name} (${providerInfo.model})`);
  console.log(`👤 CLI User ID: ${CLI_USER_ID}`);
  console.log(`Type 'help' for commands, 'exit' to quit`);
  console.log(`${'='.repeat(50)}\n`);

  const askQuestion = () => {
    rl.question('💬 Enter your message: ', async (input) => {
      const trimmed = input.trim();

      if (trimmed.toLowerCase() === 'exit' || trimmed.toLowerCase() === 'quit') {
        console.log('👋 Goodbye!');
        rl.close();
        return;
      }

      if (trimmed.toLowerCase() === 'help') {
        console.log(`
Available commands:
  help                    Show this help
  exit, quit             Exit interactive mode
  test <message>         Test generateResponse
  test-v2 <message>      Test generateResponseV2
  history                Show CLI user's chat history
  usage                  Show CLI user's usage metadata
  verbose on/off         Toggle verbose mode
  pretty on/off          Toggle pretty print mode

Examples:
  test Buy 100 shares of RELIANCE
  test-v2 Sell all TATASTEEL when profit exceeds 5000
  history
  usage
`);
        askQuestion();
        return;
      }

      if (trimmed.startsWith('test ')) {
        const message = trimmed.substring(5);
        await testGenerateResponse(message, { verbose: global.verbose, pretty: global.pretty });
        askQuestion();
        return;
      }

      if (trimmed.startsWith('test-v2 ')) {
        const message = trimmed.substring(8);
        await testGenerateResponseV2(message, { verbose: global.verbose, pretty: global.pretty });
        askQuestion();
        return;
      }

      if (trimmed === 'history') {
        await showCLIHistory({ verbose: global.verbose, pretty: global.pretty });
        askQuestion();
        return;
      }

      if (trimmed === 'usage') {
        await showCLIUsage({ verbose: global.verbose, pretty: global.pretty });
        askQuestion();
        return;
      }

      if (trimmed === 'verbose on') {
        global.verbose = true;
        console.log('✅ Verbose mode enabled');
        askQuestion();
        return;
      }

      if (trimmed === 'verbose off') {
        global.verbose = false;
        console.log('✅ Verbose mode disabled');
        askQuestion();
        return;
      }

      if (trimmed === 'pretty on') {
        global.pretty = true;
        console.log('✅ Pretty print mode enabled');
        askQuestion();
        return;
      }

      if (trimmed === 'pretty off') {
        global.pretty = false;
        console.log('✅ Pretty print mode disabled');
        askQuestion();
        return;
      }

      // Default: test with generateResponseV2
      await testGenerateResponseV2(trimmed, { verbose: global.verbose, pretty: global.pretty });
      askQuestion();
    });
  };

  askQuestion();
}

async function showCLIHistory(options = {}) {
  console.log(`\n📚 CLI User Chat History`);
  console.log(`👤 User ID: ${CLI_USER_ID}`);

  if (!db) {
    console.log('❌ Database not initialized');
    return;
  }

  try {
    const history = await getRecentMessages(db, CLI_USER_ID, 10);

    if (history.length === 0) {
      console.log('📝 No chat history found');
      return;
    }

    console.log(`📝 Found ${history.length} recent messages:\n`);

    history.forEach((msg, index) => {
      console.log(`--- Message ${history.length - index} ---`);
      console.log(`🕐 Time: ${new Date(msg.timestamp).toLocaleString()}`);
      console.log(`👤 You: ${msg.user_message}`);
      console.log(`🤖 FinAgent: ${msg.llm_response.substring(0, 200)}${msg.llm_response.length > 200 ? '...' : ''}`);
      console.log(`🆔 Message ID: ${msg.id}`);
      console.log(`📋 Session ID: ${msg.session_id}`);
      console.log('');
    });

  } catch (error) {
    console.error(`❌ Error fetching chat history: ${error.message}`);
  }
}

async function showCLIUsage(options = {}) {
  console.log(`\n📊 CLI User Usage Metadata`);
  console.log(`👤 User ID: ${CLI_USER_ID}`);

  if (!db) {
    console.log('❌ Database not initialized');
    return;
  }

  try {
    // Get messages with usage metadata
    const usageData = await getMessagesWithUsage(db, 20);
    const cliMessages = usageData.filter(msg => msg.user_id === CLI_USER_ID);

    if (cliMessages.length === 0) {
      console.log('📝 No usage data found for CLI user');
      return;
    }

    console.log(`📝 Found ${cliMessages.length} CLI messages with usage data:\n`);

    cliMessages.forEach((msg, index) => {
      console.log(`--- Message ${cliMessages.length - index} ---`);
      console.log(`🕐 Time: ${new Date(msg.timestamp).toLocaleString()}`);
      console.log(`👤 You: ${msg.user_message.substring(0, 100)}${msg.user_message.length > 100 ? '...' : ''}`);
      console.log(`🆔 Message ID: ${msg.id}`);
      console.log(`📋 Session ID: ${msg.session_id}`);

      if (msg.usage_metadata) {
        try {
          const metadata = JSON.parse(msg.usage_metadata);
          console.log(`📊 Usage Metadata:`);
          console.log(prettyPrintJSON(metadata));
        } catch (e) {
          console.log(`📊 Usage Metadata (raw): ${msg.usage_metadata}`);
        }
      } else {
        console.log(`📊 Usage Metadata: None`);
      }
      console.log('');
    });

    // Get overall usage stats
    const stats = await getUsageStats(db);
    console.log(`📈 Overall Usage Statistics:`);
    console.log(prettyPrintJSON(stats));

  } catch (error) {
    console.error(`❌ Error fetching usage data: ${error.message}`);
    if (options.verbose) {
      console.error(`🔍 Full error:`, error);
    }
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const command = args[0];
const message = args[1];
const options = {
  verbose: args.includes('--verbose') || args.includes('-v'),
  pretty: args.includes('--pretty') || args.includes('-p')
};

// Global settings for interactive mode
global.verbose = false;
global.pretty = true;

// Main CLI function
async function runCLI() {
  // Initialize everything first
  await initializeCLI();

  // Handle commands
  if (!command || command === 'help' || command === '--help' || command === '-h') {
    printHelp();
  } else if (command === 'test') {
    if (!message) {
      console.error('❌ Error: Message is required for test command');
      console.log('Example: node cli.js test "Buy 100 shares of RELIANCE"');
      process.exit(1);
    }
    await testGenerateResponse(message, options);
  } else if (command === 'test-v2') {
    if (!message) {
      console.error('❌ Error: Message is required for test-v2 command');
      console.log('Example: node cli.js test-v2 "Sell all TATASTEEL when profit exceeds 5000"');
      process.exit(1);
    }
    await testGenerateResponseV2(message, options);
  } else if (command === 'interactive') {
    await interactiveMode();
  } else if (command === 'history') {
    await showCLIHistory(options);
  } else if (command === 'usage') {
    await showCLIUsage(options);
  } else {
    console.error(`❌ Unknown command: ${command}`);
    printHelp();
    process.exit(1);
  }
}

// Start the CLI
runCLI().catch(error => {
  console.error('❌ CLI Error:', error);
  process.exit(1);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n👋 Goodbye!');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n👋 Goodbye!');
  process.exit(0);
});