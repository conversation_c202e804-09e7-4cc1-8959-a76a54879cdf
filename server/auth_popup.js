// This script handles Firebase Phone Number authentication for the external popup window.

// Import Firebase modules using ES Module syntax
import { initializeApp } from 'https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js';
import { getAuth, RecaptchaVerifier, signInWithPhoneNumber } from 'https://www.gstatic.com/firebasejs/11.6.1/firebase-auth.js';


// --- Firebase Configuration ---
// IMPORTANT: Replace with your actual Firebase project configuration
const firebaseConfig = {
  apiKey: "AIzaSyB9T5_wqb5ySiLtvDU99w7_Otj3PgJ1gcc",
  authDomain: "tradetalk-ad365.firebaseapp.com",
  projectId: "tradetalk-ad365",
  storageBucket: "tradetalk-ad365.firebasestorage.app",
  messagingSenderId: "861501410895",
  appId: "1:861501410895:web:290fb51137647d7339243a",
  measurementId: "G-D4YJVX7S4S"
};

// Initialize Firebase for the popup
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
let recaptchaVerifier;
let confirmationResult;

// --- DOM Elements ---
const phoneNumberInput = document.getElementById('phone-number');
const countryCodeInput = document.getElementById('country-code');
const sendOtpBtn = document.getElementById('send-otp-btn');
const sendOtpBtnText = document.getElementById('send-otp-btn-text');
const loadingSpinner = document.getElementById('loading-spinner');
const otpSection = document.getElementById('otp-section');
const otpInputs = otpSection ? otpSection.querySelectorAll('.otp-input') : [];
const verifyOtpBtn = document.getElementById('verify-otp-btn');
const verifyOtpBtnText = document.getElementById('verify-otp-btn-text');
const verifyLoadingSpinner = document.getElementById('verify-loading-spinner');
const messageDisplay = document.getElementById('message-display');
const recaptchaContainer = document.getElementById('recaptcha-container');
const phoneForm = document.getElementById('phone-form');
const otpPhoneDisplay = document.getElementById('otp-phone-display');
const resendOtpLink = document.getElementById('resend-otp-link');
const resendOtpContainer = document.getElementById('resend-otp-container');
const agreementClauseSection = document.getElementById('agreement-clause');

// --- Get Extension ID from URL ---
const urlParams = new URLSearchParams(window.location.search);
const extensionId = urlParams.get('extensionId');

// IMPORTANT: Define the URL of your custom backend endpoint
const CUSTOM_TOKEN_ENDPOINT = window.location.origin + "/api/exchange-token";

// --- Functions to send messages back to the extension ---

/**
 * Sends a success message and the custom token to the Chrome extension.
 * @param {string} customToken - The Firebase Custom Token.
 */
function sendAuthSuccessToExtension(customToken) {
  if (window.opener && extensionId) {
    window.opener.postMessage({
      type: "FIREBASE_AUTH_SUCCESS",
      idToken: customToken // Renaming the payload for clarity, it's now a custom token
    }, `chrome-extension://${extensionId}`); // Specify the target origin for security
    window.close(); // Close the popup after sending the message
  } else {
    if (window.location.href.indexOf('/auth') > 0) {
      window.location = window.location.origin;
    } else {
      window.location.reload();
    }
  }
}

/**
 * Sends an error message to the Chrome extension.
 * @param {string} errorMessage - The error message.
 */
function sendAuthErrorToExtension(errorMessage) {
  if (window.opener && extensionId) {
    window.opener.postMessage({
      type: "FIREBASE_AUTH_ERROR",
      errorMessage: errorMessage
    }, `chrome-extension://${extensionId}`); // Specify the target origin for security
  } else {
    console.warn("Could not send error message to opener window. Extension ID missing or window.opener not available.");
  }
  // Don't close window immediately on error, let user see it or retry
}

/**
 * Displays a message (error or info) in the popup.
 * @param {string} message - The message to display.
 * @param {boolean} isError - True if it's an error message, false for info.
 */
function displayMessage(message, isError = false) {
  messageDisplay.textContent = message;
  messageDisplay.classList.remove('hidden', 'text-red-600', 'text-green-600');
  if (isError) {
    messageDisplay.classList.add('text-red-600');
  } else {
    messageDisplay.classList.add('text-green-600');
  }
  messageDisplay.classList.remove('hidden');
  // Hide message after a few seconds unless it's an important error
  if (!isError) {
    setTimeout(() => {
      messageDisplay.classList.add('hidden');
    }, 5000);
  }
}

// --- Firebase Authentication Logic ---

// Initialize reCAPTCHA Verifier
window.onload = () => {
  recaptchaVerifier = new RecaptchaVerifier(auth, recaptchaContainer, {
    'size': 'invisible',
    'callback': (response) => {
      console.log("reCAPTCHA solved on popup!");
    },
    'expired-callback': () => {
      displayMessage("reCAPTCHA expired. Please try again.", true);
      if (recaptchaVerifier && typeof recaptchaVerifier.reset === 'function') {
        recaptchaVerifier.reset();
      }
    }
  });
  recaptchaVerifier.render().catch(error => {
    console.error("Error rendering reCAPTCHA:", error);
    displayMessage("Failed to load reCAPTCHA. Please check your Firebase settings (Authorized Domains, reCAPTCHA keys).", true);
    sendAuthErrorToExtension("Failed to load reCAPTCHA: " + error.message);
  });
};

// Helper: Show/hide loading spinner and button text
function setButtonLoading(btn, textSpan, spinner, isLoading) {
  if (isLoading) {
    if (textSpan) textSpan.classList.add('hidden');
    if (spinner) spinner.classList.remove('hidden');
    btn.disabled = true;
  } else {
    if (textSpan) textSpan.classList.remove('hidden');
    if (spinner) spinner.classList.add('hidden');
    btn.disabled = false;
  }
}

// Helper: Enable/disable verify button based on OTP input
function updateVerifyButtonState() {
  const allFilled = Array.from(otpInputs).every(input => input.value.trim().length === 1);
  verifyOtpBtn.disabled = !allFilled;
}

// OTP input auto-focus logic
otpInputs.forEach((input, idx) => {
  input.addEventListener('input', (e) => {
    if (input.value.length === 1 && idx < otpInputs.length - 1) {
      otpInputs[idx + 1].focus();
    }
    updateVerifyButtonState();
  });
  input.addEventListener('keydown', (e) => {
    if (e.key === 'Backspace' && input.value === '' && idx > 0) {
      otpInputs[idx - 1].focus();
    }
    setTimeout(updateVerifyButtonState, 0); // In case of backspace
  });
});

// Send OTP button click handler
sendOtpBtn.addEventListener('click', async () => {
  const isdCode = countryCodeInput.value;
  const phoneNumber = phoneNumberInput.value.trim();
  if (!isdCode || !phoneNumber) {
    displayMessage("Please enter a phone number.", true);
    return;
  }
  // Concatenate ISD code and phone number
  let fullPhoneNumber = isdCode + phoneNumber;
  // Ensure it starts with +
  if (!fullPhoneNumber.startsWith('+')) {
    fullPhoneNumber = '+' + fullPhoneNumber;
  }

  setButtonLoading(sendOtpBtn, sendOtpBtnText, loadingSpinner, true);
  messageDisplay.classList.add('hidden');

  try {
    confirmationResult = await signInWithPhoneNumber(auth, fullPhoneNumber, recaptchaVerifier);
    displayMessage("OTP sent successfully! Enter it below.");

    // Show OTP section, hide phone form
    if (phoneForm) phoneForm.classList.add('hidden');
    if (agreementClauseSection) agreementClauseSection.classList.add('hidden');
    if (otpSection) otpSection.classList.remove('hidden');
    if (otpPhoneDisplay) otpPhoneDisplay.textContent = `${isdCode} - ${phoneNumber}`;
    setTimeout(() => resendOtpContainer.classList.remove("hidden"), 30 * 1000); // Resend option to appear after 30 seconds
    resetOtpInputs();
  } catch (error) {
    console.error("Error sending OTP:", error);
    if (error.code === 'auth/captcha-check-failed' || error.code === 'auth/web-context-unsupported') {
      displayMessage("reCAPTCHA verification failed or environment not supported. Ensure your domain is authorized in Firebase and reCAPTCHA is configured.", true);
    } else if (error.code === 'auth/invalid-phone-number') {
      displayMessage("Invalid phone number. Please use E.164 format (e.g., +11234567890).", true);
    } else if (error.code === 'auth/quota-exceeded') {
      displayMessage("SMS quota exceeded. Please try again later.", true);
    } else {
      displayMessage(`Failed to send OTP: ${error.message}`, true);
    }
    sendAuthErrorToExtension("Error sending OTP: " + error.message);
    setButtonLoading(sendOtpBtn, sendOtpBtnText, loadingSpinner, false);
    phoneNumberInput.disabled = false;
    if (recaptchaVerifier && typeof recaptchaVerifier.reset === 'function') {
      recaptchaVerifier.reset();
    }
  }
});

// Verify OTP button click handler
verifyOtpBtn.addEventListener('click', async () => {
  // Concatenate OTP from 6 input boxes
  const otpCode = Array.from(otpInputs).map(input => input.value.trim()).join('');
  if (!otpCode || otpCode.length !== 6) {
    displayMessage("Please enter the 6-digit OTP.", true);
    return;
  }
  if (!confirmationResult) {
    displayMessage("No OTP was sent. Please send OTP first.", true);
    return;
  }
  setButtonLoading(verifyOtpBtn, verifyOtpBtnText, verifyLoadingSpinner, true);
  messageDisplay.classList.add('hidden');

  try {
    const userCredential = await confirmationResult.confirm(otpCode);
    const user = userCredential.user;
    const idTokenResult = await user.getIdTokenResult();
    const idToken = idTokenResult.token;
    document.cookie = `idToken=${idToken}; path=/; max-age=3600; SameSite=Strict`;
    displayMessage("Authentication successful. Exchanging token...", false);
    // Call your custom backend endpoint to exchange ID token for custom token
    const response = await fetch(CUSTOM_TOKEN_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ idToken: idToken })
    });
    const data = await response.json();
    if (!response.ok) {
      throw new Error(data.error || 'Failed to exchange ID token for custom token.');
    }
    const customToken = data.customToken;
    if (!customToken) {
      throw new Error("Backend did not return a custom token.");
    }
    // Check if user exists in our DB
    const userId = user.uid;
    const userResp = await fetch(`/api/user/${userId}`);
    const userData = await userResp.json();
    if (userData.exists && userData.user && userData.user.name && userData.user.name.trim() !== '') {
      sendAuthSuccessToExtension(customToken);
    } else {
      // Show name form until a non-empty name is provided
      pendingUserId = userId;
      pendingCustomToken = customToken;
      showSection('name-form-section');
    }
  } catch (error) {
    console.error("Error verifying OTP or exchanging token:", error);
    if (error.code === 'auth/invalid-verification-code') {
      displayMessage("Invalid OTP. Please check the code and try again.", true);
    } else if (error.code === 'auth/code-expired') {
      displayMessage("OTP expired. Please send a new OTP.", true);
      phoneNumberInput.disabled = false;
      setButtonLoading(verifyOtpBtn, verifyOtpBtnText, verifyLoadingSpinner, false);
    } else {
      // General error from fetch or other issues
      displayMessage(`Failed during token exchange: ${error.message}`, true);
    }
    sendAuthErrorToExtension("Error verifying OTP or exchanging token: " + error.message);
    setButtonLoading(verifyOtpBtn, verifyOtpBtnText, verifyLoadingSpinner, false);
  }
});

// Resend OTP link handler (optional, just reload page for now)
if (resendOtpLink) {
  resendOtpLink.addEventListener('click', (e) => {
    e.preventDefault();
    window.location.reload();
  });
}

// When showing OTP section, always reset and disable the button
function resetOtpInputs() {
  otpInputs.forEach(input => input.value = '');
  if (otpInputs.length > 0) otpInputs[0].focus();
  updateVerifyButtonState();
}

// On page load, ensure verify button is disabled
updateVerifyButtonState();

// Helper to show/hide sections
function showSection(sectionId) {
  document.getElementById('phone-form')?.classList.add('hidden');
  document.getElementById('otp-section')?.classList.add('hidden');
  document.getElementById('name-form-section')?.classList.add('hidden');
  document.getElementById(sectionId)?.classList.remove('hidden');
}

// --- Name Form Logic ---
const nameFormSection = document.getElementById('name-form-section');
const userNameForm = document.getElementById('user-name-form');
const fullNameInput = document.getElementById('full-name');

let pendingUserId = null;
let pendingCustomToken = null;

if (userNameForm) {
  userNameForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    const name = fullNameInput.value.trim();
    if (!name || !pendingUserId) return;
    try {
      const resp = await fetch('/api/user', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId: pendingUserId, name })
      });
      const data = await resp.json();
      if (resp.ok && data.success) {
        // User created, now send token to extension
        sendAuthSuccessToExtension(pendingCustomToken);
      } else {
        displayMessage(data.error || 'Failed to save name', true);
      }
    } catch (err) {
      displayMessage('Failed to save name: ' + err.message, true);
    }
  });
}