# Smart Agent Server

This Node.js server provides authentication, chat functionality, and AI-powered responses using LLM (Google's Gemini or OpenAI's ChatGPT) API for the Smart Agent browser extension.

## Features

- **Authentication**: Firebase-based phone authentication with custom token exchange
- **Chat Interface**: Web-based chat interface accessible at `/`
- **AI Chat**: Integration with LLM API for intelligent responses
- **Message Persistence**: SQLite database for storing chat history and usage metadata
- **Usage Analytics**: Comprehensive tracking of LLM performance and token usage
- **Admin Dashboard**: Full-featured admin interface with analytics and user management
- **CORS Support**: Configured for cross-origin requests from the extension

## Setup

1. Install dependencies:

   ```bash
   npm install
   ```

2. Set up environment variables:

   ```bash
   cp env.example .env
   ```

   Then edit `.env` and add your Gemini API key:

   ```
   GEMINI_API_KEY=your_actual_gemini_api_key_here
   ```

3. Ensure you have the Firebase service account key file:

   - Download from Firebase Console: Project settings > Service accounts > Generate new private key
   - Place it in the server directory as `tradetalk-ad365-firebase-adminsdk-fbsvc-a4799ff62b.json`

4. Start the server:
   ```bash
   node index.js
   ```

## Endpoints

### Authentication

- `GET /auth` - Serves the authentication popup HTML
- `GET /auth_popup.js` - Serves the authentication popup JavaScript
- `POST /api/exchange-token` - Exchanges Firebase ID token for custom token

### Chat Interface

- `GET /` - Main chat interface (sidepanel HTML)
- `GET /sidepanel.js` - Chat interface JavaScript
- `GET /firebase-app.js` - Firebase App SDK
- `GET /firebase-auth.js` - Firebase Auth SDK
- `GET /tailwind.js` - Tailwind CSS
- `GET /icons/:iconName` - Extension icons

### Chat API

- `POST /api/chat` - Send a message and get LLM response
- `GET /api/chat/history/:userId` - Get chat history for a user
- `GET /api/chat/sessions/:userId` - Get sessions for a user
- `GET /api/chat/session/:sessionId` - Get messages for a specific session

### Admin API (Protected)

- `GET /admin` - Admin dashboard interface
- `POST /api/admin/validate-firebase-user` - Validate Firebase user for admin access
- `GET /api/admin/stats` - Get database statistics
- `GET /api/admin/users` - Get all users
- `GET /api/admin/sessions` - Get all sessions
- `GET /api/admin/messages` - Get recent messages with usage analytics
- `GET /api/admin/usage` - Get messages with usage metadata
- `GET /api/admin/usage/stats` - Get usage statistics
- `GET /api/admin/usage/user/:userId` - Get user-specific usage data
- `GET /api/admin/session/:sessionId` - Get session messages (admin endpoint)

## Usage

### Accessing the Chat Interface

Once the server is running, you can access the chat interface at:

```
http://localhost:3000/
```

### Accessing the Admin Dashboard

Navigate to the admin dashboard at:

```
http://localhost:3000/admin
```

**Note**: Admin access requires Firebase authentication and an authorized phone number. See `ADMIN_README.md` for setup details.

### Chat API Usage

#### Send a Message

```bash
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Hello, how are you?",
    "userId": "user123",
    "sessionId": "session_123"
  }'
```

Response:

```json
{
  "success": true,
  "messageId": 1,
  "sessionId": "session_123",
  "response": "Hello! I'm doing well, thank you for asking...",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

#### Get Chat History

```bash
curl http://localhost:3000/api/chat/history/user123?limit=10
```

Response:

```json
{
  "success": true,
  "history": [
    {
      "id": 1,
      "user_id": "user123",
      "user_message": "Hello",
      "llm_response": "Hi there!",
      "timestamp": "2024-01-01T12:00:00.000Z",
      "session_id": "session_123",
      "latency_ms": 1250,
      "model_name": "gemini-2.0-flash",
      "total_tokens": 150
    }
  ],
  "count": 1
}
```

### For Browser Extension Development

The chat interface is designed to work both as:

1. A standalone web page (when accessed via `/`)
2. A browser extension sidepanel (when loaded from the extension)

### Authentication Flow

1. User clicks "Login / Sign Up" in the chat interface
2. Authentication popup opens at `/auth`
3. User completes phone authentication
4. Popup sends custom token back to main window
5. User is signed in and can use the chat

## Configuration

### Environment Variables

Create a `.env` file with:

```
GEMINI_API_KEY=your_gemini_api_key_here
PORT=3000
```

### CORS Settings

The server is configured to allow requests from:

- `http://localhost:3000` (for local development)
- `https://smartagent.pandeyanshuman.com` (your hosted domain)

### Firebase Configuration

Update the Firebase config in `extension/sidepanel.js` if needed:

```javascript
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "your-project.firebaseapp.com",
  // ... other config
};
```

## Database

The server uses SQLite to store chat messages and usage metadata. The database file (`chat.db`) is automatically created when the server starts.

### Database Schema

```sql
-- Users table
CREATE TABLE users (
  user_id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Sessions table
CREATE TABLE sessions (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  title TEXT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Messages table (with usage metadata)
CREATE TABLE messages (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id TEXT NOT NULL,
  user_message TEXT NOT NULL,
  llm_response TEXT NOT NULL,
  timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
  session_id TEXT,
  latency_ms INTEGER,
  generation_config TEXT,
  model_name TEXT,
  input_tokens INTEGER,
  output_tokens INTEGER,
  total_tokens INTEGER,
  FOREIGN KEY (session_id) REFERENCES sessions (id)
);
```

### Usage Metadata

The system automatically tracks:

- **Response Latency**: Time taken for each LLM call
- **Token Usage**: Input, output, and total tokens consumed
- **Model Information**: Which model was used (e.g., "gemini-2.0-flash")
- **Generation Config**: Settings used for the LLM call
- **Performance Metrics**: For monitoring and optimization

## Development

### Adding New Features

1. **New Routes**: Add them to `server/index.js`
2. **Static Files**: Place them in the `extension/` directory and add routes to serve them
3. **Authentication**: Use the existing `/api/exchange-token` endpoint pattern
4. **Database Operations**: Add functions to `database.js`
5. **AI Integration**: Extend `gemini.js` for additional AI features
6. **Admin Features**: Add new admin endpoints and update `admin.html`

### Testing

1. Start the server: `node index.js`
2. Open `http://localhost:3000/` in your browser
3. Test the authentication flow
4. Test the chat functionality with LLM
5. Test the chat history API
6. Test the admin dashboard at `http://localhost:3000/admin`

### API Testing

You can test the chat API directly:

```bash
# Test sending a message
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "What is the weather like?", "userId": "testuser"}'

# Test getting history
curl http://localhost:3000/api/chat/history/testuser
```

## Troubleshooting

### Common Issues

1. **CORS Errors**: Check that the origin is allowed in the CORS configuration
2. **Firebase Errors**: Verify your service account key is correct and has proper permissions
3. **LLM API Errors**: Ensure your API key is valid and has proper permissions
4. **Database Errors**: Check that the server has write permissions to create the SQLite database
5. **File Not Found**: Ensure all required files exist in the `extension/` directory
6. **Admin Access Denied**: Verify your phone number is in the authorized list in `config.js`

### Logs

The server logs important information:

- Server startup with available endpoints
- Database initialization and migration status
- Authentication attempts and errors
- Token exchange operations
- Chat message processing with usage metadata
- LLM API responses and performance metrics

### Getting a Gemini API Key

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Add it to your `.env` file as `GEMINI_API_KEY`

## Related Documentation

- `ADMIN_README.md` - Detailed admin dashboard setup and usage
- `USAGE_METADATA_README.md` - Usage analytics and performance monitoring
