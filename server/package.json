{"name": "smart-agent-server", "version": "1.0.0", "description": "Server side for Smart Agent", "main": "index.js", "scripts": {"start": "node --watch-path=../ index.js", "test": "echo \"Error: no test specified\" && exit 1", "cli": "node cli.js", "cli:test": "node cli.js test", "cli:test-v2": "node cli.js test-v2", "cli:interactive": "node cli.js interactive"}, "author": "", "license": "ISC", "dependencies": {"@google/generative-ai": "^0.2.1", "@tailwindcss/cli": "^4.1.10", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^5.1.0", "firebase-admin": "^13.4.0", "openai": "^4.28.0", "sqlite3": "^5.1.6", "tailwindcss": "^4.1.10"}}