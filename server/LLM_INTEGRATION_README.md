# LLM Integration Guide

This guide explains how to use and configure different Large Language Model (LLM) providers in the Smart Agent system.

## Overview

The Smart Agent supports multiple LLM providers through a unified interface. You can easily switch between different providers without changing your application code.

## Supported Providers

### 1. Google Gemini

- **Model**: gemini-2.0-flash
- **API**: Google AI Studio
- **Cost**: Generally lower cost than OpenAI
- **Performance**: Good for structured outputs

### 2. OpenAI

- **Models**: gpt-4o-mini, gpt-4, gpt-3.5-turbo
- **API**: OpenAI API
- **Cost**: Varies by model
- **Performance**: Excellent for complex reasoning

## Configuration

### Environment Variables

Set up your environment variables in `.env`:

```bash
# Choose your LLM provider
LLM_PROVIDER=gemini  # or 'openai'

# Gemini API Key (required if using Gemini)
GEMINI_API_KEY=your_gemini_api_key_here

# OpenAI API Key (required if using OpenAI)
OPENAI_API_KEY=your_openai_api_key_here
```

### Provider Selection

The system uses the `LLM_PROVIDER` environment variable to determine which provider to use:

- `LLM_PROVIDER=gemini` - Uses Google Gemini
- `LLM_PROVIDER=openai` - Uses OpenAI

If not specified, the system defaults to Gemini.

## API Keys Setup

### Getting a Gemini API Key

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Add it to your `.env` file as `GEMINI_API_KEY`

### Getting an OpenAI API Key

1. Go to [OpenAI Platform](https://platform.openai.com/api-keys)
2. Create a new API key
3. Add it to your `.env` file as `OPENAI_API_KEY`

## Usage

### Switching Providers

To switch between providers, simply change the `LLM_PROVIDER` environment variable:

```bash
# Use Gemini
export LLM_PROVIDER=gemini

# Use OpenAI
export LLM_PROVIDER=openai
```

### Restart Required

After changing the provider, restart your server:

```bash
node index.js
```

## Provider-Specific Configuration

### Gemini Configuration

Located in `server/gemini.js`:

```javascript
const generationConfig = {
  maxOutputTokens: 1000,
  temperature: 0.7,
};
```

### OpenAI Configuration

Located in `server/openai.js`:

```javascript
const generationConfig = {
  max_tokens: 1000,
  temperature: 0.7,
};
```

You can modify these configurations in their respective files.

## Model Selection

### Available Models

#### Gemini Models

- `gemini-2.0-flash` (default) - Fast and cost-effective
- `gemini-2.0-exp` - Experimental features
- `gemini-1.5-flash` - Previous generation

#### OpenAI Models

- `gpt-4o-mini` (default) - Fast and cost-effective
- `gpt-4o` - More capable than mini
- `gpt-4` - Most capable but slower
- `gpt-3.5-turbo` - Fast and reliable

### Changing Models

To change models, edit the respective provider file:

**For Gemini** (`server/gemini.js`):

```javascript
const model = genAI.getGenerativeModel({ model: "gemini-2.0-exp" });
```

**For OpenAI** (`server/openai.js`):

```javascript
const completion = await openai.chat.completions.create({
  model: "gpt-4o", // Change this line
  // ... other options
});
```

## Error Handling

Both providers implement comprehensive error handling:

### Common Errors

1. **Rate Limiting**: Both providers handle rate limits gracefully
2. **Authentication**: Invalid API keys are caught and reported
3. **Network Issues**: Timeout and connection errors are handled
4. **Quota Exceeded**: Usage limits are detected and reported

### Error Messages

The system provides user-friendly error messages for common issues:

- "I'm currently experiencing high traffic. Please try again in a few moments."
- "I've reached my rate limit. Please wait a moment before trying again."
- "Authentication error. Please check your API configuration."

## Performance Monitoring

Both providers track the same metrics:

- **Latency**: Response time in milliseconds
- **Token Usage**: Input, output, and total tokens
- **Model Information**: Which model was used
- **Generation Config**: Settings used for the request

These metrics are stored in the database and visible in the admin dashboard.

## Cost Comparison

### Gemini Pricing

- Generally lower cost per token
- Good for high-volume usage
- Predictable pricing

### OpenAI Pricing

- Varies significantly by model
- gpt-4o-mini: Very cost-effective
- gpt-4: Higher cost but more capable

## Best Practices

### 1. Provider Selection

- **Use Gemini** for: Cost-sensitive applications, structured outputs
- **Use OpenAI** for: Complex reasoning, creative tasks

### 2. Model Selection

- **Start with default models** (gemini-2.0-flash or gpt-4o-mini)
- **Upgrade only when needed** for better performance
- **Monitor costs** in the admin dashboard

### 3. Error Handling

- Always handle API errors gracefully
- Implement retry logic for transient failures
- Monitor usage and costs regularly

### 4. Environment Management

- Keep API keys secure
- Use different keys for development and production
- Rotate keys regularly

## Troubleshooting

### Common Issues

1. **"Authentication error"**

   - Check your API key is correct
   - Verify the key has proper permissions
   - Ensure the key is not expired

2. **"Rate limit exceeded"**

   - Wait a few moments before retrying
   - Consider upgrading your API plan
   - Implement request throttling

3. **"Model not found"**

   - Check the model name is correct
   - Verify the model is available in your region
   - Update to a supported model

4. **"Provider not found"**
   - Check `LLM_PROVIDER` environment variable
   - Ensure the provider is supported
   - Verify the provider module exists

### Debugging

Enable debug logging by setting:

```bash
DEBUG=llm:*
```

This will show detailed information about API calls and responses.

## Development

### Adding New Providers

To add a new LLM provider:

1. Create a new provider file (e.g., `server/anthropic.js`)
2. Implement the `generateResponse` function with the same signature
3. Add the provider to `server/llm-config.js`
4. Update environment validation
5. Test thoroughly

### Provider Interface

All providers must implement:

```javascript
async function generateResponse(userMessage, chatHistory = []) {
  // Implementation here
  return {
    response: text,
    metadata: {
      latency_ms: latency,
      generation_config: config,
      model_name: modelName,
      input_tokens: inputTokens,
      output_tokens: outputTokens,
      total_tokens: totalTokens,
    },
  };
}
```

## Related Files

- `server/llm-config.js` - Provider configuration and selection
- `server/gemini.js` - Google Gemini integration
- `server/openai.js` - OpenAI integration
- `server/index.js` - Dynamic provider loading
- `.env` - Environment configuration
