// LLM Provider Configuration
// This file allows you to easily switch between different LLM providers

const LLM_PROVIDERS = {
  GEMINI: 'gemini',
  OPENAI: 'openai'
};

// Current active provider
const ACTIVE_PROVIDER = process.env.LLM_PROVIDER || LLM_PROVIDERS.GEMINI;

// Provider configurations
const PROVIDER_CONFIGS = {
  [LLM_PROVIDERS.GEMINI]: {
    name: 'Gemini',
    model: 'gemini-2.0-flash',
    maxTokens: 1000,
    temperature: 0,
    module: './gemini.js'
  },
  [LLM_PROVIDERS.OPENAI]: {
    name: 'OpenAI',
    model: 'gpt-4o-mini', // Can be changed to gpt-4, gpt-3.5-turbo, etc.
    maxTokens: 1000,
    temperature: 0,
    module: './openai.js'
  }
};

// Available models for each provider
// Todo: add commnents inside the json which can be used to display in admin panel.
const AVAILABLE_MODELS = {
  [LLM_PROVIDERS.GEMINI]: [
    { id: 'gemini-2.0-flash', name: 'Gemini 2.0 Flash', description: 'Fast and cost-effective (Speed: 4, Intelligence: 2)' },
    { id: 'gemini-2.0-exp', name: 'Gemini 2.0 Experimental', description: 'Latest features (Speed: 3, Intelligence: 3)' },
    { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash', description: 'Previous generation — not recommended due to very low intelligence' }
  ],
  [LLM_PROVIDERS.OPENAI]: [
    { id: 'gpt-4.1', name: 'GPT-4.1', description: 'Most capable and cost-efficient — ideal for complex tasks (Speed: 3, Intelligence: 4)' },
    { id: 'gpt-4.1-mini', name: 'GPT-4.1 Mini', description: 'Very fast and cost-effective with similar capabilities to 4-o (Speed: 4, Intelligence: 3)' },
    { id: 'gpt-4.1-nano', name: 'GPT-4.1 Nano', description: 'Cheapest and lightning fast, but with lower intelligence (Speed: 5, Intelligence: 2)' },
    { id: 'gpt-4o-mini', name: 'GPT-4o Mini', description: 'Fast and cost-effective, but inferior in capability (Speed: 4, Intelligence: 2)' },
    { id: 'gpt-4o', name: 'GPT-4o', description: 'More capable than mini, moderate speed and intelligence (Speed: 3, Intelligence: 3)' },
    { id: 'gpt-4', name: 'GPT-4', description: 'Most capable but slower — not recommended due to low speed and high cost' },
    { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: 'Fast and reliable — not recommended due to lower intelligence and value' }
  ]
};

// Get the current provider configuration
function getCurrentProvider() {
  return ACTIVE_PROVIDER;
}

// Get the configuration for the current provider
function getCurrentProviderConfig() {
  return PROVIDER_CONFIGS[ACTIVE_PROVIDER];
}

// Get the module path for the current provider
function getCurrentProviderModule() {
  return PROVIDER_CONFIGS[ACTIVE_PROVIDER].module;
}

// Check if a provider is available
function isProviderAvailable(provider) {
  return LLM_PROVIDERS[provider.toUpperCase()] !== undefined;
}

// Validate environment variables for the current provider
function validateProviderEnvironment() {
  const config = getCurrentProviderConfig();

  switch (ACTIVE_PROVIDER) {
    case LLM_PROVIDERS.GEMINI:
      if (!process.env.GEMINI_API_KEY) {
        throw new Error('GEMINI_API_KEY environment variable is required for Gemini provider');
      }
      break;
    case LLM_PROVIDERS.OPENAI:
      if (!process.env.OPENAI_API_KEY) {
        throw new Error('OPENAI_API_KEY environment variable is required for OpenAI provider');
      }
      break;
    default:
      throw new Error(`Unknown LLM provider: ${ACTIVE_PROVIDER}`);
  }

  console.log(`Using LLM provider: ${config.name} (${config.model})`);
}

// Get all available providers
function getAvailableProviders() {
  return Object.values(LLM_PROVIDERS);
}

// Get provider info for display
function getProviderInfo() {
  const config = getCurrentProviderConfig();
  return {
    provider: ACTIVE_PROVIDER,
    name: config.name,
    model: config.model,
    maxTokens: config.maxTokens,
    temperature: config.temperature
  };
}

/**
 * Get available models for a provider
 * @param {string} provider - Provider name
 * @returns {Array} - Array of available models
 */
function getAvailableModels(provider) {
  return AVAILABLE_MODELS[provider] || [];
}

/**
 * Get all available models across all providers
 * @returns {Object} - Object with provider as key and models array as value
 */
function getAllAvailableModels() {
  return AVAILABLE_MODELS;
}

/**
 * Validate if a model is available for a provider
 * @param {string} provider - Provider name
 * @param {string} model - Model ID
 * @returns {boolean} - True if model is available
 */
function isModelAvailable(provider, model) {
  const models = AVAILABLE_MODELS[provider];
  if (!models) return false;
  return models.some(m => m.id === model);
}

/**
 * Get model info by ID
 * @param {string} provider - Provider name
 * @param {string} modelId - Model ID
 * @returns {Object|null} - Model info or null if not found
 */
function getModelInfo(provider, modelId) {
  const models = AVAILABLE_MODELS[provider];
  if (!models) return null;
  return models.find(m => m.id === modelId) || null;
}

module.exports = {
  LLM_PROVIDERS,
  ACTIVE_PROVIDER,
  PROVIDER_CONFIGS,
  AVAILABLE_MODELS,
  getCurrentProvider,
  getCurrentProviderConfig,
  getCurrentProviderModule,
  isProviderAvailable,
  validateProviderEnvironment,
  getAvailableProviders,
  getProviderInfo,
  getAvailableModels,
  getAllAvailableModels,
  isModelAvailable,
  getModelInfo
}; 