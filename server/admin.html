<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Agent Admin Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .fade-enter-active, .fade-leave-active {
            transition: opacity 0.3s ease;
        }
        .fade-enter-from, .fade-leave-to {
            opacity: 0;
        }
        .message-content {
            max-height: 200px;
            overflow-y: auto;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .stats-card.users {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .stats-card.sessions {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .stats-card.messages {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div id="app" class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">
                <i class="fas fa-chart-line text-blue-600 mr-3"></i>
                Smart Agent Admin Dashboard
            </h1>
            <p class="text-gray-600">Database analytics and user management</p>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="flex items-center justify-center min-h-64">
            <div class="text-center">
                <i class="fas fa-spinner fa-spin text-4xl text-blue-600 mb-4"></i>
                <p class="text-gray-600">Checking authentication...</p>
            </div>
        </div>

        <!-- Authentication Required -->
        <div v-else-if="!isAuthenticated" class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
            <div class="text-center mb-6">
                <i class="fas fa-lock text-4xl text-gray-400 mb-4"></i>
                <h2 class="text-xl font-semibold text-gray-800">Admin Access Required</h2>
                <p class="text-gray-600 mt-2">Please log in with an authorized account</p>
            </div>
            
            <div v-if="!firebaseUser" class="space-y-4">
                <button 
                    @click="signInWithGoogle" 
                    :disabled="signingIn"
                    class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    <i v-if="signingIn" class="fas fa-spinner fa-spin mr-2"></i>
                    <i v-else class="fab fa-google mr-2"></i>
                    {{ signingIn ? 'Signing in...' : 'Sign in with Google' }}
                </button>
            </div>
            
            <div v-else class="space-y-4">
                <div class="bg-gray-50 p-4 rounded-md">
                    <div class="flex items-center mb-3">
                        <img v-if="firebaseUser.photoURL" :src="firebaseUser.photoURL" class="w-10 h-10 rounded-full mr-3">
                        <div v-else class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                            <i class="fas fa-user text-blue-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900">{{ adminUser ? (adminUser.displayName || 'Admin User') : 'Admin User' }}</p>
                            <p class="text-sm text-gray-500">{{ firebaseUser.email || 'No email' }}</p>
                        </div>
                    </div>
                    <p class="text-sm text-gray-600">Phone: {{ adminUser ? adminUser.phoneNumber : (firebaseUser.phoneNumber || 'Not available') }}</p>
                </div>
                
                <button 
                    @click="signOut" 
                    class="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                >
                    <i class="fas fa-sign-out-alt mr-2"></i>Sign Out
                </button>
            </div>
            
            <div v-if="error" class="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-md">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                {{ error }}
            </div>
        </div>

        <!-- Dashboard Content -->
        <div v-else class="space-y-8">
            <!-- Header with Logout -->
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <div>
                        <h2 class="text-2xl font-semibold text-gray-800">Dashboard Overview</h2>
                        <p class="text-gray-600">Welcome {{ adminUser ? (adminUser.displayName || adminUser.phoneNumber || 'Admin User') : 'Admin User' }}!</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-right">
                        <p class="text-sm text-gray-600">Logged in as</p>
                        <p class="text-sm font-medium text-gray-900">{{ adminUser ? (adminUser.phoneNumber || adminUser.email || 'Unknown') : 'Unknown' }}</p>
                    </div>
                    <button @click="signOut" class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                        <i class="fas fa-sign-out-alt mr-2"></i>Logout
                    </button>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="stats-card users bg-white rounded-lg shadow-md p-6 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm opacity-90">Total Users</p>
                            <p class="text-3xl font-bold">{{ stats.totalUsers || 0 }}</p>
                        </div>
                        <i class="fas fa-users text-3xl opacity-80"></i>
                    </div>
                </div>
                
                <div class="stats-card sessions bg-white rounded-lg shadow-md p-6 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm opacity-90">Total Sessions</p>
                            <p class="text-3xl font-bold">{{ stats.totalSessions || 0 }}</p>
                        </div>
                        <i class="fas fa-comments text-3xl opacity-80"></i>
                    </div>
                </div>
                
                <div class="stats-card messages bg-white rounded-lg shadow-md p-6 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm opacity-90">Total Messages</p>
                            <p class="text-3xl font-bold">{{ stats.totalMessages || 0 }}</p>
                        </div>
                        <i class="fas fa-envelope text-3xl opacity-80"></i>
                    </div>
                </div>
                
                <div class="stats-card bg-white rounded-lg shadow-md p-6 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm opacity-90">Active Users</p>
                            <p class="text-3xl font-bold">{{ stats.activeUsers || 0 }}</p>
                        </div>
                        <i class="fas fa-user-check text-3xl opacity-80"></i>
                    </div>
                </div>
            </div>

            <!-- Navigation Tabs -->
            <div class="bg-white rounded-lg shadow-md">
                <div class="border-b border-gray-200">
                    <nav class="flex space-x-8 px-6">
                        <button 
                            v-for="tab in tabs" 
                            :key="tab.id"
                            @click="activeTab = tab.id"
                            :class="[
                                'py-4 px-1 border-b-2 font-medium text-sm',
                                activeTab === tab.id 
                                    ? 'border-blue-500 text-blue-600' 
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            ]"
                        >
                            <i :class="tab.icon + ' mr-2'"></i>
                            {{ tab.name }}
                        </button>
                    </nav>
                </div>

                <div class="p-6">
                    <!-- Users Tab -->
                    <div v-if="activeTab === 'users'" class="space-y-4">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-semibold text-gray-800">All Users</h3>
                            <button @click="refreshData" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                <i class="fas fa-refresh mr-2"></i>Refresh
                            </button>
                        </div>
                        
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Messages</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sessions</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joined</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-for="user in users" :key="user.user_id" class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                                        <i class="fas fa-user text-blue-600"></i>
                                                    </div>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">{{ user.name }}</div>
                                                    <div class="text-sm text-gray-500">{{ user.user_id }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ user.messageCount }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ user.sessionCount }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatDate(user.created_at) }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Sessions Tab -->
                    <div v-if="activeTab === 'sessions'" class="space-y-4">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-semibold text-gray-800">All Sessions</h3>
                            <button @click="refreshData" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                <i class="fas fa-refresh mr-2"></i>Refresh
                            </button>
                        </div>
                        
                        <div class="grid gap-4">
                            <div v-for="session in sessions" :key="session.id" class="bg-gray-50 rounded-lg">
                                <!-- Session Header -->
                                <div class="p-4 cursor-pointer hover:bg-gray-100 transition-colors" 
                                     @click="toggleSession(session.id)">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1">
                                            <div class="flex items-center">
                                                <i :class="[
                                                    'fas mr-2 transition-transform duration-200',
                                                    expandedSessions.includes(session.id) ? 'fa-chevron-down' : 'fa-chevron-right'
                                                ]"></i>
                                                <h4 class="font-medium text-gray-900">{{ session.title }}</h4>
                                            </div>
                                            <p class="text-sm text-gray-600 mt-1">User: {{ session.user_name || session.user_id }}</p>
                                            <p class="text-sm text-gray-500">{{ session.messageCount }} messages</p>
                                        </div>
                                        <div class="text-right text-sm text-gray-500">
                                            <div>Created: {{ formatDate(session.created_at) }}</div>
                                            <div>Updated: {{ formatDate(session.updated_at) }}</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Session Messages (Expandable) -->
                                <div v-if="expandedSessions.includes(session.id)" 
                                     class="border-t border-gray-200 bg-white rounded-b-lg">
                                    <div class="p-4">
                                        <div v-if="sessionMessages[session.id] && sessionMessages[session.id].length > 0" 
                                             class="space-y-3">
                                            <div v-for="message in sessionMessages[session.id]" 
                                                 :key="message.id" 
                                                 class="bg-gray-50 rounded-lg p-3">
                                                <div class="flex justify-between items-start mb-2">
                                                    <span class="text-xs text-gray-500">{{ formatDate(message.timestamp) }}</span>
                                                    <!-- Usage Analytics Badge -->
                                                    <span v-if="message.latency_ms" 
                                                          class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                                          :class="getLatencyClass(message.latency_ms)">
                                                        {{ formatLatency(message.latency_ms) }}
                                                    </span>
                                                </div>
                                                
                                                <div class="space-y-2">
                                                    <div>
                                                        <div class="text-xs font-medium text-gray-700 mb-1">User:</div>
                                                        <div class="text-sm text-gray-800 bg-white p-2 rounded border">{{ message.user_message }}</div>
                                                    </div>
                                                    <div>
                                                        <div class="text-xs font-medium text-gray-700 mb-1">AI Response:</div>
                                                        <div class="text-sm text-gray-800 bg-white p-2 rounded border max-h-32 overflow-y-auto">{{ message.llm_response }}</div>
                                                    </div>
                                                    
                                                    <!-- Usage Analytics Section -->
                                                    <div v-if="message.latency_ms" class="bg-white p-2 rounded border">
                                                        <div class="text-xs font-medium text-gray-700 mb-1">Performance:</div>
                                                        <div class="grid grid-cols-2 gap-2 text-xs">
                                                            <div>
                                                                <span class="text-gray-500">Model:</span>
                                                                <span class="ml-1 font-medium">{{ message.model_name || 'N/A' }}</span>
                                                            </div>
                                                            <div>
                                                                <span class="text-gray-500">Tokens:</span>
                                                                <span class="ml-1 font-medium">{{ message.total_tokens || 0 }}</span>
                                                            </div>
                                                            <div>
                                                                <span class="text-gray-500">Config:</span>
                                                                <button @click="showConfig(message.generation_config)" 
                                                                        class="ml-1 text-blue-600 hover:text-blue-800">
                                                                    <i class="fas fa-cog mr-1"></i>View
                                                                </button>
                                                            </div>
                                                            <div>
                                                                <span class="text-gray-500">Latency:</span>
                                                                <span class="ml-1 font-medium" :class="getLatencyTextClass(message.latency_ms)">
                                                                    {{ formatLatency(message.latency_ms) }}
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-else class="text-center py-4 text-gray-500">
                                            <i class="fas fa-spinner fa-spin mr-2"></i>
                                            Loading messages...
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Messages Tab -->
                    <div v-if="activeTab === 'messages'" class="space-y-6">
                        <!-- Usage Statistics Cards -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm opacity-90">Total API Calls</p>
                                        <p class="text-2xl font-bold">{{ usageStats.totalCalls || 0 }}</p>
                                    </div>
                                    <i class="fas fa-phone text-2xl opacity-80"></i>
                                </div>
                            </div>
                            
                            <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm opacity-90">Avg Latency</p>
                                        <p class="text-2xl font-bold">{{ formatLatency(usageStats.avgLatency) }}</p>
                                    </div>
                                    <i class="fas fa-tachometer-alt text-2xl opacity-80"></i>
                                </div>
                            </div>
                            
                            <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-4 text-white">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm opacity-90">Total Tokens</p>
                                        <p class="text-2xl font-bold">{{ formatNumber(usageStats.totalTokens) }}</p>
                                    </div>
                                    <i class="fas fa-coins text-2xl opacity-80"></i>
                                </div>
                            </div>
                            
                            <div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-4 text-white">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm opacity-90">Unique Users</p>
                                        <p class="text-2xl font-bold">{{ usageStats.uniqueUsers || 0 }}</p>
                                    </div>
                                    <i class="fas fa-user-friends text-2xl opacity-80"></i>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-semibold text-gray-800">Recent Messages with Analytics</h3>
                            <button @click="refreshData" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                <i class="fas fa-refresh mr-2"></i>Refresh
                            </button>
                        </div>
                        
                        <div class="space-y-4">
                            <div v-for="message in messages" :key="message.id" class="bg-gray-50 rounded-lg p-4">
                                <div class="flex justify-between items-start mb-3">
                                    <div>
                                        <span class="text-sm font-medium text-gray-900">{{ message.user_name || message.user_id }}</span>
                                        <span class="text-sm text-gray-500 ml-2">{{ formatDate(message.timestamp) }}</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">{{ message.session_title || 'No Session' }}</span>
                                        <!-- Usage Analytics Badge -->
                                        <span v-if="message.latency_ms" 
                                              class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                              :class="getLatencyClass(message.latency_ms)">
                                            {{ formatLatency(message.latency_ms) }}
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="space-y-3">
                                    <div>
                                        <div class="text-sm font-medium text-gray-700 mb-1">User Message:</div>
                                        <div class="message-content bg-white p-3 rounded border text-sm text-gray-800">{{ message.user_message }}</div>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-700 mb-1">AI Response:</div>
                                        <div class="message-content bg-white p-3 rounded border text-sm text-gray-800">{{ message.llm_response }}</div>
                                    </div>
                                    
                                    <!-- Usage Analytics Section -->
                                    <div v-if="message.latency_ms" class="bg-white p-3 rounded border">
                                        <div class="text-sm font-medium text-gray-700 mb-2">Performance Analytics:</div>
                                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
                                            <div>
                                                <span class="text-gray-500">Model:</span>
                                                <span class="ml-1 font-medium">{{ message.model_name || 'N/A' }}</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">Tokens:</span>
                                                <span class="ml-1 font-medium">
                                                    {{ message.input_tokens || 0 }} → {{ message.output_tokens || 0 }} 
                                                    ({{ message.total_tokens || 0 }} total)
                                                </span>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">Config:</span>
                                                <button @click="showConfig(message.generation_config)" 
                                                        class="ml-1 text-blue-600 hover:text-blue-800">
                                                    <i class="fas fa-cog mr-1"></i>View
                                                </button>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">Performance:</span>
                                                <span class="ml-1 font-medium" :class="getLatencyTextClass(message.latency_ms)">
                                                    {{ formatLatency(message.latency_ms) }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>

    <script>
        const { createApp } = Vue;

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyB9T5_wqb5ySiLtvDU99w7_Otj3PgJ1gcc",
            authDomain: "tradetalk-ad365.firebaseapp.com",
            projectId: "tradetalk-ad365",
            storageBucket: "tradetalk-ad365.firebasestorage.app",
            messagingSenderId: "861501410895",
            appId: "1:861501410895:web:290fb51137647d7339243a",
            measurementId: "G-D4YJVX7S4S"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);

        createApp({
            data() {
                return {
                    loading: true,
                    isAuthenticated: false,
                    firebaseUser: null,
                    adminUser: null, // Store admin user info from server
                    signingIn: false,
                    error: '',
                    activeTab: 'users',
                    stats: {},
                    users: [],
                    sessions: [],
                    messages: [],
                    usageStats: {},
                    expandedSessions: [],
                    sessionMessages: {},
                    tabs: [
                        { id: 'users', name: 'Users', icon: 'fas fa-users' },
                        { id: 'sessions', name: 'Sessions', icon: 'fas fa-comments' },
                        { id: 'messages', name: 'Messages & Analytics', icon: 'fas fa-envelope' }
                    ]
                }
            },
            async mounted() {
                // Listen for Firebase auth state changes
                firebase.auth().onAuthStateChanged(async (user) => {
                    this.firebaseUser = user;
                    
                    if (user) {
                        // User is signed in, validate their phone number
                        await this.validateUserAccess();
                    } else {
                        // User is signed out
                        this.isAuthenticated = false;
                        this.loading = false;
                    }
                });
            },
            methods: {
                async validateUserAccess() {
                    try {
                        // Get the user's ID token
                        const idToken = await this.firebaseUser.getIdToken();
                        
                        // Validate with the server
                        const response = await fetch('/api/admin/validate-firebase-user', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${idToken}`
                            }
                        });
                        
                        const data = await response.json();
                        
                        if (data.success) {
                            this.isAuthenticated = true;
                            this.adminUser = data.user; // Store admin user info
                            this.loadDashboardData();
                        } else {
                            this.error = data.error || 'Access denied. Your account is not authorized.';
                            this.isAuthenticated = false;
                        }
                    } catch (error) {
                        console.error('Error validating user access:', error);
                        this.error = 'Error validating access. Please try again.';
                        this.isAuthenticated = false;
                    } finally {
                        this.loading = false;
                    }
                },
                
                async signInWithGoogle() {
                    this.signingIn = true;
                    this.error = '';
                    
                    try {
                        const provider = new firebase.auth.GoogleAuthProvider();
                        await firebase.auth().signInWithPopup(provider);
                    } catch (error) {
                        console.error('Sign-in error:', error);
                        this.error = 'Sign-in failed. Please try again.';
                    } finally {
                        this.signingIn = false;
                    }
                },
                
                async signOut() {
                    try {
                        await firebase.auth().signOut();
                        this.isAuthenticated = false;
                        this.adminUser = null; // Clear admin user info
                        this.stats = {};
                        this.users = [];
                        this.sessions = [];
                        this.messages = [];
                        this.usageStats = {};
                        this.expandedSessions = [];
                        this.sessionMessages = {};
                        this.error = '';
                    } catch (error) {
                        console.error('Sign-out error:', error);
                    }
                },
                
                async loadDashboardData() {
                    try {
                        // Get fresh ID token for API calls
                        const idToken = await this.firebaseUser.getIdToken();
                        
                        // Load stats
                        const statsResponse = await fetch('/api/admin/stats', {
                            headers: {
                                'Authorization': `Bearer ${idToken}`
                            }
                        });
                        const statsData = await statsResponse.json();
                        if (statsData.success) {
                            this.stats = statsData.stats;
                        }
                        
                        // Load users
                        const usersResponse = await fetch('/api/admin/users', {
                            headers: {
                                'Authorization': `Bearer ${idToken}`
                            }
                        });
                        const usersData = await usersResponse.json();
                        if (usersData.success) {
                            this.users = usersData.users;
                        }
                        
                        // Load sessions
                        const sessionsResponse = await fetch('/api/admin/sessions', {
                            headers: {
                                'Authorization': `Bearer ${idToken}`
                            }
                        });
                        const sessionsData = await sessionsResponse.json();
                        if (sessionsData.success) {
                            this.sessions = sessionsData.sessions;
                        }
                        
                        // Load messages
                        const messagesResponse = await fetch('/api/admin/messages', {
                            headers: {
                                'Authorization': `Bearer ${idToken}`
                            }
                        });
                        const messagesData = await messagesResponse.json();
                        if (messagesData.success) {
                            this.messages = messagesData.messages;
                        }
                        
                        // Load usage stats
                        const usageStatsResponse = await fetch('/api/admin/usage/stats', {
                            headers: {
                                'Authorization': `Bearer ${idToken}`
                            }
                        });
                        const usageStatsData = await usageStatsResponse.json();
                        if (usageStatsData.success) {
                            this.usageStats = usageStatsData.stats;
                        }
                    } catch (error) {
                        console.error('Error loading dashboard data:', error);
                    }
                },
                
                async refreshData() {
                    await this.loadDashboardData();
                },
                
                formatDate(dateString) {
                    if (!dateString) return 'N/A';
                    const date = new Date(dateString);
                    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
                },
                
                formatLatency(latency) {
                    if (!latency) return 'N/A';
                    if (latency < 1000) {
                        return `${latency}ms`;
                    } else {
                        return `${(latency / 1000).toFixed(1)}s`;
                    }
                },
                
                formatNumber(num) {
                    if (!num) return '0';
                    return num.toLocaleString();
                },
                
                getLatencyClass(latency) {
                    if (!latency) return 'bg-gray-100 text-gray-800';
                    if (latency < 1000) return 'bg-green-100 text-green-800';
                    if (latency < 3000) return 'bg-yellow-100 text-yellow-800';
                    return 'bg-red-100 text-red-800';
                },
                
                getLatencyTextClass(latency) {
                    if (!latency) return 'text-gray-600';
                    if (latency < 1000) return 'text-green-600';
                    if (latency < 3000) return 'text-yellow-600';
                    return 'text-red-600';
                },
                
                showConfig(configString) {
                    try {
                        const config = JSON.parse(configString);
                        const formattedConfig = JSON.stringify(config, null, 2);
                        alert('Generation Configuration:\n\n' + formattedConfig);
                    } catch (error) {
                        alert('Configuration:\n\n' + configString);
                    }
                },
                
                toggleSession(sessionId) {
                    if (this.expandedSessions.includes(sessionId)) {
                        // Collapse session
                        this.expandedSessions = this.expandedSessions.filter(id => id !== sessionId);
                    } else {
                        // Expand session and load messages
                        this.expandedSessions.push(sessionId);
                        this.loadSessionMessages(sessionId);
                    }
                },
                
                async loadSessionMessages(sessionId) {
                    // Check if messages are already loaded
                    if (this.sessionMessages[sessionId]) {
                        return;
                    }
                    
                    try {
                        // Get fresh ID token for API calls
                        const idToken = await this.firebaseUser.getIdToken();
                        
                        // Load session messages
                        const response = await fetch(`/api/admin/session/${sessionId}?userId=${this.getSessionUserId(sessionId)}`, {
                            headers: {
                                'Authorization': `Bearer ${idToken}`
                            }
                        });
                        
                        const data = await response.json();
                        if (data.success) {
                            // Use direct assignment for Vue 3
                            this.sessionMessages[sessionId] = data.messages;
                        }
                    } catch (error) {
                        console.error('Error loading session messages:', error);
                        this.sessionMessages[sessionId] = [];
                    }
                },
                
                getSessionUserId(sessionId) {
                    const session = this.sessions.find(s => s.id === sessionId);
                    return session ? session.user_id : '';
                }
            }
        }).mount('#app');
    </script>
</body>
</html> 