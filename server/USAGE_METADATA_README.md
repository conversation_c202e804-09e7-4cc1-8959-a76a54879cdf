# Usage Metadata Tracking

This document describes the usage metadata tracking feature that has been implemented to monitor LLM (Gemini API) usage patterns and performance.

## Overview

The system now tracks detailed metadata for each LLM call, including:

- Response latency
- Generation configuration used
- Token usage (input, output, total)
- Model information
- User and session context

## Database Schema

### messages Table (Enhanced)

The messages table now includes usage metadata fields:

```sql
CREATE TABLE messages (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id TEXT NOT NULL,
  user_message TEXT NOT NULL,
  gemini_response TEXT NOT NULL,
  timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
  session_id TEXT,
  latency_ms INTEGER,
  generation_config TEXT,
  model_name TEXT,
  input_tokens INTEGER,
  output_tokens INTEGER,
  total_tokens INTEGER,
  FOREIGN KEY (session_id) REFERENCES sessions (id)
);
```

**Usage Metadata Fields:**

- `latency_ms`: Response time in milliseconds
- `generation_config`: JSON string of the generation configuration used
- `model_name`: Name of the LLM model (e.g., "gemini-2.0-flash")
- `input_tokens`: Number of input tokens consumed
- `output_tokens`: Number of output tokens generated
- `total_tokens`: Total tokens used (input + output)

## API Endpoints

### Admin Endpoints (Protected by Firebase Admin Authentication)

#### Get All Messages with Usage Metadata

```
GET /api/admin/usage?limit=100
```

Returns all messages that have usage metadata with user and session information.

#### Get Usage Statistics

```
GET /api/admin/usage/stats
```

Returns aggregated statistics including:

- Total API calls
- Average, min, max latency
- Total token usage
- Unique users
- Active days

#### Get User-Specific Usage

```
GET /api/admin/usage/user/:userId?limit=50
```

Returns usage metadata for a specific user.

#### Get Session Messages (Admin)

```
GET /api/admin/session/:sessionId?userId=xxx
```

Returns messages for a specific session with usage metadata.

## Data Flow

1. **LLM Call Initiation**: When a user sends a message, the system starts timing the request
2. **Gemini API Call**: The system calls the Gemini API with the configured generation parameters
3. **Response Processing**: The system extracts usage information from the API response
4. **Unified Storage**: Message and usage metadata are saved together in a single database operation
5. **Error Handling**: If saving fails, the chat response is still returned to the user

## Generation Configuration

The current generation configuration used for all LLM calls:

```javascript
{
  maxOutputTokens: 1000,
  temperature: 0.7
}
```

## Admin Dashboard Integration

The usage metadata is fully integrated into the admin dashboard:

### Messages & Analytics Tab

- **Usage Statistics Cards**: High-level performance metrics
  - Total API calls
  - Average latency with color coding
  - Total tokens consumed
  - Unique users
- **Message List**: Each message displays:
  - Response latency with performance indicators
  - Token usage breakdown
  - Model information
  - Generation configuration (clickable)
  - Performance color coding

### Sessions Tab

- **Expandable Sessions**: Click any session to view its messages
- **Message Details**: Each message within a session shows:
  - User message and AI response
  - Performance metrics
  - Token usage
  - Model and configuration info

### Performance Indicators

- **Latency Color Coding**:
  - 🟢 Green: < 1 second (fast)
  - 🟡 Yellow: 1-3 seconds (moderate)
  - 🔴 Red: > 3 seconds (slow)

## Monitoring and Analytics

The usage metadata can be used for:

- **Performance Monitoring**: Track response times and identify slow requests
- **Cost Analysis**: Monitor token usage for billing purposes
- **User Analytics**: Understand usage patterns across different users
- **System Optimization**: Identify opportunities to improve response times
- **Capacity Planning**: Plan for scaling based on usage trends
- **Model Performance**: Compare different models and configurations
- **Session Analysis**: Understand conversation patterns and complexity

## Error Handling

- If metadata saving fails, the chat functionality continues to work
- All errors are logged for debugging purposes
- The system gracefully handles missing token information from the API
- Database migrations are automatic and handle existing data

## Security

- Usage metadata is only accessible through admin endpoints
- Admin access is protected by Firebase authentication
- Only authorized phone numbers can access admin features
- All API calls require valid Firebase ID tokens

## Database Migration

The system automatically handles database migrations to add usage metadata columns to existing messages tables. No manual intervention is required.

### Migration Process

1. **Schema Check**: System checks for existing usage metadata columns
2. **Column Addition**: Missing columns are automatically added
3. **Data Preservation**: All existing data is preserved
4. **Backward Compatibility**: Old messages continue to work without metadata

## Example Usage Data

```json
{
  "id": 123,
  "user_id": "user123",
  "user_message": "buy 100 shares of ITC",
  "gemini_response": "[{\"action\":\"Buy\",\"arguments\":{\"symbol\":\"ITC\"}}]",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "session_id": "session_456",
  "latency_ms": 1250,
  "generation_config": "{\"maxOutputTokens\":1000,\"temperature\":0.7}",
  "model_name": "gemini-2.0-flash",
  "input_tokens": 150,
  "output_tokens": 200,
  "total_tokens": 350
}
```

## Benefits of Merged Structure

### Performance

- **Faster Queries**: No JOINs between separate tables
- **Atomic Operations**: Message and metadata saved together
- **Reduced Complexity**: Single table operations

### Data Integrity

- **No Orphaned Records**: Metadata always exists with messages
- **Consistent State**: No risk of partial saves
- **Simplified Backups**: Single table to backup

### Maintenance

- **Easier Schema Changes**: One table to modify
- **Simplified Queries**: Direct access to all data
- **Better Indexing**: Single table indexes

## Related Documentation

- `README.md` - Main server documentation
- `ADMIN_README.md` - Admin dashboard setup and usage
