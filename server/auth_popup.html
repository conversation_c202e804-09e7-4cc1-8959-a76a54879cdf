<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Smart FinAgent</title>
    <!-- Load Tailwind CSS from CDN for the external page -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
    </style>
</head>
<body class="bg-[#f6f7fa] min-h-screen flex items-center justify-center">
    <div id="auth-container" class="bg-white rounded-2xl shadow-xl w-full max-w-md p-8 flex flex-col items-center">
        <!-- Logo/Icon -->
        <div class="mb-4 flex items-center">
            <div class="bg-violet-200 rounded-xl p-3 mb-2 mr-4">
                <!-- Placeholder for logo/icon -->
                <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect width="24" height="24" rx="8" fill="#8B5CF6"/>
                  <path d="M12 6v6l4 2" stroke="#fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </div>
            <span class="text-xl font-bold text-gray-900">Smart FinAgent</span>
        </div>

        <!-- Phone Input Form -->
        <form id="phone-form" class="w-full flex flex-col gap-4">
          <h2 class="text-2xl font-bold mb-2 text-center text-gray-900">Your AI Stock Assistant,<br/>Ready to Guide You</h2>
          <p class="text-gray-600 text-center mb-2">Get personalized insights, real-time market updates, and smart trading support — all in one place.</p>
          <p class="font-semibold text-center mb-6">Let's get started! Please enter your phone number to begin.</p>
            <div>
            <label for="phone-number" class="text-sm font-medium text-gray-700 w-full mb-2">Phone number</label>
            <div class="flex w-full gap-2">
                <select id="country-code" class="rounded-md border border-gray-300 bg-gray-50 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="+91">IN</option>
                    <option value="+1">US</option>
                    <!-- Add more country codes as needed -->
                </select>
                <input
                    type="tel"
                    id="phone-number"
                    required
                    placeholder="9975846515"
                    class="flex-1 p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200"
                />
            </div>
          </div>
            <button
                id="send-otp-btn"
                type="button"
                class="w-full bg-violet-600 text-white p-3 rounded-md font-semibold hover:bg-violet-700 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-offset-2 transition-all duration-200 ease-in-out mt-2 disabled:opacity-50 flex items-center justify-center"
            >
                <span id="send-otp-btn-text">Continue</span>
                <svg id="loading-spinner" class="animate-spin ml-2 h-5 w-5 hidden" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path></svg>
            </button>
            <div id="recaptcha-container" class="w-full flex justify-center"></div>
        </form>

        <!-- OTP Verification Section (hidden by default) -->
        <div id="otp-section" class="w-full flex flex-col items-center mt-2 hidden">
            <h3 class="text-xl font-bold mb-2 text-center text-gray-900">OTP Verification</h3>
            <p class="text-gray-700 text-center mb-4">We've sent a code to <span id="otp-phone-display">+91 - 123456789</span></p>
            <label for="otp-input-1" class="text-sm font-medium text-gray-700 w-full mb-2">Verification code</label>
            <div class="flex justify-center gap-3 mb-2">
                <input id="otp-input-1" maxlength="1" class="otp-input w-14 h-14 text-2xl text-center border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-violet-500" type="text" />
                <input maxlength="1" class="otp-input w-14 h-14 text-2xl text-center border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-violet-500" type="text" />
                <input maxlength="1" class="otp-input w-14 h-14 text-2xl text-center border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-violet-500" type="text" />
                <input maxlength="1" class="otp-input w-14 h-14 text-2xl text-center border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-violet-500" type="text" />
                <input maxlength="1" class="otp-input w-14 h-14 text-2xl text-center border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-violet-500" type="text" />
                <input maxlength="1" class="otp-input w-14 h-14 text-2xl text-center border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-violet-500" type="text" />
            </div>
            <div id="resend-otp-container" class="hidden mb-4 text-sm text-gray-600">Didn't get a code? <a href="#" id="resend-otp-link" class="text-violet-600 hover:underline">Click to resend.</a></div>
            <button
                id="verify-otp-btn"
                class="w-full bg-violet-600 text-white p-3 rounded-lg font-semibold text-lg hover:bg-violet-700 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-offset-2 transition-all duration-200 ease-in-out mt-2 disabled:opacity-50 flex items-center justify-center"
            >
                <span id="verify-otp-btn-text">Verify</span>
                <svg id="verify-loading-spinner" class="animate-spin ml-2 h-5 w-5 hidden" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path></svg>
            </button>
        </div>

        <!-- Name Form Section (hidden by default) -->
        <div id="name-form-section" class="w-full flex flex-col items-center mt-2 hidden">
          <div class="w-full max-w-md flex flex-col items-center">
            <h2 class="text-2xl font-bold mb-2 text-center text-gray-900">Your stock journey starts now!</h2>
            <p class="text-gray-600 text-center mb-2">What's your full name so we can tailor your experience?</p>
            <form id="user-name-form" class="w-full flex flex-col mt-4">
              <label for="full-name" class="text-sm font-medium text-gray-700 w-full mb-2">Full Name</label>
              <input type="text" id="full-name" name="full-name" required placeholder="Eg. Jogn Doe" class="flex-1 p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 mb-4" />
              <button type="submit" id="submit-name-btn" class="w-full bg-violet-600 text-white p-3 rounded-md font-semibold hover:bg-violet-700 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-offset-2 transition-all duration-200 ease-in-out mt-2 disabled:opacity-50 flex items-center justify-center">Submit</button>
            </form>
          </div>
        </div>

        <p id="message-display" class="text-sm text-center mt-4 hidden"></p>

        <p id="agreement-clause" class="text-xs text-gray-500 text-center mt-8">By continuing, you agree to our <a href="#" class="text-violet-600 hover:underline">Terms & Conditions</a> and <a href="#" class="text-violet-600 hover:underline">Privacy Policy</a></p>
    </div>

    <!-- Load Firebase SDKs from CDN for the external page -->
    <script type="module" src="https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js"></script>
    <script type="module" src="https://www.gstatic.com/firebasejs/11.6.1/firebase-auth.js"></script>

    <!-- Your popup JavaScript -->
    <script type="module" src="auth_popup.js"></script>
</body>
</html>
