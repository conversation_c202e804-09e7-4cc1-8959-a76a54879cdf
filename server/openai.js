const OpenAI = require('openai');
const { loadPrompt } = require('./prompt-loader');
require('dotenv').config();

// Initialize OpenAI API
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

/**
 * Handle OpenAI API errors and return user-friendly messages
 * @param {Error} error - The error object from OpenAI API
 * @returns {string} - User-friendly error message
 */
function handleOpenAIError(error) {
  console.error('OpenAI API Error:', error);

  // Check for specific error types
  if (error.message && error.message.includes('rate_limit_exceeded')) {
    return "I'm currently experiencing high traffic. Please try again in a few moments.";
  }

  if (error.message && error.message.includes('quota_exceeded')) {
    return "I've reached my rate limit. Please wait a moment before trying again.";
  }

  if (error.message && error.message.includes('invalid_request_error')) {
    return "I couldn't process your request. Please try rephrasing your message.";
  }

  if (error.message && error.message.includes('authentication_error') || error.message.includes('invalid_api_key')) {
    return "Authentication error. Please check your API configuration.";
  }

  if (error.message && error.message.includes('timeout')) {
    return "Request timed out. Please try again.";
  }

  if (error.message && error.message.includes('network')) {
    return "Network error. Please check your internet connection and try again.";
  }

  // Default error message
  return "I'm having trouble processing your request right now. Please try again later.";
}

/**
 * Generate response from OpenAI API
 * @param {string} userMessage - The user's message
 * @param {Array} chatHistory - Previous messages for context
 * @param {string} modelName - Model name to use (optional)
 * @returns {Promise<Object>} - Object containing response and metadata
 */
async function generateResponse(userMessage, chatHistory = [], modelName = "gpt-4o-mini") {
  const startTime = Date.now();

  try {
    // Load the system prompt from file (will prefer .md over .txt)
    const systemPrompt = await loadPrompt('trading-assistant-prompt');

    // Build conversation history for context
    let messages = [
      {
        role: "system",
        content: systemPrompt
      }
    ];

    if (chatHistory.length > 0) {
      // Add recent conversation context
      chatHistory.forEach((msg, index) => {
        console.log(`Adding message ${index}: user_message="${msg.user_message}", llm_response type=${typeof msg.llm_response}`);
        messages.push({
          role: "user",
          content: msg.user_message
        });
        // Only add assistant message if llm_response exists
        if (msg.llm_response) {
          const assistantContent = typeof msg.llm_response === 'string' ? msg.llm_response : JSON.stringify(msg.llm_response);
          console.log(`Adding assistant message ${index}: content type=${typeof assistantContent}`);
          messages.push({
            role: "assistant",
            content: assistantContent
          });
        } else {
          console.log(`Skipping assistant message ${index}: llm_response is null/undefined`);
        }
      });
    }

    // Add the current user message
    messages.push({
      role: "user",
      content: userMessage
    });

    // Validate all messages have string content
    messages = messages.filter(msg => msg.content && typeof msg.content === 'string');
    console.log(`Final messages array length: ${messages.length}`);

    // Define generation config
    const generationConfig = {
      max_tokens: 1000,
      temperature: 0,
    };

    // Call OpenAI API
    const completion = await openai.chat.completions.create({
      model: modelName,
      messages: messages,
      max_tokens: generationConfig.max_tokens,
      temperature: generationConfig.temperature,
    });

    const text = completion.choices[0].message.content.replace("```json", "").replace("```", "").trim();

    // Calculate latency
    const endTime = Date.now();
    const latency = endTime - startTime;

    // Extract usage information
    const usage = completion.usage;
    const inputTokens = usage?.prompt_tokens || null;
    const outputTokens = usage?.completion_tokens || null;
    const totalTokens = usage?.total_tokens || null;

    console.log('OpenAI response generated successfully');

    return {
      response: text,
      metadata: {
        latency_ms: latency,
        generation_config: generationConfig,
        model_name: modelName,
        input_tokens: inputTokens,
        output_tokens: outputTokens,
        total_tokens: totalTokens
      }
    };

  } catch (error) {
    console.error('Error generating OpenAI response:', error);

    // Handle specific OpenAI API errors
    const userFriendlyMessage = handleOpenAIError(error);
    throw new Error(userFriendlyMessage);
  }
}

// ----- Send message and get response -----
async function generateResponseV2(userInput, chatHistory = [], modelName = "gpt-4o") {
  const startTime = Date.now();

  try {
    // Load the system prompt from file (will prefer .md over .txt)
    const systemPrompt = await loadPrompt('trading-assistant-prompt');

    // Build conversation history for context
    let messages = [
      {
        role: "system",
        content: systemPrompt
      }
    ];

    if (chatHistory.length > 0) {
      
      // Add recent conversation context
      chatHistory.forEach((msg, index) => {
        console.log(`Adding message ${index}: user_message="${msg.user_message}", llm_response type=${typeof msg.llm_response}`);
        if (!msg.user_message || typeof msg.user_message !== 'string' || msg.llm_response.primitives[0].action === "llmChat") {
          return;
        }
        messages.push({
          role: "user",
          content: msg.user_message
        });
        // Only add assistant message if llm_response exists
        if (msg.llm_response) {
          const assistantContent = typeof msg.llm_response === 'string' ? msg.llm_response : JSON.stringify(msg.llm_response);
          console.log(`Adding assistant message ${index}: content type=${typeof assistantContent}`);
          messages.push({
            role: "assistant",
            content: assistantContent
          });
        } else {
          console.log(`Skipping assistant message ${index}: llm_response is null/undefined`);
        }
      });
    }

    // Add the current user message
    messages.push({
      role: "user",
      content: userInput
    });

    // Validate all messages have string content
    messages = messages.filter(msg => msg.content && typeof msg.content === 'string');
    console.log(`Final messages array length: ${messages.length}`);

    const response = await openai.chat.completions.create({
      model: modelName,
      messages: messages,
      temperature: 0
    });

    const assistantMsg = response.choices[0].message.content;

    // Calculate latency
    const endTime = Date.now();
    const latency = endTime - startTime;

    // Extract usage information
    const usage = response.usage;
    const inputTokens = usage?.prompt_tokens || null;
    const outputTokens = usage?.completion_tokens || null;
    const totalTokens = usage?.total_tokens || null;

    const cleanedResponse = assistantMsg.replace("```json", "").replace("```", "").trim();

    return {
      response: cleanedResponse,
      metadata: {
        latency_ms: latency,
        generation_config: {
          model: modelName,
          temperature: 0
        },
        model_name: modelName,
        input_tokens: inputTokens,
        output_tokens: outputTokens,
        total_tokens: totalTokens
      }
    };

  } catch (error) {
    console.error('Error generating OpenAI V2 response:', error);
    const userFriendlyMessage = handleOpenAIError(error);
    throw new Error(userFriendlyMessage);
  }
}


module.exports = {
  generateResponse,
  generateResponseV2
}; 