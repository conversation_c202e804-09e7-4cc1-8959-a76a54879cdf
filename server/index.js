// This is server-side Node.js code, NOT for the browser.
// You need to install packages: npm install express firebase-admin cors sqlite3 @google/generative-ai dotenv

const express = require('express');
const admin = require('firebase-admin'); // Firebase Admin SDK
const cors = require('cors'); // For handling CORS from your auth popup
const path = require('path'); // For serving static files
const cookieParser = require('cookie-parser'); // For parsing cookies
require('dotenv').config(); // Load environment variables

// Import database and LLM modules
const { initializeDatabase, saveMessage, getChatHistory, getRecentSessionMessages, getSessions, getSessionMessages, createOrUpdateSession, generateSessionTitle, getUserById, createUser, getAllUsers, getAllSessions, getAllMessages, getDatabaseStats, getMessagesWithUsage, getUsageStats, getUserUsageMetadata, getUserPreferences, updateUserPreferences } = require('./database');
const { validateProviderEnvironment, getCurrentProviderModule, getProviderInfo, getAvailableModels, getAllAvailableModels, isModelAvailable, getModelInfo, ACTIVE_PROVIDER, getCurrentProviderConfig } = require('./llm-config');
const { AUTHORIZED_PHONES, ADMIN_SESSION_CONFIG, QUERY_LIMITS } = require('./config');
const { loadPrompt, renderPrompt } = require('./prompt-loader');

const app = express();
app.use(express.json()); // To parse JSON request bodies
app.use(cookieParser()); // To parse cookies

// Initialize database
let db;
initializeDatabase()
  .then(database => {
    db = database;
    console.log('Database initialized successfully');
  })
  .catch(err => {
    console.error('Failed to initialize database:', err);
    process.exit(1);
  });

// Initialize LLM provider
let generateResponse;
let generateResponseV2;
try {
  validateProviderEnvironment();
  const llmModule = require(getCurrentProviderModule());
  generateResponse = llmModule.generateResponse;
  generateResponseV2 = llmModule.generateResponseV2;
  const providerInfo = getProviderInfo();
  console.log(`LLM provider initialized: ${providerInfo.name} (${providerInfo.model})`);
} catch (error) {
  console.error('Failed to initialize LLM provider:', error.message);
  process.exit(1);
}

// IMPORTANT: Initialize Firebase Admin SDK with your service account key.
// Download your service account key JSON file from Firebase Console:
// Project settings > Service accounts > Generate new private key.
// Store this file securely on your server and NEVER commit it to public repositories.
// Replace 'path/to/your/serviceAccountKey.json' with the actual path.
const serviceAccount = require('./tradetalk-ad365-firebase-adminsdk-fbsvc-a4799ff62b.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

// --- Authentication Middleware ---
/**
 * Middleware to check if user is authenticated via Firebase ID token
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function checkAuthStatus(req, res, next) {
  try {
    // Check for ID token in cookies, headers, or query parameters
    let idToken = null;

    // Check Authorization header
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      idToken = authHeader.substring(7);
    }

    // Check for token in cookies
    if (!idToken && req.cookies && req.cookies.idToken) {
      idToken = req.cookies.idToken;
    }

    // Check for token in query parameters (for testing)
    if (!idToken && req.query.token) {
      idToken = req.query.token;
    }

    if (!idToken) {
      // No token found, user is not authenticated
      req.isAuthenticated = false;
      req.user = null;
      return next();
    }

    // Verify the ID token
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    req.isAuthenticated = true;
    req.user = decodedToken;
    next();
  } catch (error) {
    console.error('Token verification error:', error);
    // Token is invalid, user is not authenticated
    req.isAuthenticated = false;
    req.user = null;
    next();
  }
}

// Configure CORS for your hosted authentication popup page.
// Replace 'https://smartagent.pandeyanshuman.com' with the actual origin of your auth_popup.html if different.
// Make sure this is precise to avoid security vulnerabilities.
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    // Allow requests from the same origin (when serving sidepanel locally)
    if (origin === `http://localhost:${PORT}` || origin === `https://localhost:${PORT}`) {
      return callback(null, true);
    }

    // Allow requests from your hosted domain
    if (origin === 'https://smartagent.pandeyanshuman.com') {
      return callback(null, true);
    }

    // Allow other origins if needed (add your domains here)
    callback(null, true);
  },
  methods: ['GET', 'POST'],
  allowedHeaders: ['Content-Type', 'Authorization']
};
app.use(cors(corsOptions)); // Apply CORS middleware

// Serve the auth popup JavaScript file
app.get('/auth_popup.js', (req, res) => {
  res.sendFile(__dirname + '/auth_popup.js');
});

// Serve the auth popup HTML file
app.get('/auth', (req, res) => {
  res.sendFile(__dirname + '/auth_popup.html');
});

// --- Chat Sidepanel Routes ---
// Serve the main chat sidepanel HTML at / - let client-side handle auth
app.get('/', (req, res) => {
  // Always serve the sidepanel.html and let client-side JavaScript handle authentication
  console.log('Serving sidepanel.html - authentication will be handled client-side');
  res.sendFile(path.join(__dirname, '../extension/sidepanel.html'));
});

// Serve the sidepanel JavaScript
app.get('/sidepanel.js', (req, res) => {
  res.setHeader('Content-Type', 'application/javascript');
  res.sendFile(path.join(__dirname, '../extension/sidepanel.js'));
});

// Serve Firebase SDK files
app.get('/firebase-app.js', (req, res) => {
  res.setHeader('Content-Type', 'application/javascript');
  res.sendFile(path.join(__dirname, '../extension/firebase-app.js'));
});

app.get('/firebase-auth.js', (req, res) => {
  res.setHeader('Content-Type', 'application/javascript');
  res.sendFile(path.join(__dirname, '../extension/firebase-auth.js'));
});

// Serve Tailwind CSS
app.get('/tailwind.css', (req, res) => {
  res.setHeader('Content-Type', 'text/css');
  res.sendFile(path.join(__dirname, '../extension/tailwind.css'));
});

// Serve Tailwind JS
app.get('/tailwind.js', (req, res) => {
  res.setHeader('Content-Type', 'application/javascript');
  res.sendFile(path.join(__dirname, '../extension/tailwind.js'));
});

// Serve extension icons (optional, for favicon or other uses)
app.get('/icons/:iconName', (req, res) => {
  const iconName = req.params.iconName;
  res.sendFile(path.join(__dirname, '../extension/icons', iconName));
});

// --- Chat API Endpoints ---

// --- Helper Functions ---

/**
 * Validate chat message request parameters
 * @param {Object} req - Express request object
 * @returns {Object|null} - Error response object if validation fails, null if valid
 */
function validateChatRequest(req) {
  const { message, userId } = req.body;

  // Validate required fields
  if (!message || !userId) {
    return {
      status: 400,
      error: 'Message and userId are required'
    };
  }

  // Validate message length
  if (message.trim().length === 0) {
    return {
      status: 400,
      error: 'Message cannot be empty'
    };
  }

  if (message.length > 4000) {
    return {
      status: 400,
      error: 'Message is too long. Please keep it under 4000 characters.'
    };
  }

  return null; // Validation passed
}

/**
 * Handle chat endpoint errors consistently
 * @param {Error} error - The error object
 * @param {Object} res - Express response object
 */
function handleChatError(error, res) {
  console.error('Error processing chat message:', error);

  // Determine if this is a known error type
  let statusCode = 500;
  let errorMessage = 'An unexpected error occurred. Please try again.';

  if (error.message.includes('rate limit') || error.message.includes('high traffic')) {
    statusCode = 429;
    errorMessage = error.message;
  } else if (error.message.includes('timeout')) {
    statusCode = 408;
    errorMessage = error.message;
  } else if (error.message.includes('network')) {
    statusCode = 503;
    errorMessage = error.message;
  }

  res.status(statusCode).json({
    success: false,
    error: errorMessage,
    errorType: 'server_error',
    timestamp: new Date().toISOString()
  });
}

/**
 * Get the appropriate LLM function based on user preferences
 * @param {string} userId - User ID
 * @param {string} version - API version ('v1' or 'v2')
 * @returns {Promise<Object>} - Object with function and model info
 */
async function getLLMFunctionForUser(userId, version = 'v2') {
  try {
    // Get user preferences
    const preferences = await getUserPreferences(db, userId);
    const provider = preferences?.preferred_provider || 'gemini';
    const model = preferences?.preferred_model || 'gemini-2.0-flash';

    // Validate model is available
    if (!isModelAvailable(provider, model)) {
      console.warn(`Model ${model} not available for provider ${provider}, using default`);
      // Fall back to default provider
      const llmModule = require(getCurrentProviderModule());
      const defaultConfig = getCurrentProviderConfig();
      return {
        function: version === 'v2' ? llmModule.generateResponseV2 : llmModule.generateResponse,
        model: defaultConfig.model,
        provider: ACTIVE_PROVIDER
      };
    }

    // Load the appropriate provider module
    let llmModule;
    switch (provider) {
      case 'gemini':
        llmModule = require('./gemini.js');
        break;
      case 'openai':
        llmModule = require('./openai.js');
        break;
      default:
        // Fall back to default provider
        llmModule = require(getCurrentProviderModule());
    }

    return {
      function: version === 'v2' ? llmModule.generateResponseV2 : llmModule.generateResponse,
      model: model,
      provider: provider
    };
  } catch (error) {
    console.error('Error getting LLM function for user:', error);
    // Fall back to default provider
    const llmModule = require(getCurrentProviderModule());
    const defaultConfig = getCurrentProviderConfig();
    return {
      function: version === 'v2' ? llmModule.generateResponseV2 : llmModule.generateResponse,
      model: defaultConfig.model,
      provider: ACTIVE_PROVIDER
    };
  }
}

/**
 * Shared function to process chat messages with plan confirmation detection
 * @param {string} message - The user's message
 * @param {string} userId - The user's ID
 * @param {string} sessionId - The session ID
 * @param {Array} recentMessages - Recent chat history
 * @param {Function} generateFunction - The LLM generation function to use
 * @param {string} modelName - The model name to use
 * @returns {Promise<Object>} - The processed response
 */
async function processChatMessage(message, userId, sessionId, recentMessages, generateFunction, modelName) {
  // Check if this message is a response to a plan of action
  const planConfirmationInfo = detectPlanConfirmationFromHistory(recentMessages);

  if (planConfirmationInfo) {
    console.log('Detected plan confirmation response, routing to plan confirmation handler');

    // Externalize plan confirmation prompt
    const promptTemplate = await loadPrompt('plan-confirmation-prompt');
    const planConfirmationPrompt = renderPrompt(promptTemplate, {
      originalRequest: planConfirmationInfo.originalRequest,
      planDetails: planConfirmationInfo.planDetails,
      message: message
    });

    // Generate response from LLM using the specialized prompt
    let llmResponse;
    let usageMetadata;
    try {
      const result = await generateFunction(planConfirmationPrompt, [], modelName);
      llmResponse = result.response || result; // Handle both structured and unstructured responses
      usageMetadata = result.metadata || null;
    } catch (llmError) {
      console.error('LLM API error for plan confirmation:', llmError.message);
      throw new Error(llmError.message);
    }

    // Validate that LLM response is valid JSON
    let structured;
    try {
      structured = JSON.parse(llmResponse);

      // Validate the response structure
      if (!structured.intent || !['agree', 'disagree', 'modify'].includes(structured.intent)) {
        throw new Error('Invalid intent in response');
      }
    } catch (jsonError) {
      console.error('Invalid JSON response from LLM for plan confirmation:', jsonError.message);
      console.error('Raw response:', llmResponse);
      throw new Error('Invalid response format from AI service. Please try again.');
    }

    // Save message and response to database
    let messageId;
    let currentSessionId = sessionId;
    try {
      const saveResult = await saveMessage(db, userId, message, llmResponse, sessionId, usageMetadata);
      messageId = saveResult.messageId;
      currentSessionId = saveResult.sessionId;
      console.log(`Plan confirmation processed successfully. Message ID: ${messageId}, Session ID: ${currentSessionId}`);
    } catch (dbError) {
      console.error('Failed to save plan confirmation to database:', dbError.message);
      messageId = null;
    }

    // Log the validated response
    console.log('\n📋 Plan Confirmation Result:');
    console.log(JSON.stringify(structured, null, 2));

    // Return the plan confirmation response
    return {
      success: true,
      messageId: messageId,
      sessionId: currentSessionId,
      responseType: 'plan_confirmation',
      intent: structured.intent,
      reasoning: structured.reasoning,
      timestamp: new Date().toISOString()
    };
  }

  // Normal chat flow - generate response from LLM
  let llmResponse;
  let usageMetadata;
  try {
    const result = await generateFunction(message, recentMessages, modelName);
    llmResponse = result.response || result; // Handle both structured and unstructured responses
    usageMetadata = result.metadata || null;
  } catch (llmError) {
    console.error('LLM API error:', llmError.message);
    throw new Error(llmError.message);
  }

  // Validate that LLM response is valid JSON
  try {
    JSON.parse(llmResponse);
  } catch (jsonError) {
    console.error('Invalid JSON response from LLM:', jsonError.message);
    console.error('Raw response:', llmResponse);
    throw new Error('Invalid response format from AI service. Please try again.');
  }

  // Save message and response to database
  let messageId;
  let currentSessionId = sessionId;
  try {
    const saveResult = await saveMessage(db, userId, message, llmResponse, sessionId, usageMetadata);
    messageId = saveResult.messageId;
    currentSessionId = saveResult.sessionId;
    console.log(`Chat message processed successfully. Message ID: ${messageId}, Session ID: ${currentSessionId}`);
  } catch (dbError) {
    console.error('Failed to save message to database:', dbError.message);
    // Still return the response even if saving fails
    messageId = null;
  }

  // Log the validated JSON response
  const structured = JSON.parse(llmResponse);
  console.log('\n📦 Structured Output:');
  console.log(JSON.stringify(structured, null, 2));

  // Return the response with parsed JSON
  return {
    success: true,
    messageId: messageId,
    sessionId: currentSessionId,
    response: structured, // Send parsed JSON object instead of string
    timestamp: new Date().toISOString()
  };
}

/**
 * Detect if the last message in chat history was a plan of action
 * @param {Array} recentMessages - Array of recent messages from the session
 * @returns {Object|null} - Object with plan details and original request, or null if not a plan confirmation
 */
function detectPlanConfirmationFromHistory(recentMessages) {
  if (!recentMessages || recentMessages.length < 2) {
    return null;
  }

  // Get the last two messages (user message and LLM response)
  const lastMessage = recentMessages[recentMessages.length - 1];
  if( !lastMessage || !lastMessage.llm_response || lastMessage.llm_response.primitives[0].action === "llmChat") {
    return null; // No LLM response to check
  }
  const secondLastMessage = recentMessages[recentMessages.length - 2];

  // Check if the last message is from LLM and contains explanations (plan of action)
  if (lastMessage.llm_response && secondLastMessage.user_message) {
    try {
      const llmResponse = lastMessage.llm_response.primitives;
      let hasNeedMoreInfo = false;
      let explanations = [];

      // Helper function to check if an item needs more information
      const checkNeedMoreInfo = (item) => {
        if (item && typeof item === 'object') {
          // Check if need_more_info array exists and is not empty
          if (item.need_more_info && Array.isArray(item.need_more_info) && item.need_more_info.length > 0) {
            hasNeedMoreInfo = true;
          }

          // Always collect explanations if available
          if (item.human_friendly_explanation && item.human_friendly_explanation.trim() !== '') {
            explanations.push(item.human_friendly_explanation);
          }
        }
      };

      // Check if the response contains explanations (plan of action)
      if (llmResponse.explanations && Array.isArray(llmResponse.explanations) && llmResponse.explanations.length > 0) {
        // Check if there are any need_more_info arrays in the response
        if (llmResponse.need_more_info && Array.isArray(llmResponse.need_more_info) && llmResponse.need_more_info.length > 0) {
          hasNeedMoreInfo = true;
        }

        // If no need_more_info found, treat as plan of action
        if (!hasNeedMoreInfo && llmResponse.action !== 'llmChat') {
          return {
            planDetails: llmResponse.explanations.join('\n'),
            originalRequest: secondLastMessage.user_message
          };
        }
      }

      // Also check if the response itself is an array with explanations
      if (Array.isArray(llmResponse)) {
        llmResponse.forEach(checkNeedMoreInfo);

        // If no need_more_info found and we have explanations, treat as plan of action
        if (!hasNeedMoreInfo && explanations.length > 0) {
          return {
            planDetails: explanations.join('\n'),
            originalRequest: secondLastMessage.user_message
          };
        }
      }

      // Check if response has a response field that's an array
      if (llmResponse.response && Array.isArray(llmResponse.response)) {
        llmResponse.response.forEach(checkNeedMoreInfo);

        // If no need_more_info found and we have explanations, treat as plan of action
        if (!hasNeedMoreInfo && explanations.length > 0) {
          return {
            planDetails: explanations.join('\n'),
            originalRequest: secondLastMessage.user_message
          };
        }
      }

      // Check if response has a data field that might contain explanations
      if (llmResponse.data && Array.isArray(llmResponse.data)) {
        llmResponse.data.forEach(checkNeedMoreInfo);

        // If no need_more_info found and we have explanations, treat as plan of action
        if (!hasNeedMoreInfo && explanations.length > 0) {
          return {
            planDetails: explanations.join('\n'),
            originalRequest: secondLastMessage.user_message
          };
        }
      }

    } catch (error) {
      // If parsing fails, it's not a JSON response, so no plan confirmation
      console.debug('Failed to parse LLM response for plan detection:', error.message);
    }
  }

  return null;
}

// --- Model Selection API Endpoints ---

// Get available models
app.get('/api/models', checkAuthStatus, async (req, res) => {
  try {
    const models = getAllAvailableModels();
    res.json({
      success: true,
      models: models
    });
  } catch (error) {
    console.error('Error fetching available models:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch available models'
    });
  }
});

// Get user's current model preference
app.get('/api/user/model-preference', checkAuthStatus, async (req, res) => {
  try {
    if (!req.isAuthenticated) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const userId = req.user.uid;
    const preferences = await getUserPreferences(db, userId);

    // Default preferences if none set
    const defaultPreferences = {
      preferred_provider: 'gemini',
      preferred_model: 'gemini-2.0-flash'
    };

    res.json({
      success: true,
      preferences: preferences || defaultPreferences
    });
  } catch (error) {
    console.error('Error fetching user model preference:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch user model preference'
    });
  }
});

// Update user's model preference
app.post('/api/user/model-preference', checkAuthStatus, async (req, res) => {
  try {
    if (!req.isAuthenticated) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const userId = req.user.uid;
    const { preferred_provider, preferred_model } = req.body;

    // Validate input
    if (!preferred_provider || !preferred_model) {
      return res.status(400).json({
        success: false,
        error: 'Provider and model are required'
      });
    }

    // Validate model is available for provider
    if (!isModelAvailable(preferred_provider, preferred_model)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid model for the specified provider'
      });
    }

    // Update preferences
    const updatedPreferences = await updateUserPreferences(db, userId, {
      preferred_provider,
      preferred_model
    });

    res.json({
      success: true,
      preferences: updatedPreferences
    });
  } catch (error) {
    console.error('Error updating user model preference:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update user model preference'
    });
  }
});

// Send chat message and get LLM response
app.post('/api/chat', checkAuthStatus, async (req, res) => {
  try {
    // Validate request parameters
    const validationError = validateChatRequest(req);
    if (validationError) {
      return res.status(validationError.status).json({
        error: validationError.error
      });
    }

    const { message, userId, sessionId } = req.body;
    console.log(`Processing chat message from user ${userId}: ${message}`);

    // Get recent chat history for context from the current session
    let recentMessages = [];
    try {
      if (sessionId) {
        recentMessages = await getRecentSessionMessages(db, sessionId, userId, 5);
      }
    } catch (dbError) {
      console.warn('Failed to load chat history, proceeding without context:', dbError.message);
      // Continue without chat history if database fails
    }

    // Get the appropriate LLM function for this user
    const llmConfig = await getLLMFunctionForUser(userId, 'v1');

    // Process chat message with plan confirmation detection
    const response = await processChatMessage(message, userId, sessionId, recentMessages, llmConfig.function, llmConfig.model);

    res.json(response);

  } catch (error) {
    handleChatError(error, res);
  }
});

// Send chat message and get LLM response
app.post('/api/chat/v2', checkAuthStatus, async (req, res) => {
  try {
    // Validate request parameters
    const validationError = validateChatRequest(req);
    if (validationError) {
      return res.status(validationError.status).json({
        error: validationError.error
      });
    }

    const { message, userId, sessionId } = req.body;
    console.log(`Processing chat message from user ${userId}: ${message}`);

    // Get recent chat history for context from the current session
    let recentMessages = [];
    try {
      if (sessionId) {
        recentMessages = await getRecentSessionMessages(db, sessionId, userId, 5);
      }
    } catch (dbError) {
      console.warn('Failed to load chat history, proceeding without context:', dbError.message);
      // Continue without chat history if database fails
    }

    // Get the appropriate LLM function for this user
    const llmConfig = await getLLMFunctionForUser(userId, 'v2');

    // Process chat message with plan confirmation detection
    const response = await processChatMessage(message, userId, sessionId, recentMessages, llmConfig.function, llmConfig.model);

    res.json(response);

  } catch (error) {
    handleChatError(error, res);
  }
});

// Get chat history for a user
app.get('/api/chat/history/:userId', checkAuthStatus, async (req, res) => {
  try {
    const { userId } = req.params;
    const limit = parseInt(req.query.limit) || 10;

    if (!userId) {
      return res.status(400).json({ error: 'userId is required' });
    }

    const history = await getChatHistory(db, userId, limit);

    res.json({
      success: true,
      history: history,
      count: history.length
    });

  } catch (error) {
    console.error('Error fetching chat history:', error);
    res.status(500).json({
      error: 'Failed to fetch chat history',
      details: error.message
    });
  }
});

// Get sessions list for a user
app.get('/api/chat/sessions/:userId', checkAuthStatus, async (req, res) => {
  try {
    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({ error: 'userId is required' });
    }

    const sessions = await getSessions(db, userId);

    res.json({
      success: true,
      sessions: sessions
    });

  } catch (error) {
    console.error('Error fetching sessions:', error);
    res.status(500).json({
      error: 'Failed to fetch sessions',
      details: error.message
    });
  }
});

// Get messages for a specific session
app.get('/api/chat/session/:sessionId', checkAuthStatus, async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { userId } = req.query;

    if (!sessionId || !userId) {
      return res.status(400).json({ error: 'sessionId and userId are required' });
    }

    const messages = await getSessionMessages(db, sessionId, userId);

    res.json({
      success: true,
      messages: messages
    });

  } catch (error) {
    console.error('Error fetching session messages:', error);
    res.status(500).json({
      error: 'Failed to fetch session messages',
      details: error.message
    });
  }
});

// Endpoint to exchange Firebase ID Token for a Firebase Custom Token
app.post('/api/exchange-token', async (req, res) => {
  const idToken = req.body.idToken;

  if (!idToken) {
    return res.status(400).json({ error: 'ID token is required.' });
  }

  try {
    // 1. Verify the ID token received from the client.
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    const uid = decodedToken.uid;

    // 2. Mint a custom token for the same UID.
    const customToken = await admin.auth().createCustomToken(uid);

    // 3. Return the custom token to the client.
    return res.status(200).json({ customToken: customToken });

  } catch (error) {
    console.error('Error exchanging ID token for custom token:', error);
    if (error.code === 'auth/id-token-expired') {
      return res.status(401).json({ error: 'ID Token expired. Please re-authenticate.' });
    }
    if (error.code === 'auth/argument-error') {
      return res.status(400).json({ error: 'Invalid ID Token provided.' });
    }
    return res.status(500).json({ error: 'Internal server error during token exchange.' });
  }
});

// --- User API Endpoints ---
// Check if user exists
app.get('/api/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    if (!userId) return res.status(400).json({ error: 'userId is required' });
    const user = await getUserById(db, userId);
    if (user) {
      res.json({ exists: true, user });
    } else {
      res.json({ exists: false });
    }
  } catch (error) {
    console.error('Error checking user:', error);
    res.status(500).json({ error: 'Failed to check user', details: error.message });
  }
});

// Create new user
app.post('/api/user', async (req, res) => {
  try {
    const { userId, name } = req.body;
    if (!userId || !name) return res.status(400).json({ error: 'userId and name are required' });
    const user = await createUser(db, userId, name);
    res.json({ success: true, user });
  } catch (error) {
    if (error && error.message && error.message.includes('UNIQUE constraint failed')) {
      return res.status(409).json({ error: 'User already exists' });
    }
    console.error('Error creating user:', error);
    res.status(500).json({ error: 'Failed to create user', details: error.message });
  }
});

// --- Phone Number Validation for Admin Access ---
// Authorized phone numbers are now imported from config.js

/**
 * Middleware to validate Firebase user for admin access
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function validateFirebaseAdminUser(req, res, next) {
  try {
    // Check for ID token in Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: 'Authorization header required'
      });
    }

    const idToken = authHeader.substring(7);

    // Verify the ID token
    const decodedToken = await admin.auth().verifyIdToken(idToken);

    // Check if user's phone number is in the authorized list
    const userPhoneNumber = decodedToken.phone_number;

    if (!userPhoneNumber) {
      return res.status(403).json({
        success: false,
        error: 'Phone number not available for this account'
      });
    }

    // Normalize phone number (remove spaces, dashes, etc.)
    const normalizedPhone = userPhoneNumber.replace(/[\s\-\(\)]/g, '');

    if (AUTHORIZED_PHONES.includes(normalizedPhone)) {
      // Fetch user's name from database
      let userName = null;
      try {
        const user = await getUserById(db, decodedToken.uid);
        if (user && user.name) {
          userName = user.name;
        }
      } catch (error) {
        console.error('Error fetching user name:', error);
        // Continue without user name if there's an error
      }

      // Store user info in request for later use
      req.adminUser = {
        uid: decodedToken.uid,
        email: decodedToken.email,
        phoneNumber: normalizedPhone,
        displayName: userName || decodedToken.name || null
      };
      next();
    } else {
      res.status(403).json({
        success: false,
        error: 'Access denied. Your phone number is not authorized.'
      });
    }
  } catch (error) {
    console.error('Firebase token verification error:', error);
    res.status(401).json({
      success: false,
      error: 'Invalid or expired token'
    });
  }
}

/**
 * Middleware to check admin authentication using Firebase
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function checkFirebaseAdminAuth(req, res, next) {
  try {
    // Check for ID token in Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: 'Authorization header required'
      });
    }

    const idToken = authHeader.substring(7);

    // Verify the ID token
    const decodedToken = await admin.auth().verifyIdToken(idToken);

    // Check if user's phone number is in the authorized list
    const userPhoneNumber = decodedToken.phone_number;

    if (!userPhoneNumber) {
      return res.status(403).json({
        success: false,
        error: 'Phone number not available for this account'
      });
    }

    // Normalize phone number (remove spaces, dashes, etc.)
    const normalizedPhone = userPhoneNumber.replace(/[\s\-\(\)]/g, '');

    if (AUTHORIZED_PHONES.includes(normalizedPhone)) {
      // Fetch user's name from database
      let userName = null;
      try {
        const user = await getUserById(db, decodedToken.uid);
        if (user && user.name) {
          userName = user.name;
        }
      } catch (error) {
        console.error('Error fetching user name:', error);
        // Continue without user name if there's an error
      }

      // Store user info in request for later use
      req.adminUser = {
        uid: decodedToken.uid,
        email: decodedToken.email,
        phoneNumber: normalizedPhone,
        displayName: userName || decodedToken.name || null
      };
      next();
    } else {
      res.status(403).json({
        success: false,
        error: 'Access denied. Your phone number is not authorized.'
      });
    }
  } catch (error) {
    console.error('Firebase token verification error:', error);
    res.status(401).json({
      success: false,
      error: 'Invalid or expired token'
    });
  }
}

// --- Admin Routes ---
// Serve admin dashboard
app.get('/admin', (req, res) => {
  res.sendFile(__dirname + '/admin.html');
});

// Firebase user validation endpoint
app.post('/api/admin/validate-firebase-user', validateFirebaseAdminUser, (req, res) => {
  res.json({
    success: true,
    message: 'User validated successfully',
    user: req.adminUser
  });
});

// Admin API endpoints (protected by Firebase admin authentication)
app.get('/api/admin/stats', checkFirebaseAdminAuth, async (req, res) => {
  try {
    const stats = await getDatabaseStats(db);
    res.json({
      success: true,
      stats: stats
    });
  } catch (error) {
    console.error('Error fetching admin stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch database statistics'
    });
  }
});

app.get('/api/admin/users', checkFirebaseAdminAuth, async (req, res) => {
  try {
    const users = await getAllUsers(db);
    res.json({
      success: true,
      users: users
    });
  } catch (error) {
    console.error('Error fetching admin users:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch users'
    });
  }
});

app.get('/api/admin/sessions', checkFirebaseAdminAuth, async (req, res) => {
  try {
    const sessions = await getAllSessions(db);
    res.json({
      success: true,
      sessions: sessions
    });
  } catch (error) {
    console.error('Error fetching admin sessions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch sessions'
    });
  }
});

app.get('/api/admin/messages', checkFirebaseAdminAuth, async (req, res) => {
  try {
    const limit = Math.min(
      parseInt(req.query.limit) || QUERY_LIMITS.defaultMessages,
      QUERY_LIMITS.maxMessages
    );
    const messages = await getAllMessages(db, limit);
    res.json({
      success: true,
      messages: messages
    });
  } catch (error) {
    console.error('Error fetching admin messages:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch messages'
    });
  }
});

// Get all usage metadata
app.get('/api/admin/usage', checkFirebaseAdminAuth, async (req, res) => {
  try {
    const limit = Math.min(
      parseInt(req.query.limit) || QUERY_LIMITS.defaultMessages,
      QUERY_LIMITS.maxMessages
    );
    const usageData = await getMessagesWithUsage(db, limit);
    res.json({
      success: true,
      usage: usageData
    });
  } catch (error) {
    console.error('Error fetching usage metadata:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch usage metadata'
    });
  }
});

// Get usage statistics
app.get('/api/admin/usage/stats', checkFirebaseAdminAuth, async (req, res) => {
  try {
    const stats = await getUsageStats(db);
    res.json({
      success: true,
      stats: stats
    });
  } catch (error) {
    console.error('Error fetching usage stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch usage statistics'
    });
  }
});

// Get usage metadata for a specific user
app.get('/api/admin/usage/user/:userId', checkFirebaseAdminAuth, async (req, res) => {
  try {
    const { userId } = req.params;
    const limit = parseInt(req.query.limit) || 50;

    if (!userId) {
      return res.status(400).json({ error: 'userId is required' });
    }

    const usageData = await getUserUsageMetadata(db, userId, limit);
    res.json({
      success: true,
      usage: usageData
    });
  } catch (error) {
    console.error('Error fetching user usage metadata:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch user usage metadata'
    });
  }
});

// Get messages for a specific session (admin endpoint)
app.get('/api/admin/session/:sessionId', checkFirebaseAdminAuth, async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { userId } = req.query;

    if (!sessionId || !userId) {
      return res.status(400).json({ error: 'sessionId and userId are required' });
    }

    const messages = await getSessionMessages(db, sessionId, userId);
    res.json({
      success: true,
      messages: messages
    });

  } catch (error) {
    console.error('Error fetching admin session messages:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch session messages'
    });
  }
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Chat sidepanel available at: http://localhost:${PORT}`);
  console.log(`Chat API available at: http://localhost:${PORT}/api/chat`);
});