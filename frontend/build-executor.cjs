#!/usr/bin/env node

const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

// Version management functions
function incrementVersion(version) {
  const parts = version.split('.');
  // Ensure we have at least 3 parts (major.minor.patch)
  while (parts.length < 3) {
    parts.push('0');
  }
  // Increment the patch version (third part)
  parts[2] = (parseInt(parts[2]) + 1).toString();
  return parts.join('.');
}

function updateManifestVersion(manifestPath, newVersion) {
  const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
  manifest.version = newVersion;
  fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));
  return manifest.version;
}

function createZipFile(executorPath, version) {
  const zipName = `aagman-smartagent-${version}.zip`;
  const zipPath = path.join(executorPath, '..', zipName);
  
  console.log(`Creating zip file: ${zipName}`);
  
  // Check if zip command is available
  try {
    execSync('which zip', { stdio: 'pipe' });
  } catch (error) {
    console.error("❌ 'zip' command not found. Please install zip utility:");
    console.error("   macOS: brew install zip");
    console.error("   Ubuntu/Debian: sudo apt-get install zip");
    console.error("   CentOS/RHEL: sudo yum install zip");
    return null;
  }
  
  // Remove existing zip if it exists
  if (fs.existsSync(zipPath)) {
    fs.unlinkSync(zipPath);
  }
  
  // Create zip file using system zip command
  try {
    execSync(`cd ${executorPath} && zip -r ../${zipName} . -x "*.git*" "*.DS_Store*" "node_modules/*" "*.zip"`, { 
      stdio: "inherit" 
    });
    console.log(`✅ Zip file created: ${zipPath}`);
    return zipPath;
  } catch (error) {
    console.error("❌ Failed to create zip file:", error.message);
    return null;
  }
}

// Load environment variables from .env.executor
function loadEnvFile(filePath) {
  const env = {};
  if (fs.existsSync(filePath)) {
    const content = fs.readFileSync(filePath, "utf8");
    content.split("\n").forEach((line) => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith("#")) {
        const [key, ...valueParts] = trimmedLine.split("=");
        if (key && valueParts.length > 0) {
          env[key.trim()] = valueParts.join("=").trim();
        }
      }
    });
  }
  return env;
}

// Ensure .env.executor exists by running setup-env.cjs
console.log("Ensuring .env.executor exists...");
try {
  execSync("node scripts/setup-env.cjs executor", { stdio: "inherit" });
} catch (error) {
  console.warn(
    "Warning: Could not run setup-env.cjs, continuing with existing env files"
  );
}

// Load executor environment
const executorEnv = loadEnvFile(".env.executor");

// Also check for development environment files
const devEnv = loadEnvFile(".env.development");
const localEnv = loadEnvFile(".env.dev");

// Determine if this is a development build
const isDevelopmentBuild =
  fs.existsSync(".env.development") || fs.existsSync(".env.dev");

// Set environment variables for the build process
Object.entries(executorEnv).forEach(([key, value]) => {
  process.env[key] = value;
});

console.log(
  "Building executor with environment variables:",
  Object.keys(executorEnv)
);
console.log("Environment values:", {
  VITE_WS_URL: process.env.VITE_WS_URL,
  VITE_WS_CHAT_ENDPOINT: process.env.VITE_WS_CHAT_ENDPOINT,
  VITE_API_BASE_URL: process.env.VITE_API_BASE_URL,
});
if (isDevelopmentBuild) {
  console.log("🔧 Development build detected - enabling auto-reload");
}

try {
  // Increment version in manifest.json
  const manifestPath = path.join(__dirname, '..', 'executor', 'manifest.json');
  const currentVersion = JSON.parse(fs.readFileSync(manifestPath, 'utf8')).version;
  const newVersion = incrementVersion(currentVersion);
  
  console.log(`📦 Incrementing version from ${currentVersion} to ${newVersion}`);
  updateManifestVersion(manifestPath, newVersion);

  // Run TypeScript compilation
  console.log("Running TypeScript compilation...");
  execSync("npx tsc --skipLibCheck --noEmit", { stdio: "inherit" });

  // Run Vite build
  console.log("Running Vite build...");
  execSync("npx vite build --mode executor", { stdio: "inherit" });

  // Create executor/ui directory and copy files
  console.log("Copying files to executor/ui...");
  execSync("mkdir -p ../executor/ui", { stdio: "inherit" });
  execSync("cp -r dist/* ../executor/ui/", { stdio: "inherit" });

  // Add development auto-reload functionality
  if (isDevelopmentBuild) {
    console.log("🔧 Adding auto-reload functionality...");

    // Create timestamp file
    const timestamp = Date.now().toString();
    fs.writeFileSync("../executor/ui/build-timestamp.txt", timestamp);

    // Copy auto-reload script
    if (fs.existsSync("scripts/dev-reload.js")) {
      fs.copyFileSync("scripts/dev-reload.js", "../executor/ui/dev-reload.js");
    }

    // Inject auto-reload script into HTML
    const htmlPath = "../executor/ui/index.html";
    if (fs.existsSync(htmlPath)) {
      let htmlContent = fs.readFileSync(htmlPath, "utf8");

      // Add auto-reload script before closing body tag
      const autoReloadScript = `  <script src="dev-reload.js"></script>\n</body>`;
      htmlContent = htmlContent.replace("</body>", autoReloadScript);

      fs.writeFileSync(htmlPath, htmlContent);
      console.log("✅ Auto-reload script injected into HTML");
    }
  }

  // Create zip file with version
  const executorPath = path.join(__dirname, '..', 'executor');
  const zipPath = createZipFile(executorPath, newVersion);

  console.log("✅ Executor build completed successfully!");
  console.log(`📦 Version: ${newVersion}`);
  if (zipPath) {
    console.log(`📦 Zip file: ${zipPath}`);
  }
} catch (error) {
  console.error("❌ Build failed:", error.message);
  process.exit(1);
}
