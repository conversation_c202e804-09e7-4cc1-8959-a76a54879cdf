= Status Keys for All Orders and Monitoring
:toc:
:icons: font
:sectnums:

== Summary

- Orders status key: `status` in {`pending`, `executed`, `cancelled`}
- Monitoring status key: `status` in {`pending`, `triggered`, `stopped`}

[NOTE]
====
The BUY/SELL badge on order cards is driven by `tradeType`/`type` and is separate from `status`.
====

== Data Flow Overview

. Backend/PouchDB document fields contain `status`.
. Frontend service maps documents to UI models, preserving `status`.
. UI components read `status` to render badges/text and to filter lists.

== Orders

=== Source Mapping

`frontend/src/services/ExecutionPouchDBSyncService.ts` maps order docs to the `Order` model, passing through `doc.status`:

[source,typescript]
----
const mappedOrders = result.docs.map((doc: any) => {
  const mappedOrder: Order = {
    id: doc.id,
    symbol: doc.symbol,
    type: doc.tradeType?.toLowerCase() === "buy" ? "buy" : "sell",
    quantity: parseInt(doc.quantity) || 0,
    price: doc.price,
    status: doc.status,            // <= uses `status`
    timestamp: doc.timestamp,
    orderType: doc.orderType,
    product: doc.product,
    broker: doc.broker || "zerodha",
  };
  return mappedOrder;
});
----

`Order` interface (expected `status` values):

[source,typescript]
----
export interface Order {
  id: string;
  symbol: string;
  type: "buy" | "sell";
  quantity: number;
  price: string;
  status: "pending" | "executed" | "cancelled";
  timestamp: string;
  orderType: string;
  product: string;
  broker: string;
}
----

=== UI Consumption

- `frontend/src/components/OrderCardEnhanced.tsx` renders the status badge based on `status`:

[source,typescript]
----
const statusConfig = {
  pending:   { text: "PENDING",   /* colors/icons */ },
  executed:  { text: "EXECUTED",  /* colors/icons */ },
  cancelled: { text: "CANCELLED", /* colors/icons */ },
};

const statusInfo = statusConfig[status];
----

- `frontend/src/pages/AllOrdersPage.tsx` filters by `order.status`:

[source,typescript]
----
const isOpenOrder = order.status === "pending";
const tabMatch = selectedTab === "open" ? isOpenOrder : order.status === "executed";
...
<OrderCardEnhanced status={order.status} ... />
----

== Monitoring

=== Source Mapping

`frontend/src/services/ExecutionPouchDBSyncService.ts` maps monitoring docs to the `MonitoringAlert` model, passing through `doc.status`:

[source,typescript]
----
const mappedAlerts = result.docs.map((doc: any) => {
  const mappedAlert: MonitoringAlert = {
    id: doc.id,
    description: doc.description,
    symbol: doc.symbol,
    triggerPrice: doc.triggerPrice,
    currentPrice: doc.currentPrice,
    progress: doc.progress,
    progressPercent: doc.progressPercent,
    status: doc.status,        // <= uses `status`
    orderType: doc.orderType,
    stopLoss: doc.stopLoss,
    product: doc.product,
  };
  return mappedAlert;
});
----

`MonitoringAlert` interface (expected `status` values):

[source,typescript]
----
export interface MonitoringAlert {
  id: string;
  description: string;
  symbol: string;
  triggerPrice: string;
  currentPrice: string;
  progress: string;
  progressPercent: number;
  status: "pending" | "triggered" | "stopped";
  orderType: string;
  stopLoss: string;
  product: string;
}
----

=== UI Consumption

- `frontend/src/components/MonitoringAlertCard.tsx` derives badge class/text from `status`:

[source,typescript]
----
const getBadgeClasses = (status: string) => {
  switch (status) {
    case "triggered": return "bg-green-50 text-green-600 border-green-200";
    case "stopped":   return "bg-red-50 text-red-600 border-red-200";
    default:           return "bg-[#f6eafc] text-[#a330e5] border-[#daacf5]";
  }
};

const getStatusText = (status: string) => {
  switch (status) {
    case "triggered": return "Triggered";
    case "stopped":   return "Stopped";
    default:           return `${alert.progressPercent}% to trigger reached`;
  }
};
----

== Quick Reference

- Orders: `status` → `pending` | `executed` | `cancelled`
- Monitoring: `status` → `pending` | `triggered` | `stopped` 