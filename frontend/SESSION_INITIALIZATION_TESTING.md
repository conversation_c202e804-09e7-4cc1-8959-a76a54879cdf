# Session Initialization Testing Guide

## Overview

This guide explains how to test the new session initialization system that handles user_id and conversation_id management across different brokers and feature types (chat, orders, monitoring).

## Session Management Architecture

### LocalStorage Format

```json
{
  "user_id": "uuid-string",
  "conversations": {
    "zerodha": {
      "chat": "zerodha-chat-uuid",
      "orders": "zerodha-orders-uuid",
      "monitoring": "zerodha-monitoring-uuid"
    },
    "groww": {
      "chat": "groww-chat-uuid"
    }
  }
}
```

### API Endpoint

- **Endpoint**: `POST /chatHistory`
- **Purpose**: Initialize session for a specific broker + feature type
- **Request Body**:
  ```json
  {
    "user_id": "uuid-string-or-null",
    "conversation_id": "uuid-string-or-null",
    "type": "chat" | "orders" | "monitoring",
    "brokerName": "zerodha" | "groww" | "upstox"
  }
  ```
- **Response**:
  ```json
  {
    "user_id": "uuid-string",
    "conversation_id": "uuid-string",
    "history": [
      {
        "id": "message-id",
        "timestamp": 1234567890,
        "data": {
          /* message data */
        }
      }
    ]
  }
  ```

## Testing Scenarios

### 1. Fresh User (No Session Data)

**Setup**: Clear localStorage completely

```javascript
localStorage.removeItem("orderGPT_session");
```

**Expected Behavior**:

1. Navigate to chat/orders/monitoring tab
2. System should make POST request to `/chatHistory` with:
   - `user_id`: null
   - `conversation_id`: null
   - `type`: corresponding to the tab
   - `brokerName`: "zerodha" (default)
3. Backend returns new user_id and conversation_id
4. localStorage gets populated with session data
5. Chat history (if any) gets loaded into the tab

**Test Commands**:

```javascript
// Clear session
localStorage.removeItem("orderGPT_session");

// Navigate to chat tab and check network requests
// Should see POST /chatHistory with null IDs
```

### 2. Existing User, New Feature Type

**Setup**: Set up user with only chat session

```javascript
localStorage.setItem(
  "orderGPT_session",
  JSON.stringify({
    user_id: "existing-user-id",
    conversations: {
      zerodha: {
        chat: "existing-chat-id",
      },
    },
  })
);
```

**Expected Behavior**:

1. Navigate to orders tab
2. System should make POST request with:
   - `user_id`: "existing-user-id"
   - `conversation_id`: null
   - `type`: "orders"
   - `brokerName`: "zerodha"
3. Backend returns new conversation_id for orders
4. localStorage gets updated with new orders conversation_id

### 3. Existing User, Existing Feature Type

**Setup**: Complete session data

```javascript
localStorage.setItem(
  "orderGPT_session",
  JSON.stringify({
    user_id: "existing-user-id",
    conversations: {
      zerodha: {
        chat: "existing-chat-id",
        orders: "existing-orders-id",
        monitoring: "existing-monitoring-id",
      },
    },
  })
);
```

**Expected Behavior**:

1. Navigate to any tab
2. No API call should be made (session exists)
3. WebSocket should use existing session IDs
4. Chat history should be loaded from previous session

### 4. Tab Navigation Flow

**Test Steps**:

1. Start with fresh session
2. Navigate to chat → should initialize chat session
3. Navigate to orders → should initialize orders session
4. Navigate to monitoring → should initialize monitoring session
5. Navigate back to chat → should use existing session (no API call)

## Testing Commands

### Clear Session Data

```javascript
// Clear all session data
localStorage.removeItem("orderGPT_session");

// Check current session status
import { checkSessionStatus } from "./src/utils/navigationManager";
console.log(checkSessionStatus());
```

### Manually Set Session Data

```javascript
// Set up partial session
localStorage.setItem(
  "orderGPT_session",
  JSON.stringify({
    user_id: "test-user-123",
    conversations: {
      zerodha: {
        chat: "test-chat-456",
      },
    },
  })
);
```

### Check Session Status

```javascript
// Check what sessions exist
import { checkSessionStatus } from "./src/utils/navigationManager";
console.log(checkSessionStatus());
// Output: { chat: true, orders: false, monitoring: false }
```

### Force Session Initialization

```javascript
// Force initialize session for specific tab
import { initializeTabSession } from "./src/utils/navigationManager";

// This will make API call if session doesn't exist
initializeTabSession("orders").then((result) => {
  console.log("Orders session result:", result);
});
```

## Network Monitoring

### Chrome DevTools

1. Open DevTools → Network tab
2. Navigate between tabs
3. Look for POST requests to `/chatHistory`
4. Check request/response payloads

### Expected Network Calls

- **Fresh user**: 3 API calls (chat, orders, monitoring)
- **Existing user, new tab**: 1 API call for the new tab
- **Existing user, existing tab**: 0 API calls

## Environment-Specific Testing

### Mock Mode (`NODE_ENV=development`)

- API calls return mock data
- Check console for "Mock: POST request" logs
- Mock response includes:
  ```json
  {
    "success": true,
    "user_id": "mock-user-id",
    "conversation_id": "mock-conversation-id",
    "history": []
  }
  ```

### Production Mode

- Real API calls to backend
- Check for proper error handling
- Verify session persistence across page reloads

## Error Scenarios

### 1. API Failure

**Test**: Network offline or API endpoint down
**Expected**: Error handling, fallback behavior

### 2. Invalid Session Data

**Test**: Corrupt localStorage data

```javascript
localStorage.setItem("orderGPT_session", "invalid-json");
```

**Expected**: Session reset, fresh initialization

### 3. Missing Session IDs

**Test**: Partial session data

```javascript
localStorage.setItem(
  "orderGPT_session",
  JSON.stringify({
    user_id: "test-user",
    conversations: {}, // Empty conversations
  })
);
```

**Expected**: API calls to initialize missing sessions

## WebSocket Integration Testing

### Session ID Usage

1. Check WebSocket messages include correct user_id and conversation_id
2. Verify conversation_id changes based on active tab
3. Test message routing to correct tab-specific history

### Message Format

```json
{
  "user_id": "session-user-id",
  "conversation_id": "tab-specific-conversation-id",
  "brokerName": "zerodha",
  "message": "user message",
  "typeOfMessage": "chat|orders|monitoring",
  "modelId": "default-model",
  "sender": "user"
}
```

## Browser Developer Tools

### Storage Inspector

1. Application tab → Local Storage
2. Check `orderGPT_session` key
3. Verify session data structure

### Console Commands

```javascript
// Check current session
console.log(JSON.parse(localStorage.getItem("orderGPT_session")));

// Clear session
localStorage.removeItem("orderGPT_session");

// Check WebSocket store state
import { useWebSocketStore } from "./src/stores/websocketStore";
console.log(useWebSocketStore.getState());
```

## Troubleshooting

### Common Issues

1. **Session not initializing**: Check network requests, API endpoint availability
2. **Wrong conversation_id**: Verify tab-specific session logic
3. **WebSocket not using session**: Check session initialization order
4. **localStorage corruption**: Clear and reinitialize

### Debug Commands

```javascript
// Debug session manager
import { getSessionData, hasValidSession } from "./src/utils/sessionManager";
console.log("Session data:", getSessionData());
console.log("Has valid chat session:", hasValidSession("zerodha", "chat"));

// Debug navigation manager
import { checkSessionStatus } from "./src/utils/navigationManager";
console.log("Session status:", checkSessionStatus());
```

## Success Criteria

✅ **Fresh user flow**: Sessions initialize correctly for each tab
✅ **Existing user flow**: No unnecessary API calls, sessions reused
✅ **Tab switching**: Correct session IDs used for each tab
✅ **WebSocket integration**: Messages use correct session IDs
✅ **Error handling**: Graceful fallback on failures
✅ **localStorage persistence**: Sessions survive page reloads
✅ **Network handling**: Proper behavior in offline/online states
