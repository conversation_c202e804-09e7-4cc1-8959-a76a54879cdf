# OrderGPT

OrderGPT is an AI‑based trading automation platform that automates order placement and real‑time monitoring of stocks across multiple brokers and financial sites (e.g., Moneycontrol). Toggle between mock and production data with ease, and navigate pages using our intuitive switch‑style UI.


---

## 🚀 Prerequisites

- Node.js ≥ 16.x
- npm ≥ 8.x

---

## 🔧 Installation

Clone the repository and install dependencies:

```bash
git clone https://github.com/kotilabs/smart-agent.git
cd smart-agent
npm install
```

---

## ⚙️ Configuration

Create a `.env` file in the project root with the following setting:

```dotenv
VITE_USE_MOCK=true   # Set to false to use production APIs
```

---

## 🏃‍♂️ Running the Application

- **Development Mode** (hot‑reload enabled):

  ```bash
  npm run dev
  ```

  Launches a local server (default: [http://localhost:3000](http://localhost:3000)) that respects `VITE_USE_MOCK`.

- **Production Preview**:

  ```bash
  npm run build
  npm run preview
  ```

  Builds optimized assets into `dist/` and serves them locally for verification.

---
