# OrderGPT

OrderGPT is an AI‑based trading automation platform that automates order placement and real‑time monitoring of stocks across multiple brokers and financial sites (e.g., Moneycontrol). Toggle between mock and production data with ease, and navigate pages using our intuitive switch‑style UI.

---

## 🚀 Prerequisites

- Node.js ≥ 16.x
- npm ≥ 8.x

---

## 🔧 Installation

Clone the repository and install dependencies:

```bash
git clone https://github.com/kotilabs/smart-agent.git
cd smart-agent/frontend
npm install
```

---

## ⚙️ Environment Configuration

This project uses environment-specific configuration files:

- **`.env.development`** - Mock data mode (uses local JSON files)
- **`.env.local`** - Local development with real API (localhost:3000)
- **`.env.production`** - Production environment (api.orderGPT.com)
- **`.env.executor`** - Browser extension mode (uses real API connections for extension)

### Environment Variables

Copy `.env.example` to your desired environment file and update the values:

```bash
# For local development
cp .env.example .env.local

# For production
cp .env.example .env.production
```

**Key Environment Variables:**

```dotenv
VITE_USE_MOCK=false                       # Enable mock mode for development
VITE_API_BASE_URL=http://localhost:8000   # Backend API base URL
VITE_WS_URL=ws://localhost:8000           # WebSocket URL for real-time features
VITE_NOTIFICATIONS_ENDPOINT=/api/notifications  # Notifications endpoint
VITE_PROFILE_ENDPOINT=/api/profile        # User profile endpoint
VITE_ORDERS_ENDPOINT=/api/v1/orders       # Trading orders endpoint
VITE_MONITORING_ENDPOINT=/api/v1/monitoring/instances # Monitoring alerts endpoint
VITE_CHAT_ENDPOINT=/api/v1/chatHistory    # Chat history endpoint
VITE_WS_CHAT_ENDPOINT=/api/v1/ws/chat     # WebSocket chat endpoint
VITE_HEALTH_ENDPOINT=/health              # Health check endpoint
```

> **📝 Note**: See `.env.example` for complete documentation and environment-specific examples.

> **🔧 Browser Extension Note**: The executor build uses real API connections pointing to the production server. Ensure proper CORS configuration on the server to allow browser extension connections.

---

## 🏃‍♂️ Running the Application

### Development Scripts

- **Default Development** (uses current environment):

  ```bash
  npm run dev
  ```

- **Mock Data Mode** (uses local JSON files):

  ```bash
  npm run dev:mock
  ```

- **Local Development** (connects to localhost:3000 API):

  ```bash
  npm run dev:local
  ```

- **Production Environment** (connects to production API):
  ```bash
  npm run dev:prod
  ```

### Build Scripts

- **Default Build** (production mode):

  ```bash
  npm run build
  ```

- **Single File Build** (creates one HTML file with inline JavaScript):

  ```bash
  npm run build:single
  ```

- **Separate Files Build** (creates separate HTML, JS, and CSS files):

  ```bash
  npm run build:separate
  ```

- **Executor Build** (builds separate files with real API connections for browser extension and copies to executor/ui directory):

  ```bash
  npm run build:executor
  ```
  
  > **📝 Note**: Uses a dedicated build script (`build-executor.cjs`) that loads environment variables from `.env.executor` without overwriting existing `.env.local` files.

- **Build for Mock Environment**:

  ```bash
  npm run build:mock
  ```

- **Build for Local Environment**:

  ```bash
  npm run build:local
  ```

- **Build for Production**:
  ```bash
  npm run build:prod
  ```

### Code Quality & Testing

- **Lint Code**:

  ```bash
  npm run lint
  ```

### Environment Management

- **Check Current Environment**:

  ```bash
  npm run env:status
  ```

- **Clean Environment File**:

  ```bash
  npm run env:clean
  ```

### Preview & Testing

- **Preview Build**:
  ```bash
  npm run preview
  ```

---
