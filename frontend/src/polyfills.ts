// Simple polyfills for PouchDB browser compatibility
// This provides the minimal polyfills needed for PouchDB to work

// Global object polyfill
import "global";

// Core Node.js modules
import { EventEmitter } from "events";
import util from "util";
import { <PERSON><PERSON><PERSON> } from "buffer";

// Simple inherits polyfill (what PouchDB actually needs)
const inherits = (ctor: any, superCtor: any) => {
  if (superCtor) {
    ctor.super_ = superCtor;
    ctor.prototype = Object.create(superCtor.prototype, {
      constructor: {
        value: ctor,
        enumerable: false,
        writable: true,
        configurable: true,
      },
    });
  }
};

// Simple process polyfill
const process = {
  env: {},
  browser: true,
  version: "",
  versions: {},
  nextTick: (fn: Function, ...args: any[]) => {
    setTimeout(() => fn(...args), 0);
  },
};

// Make these available globally for modules that expect them
(globalThis as any).EventEmitter = EventEmitter;
(globalThis as any).util = util;
(globalThis as any).inherits = inherits;
(globalThis as any).Buffer = Buffer;
(globalThis as any).process = process;

// Additional polyfills for PouchDB compatibility
if (typeof (globalThis as any).setImmediate === "undefined") {
  (globalThis as any).setImmediate = (fn: Function, ...args: any[]) => {
    return setTimeout(fn, 0, ...args);
  };
}

if (typeof (globalThis as any).clearImmediate === "undefined") {
  (globalThis as any).clearImmediate = (id: any) => {
    clearTimeout(id);
  };
}

// Console debug message
console.log("✅ Node.js polyfills loaded successfully");

export {};
