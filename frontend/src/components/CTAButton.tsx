import React from 'react';
import { cn } from '../utils/cn';

const CTAButton: React.FC<React.ButtonHTMLAttributes<HTMLButtonElement>> = ({ className, children, ...props }) => {
  return (
    <button
      className={cn(
        "bg-[#5c54fd] text-white font-medium rounded-lg px-4 py-3 flex items-center justify-center",
        "shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]",
        "border-2 border-[#7d76fd] ",
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
};

export default CTAButton;

/*
* Usage Example:
*
* import CTAButton from './CTAButton';
*
* const MyComponent = () => {
*   return (
*     <CTAButton onClick={() => alert('Button Clicked!')}>
*       Continue
*     </CTAButton>
*   );
* };
*
* Note: The colors and styles are based on the Figma design.
* You can further customize them by extending your Tailwind CSS theme.
*
* Design Variables:
* Text/Button/Primary/Default: #ffffff
* Background/Button/Primary/Default: #5c54fd
* Border/Button/Primary/Default: #7d76fd
*/ 