import React from "react";
import Input from "../Input";
import CTAButton from "../CTAButton";
import OTPScreen from "../OTPScreen";
import CountdownTimer from "../CountdownTimer";
import OrderGPTIcon from "../../assets/orderGPT.svg";
import LinkExternalIcon from "../../assets/link-external.svg";
import { brokers } from "../../data/brokers";

type LoginStep = "phone" | "otp" | "name" | "broker";

interface LoginStepProps {
  currentStep: LoginStep;
  isDialog?: boolean; // true for extension dialog, false for web card

  // Form data
  phoneNumber: string;
  otpValue: string;
  fullName: string;

  // Loading states
  isLoading: boolean;
  isResending: boolean;

  // Error states
  errorMessage: string;

  // Event handlers
  onPhoneNumberChange: (value: string) => void;
  onOtpChange: (value: string) => void;
  onNameChange: (value: string) => void;

  // Action handlers
  onContinue: () => void;
  onVerify: () => void;
  onSubmit: () => void;
  onBrokerSubmit: (brokerId: string) => void;
  onResend: () => void;
}

// brokers is imported from data/brokers.ts

export const renderLoginStep = (props: LoginStepProps): React.ReactNode => {
  const {
    currentStep,
    isDialog = false,
    phoneNumber,
    otpValue,
    fullName,
    isLoading,
    isResending,
    errorMessage,
    onPhoneNumberChange,
    onOtpChange,
    onNameChange,
    onContinue,
    onVerify,
    onSubmit,
    onBrokerSubmit,
    onResend,
  } = props;

  // Base container classes - different for dialog vs web
  const containerClass = isDialog
    ? "flex flex-col justify-start items-center gap-10" // Dialog version
    : "p-12 relative bg-white rounded-2xl outline outline-2 inline-flex flex-col justify-start items-center gap-8 overflow-hidden border-none outline-none"; // Web card version

  const contentWrapperClass = isDialog
    ? "" // No extra wrapper for dialog
    : "w-96 flex flex-col justify-start items-center gap-10"; // Web needs w-96 wrapper

  const renderStepContent = () => {
    switch (currentStep) {
      case "phone":
        return (
          <>
            <div className="self-stretch flex flex-col justify-center items-center gap-10 w-full">
              {/* Logo and Title */}
              <div className="inline-flex justify-center items-center gap-4">
                <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl shadow-[0px_1.3333333730697632px_2.6666667461395264px_0px_rgba(16,24,40,0.05)] shadow-[inset_0px_-2.6666667461395264px_0px_0px_rgba(16,24,40,0.05)] shadow-[inset_0px_0px_0px_1.3333333730697632px_rgba(16,24,40,0.18)] border-[2.67px] border-white/10 flex items-center justify-center">
                  <img src={OrderGPTIcon} alt="OrderGPT" className="w-7 h-7" />
                </div>
                <div className="text-2xl font-bold text-gray-900 font-['Inter'] leading-loose">
                  OrderGPT
                </div>
              </div>

              {/* Main Content */}
              <div className="self-stretch flex flex-col justify-center items-center gap-4">
                <h1 className="self-stretch text-center text-3xl font-bold text-gray-900 font-['Inter'] leading-9">
                  Your AI Stock Assistant, Ready to Guide You
                </h1>
                <p className="self-stretch text-center text-base text-gray-600 font-normal font-['Inter'] leading-normal">
                  Get personalized insights, real-time market updates, and smart
                  trading support — all in one place.
                </p>
                <p className="self-stretch text-center text-base text-gray-900 font-semibold font-['Inter'] leading-normal">
                  Let's get started! Please enter your phone number to begin.
                </p>
                <p className="self-stretch text-center text-sm text-gray-500 font-normal font-['Inter'] leading-normal">
                  Select your country and enter your phone number
                </p>
              </div>
            </div>

            {/* Form Section */}
            <div className="self-stretch rounded-xl flex flex-col justify-start items-center gap-6">
              {/* Phone Input */}
              <div className="self-stretch flex flex-col justify-start items-start gap-5">
                <div className="self-stretch flex flex-col justify-start items-start gap-2">
                  <div className="self-stretch flex flex-col justify-start items-start gap-2">
                    <div className="inline-flex justify-start items-start gap-0.5">
                      <div className="text-sm font-medium text-gray-600 font-['Inter'] leading-tight">
                        Phone number
                      </div>
                    </div>
                    <div className="w-full">
                      <Input
                        variant="phone"
                        value={phoneNumber}
                        onChange={(e) => onPhoneNumberChange(e.target.value)}
                        errorMessage={errorMessage}
                        placeholder="9975846515"
                        className="w-full"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Continue Button */}
              <div className="self-stretch">
                <CTAButton
                  onClick={onContinue}
                  disabled={
                    !phoneNumber.startsWith("+") ||
                    phoneNumber.replace(/\D/g, "").length < 10
                  }
                  loading={isLoading}
                  className="w-full"
                >
                  Continue
                </CTAButton>
              </div>
            </div>

            {/* Terms and Conditions */}
            <div className="self-stretch inline-flex justify-center items-baseline gap-1">
              <div className="flex-1 text-center">
                <span className="text-xs text-gray-600 font-normal font-['Inter'] leading-none">
                  By continuing, you agree to our{" "}
                </span>
                <span className="text-xs text-[#5c54fd] font-normal font-['Inter'] leading-none cursor-pointer hover:underline">
                  Terms & Conditions
                </span>
                <span className="text-xs text-gray-600 font-normal font-['Inter'] leading-none">
                  {" "}
                  and{" "}
                </span>
                <span className="text-xs text-[#5c54fd] font-normal font-['Inter'] leading-none cursor-pointer hover:underline">
                  Privacy Policy
                </span>
              </div>
            </div>
          </>
        );

      case "otp":
        return (
          <div className="flex flex-col justify-start items-center gap-10">
            <div className="self-stretch flex flex-col justify-start items-center gap-10">
              {/* Logo and Title */}
              <div className="inline-flex justify-start items-center gap-4">
                <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl shadow-[0px_1.3333333730697632px_2.6666667461395264px_0px_rgba(16,24,40,0.05)] shadow-[inset_0px_-2.6666667461395264px_0px_0px_rgba(16,24,40,0.05)] shadow-[inset_0px_0px_0px_1.3333333730697632px_rgba(16,24,40,0.18)] border-[2.67px] border-white/10 flex items-center justify-center">
                  <img src={OrderGPTIcon} alt="OrderGPT" className="w-7 h-7" />
                </div>
                <div className="text-2xl font-bold text-gray-900 font-['Inter'] leading-loose">
                  OrderGPT
                </div>
              </div>

              {/* Main Content */}
              <div className="self-stretch flex flex-col justify-start items-start gap-4">
                <h1 className="self-stretch text-center text-3xl font-bold text-gray-900 font-['Inter'] leading-9">
                  OTP Verification
                </h1>
                <p className="self-stretch text-center text-base text-gray-600 font-normal font-['Inter'] leading-normal">
                  We've sent a code to +91 - {phoneNumber}
                </p>
              </div>
            </div>

            {/* Form Section */}
            <div className="self-stretch rounded-xl flex flex-col justify-start items-center gap-6">
              {/* OTP Input */}
              <div className="self-stretch flex flex-col justify-start items-start gap-5">
                <div className="w-full">
                  <OTPScreen
                    title="Verification code"
                    otpLength={6}
                    value={otpValue}
                    onChange={onOtpChange}
                    errorMessage={errorMessage}
                    className="w-full"
                  />
                </div>
                <div className="self-stretch text-center text-sm font-semibold text-[#5c54fd] font-['Inter'] leading-tight">
                  <CountdownTimer
                    onResend={onResend}
                    isResending={isResending}
                  />
                </div>
              </div>

              {/* Verify Button */}
              <div className="self-stretch">
                <CTAButton
                  onClick={onVerify}
                  disabled={otpValue.length !== 6}
                  loading={isLoading}
                  className="w-full"
                >
                  Verify
                </CTAButton>
              </div>
            </div>
          </div>
        );

      case "name":
        return (
          <div className="flex flex-col justify-start items-center gap-10">
            <div className="self-stretch flex flex-col justify-start items-center gap-10">
              {/* Logo and Title */}
              <div className="inline-flex justify-start items-center gap-4">
                <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl shadow-[0px_1.3333333730697632px_2.6666667461395264px_0px_rgba(16,24,40,0.05)] shadow-[inset_0px_-2.6666667461395264px_0px_0px_rgba(16,24,40,0.05)] shadow-[inset_0px_0px_0px_1.3333333730697632px_rgba(16,24,40,0.18)] border-[2.67px] border-white/10 flex items-center justify-center">
                  <img src={OrderGPTIcon} alt="OrderGPT" className="w-7 h-7" />
                </div>
                <div className="text-2xl font-bold text-gray-900 font-['Inter'] leading-loose">
                  OrderGPT
                </div>
              </div>

              {/* Main Content */}
              <div className="self-stretch flex flex-col justify-start items-start gap-4">
                <h1 className="self-stretch text-center text-3xl font-bold text-gray-900 font-['Inter'] leading-9">
                  Your stock journey starts now!
                </h1>
                <p className="self-stretch text-center text-base text-gray-600 font-normal font-['Inter'] leading-normal">
                  What's your full name so we can tailor your experience?
                </p>
              </div>
            </div>

            {/* Form Section */}
            <div className="self-stretch rounded-xl flex flex-col justify-start items-center gap-6">
              {/* Name Input */}
              <div className="self-stretch flex flex-col justify-start items-start gap-5">
                <div className="self-stretch flex flex-col justify-start items-start gap-2">
                  <div className="self-stretch flex flex-col justify-start items-start gap-2">
                    <div className="inline-flex justify-start items-start gap-0.5">
                      <div className="text-sm font-medium text-gray-600 font-['Inter'] leading-tight">
                        Full Name
                      </div>
                    </div>
                    <div className="w-full">
                      <Input
                        variant="text"
                        value={fullName}
                        onChange={(e) => onNameChange(e.target.value)}
                        errorMessage={errorMessage}
                        placeholder="Eg. John Doe"
                        className="w-full"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Submit Button */}
              <div className="self-stretch">
                <CTAButton
                  onClick={onSubmit}
                  disabled={!fullName.trim()}
                  loading={isLoading}
                  className="w-full"
                >
                  {isDialog ? "Submit" : "Continue"}
                </CTAButton>
              </div>
            </div>
          </div>
        );

      case "broker":
        return (
          <div className="flex flex-col justify-start items-center gap-10">
            <div className="self-stretch flex flex-col justify-start items-center gap-10">
              {/* Logo and Title */}
              <div className="inline-flex justify-start items-center gap-4">
                <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl shadow-[0px_1.3333333730697632px_2.6666667461395264px_0px_rgba(16,24,40,0.05)] shadow-[inset_0px_-2.6666667461395264px_0px_0px_rgba(16,24,40,0.05)] shadow-[inset_0px_0px_0px_1.3333333730697632px_rgba(16,24,40,0.18)] border-[2.67px] border-white/10 flex items-center justify-center">
                  <img src={OrderGPTIcon} alt="OrderGPT" className="w-7 h-7" />
                </div>
                <div className="text-2xl font-bold text-gray-900 font-['Inter'] leading-loose">
                  OrderGPT
                </div>
              </div>

              {/* Main Content */}
              <div className="self-stretch flex flex-col justify-start items-start gap-4">
                <h1 className="self-stretch text-center text-3xl font-bold text-gray-900 font-['Inter'] leading-9">
                  OrderGPT is ready to help you!
                </h1>
                <p className="self-stretch text-center text-base text-gray-600 font-normal font-['Inter'] leading-normal">
                  Navigate to a trading platform of your choice (like Zerodha,
                  Groww, or TradingView).
                </p>
              </div>
            </div>

            {/* Broker Selection */}
            <div className="self-stretch rounded-lg flex flex-col justify-start items-center gap-4">
              {brokers.map((broker) => (
                <div
                  key={broker.id}
                  onClick={() => onBrokerSubmit && onBrokerSubmit(broker.id)}
                  className={`self-stretch p-4 bg-[#F4F6FA] rounded-2xl outline outline-1 outline-offset-[-1px] outline-Border-Neutral-Primary inline-flex justify-start items-center gap-4 cursor-pointer transition-colors hover:bg-gray-50 ${
                    isLoading
                      ? "opacity-50 cursor-not-allowed"
                      : "hover:bg-gray-50"
                  }`}
                >
                  <img
                    className="w-8 h-8 rounded-lg"
                    src={broker.icon}
                    alt={broker.name}
                  />
                  <div className="flex-1 justify-start text-Text-Neutral-Primary text-base font-medium font-['Inter'] leading-normal">
                    {broker.name}
                  </div>
                  <div className="w-6 h-6 relative overflow-hidden">
                    <img
                      src={LinkExternalIcon}
                      alt="Open in new tab"
                      className="w-4 h-4 absolute left-[3px] top-[3px]"
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={containerClass}>
      {isDialog ? (
        renderStepContent()
      ) : (
        <div className={contentWrapperClass}>{renderStepContent()}</div>
      )}
    </div>
  );
};

export type { LoginStep, LoginStepProps };
