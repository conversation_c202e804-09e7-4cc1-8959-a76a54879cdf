import React from "react";
import { cn } from "../utils/cn";
import Card from "./Card";
import { formatNumber } from "../utils/formatNumber";
// import Pill from "./Pill"; // Don't need Pill component as it's not used in this component

// Assuming these icons exist in your assets folder as per your instructions.
// If the paths are different, you may need to adjust them.
import LineChartUpIcon from "../assets/line-chart-up-04.svg";
import TargetIcon from "../assets/target-04.svg";

// Modular component for each row in the monitoring card
const MonitoringRow: React.FC<{
  icon: string;
  label: string;
  value: string;
  className?: string;
}> = ({ icon, label, value, className }) => (
  <div className={cn("flex w-full items-center justify-between", className)}>
    <div className="flex items-center gap-2">
      <img src={icon} alt={label} className="h-5 w-5" />
      <p className="text-sm text-gray-600">{label}</p>
    </div>
    <p className="font-medium text-gray-900">{value}</p>
  </div>
);

interface MonitoringCardProps {
  stockName: string;
  status: string;
  triggerPrice: string;
  currentPrice: string;
  className?: string;
}

const MonitoringCard: React.FC<MonitoringCardProps> = ({
  stockName,
  status,
  triggerPrice,
  currentPrice,
  className,
}) => {
  return (
    <Card
      className={cn(
        "w-full max-w-sm p-4 border-gray-100 outline-none",
        className
      )}
    >
      <div className="flex w-full flex-col gap-3">
        {/* Header */}
        <div className="flex w-full items-center justify-between">
          <h3 className="font-semibold text-gray-900">{stockName}</h3>
          <p className="text-xs text-gray-500">{status}</p>
        </div>

        {/* Rows */}
        <MonitoringRow
          icon={TargetIcon}
          label="Trigger Price"
          value={formatNumber(triggerPrice)}
        />
        <MonitoringRow
          icon={LineChartUpIcon}
          label="Current Price"
          value={formatNumber(currentPrice)}
        />
      </div>
    </Card>
  );
};

export default MonitoringCard;

/*
 * Usage Example:
 *
 * import MonitoringCard from './MonitoringCard';
 *
 * const MyComponent = () => {
 *   return (
 *     <MonitoringCard
 *       stockName="INFOSYS"
 *       status="0/100"
 *       triggerPrice="25,000"
 *       currentPrice="26,540"
 *     />
 *   );
 * };
 */
