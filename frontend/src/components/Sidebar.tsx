import React, { useState } from "react";
import { cn } from "../utils/cn";

// Icon imports from your assets folder
import ChevronRightDouble from "../assets/chevron-right-double.svg";
import MessageDotsCircle from "../assets/message-dots-circle.svg";
import BookClosed from "../assets/book-closed.svg";
import SpinningDots from "../assets/spinning-dots.svg";
import Bell01 from "../assets/bell-01.svg";
import Ellipse7 from "../assets/Ellipse 7.svg";

// Modular Notification Bell component
const NotificationBell = ({ count }: { count: number }) => (
  <div className="relative h-6 w-6">
    <img src={Bell01} alt="Notifications" className="h-full w-full" />
    {count > 0 && (
      <div className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-600 text-xs text-white">
        {count}
      </div>
    )}
  </div>
);

// Modular Monitoring Icon component
const MonitoringIcon = ({ count }: { count: number }) => (
  <div className="relative h-6 w-6">
    <img
      src={SpinningDots}
      alt="Monitoring"
      className="h-full w-full animate-spin"
      style={{ animationDuration: "3s" }}
    />
    {count > 0 && (
      <div className="absolute inset-0 flex items-center justify-center bg-clip-text text-transparent bg-gradient-to-b from-[#5c54fd] to-[#a330e5] text-sm font-semibold">
        {count}
      </div>
    )}
  </div>
);

const navItems = [
  {
    name: "Chat",
    icon: () => (
      <div className="h-6 w-6">
        <img src={MessageDotsCircle} alt="Chat" className="h-full w-full" />
      </div>
    ),
  },
  {
    name: "Orders",
    icon: () => (
      <div className="h-6 w-6">
        <img src={BookClosed} alt="Orders" className="h-full w-full" />
      </div>
    ),
  },
  { name: "Monitoring", icon: MonitoringIcon, count: 1 },
];

const Sidebar: React.FC<{ className?: string }> = ({ className }) => {
  const [activeTab, setActiveTab] = useState("Chat");

  return (
    <div
      className={cn(
        "flex h-screen w-24 flex-col justify-between border-r bg-gradient-to-b from-[#a330e50f] to-[#5c54fd0f] p-6",
        className
      )}
    >
      <div className="flex flex-col items-center gap-8">
        <button>
          <img src={ChevronRightDouble} alt="Collapse" className="h-8 w-8" />
        </button>
        <nav className="flex flex-col items-center gap-2">
          {navItems.map((item) => (
            <button
              key={item.name}
              onClick={() => setActiveTab(item.name)}
              className={cn(
                "rounded-lg p-4 transition-colors hover:bg-white/50",
                activeTab === item.name && "bg-white"
              )}
              // Add a border to the left if active, mimicking the figma highlight
              style={
                activeTab === item.name
                  ? { borderLeft: "4px solid #A330E5" }
                  : {}
              }
            >
              <item.icon count={item.count || 0} />
            </button>
          ))}
        </nav>
      </div>

      <div className="flex flex-col items-center gap-2">
        <button
          onClick={() => setActiveTab("Notifications")}
          className={cn(
            "rounded-lg p-4 transition-colors hover:bg-white/50",
            activeTab === "Notifications" && "bg-white"
          )}
          style={
            activeTab === "Notifications"
              ? { borderLeft: "4px solid #A330E5" }
              : {}
          }
        >
          <NotificationBell count={5} />
        </button>
        <div className="relative">
          <img src={Ellipse7} alt="User Profile" className="h-8 w-8" />
          <p className="absolute inset-0 flex items-center justify-center font-semibold text-white">
            S
          </p>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;

/*
 * Usage Example:
 *
 * import Sidebar from './Sidebar';
 *
 * const App = () => {
 *   return (
 *     <div className="flex">
 *       <Sidebar />
 *       <main className="flex-1 p-8">
 *         // Your main content here
 *       </main>
 *     </div>
 *   );
 * };
 */
