import React from "react";
import { useSidebarStore } from "../../stores/sidebarStore";
import { useAuthStore } from "../../stores/authStore";
import ProfileUser from "../ProfileUser";
import ProfileBroker from "../ProfileBroker";
import { openDialog } from "../../navigation/dialogNavigator";

// Import asset paths
import zerodhaLogo from "../../assets/zerodha.svg";
import growwLogo from "../../assets/groww.svg";
import upstoxLogo from "../../assets/upstox.svg";

const ProfileContent: React.FC = () => {
  const { profileData } = useSidebarStore();
  const { signOut } = useAuthStore();

  // Handle broker actions
  const handleViewDetails = (brokerId: string) => {
    console.log("View details for broker:", brokerId);
  };

  const handleConnect = (brokerId: string) => {
    console.log("Connect to broker:", brokerId);
  };

  const handleEditProfile = () => {
    openDialog("edit-profile");
  };

  const handleLogout = async () => {
    try {
      console.log("[Extension] 🚪 Starting logout process...");

      // Step 1: Clear web context auth state by opening a special logout tab
      console.log("[Extension] 🔄 Opening web tab to clear web auth state...");

      // Open logout web tab with special logout action
      const frontendUrl = import.meta.env.VITE_FRONTEND_URL;
      if (!frontendUrl || typeof frontendUrl !== "string" || frontendUrl.trim() === "") {
        console.error("[Extension] ❌ VITE_FRONTEND_URL is not defined or invalid. Cannot perform logout.");
        return;
      }
      const webLogoutUrl = `${frontendUrl}/?mode=extension&action=logout`;
      console.log(`[Extension] Opening logout tab at: ${webLogoutUrl}`);

      // Use Chrome Extension API to create logout tab
      const chromeAPI = (globalThis as any).chrome;
      if (chromeAPI && chromeAPI.tabs) {
        chromeAPI.tabs.create({ url: webLogoutUrl }, (tab: any) => {
          console.log(`[Extension] ✅ Logout tab created:`, tab.id);

          // Close the logout tab after a short delay (it will clear auth and close itself)
          setTimeout(() => {
            if (tab.id) {
              chromeAPI.tabs.remove(tab.id, () => {
                console.log(`[Extension] ✅ Logout tab closed`);
              });
            }
          }, 2000); // Give web tab time to clear auth state
        });
      } else {
        console.warn(
          "[Extension] ⚠️ Chrome tabs API not available, skipping web auth cleanup"
        );
      }

      // Step 2: Clear extension auth state
      console.log("[Extension] 🔄 Clearing extension auth state...");
      await signOut();

      // Step 3: Clear any additional extension-specific storage
      localStorage.removeItem("firebase_id_token");
      localStorage.removeItem("firebase_uid");
      localStorage.removeItem("sessionUserId");
      localStorage.removeItem("extension_login_completed");

      console.log("[Extension] ✅ Logout completed successfully");
      console.log("[Extension] 🔄 Extension will return to login screen");
    } catch (error) {
      console.error("[Extension] ❌ Logout failed:", error);
    }
  };

  // Map broker IDs to logo paths
  const getBrokerLogo = (brokerId: string) => {
    switch (brokerId) {
      case "zerodha":
        return zerodhaLogo;
      case "groww":
        return growwLogo;
      case "upstox":
        return upstoxLogo;
      default:
        return zerodhaLogo;
    }
  };

  if (!profileData) {
    return (
      <div className="flex-1 flex items-center justify-center bg-[#f4f6fa]">
        <div className="text-[#6d82a6] text-sm">Loading profile...</div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col bg-[#f4f6fa]">
      {/* Content Container */}
      <div className="flex-1 flex flex-col gap-6 p-4">
        {/* Page Title */}
        <h1 className="font-bold text-[18px] leading-[26px] text-[#181e29]">
          Profile
        </h1>

        {/* Profile Content */}
        <div className="flex flex-col gap-8">
          {/* User Profile Card */}
          <ProfileUser
            name={profileData.user.name}
            phone={profileData.user.phone}
            email={profileData.user.email}
            avatarLetter={profileData.user.avatar}
            onEdit={handleEditProfile}
          />

          {/* Broker Integration Section */}
          <div className="flex flex-col gap-2">
            <h2 className="font-bold text-[18px] leading-[26px] text-[#181e29]">
              Broker Integration
            </h2>

            <div className="flex flex-col gap-4">
              {profileData.brokers.map((broker) => (
                <ProfileBroker
                  key={broker.id}
                  brokerName={broker.name}
                  brokerLogo={getBrokerLogo(broker.id)}
                  isConnected={broker.isConnected}
                  description={broker.description}
                  onViewDetails={() => handleViewDetails(broker.id)}
                  onConnect={() => handleConnect(broker.id)}
                />
              ))}
            </div>
          </div>

          {/* Logout Button */}
          <button
            onClick={handleLogout}
            className="flex items-center justify-center gap-2 bg-white rounded-lg px-4 py-2 border border-[#f35a68] shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)] hover:bg-gray-50 transition-colors"
          >
            <div className="w-6 h-6">
              <svg
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="w-6 h-6 text-[#f03142]"
              >
                <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                <polyline points="16,17 21,12 16,7"></polyline>
                <line x1="21" y1="12" x2="9" y2="12"></line>
              </svg>
            </div>
            <span className="font-medium text-[14px] leading-[20px] text-[#f03142]">
              Logout
            </span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProfileContent;

/*
 * ProfileContent Component
 *
 * This component renders the profile page with:
 * - User profile card with avatar, name, contact info, and edit button
 * - Broker integration section with connected/not connected states
 * - Logout button
 *
 * Data Flow:
 * - Reads profile data from zustand store (populated by contentNavigator)
 * - Uses ProfileUser and ProfileBroker components
 * - Handles user actions (edit, connect, view details, logout)
 *
 * Design Variables (from Figma):
 * - Background: #f4f6fa
 * - Text Primary: #181e29
 * - Text Secondary: #43556e
 * - Text Tertiary: #6d82a6
 * - Button Error: #f03142
 * - Button Error Border: #f35a68
 * - Success: #1f8b4d, #e9f7ef, #a9dfbf
 * - Border: #dee4f0
 * - Typography: Inter font family
 */
