import React from "react";
import { useSidebarStore } from "../../stores/sidebarStore";
import NotificationCard from "../NotificationCard";
import EmptyState from "../EmptyState";

const NotificationsContent: React.FC = () => {
  const { notifications } = useSidebarStore();

  // Handle notification action
  const handleNotificationAction = (notificationId: string) => {
    console.log("Notification action clicked:", notificationId);
    // TODO: Implement notification action logic
  };

  return (
    <div className="flex-1 flex flex-col bg-[#f4f6fa]">
      {/* Header */}
      <div className="shrink-0 flex items-center justify-between p-4 pb-2">
        <h1 className="font-bold text-[18px] leading-[26px] text-[#5c54fd]">
          All Notifications
        </h1>
      </div>

      {/* Notifications List */}
      <div className="flex-1 pb-4 min-h-0">
        {notifications.length === 0 ? (
          <EmptyState type="notifications" className="h-full" />
        ) : (
          <div className="space-y-0">
            {notifications.map((notification) => (
              <NotificationCard
                key={notification.id}
                variant={notification.variant}
                title={notification.title}
                time={notification.time}
                description={notification.description}
                actionText={notification.actionText}
                isRead={notification.isRead}
                onActionClick={() => handleNotificationAction(notification.id)}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default NotificationsContent;
