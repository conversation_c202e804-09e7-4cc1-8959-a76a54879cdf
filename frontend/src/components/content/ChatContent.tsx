import React, { useState, useEffect } from "react";
import ModelDropdown from "../ModelDropdown";
import ChatInput from "../ChatInput";
import { useWebSocketStore } from "../../stores/websocketStore";
import EmptyState from "../EmptyState";
import ChatMessagesView from "../ChatMessagesView";
import { useNetworkStatus } from "../../hooks/useNetworkStatus";
import { useWebSocket } from "../../hooks/useWebSocket";
import { useSidebarStore } from "../../stores/sidebarStore";
import StarsIcon from "../../assets/stars.svg";
import MessagePlusCircle from "../../assets/message-plus-circle.svg";
import type { Model } from "../../types/chat";
import { cn } from "../../utils/cn";
import { handleNewChat } from "../../utils/navigationManager";

const ChatContent: React.FC = () => {
  const [isNewChatLoading, setIsNewChatLoading] = useState(false);
  const { isOnline } = useNetworkStatus();
  const { setActiveTab } = useSidebarStore();
  const {
    messages,
    queueLength,
    sendMessage,
    isConnected,
    connectionStatus,
    triggerAction,
    selectedModelId,
    setSelectedModel,
    isWaitingForResponse,
  } = useWebSocket("chat");

  // Initialize monitoring baseline at first mount if unset
  useEffect(() => {
    const { lastSeenMonitoringAt, setLastSeenMonitoringAt } =
      useSidebarStore.getState();
    if (!lastSeenMonitoringAt) {
      setLastSeenMonitoringAt(Date.now());
    }
  }, []);

  const models: Model[] = [
    {
      id: "mock-llm-v1",
      name: "mock-llm-v1",
      displayName: "Mock LLM v1",
      avatar: "https://via.placeholder.com/20x20/3b82f6/ffffff?text=M",
    },
    {
      id: "deepseek-r1",
      name: "deepseek-r1-distill-llama-70b",
      displayName: "deepseek-r1...",
      avatar: "https://via.placeholder.com/20x20/9333ea/ffffff?text=DS",
    },
    {
      id: "gpt-4",
      name: "gpt-4-turbo",
      displayName: "GPT-4 Turbo",
      avatar: "https://via.placeholder.com/20x20/10b981/ffffff?text=GPT",
    },
    {
      id: "claude-3",
      name: "claude-3-sonnet",
      displayName: "Claude 3 Sonnet",
      avatar: "https://via.placeholder.com/20x20/f59e0b/ffffff?text=C",
    },
  ];

  const selectedModel =
    models.find((model) => model.id === selectedModelId) || models[0];
  const [message, setMessage] = useState("");
  const { brokerLoginRequired, chatInputBlocked } = useWebSocketStore();

  const handleSendMessage = (msg: string) => {
    sendMessage(msg, selectedModel.id);
    setMessage("");
  };

  const handleModelChange = (model: Model) => {
    setSelectedModel(model.id);
  };

  const handleActionClick = (action: {
    description: string;
    type: "chat" | "orders" | "monitoring";
    message: string;
  }) => {
    // Switch to the tab specified by action.type
    setActiveTab(action.type);
    triggerAction(action);
  };

  const handleNewChatClick = async () => {
    if (isNewChatLoading) return; // Prevent multiple clicks

    setIsNewChatLoading(true);

    try {
      const result = await handleNewChat();

      if (result.success) {
        console.log("✅ New chat session created successfully");
        // Optional: Show success feedback to user
      } else {
        console.error("❌ Failed to create new chat session:", result.error);
        // Optional: Show error message to user
        alert(`Failed to start new chat: ${result.error}`);
      }
    } catch (error) {
      console.error("❌ Error creating new chat session:", error);
      alert("Failed to start new chat. Please try again.");
    } finally {
      setIsNewChatLoading(false);
    }
  };

  // Don't need to manage waiting state as it's being managed globally in the store

  // Show offline state when not connected to network
  if (!isOnline) {
    return (
      <div className="flex-1 flex flex-col bg-[#f4f6fa]">
        <EmptyState type="offline" className="h-full" />
      </div>
    );
  }

  // Show connection status
  const renderConnectionStatus = () => {
    if (!isConnected) {
      return (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-yellow-800">
              {connectionStatus === "connecting"
                ? "Connecting..."
                : "Disconnected"}
            </span>
            {queueLength > 0 && (
              <span className="text-sm text-yellow-800 ml-2">
                ({queueLength} message{queueLength > 1 ? "s" : ""} queued)
              </span>
            )}
          </div>
        </div>
      );
    }

    // Show queue status even when connected
    if (queueLength > 0) {
      return (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-blue-800">
              Processing {queueLength} queued message
              {queueLength > 1 ? "s" : ""}...
            </span>
          </div>
        </div>
      );
    }

    return null;
  };

  return (
    <div className="h-full flex flex-col p-4 bg-white">
      {/* Connection Status */}
      {renderConnectionStatus()}

      {/* Content Area */}
      <div className="flex-1 flex flex-col min-h-0">
        {messages.length === 0 ? (
          /* Empty State - Show welcome message */
          <div className="flex-1 flex flex-col gap-4 sm:gap-6 items-center justify-start overflow-x-hidden">
            <div className="flex flex-col gap-2 items-start justify-start py-10 sm:py-20 w-full max-w-sm sm:max-w-md md:max-w-lg relative">
              {/* Decorative Stars */}
              <div className="absolute top-2 pointer-events-none z-0">
                <img
                  src={StarsIcon}
                  alt="Decorative stars"
                  className="size-32 opacity-80"
                />
              </div>

              {/* Greeting */}
              <div className="w-full mt-16">
                <h1 className="font-['Inter:Bold',_sans-serif] font-bold text-[#181e29] text-2xl sm:text-3xl lg:text-4xl leading-tight text-left">
                  Hi,
                </h1>
              </div>

              {/* Main Message with Gradient */}
              <div className="w-full">
                <h2
                  className="font-['Inter:Bold',_sans-serif] font-bold text-2xl leading-tight text-left bg-clip-text text-transparent"
                  style={{
                    backgroundImage:
                      "linear-gradient(100.076deg, rgb(92, 84, 253) 9.8213%, rgb(163, 48, 229) 80.799%)",
                    textShadow: "rgba(16,24,40,0.05) 0px 1px 2px",
                    WebkitTextFillColor: "transparent",
                  }}
                >
                  I'm your AI trading co-pilot — place orders and track
                  positions.
                </h2>
              </div>
            </div>
          </div>
        ) : (
          /* Chat Messages View */
          <ChatMessagesView
            messages={messages}
            isWaitingForResponse={isWaitingForResponse}
            onActionClick={handleActionClick}
            className="flex-1 min-h-0"
          />
        )}
      </div>

      {/* Bottom Section: Model Dropdown and Input - Fixed at bottom */}
      <div className="flex flex-col-reverse gap-1.5 items-end justify-start w-full">
        {/* Model Dropdown */}
        <div className="order-3 flex justify-start w-full">
          <ModelDropdown
            className="hidden"
            selectedModel={selectedModel}
            onModelChange={handleModelChange}
          />

          <button
            onClick={handleNewChatClick}
            disabled={isNewChatLoading}
            className={cn(
              "p-2 transition-all duration-200 rounded-lg group relative",
              isNewChatLoading
                ? "bg-[#F4F6FA] cursor-not-allowed"
                : "bg-[#F4F6FA] hover:bg-white/50 hover:bg-gradient-to-r hover:from-[#A330E5]/10 hover:to-[#5C54FD]/10"
            )}
            title={isNewChatLoading ? "Starting new chat..." : "Start New Chat"}
          >
            {isNewChatLoading ? (
              <div className="h-6 w-6 flex items-center justify-center">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-[#A330E5] border-t-transparent"></div>
              </div>
            ) : (
              <img
                src={MessagePlusCircle}
                alt="Start New Chat"
                className="h-5 w-5 transition-transform group-hover:scale-110"
              />
            )}
          </button>
        </div>

        {/* Chat Input */}
        <div className="order-2 w-full">
          <ChatInput
            placeholder={
              brokerLoginRequired
                ? "Please log in to your broker to continue"
                : "Ask anything ..."
            }
            value={message}
            onChange={setMessage}
            onSend={handleSendMessage}
            disabled={!isConnected || brokerLoginRequired || chatInputBlocked}
            isWaitingForResponse={isWaitingForResponse}
            disabledReason={
              brokerLoginRequired
                ? "Please log in to continue chatting"
                : chatInputBlocked
                  ? "Order execution in progress"
                  : undefined
            }
          />
        </div>
      </div>
    </div>
  );
};

export default ChatContent;
