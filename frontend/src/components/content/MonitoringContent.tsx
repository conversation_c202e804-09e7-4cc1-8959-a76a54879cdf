import React, { useState } from "react";
import ChatInput from "../ChatInput";
import EmptyState from "../EmptyState";
import ChatMessagesView from "../ChatMessagesView";
import { useNetworkStatus } from "../../hooks/useNetworkStatus";
import { useWebSocket } from "../../hooks/useWebSocket";
import { useSidebarStore } from "../../stores/sidebarStore";
import { useWebSocketStore } from "../../stores/websocketStore";
import CheckCircleIcon from "../../assets/check-circle.svg";
import { openDialog } from "../../navigation/dialogNavigator";

const MonitoringContent: React.FC = () => {
  const { isOnline } = useNetworkStatus();
  const { setActiveTab } = useSidebarStore();
  const {
    messages,
    queueLength,
    sendMessage,
    isConnected,
    connectionStatus,
    triggerAction,
    isWaitingForResponse,
  } = useWebSocket("monitoring");

  const [message, setMessage] = useState("");
  const { chatInputBlocked } = useWebSocketStore();

  const handleSendMessage = (msg: string) => {
    sendMessage(msg);
    setMessage("");
  };

  const handleAllMonitoringClick = () => {
    openDialog("all-monitoring");
  };

  const handleActionClick = (action: {
    description: string;
    type: "chat" | "orders" | "monitoring";
    message: string;
  }) => {
    // Switch to the tab specified by action.type
    setActiveTab(action.type);
    triggerAction(action);
  };

  // Don't need to manage waiting state as it's being managed globally in the store

  // Show offline state when not connected to network
  if (!isOnline) {
    return (
      <div className="flex-1 flex flex-col bg-[#f4f6fa]">
        <EmptyState type="offline" className="h-full" />
      </div>
    );
  }

  // Show connection status
  const renderConnectionStatus = () => {
    if (!isConnected) {
      return (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-yellow-800">
              {connectionStatus === "connecting"
                ? "Connecting..."
                : "Disconnected"}
            </span>
            {queueLength > 0 && (
              <span className="text-sm text-yellow-800 ml-2">
                ({queueLength} message{queueLength > 1 ? "s" : ""} queued)
              </span>
            )}
          </div>
        </div>
      );
    }

    // Show queue status even when connected
    if (queueLength > 0) {
      return (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-blue-800">
              Processing {queueLength} queued message
              {queueLength > 1 ? "s" : ""}...
            </span>
          </div>
        </div>
      );
    }

    return null;
  };

  const features = [
    { text: "Track", icon: CheckCircleIcon },
    { text: "Edit", icon: CheckCircleIcon },
    { text: "Stop", icon: CheckCircleIcon },
  ];

  const exampleSuggestions = [
    "Stop loss is missing.",
    "Do something to your order.",
    "You are incurring heavy loss, want to exit?",
    "Doing good, want to repeat the order.",
  ];

  return (
    <div className="p-4 w-full h-full bg-[#F4F6FA] flex flex-col">
      {/* Header */}
      <div className="w-full flex items-center justify-between mb-4">
        <p className="text-xl font-bold">Monitoring</p>
        <button
          onClick={handleAllMonitoringClick}
          className="text-sm text-indigo-500 hover:text-indigo-700 transition-colors"
        >
          All Monitoring
        </button>
      </div>

      {/* Connection Status */}
      {renderConnectionStatus()}

      {/* Content Area */}
      <div className="flex-1 flex flex-col min-h-0">
        {messages.length === 0 ? (
          /* Welcome State - Show centered input with features */
          <div
            className="flex-1 flex flex-col gap-14 items-center justify-start px-0 py-8 min-h-0 transition-all duration-500 ease-in-out opacity-100"
            key="welcome-screen"
          >
            {/* Header Section */}
            <div className="flex flex-col gap-4 items-center justify-start w-full">
              <div className="flex flex-col gap-2 items-center justify-start w-full">
                {/* Title with Purple Gradient */}
                <h1
                  className="font-['Inter:Bold',_sans-serif] font-bold text-[20px] leading-[28px] text-center bg-clip-text text-transparent"
                  style={{
                    backgroundImage:
                      "linear-gradient(119.875deg, rgb(92, 84, 253) 2.4051%, rgb(163, 48, 229) 80.813%)",
                    textShadow: "rgba(16,24,40,0.05) 0px 1px 2px",
                  }}
                >
                  Welcome to Monitoring Chat
                </h1>

                {/* Subtitle */}
                <p className="font-['Inter:Medium',_sans-serif] font-medium text-[#181e29] text-[16px] leading-[24px] text-center max-w-sm sm:max-w-md md:max-w-lg">
                  This is where your pre-set stock conditions live. Think of it
                  like "AI in waiting."
                </p>
              </div>

              {/* Features Section */}
              <div className="flex flex-row gap-6 items-start justify-start">
                {features.map((feature, index) => (
                  <div
                    key={index}
                    className="flex flex-row gap-2 items-center justify-start"
                  >
                    <div className="w-[18px] h-[18px]">
                      <img
                        src={feature.icon}
                        alt="check"
                        className="w-full h-full"
                      />
                    </div>
                    <span className="font-['Inter:Regular',_sans-serif] font-normal text-[#181e29] text-[14px] leading-[20px]">
                      {feature.text}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Chat Input Section */}
            <div className="flex flex-col gap-4 items-start justify-start w-full max-w-sm sm:max-w-md md:max-w-lg">
              <ChatInput
                placeholder="Want to check or stop a monitored stock? Type here..."
                onSend={handleSendMessage}
                value={message}
                onChange={setMessage}
                className="w-full"
                disabled={!isConnected}
              />

              {/* Example Suggestions */}
              <div className="flex flex-col gap-2 items-center justify-start w-full">
                <div className="flex flex-row gap-2 items-center justify-center">
                  <span className="font-['Inter:Medium',_sans-serif] font-medium text-[#181e29] text-[12px] leading-[16px]">
                    Eg:
                  </span>
                  <div className="bg-gray-200 px-2 py-1 rounded-lg">
                    <span className="font-['Inter:Regular',_sans-serif] font-normal text-[#43556e] text-xs ">
                      {exampleSuggestions[0]}
                    </span>
                  </div>
                </div>

                {/* Additional suggestions */}
                {exampleSuggestions.slice(1).map((suggestion, index) => (
                  <div key={index} className="bg-gray-200 px-2 py-1 rounded-lg">
                    <span className="font-['Inter:Regular',_sans-serif] font-normal text-[#43556E] text-xs ">
                      {suggestion}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ) : (
          /* Chat View - Show messages with input at bottom */
          <div
            className="flex-1 flex flex-col transition-all duration-500 ease-in-out opacity-100"
            key="chat-view"
          >
            {/* Chat Messages View */}
            <ChatMessagesView
              messages={messages}
              isWaitingForResponse={isWaitingForResponse}
              onActionClick={handleActionClick}
              className="flex-1 min-h-0"
            />

            {/* Chat Input - Fixed at bottom when messages exist */}
            <div className="pt-4">
              <ChatInput
                placeholder="Want to check or stop a monitored stock? Type here..."
                onSend={handleSendMessage}
                value={message}
                onChange={setMessage}
                className="w-full"
                disabled={!isConnected || chatInputBlocked}
                disabledReason={
                  chatInputBlocked ? "Order execution in progress" : undefined
                }
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MonitoringContent;
