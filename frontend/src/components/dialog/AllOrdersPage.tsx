import React, { useState } from "react";
import { useDialogStore } from "../../stores/dialogStore";
import LoadingBars from "../LoadingBars";
import RadioButton from "../RadioButton";
import Dropdown from "../Dropdown";
import OrderCardEnhanced from "../OrderCardEnhanced";
import FilterPopover from "../FilterPopover";
import EmptyState from "../EmptyState";
import CalendarIcon from "../../assets/calendar.svg";
import ChevronDownIcon from "../../assets/chevron-down.svg";
import ZerodhaIcon from "../../assets/zerodha.svg";
import GrowwIcon from "../../assets/groww.svg";
import UpstoxIcon from "../../assets/upstox.svg";

const AllOrdersPage: React.FC = () => {
  const { orders, isLoading } = useDialogStore();
  const [selectedTab, setSelectedTab] = useState<string>("open");
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
  const [selectedOrderTypes, setSelectedOrderTypes] = useState<string[]>([]);
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>("last_month");
  const [selectedBroker, setSelectedBroker] = useState<string>("zerodha");
  const [isBrokerDropdownOpen, setIsBrokerDropdownOpen] = useState(false);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64 p-4">
        <LoadingBars />
      </div>
    );
  }

  // Filter orders based on selected criteria
  const filteredOrders = orders.filter((order) => {
    const isOpenOrder = order.status === "pending";
    const tabMatch =
      selectedTab === "open" ? isOpenOrder : order.status === "executed";
    const brokerMatch = true; // No broker property in dialog store, so show all

    // Status filter - if no statuses selected, show all
    const statusMatch =
      selectedStatuses.length === 0 || selectedStatuses.includes(order.status);

    // Order type filter - if no order types selected, show all
    const orderTypeMatch =
      selectedOrderTypes.length === 0 ||
      selectedOrderTypes.includes(order.type) ||
      selectedOrderTypes.includes(order.orderType);

    return tabMatch && brokerMatch && statusMatch && orderTypeMatch;
  });

  // Broker options with icons
  const brokerOptions = [
    { value: "zerodha", label: "Zerodha", icon: ZerodhaIcon },
    { value: "groww", label: "Groww", icon: GrowwIcon },
    { value: "upstox", label: "Upstox", icon: UpstoxIcon },
  ];

  // Radio button options
  const tabOptions = [
    { value: "open", label: "Open" },
    { value: "executed", label: "Executed" },
  ];

  // Dropdown options
  const dateRangeOptions = [
    { value: "today", label: "Today" },
    { value: "this_week", label: "This week" },
    { value: "last_month", label: "Last month" },
    { value: "last_3_months", label: "Last 3 Months" },
    { value: "last_6_months", label: "Last 6 Months" },
    { value: "2025", label: "2025" },
    { value: "2024", label: "2024" },
  ];

  const handleChatClick = (orderId: string) => {
    console.log(`Chat clicked for order ${orderId}`);
    // Implement chat navigation logic here
  };

  const handleApplyFilters = (statuses: string[], orderTypes: string[]) => {
    console.log("AllOrdersPage - Applying filters:", {
      statuses,
      orderTypes,
      totalOrders: orders.length,
    });
    setSelectedStatuses(statuses);
    setSelectedOrderTypes(orderTypes);
  };

  const handleBrokerSelect = (brokerValue: string) => {
    setSelectedBroker(brokerValue);
    setIsBrokerDropdownOpen(false);
  };

  const getCurrentBroker = () => {
    return brokerOptions.find((broker) => broker.value === selectedBroker);
  };

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Container for broker selection and main content */}
      <div className="flex flex-col h-full">
        {/* Empty State */}
        {filteredOrders.length === 0 && (
          <EmptyState type="orders" className="h-full" />
        )}

        {/* Broker Selection Component */}

        {filteredOrders.length > 0 && (
          <div className="bg-white relative border-b border-[#dee4f0]">
            <div className="flex items-center justify-between px-4 py-3">
              <div className="text-[#000000] text-base font-medium">
                Viewing orders for
              </div>
              <div className="relative">
                <button
                  className="bg-[#f4f6fa] border border-[#dee4f0] rounded-lg px-3 py-2 flex items-center gap-1 min-w-[139px]"
                  onClick={() => setIsBrokerDropdownOpen(!isBrokerDropdownOpen)}
                >
                  <img
                    src={getCurrentBroker()?.icon}
                    alt={getCurrentBroker()?.label}
                    className="w-5 h-5 rounded"
                  />
                  <div className="flex-1 px-0.5">
                    <span className="text-[#181e29] text-sm font-medium">
                      {getCurrentBroker()?.label}
                    </span>
                  </div>
                  <img
                    src={ChevronDownIcon}
                    alt="chevron down"
                    className={`w-5 h-5 transition-transform duration-200 ${
                      isBrokerDropdownOpen ? "rotate-180" : ""
                    }`}
                    style={{
                      filter:
                        "brightness(0) saturate(100%) invert(16%) sepia(73%) saturate(6341%) hue-rotate(357deg) brightness(97%) contrast(94%)",
                    }}
                  />
                </button>

                {/* Broker Dropdown */}
                {isBrokerDropdownOpen && (
                  <div className="absolute top-full left-0 mt-1 w-full bg-white border border-[#dee4f0] rounded-lg shadow-lg z-10">
                    {brokerOptions.map((broker) => (
                      <button
                        key={broker.value}
                        className={`w-full px-3 py-2 flex items-center gap-2 hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg ${
                          selectedBroker === broker.value ? "bg-[#f4f6fa]" : ""
                        }`}
                        onClick={() => handleBrokerSelect(broker.value)}
                      >
                        <img
                          src={broker.icon}
                          alt={broker.label}
                          className="w-5 h-5 rounded"
                        />
                        <span className="text-[#181e29] text-sm font-medium">
                          {broker.label}
                        </span>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Main Content */}
        {filteredOrders.length > 0 && (
          <div className="flex flex-col gap-4 p-4">
            {/* Radio button tabs */}
            <div className="w-full">
              <RadioButton
                options={tabOptions}
                value={selectedTab}
                onValueChange={setSelectedTab}
                className="w-full"
              />
            </div>

            {/* Controls row */}
            <div className="flex items-stretch justify-between gap-2">
              <FilterPopover
                selectedStatuses={selectedStatuses}
                selectedOrderTypes={selectedOrderTypes}
                onApplyFilters={handleApplyFilters}
                className="min-w-[100px]"
              />
              <Dropdown
                options={dateRangeOptions}
                value={selectedDateRange}
                onSelect={setSelectedDateRange}
                placeholder="Date Range"
                icon={CalendarIcon}
                className="min-w-[120px]"
              />
            </div>
          </div>
        )}

        {/* Orders list */}
        {filteredOrders.length > 0 && (
          <div className="flex-1 overflow-auto">
            {filteredOrders.length === 0 ? (
              <EmptyState type="orders" className="h-full" />
            ) : (
              <div className="px-4 pb-4">
                <div className="flex flex-col gap-4">
                  {filteredOrders.map((order) => (
                    <OrderCardEnhanced
                      key={order.id}
                      stockName={order.symbol}
                      value={order.price}
                      quantity={order.quantity.toString()}
                      time={new Date(order.timestamp).toLocaleTimeString()}
                      exchange="NSE"
                      orderType={order.orderType}
                      ltp={order.price}
                      tradeType={order.type === "buy" ? "BUY" : "SELL"}
                      status={order.status}
                      onChatClick={() => handleChatClick(order.id)}
                      className="w-full"
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default AllOrdersPage;
