import React, { useState, useEffect } from "react";
import { useSidebarStore } from "../../stores/sidebarStore";
import { useDialogStore } from "../../stores/dialogStore";
import Input from "../Input";
import VerifiableEmailInput from "../VerifiableEmailInput";
import OTPScreen from "../OTPScreen";
import CTAButton from "../CTAButton";
import { cn } from "../../utils/cn";

// Email validation regex
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Verification states
type VerificationState = "unverified" | "verifying" | "otp-sent" | "verified";

interface FormData {
  phoneNumber: string;
  fullName: string;
  email: string;
}

const EditProfilePage: React.FC = () => {
  const { profileData, setProfileData } = useSidebarStore();
  const { closeDialog } = useDialogStore();

  const [formData, setFormData] = useState<FormData>({
    phoneNumber: profileData?.user?.phone || "",
    fullName: profileData?.user?.name || "",
    email: profileData?.user?.email || "",
  });

  const [emailVerificationState, setEmailVerificationState] =
    useState<VerificationState>("unverified");
  const [verificationAction, setVerificationAction] = useState<
    "send-otp" | "verify-otp"
  >("send-otp");
  const [isValidEmail, setIsValidEmail] = useState(false);
  const [isValidPhone, setIsValidPhone] = useState(false);
  const [otpValue, setOtpValue] = useState("");
  const [otpError, setOtpError] = useState("");
  const [isSaving, setIsSaving] = useState(false);

  // Dummy API functions
  const sendEmailVerificationOTP = async (email: string) => {
    console.log("API Call: Sending OTP to email:", email);
    // Simulate API call delay
    return new Promise((resolve) => setTimeout(resolve, 2000));
  };

  const verifyEmailOTP = async (email: string, otp: string) => {
    console.log("API Call: Verifying OTP for email:", email, "OTP:", otp);
    // Simulate API call delay
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // Mock OTP verification - accept "123456" as correct
        if (otp === "123456") {
          resolve(true);
        } else {
          reject(new Error("Invalid OTP"));
        }
      }, 1500);
    });
  };

  // Check if email is valid and manage verification state when email changes
  useEffect(() => {
    const isValid = EMAIL_REGEX.test(formData.email);
    setIsValidEmail(isValid);

    // Only reset verification state if email becomes invalid or empty
    // Don't interfere with ongoing verification process
    if (!formData.email || !isValid) {
      setEmailVerificationState("unverified");
      setVerificationAction("send-otp");
      setOtpError(""); // Clear any OTP errors
    }
  }, [formData.email]);

  // Check if phone number is valid (9-10 digits)
  useEffect(() => {
    const phoneDigits = formData.phoneNumber.replace(/\D/g, ""); // Remove non-digits
    const isValid = phoneDigits.length >= 9 && phoneDigits.length <= 10;
    setIsValidPhone(isValid);
  }, [formData.phoneNumber]);

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleVerificationClick = async () => {
    if (!isValidEmail) return;

    if (verificationAction === "send-otp") {
      // Send OTP
      setEmailVerificationState("verifying");

      try {
        await sendEmailVerificationOTP(formData.email);
        setEmailVerificationState("otp-sent");
        setVerificationAction("verify-otp");
        setOtpValue(""); // Clear any previous OTP
        setOtpError(""); // Clear any previous errors
      } catch (error) {
        console.error("Failed to send OTP:", error);
        setEmailVerificationState("unverified");
      }
    } else if (verificationAction === "verify-otp") {
      // Verify OTP
      if (!otpValue) return;

      setEmailVerificationState("verifying");

      try {
        await verifyEmailOTP(formData.email, otpValue);
        setEmailVerificationState("verified");
        setOtpValue("");
        setOtpError(""); // Clear any previous errors
      } catch (error) {
        console.error("Failed to verify OTP:", error);
        setEmailVerificationState("otp-sent");
        setOtpError("Invalid verification code. Please try again.");
      }
    }
  };

  const handleOTPComplete = (otp: string) => {
    setOtpValue(otp);
    // Auto-enable verify button when OTP is complete
    if (otp.length === 6) {
      setVerificationAction("verify-otp");
    }
  };

  const handleOTPChange = (otp: string) => {
    setOtpValue(otp);
    // Clear error when user starts typing
    if (otpError && otp) {
      setOtpError("");
    }
  };

  const handleSave = async () => {
    if (!profileData) return;

    setIsSaving(true);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Update profile data
      const updatedProfileData = {
        ...profileData,
        user: {
          ...profileData.user,
          name: formData.fullName,
          email: formData.email,
          phone: formData.phoneNumber,
        },
      };

      setProfileData(updatedProfileData);
      console.log("Profile updated successfully:", updatedProfileData);
      closeDialog();
    } catch (error) {
      console.error("Failed to save profile:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    console.log("Cancelling profile edit");
    closeDialog();
  };

  if (!profileData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">No profile data available</div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto bg-[#f4f6fa]">
        <div className="p-4 space-y-6 pb-24">
          {/* Phone Number Input */}
          <div className="space-y-2 w-full">
            <Input
              variant="phone"
              label="Phone number"
              disabled={true}
              value={formData.phoneNumber}
              onChange={(e) => handleInputChange("phoneNumber", e.target.value)}
              placeholder="9975846515"
              className="w-full"
              inputClassName={cn(
                "w-full bg-gray-300",
                !isValidPhone && formData.phoneNumber && "border-red-500"
              )}
            />
            {!isValidPhone && formData.phoneNumber && (
              <p className="text-red-500 text-sm">
                Phone number must be 9-10 digits
              </p>
            )}
          </div>

          {/* Full Name Input */}
          <Input
            variant="text"
            label="Full Name"
            value={formData.fullName}
            onChange={(e) => handleInputChange("fullName", e.target.value)}
            placeholder="Enter your full name"
            className="w-full"
            inputClassName="w-full"
          />

          {/* Email Address Input with Verification */}
          <VerifiableEmailInput
            value={formData.email}
            onChange={(e) => handleInputChange("email", e.target.value)}
            isValidEmail={isValidEmail}
            verificationState={emailVerificationState}
            onVerifyClick={handleVerificationClick}
            otpValue={otpValue}
            errorMessage={
              !isValidEmail && formData.email
                ? "Please enter a valid email address"
                : ""
            }
          />

          {/* OTP Verification Section */}
          {emailVerificationState === "otp-sent" && (
            <div className="bg-white rounded-lg border border-[#dee4f0] p-3 space-y-4">
              <OTPScreen
                title="Verification code"
                otpLength={6}
                value={otpValue}
                onChange={handleOTPChange}
                onComplete={handleOTPComplete}
                errorMessage={otpError}
              />

              <div className="text-center text-sm">
                <span className="text-[#43556e]">Didn't get a code? </span>
                <button
                  className="text-[#5c54fd] font-semibold hover:underline"
                  onClick={async () => {
                    setVerificationAction("send-otp");
                    setOtpError(""); // Clear any errors when resending
                    await handleVerificationClick();
                  }}
                >
                  Click to resend
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Fixed Footer */}
      <div className="flex-shrink-0 bg-white border-t border-[#dee4f0] p-4">
        <div className="flex justify-end gap-4">
          <button
            onClick={handleCancel}
            disabled={isSaving}
            className="px-4 py-3 bg-white border border-[#7d76fd] text-[#5c54fd] font-medium rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
          >
            Cancel
          </button>

          <CTAButton
            onClick={handleSave}
            disabled={
              isSaving ||
              !formData.phoneNumber.trim() ||
              !isValidPhone ||
              !formData.fullName.trim() ||
              !formData.email.trim() ||
              emailVerificationState !== "verified"
            }
            className={cn(
              "px-4 py-3",
              (!formData.phoneNumber.trim() ||
                !isValidPhone ||
                !formData.fullName.trim() ||
                !formData.email.trim() ||
                emailVerificationState !== "verified") &&
                "opacity-50 cursor-not-allowed"
            )}
          >
            {isSaving ? "Saving..." : "Save"}
          </CTAButton>
        </div>
      </div>
    </div>
  );
};

export default EditProfilePage;
