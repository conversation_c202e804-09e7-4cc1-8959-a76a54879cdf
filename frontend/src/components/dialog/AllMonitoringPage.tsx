import React, { useEffect } from "react";
import { useDialogStore, type MonitoringAlert } from "../../stores/dialogStore";
import { executionPouchDBSyncService } from "../../services/ExecutionPouchDBSyncService";
import MonitoringAlertCard from "../MonitoringAlertCard";
import LoadingBars from "../LoadingBars";
import EmptyState from "../EmptyState";
import { useSidebarStore } from "../../stores/sidebarStore";

const AllMonitoringPage: React.FC = () => {
  const { monitoringAlerts, isLoading, setMonitoringAlerts, setLoading } =
    useDialogStore();
  const { setLastSeenMonitoringAt, setMonitoringAlertsCount } =
    useSidebarStore();

  // Load monitoring alerts from PouchDB and set up real-time updates
  useEffect(() => {
    // On open, set last seen to now and reset count to zero BEFORE loading
    const now = Date.now();
    setLastSeenMonitoringAt(now);
    setMonitoringAlertsCount(0);

    const loadMonitoringAlerts = async () => {
      console.log(
        "🚀 [AllMonitoringPage] ===== STARTING MONITORING LOADING ====="
      );
      try {
        setLoading(true);
        console.log("📊 [AllMonitoringPage] Setting loading state to true");
        console.log(
          "📊 [AllMonitoringPage] Loading monitoring alerts from PouchDB..."
        );

        const realAlerts =
          await executionPouchDBSyncService.getMonitoringAlerts();
        console.log(
          "📊 [AllMonitoringPage] Loaded alerts count:",
          realAlerts.length
        );
        console.log(
          "📊 [AllMonitoringPage] Loaded alerts details:",
          JSON.stringify(realAlerts, null, 2)
        );
        console.log(
          "📥 [AllMonitoringPage] Monitoring alerts array received:",
          JSON.parse(JSON.stringify(realAlerts))
        );

        console.log("📊 [AllMonitoringPage] Setting alerts in dialogStore...");
        setMonitoringAlerts(realAlerts);
        console.log("✅ [AllMonitoringPage] Alerts set in store successfully");

        // Set up real-time updates
        console.log(
          "👀 [AllMonitoringPage] Setting up real-time monitoring watcher..."
        );
        executionPouchDBSyncService.watchMonitoringUpdates((updatedAlerts) => {
          console.log(
            "🔄 [AllMonitoringPage] ===== REAL-TIME MONITORING UPDATE ====="
          );
          console.log(
            "🔄 [AllMonitoringPage] Updated alerts count:",
            updatedAlerts.length
          );
          console.log(
            "🔄 [AllMonitoringPage] Updated alerts:",
            JSON.stringify(updatedAlerts, null, 2)
          );
          console.log(
            "📤 [AllMonitoringPage] Updated monitoring alert objects:",
            JSON.parse(JSON.stringify(updatedAlerts))
          );
          console.log("🔄 [AllMonitoringPage] Updating store...");
          setMonitoringAlerts(updatedAlerts);
          console.log("✅ [AllMonitoringPage] Store updated with new alerts");

          // Recompute new count based on latest last seen timestamp
          try {
            const { lastSeenMonitoringAt } = useSidebarStore.getState();
            const baseline = lastSeenMonitoringAt ?? 0;
            const newCount = updatedAlerts.filter((a: any) => {
              const tsStr = a.updatedAt || a.createdAt || "";
              const ts = tsStr ? new Date(tsStr).getTime() : 0;
              return ts > baseline;
            }).length;
            setMonitoringAlertsCount(newCount);
          } catch (_) {}
        });
        console.log("✅ [AllMonitoringPage] Real-time watcher setup completed");
      } catch (error) {
        console.error(
          "❌ [AllMonitoringPage] ===== MONITORING LOADING FAILED ====="
        );
        console.error("❌ [AllMonitoringPage] Error details:", error);
        console.error(
          "❌ [AllMonitoringPage] Error stack:",
          (error as Error).stack
        );
      } finally {
        console.log("📊 [AllMonitoringPage] Setting loading state to false");
        setLoading(false);
        console.log(
          "🎉 [AllMonitoringPage] ===== MONITORING LOADING COMPLETED ====="
        );
      }
    };

    loadMonitoringAlerts();
  }, [
    setLastSeenMonitoringAt,
    setMonitoringAlertsCount,
    setLoading,
    setMonitoringAlerts,
  ]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64 p-4">
        <LoadingBars />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-white">
      {monitoringAlerts.length === 0 ? (
        <EmptyState type="monitoring" className="h-full" />
      ) : (
        <div className="flex flex-col gap-4 p-4">
          {monitoringAlerts.map((alert: MonitoringAlert) => (
            <MonitoringAlertCard key={alert.id} alert={alert} />
          ))}
        </div>
      )}
    </div>
  );
};

export default AllMonitoringPage;
