import React from "react";
import { useDialogStore } from "../../stores/dialogStore";
import MonitoringAlertCard from "../MonitoringAlertCard";
import LoadingBars from "../LoadingBars";
import EmptyState from "../EmptyState";

const AllMonitoringPage: React.FC = () => {
  const { monitoringAlerts, isLoading } = useDialogStore();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64 p-4">
        <LoadingBars />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-white">
      {monitoringAlerts.length === 0 ? (
        <EmptyState type="monitoring" className="h-full" />
      ) : (
        <div className="flex flex-col gap-4 p-4">
          {monitoringAlerts.map((alert) => (
            <MonitoringAlertCard key={alert.id} alert={alert} />
          ))}
        </div>
      )}
    </div>
  );
};

export default AllMonitoringPage;
