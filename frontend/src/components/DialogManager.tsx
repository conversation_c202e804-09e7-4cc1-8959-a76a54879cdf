import React from "react";
import { useDialogStore } from "../stores/dialogStore";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "./Dialog";
import AllMonitoringPage from "./dialog/AllMonitoringPage";
import AllOrdersPage from "./dialog/AllOrdersPage";
import EditProfilePage from "./dialog/EditProfilePage";

// Import close icon
import CloseIcon from "../assets/x.svg";

const DialogManager: React.FC = () => {
  const { isDialogOpen, currentPage, dialogTitle, closeDialog } =
    useDialogStore();

  // Store the dialog height when it opens to prevent changes during animation
  const [dialogHeight, setDialogHeight] = React.useState<string>("h-[45vh]");

  // Update dialog height only when page changes and dialog is open
  React.useEffect(() => {
    if (isDialogOpen && currentPage) {
      const height = getDialogHeight();
      setDialogHeight(height);
    }
  }, [currentPage, isDialogOpen]);

  const renderDialogContent = () => {
    switch (currentPage) {
      case "all-monitoring":
        return <AllMonitoringPage />;
      case "all-orders":
        return <AllOrdersPage />;
      case "edit-profile":
        return <EditProfilePage />;
      default:
        return (
          <div className="flex items-center justify-center h-64">
            <div className="text-gray-500">No content available</div>
          </div>
        );
    }
  };

  // Get dialog height based on content type
  const getDialogHeight = () => {
    switch (currentPage) {
      case "all-monitoring":
      case "all-orders":
        return "h-[90vh]";
      case "edit-profile":
        return "h-auto max-h-[90vh]";
      default:
        return "h-[45vh]";
    }
  };

  return (
    <Dialog open={isDialogOpen} onOpenChange={closeDialog}>
      <DialogContent className={dialogHeight}>
        {/* Header */}
        <DialogHeader>
          <DialogTitle>{dialogTitle}</DialogTitle>
          <DialogClose asChild>
            <button
              onClick={closeDialog}
              className="rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
            >
              <img src={CloseIcon} alt="Close" className="h-6 w-6" />
            </button>
          </DialogClose>
        </DialogHeader>

        {/* Content */}
        {renderDialogContent()}
      </DialogContent>
    </Dialog>
  );
};

export default DialogManager;
