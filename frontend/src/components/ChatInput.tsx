import React, { useState, useRef, useEffect } from "react";
import { cn } from "../utils/cn";
import send from "../assets/sends.svg";

interface ChatInputProps {
  placeholder?: string;
  onSend?: (message: string) => void;
  disabled?: boolean;
  className?: string;
  value?: string;
  onChange?: (value: string) => void;
}

const ChatInput: React.FC<ChatInputProps> = ({
  placeholder = "Ask anything ...",
  onSend,
  disabled = false,
  className,
  value: controlledValue,
  onChange,
}) => {
  const [internalValue, setInternalValue] = useState("");
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const value = controlledValue !== undefined ? controlledValue : internalValue;
  const setValue = onChange || setInternalValue;

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [value]);

  const handleSend = () => {
    if (value.trim() && !disabled) {
      onSend?.(value.trim());
      setValue("");
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const canSend = value.trim().length > 0 && !disabled;

  return (
    <div
      className={cn(
        "flex flex-col gap-1.5 min-h-[80px] max-h-[200px] items-start justify-start w-full",
        className
      )}
    >
      <div className="flex-1 bg-white w-full rounded-2xl relative border border-[#dee4f0] shadow-[0px_2px_4px_0px_rgba(0,0,0,0.08),0px_0px_2px_0px_rgba(0,0,0,0.08)]">
        <div className="flex flex-col items-end justify-center h-full p-3">
          {/* Textarea */}
          <textarea
            ref={textareaRef}
            value={value}
            onChange={(e) => setValue(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled}
            className="w-full resize-none bg-transparent font-['Inter:Regular',_sans-serif] font-normal text-base sm:text-base leading-normal text-[#181e29] placeholder:text-[#8c9fbd] focus:outline-none flex-1 min-h-0"
            style={{
              minHeight: "1.5rem",
              maxHeight: "4.5rem",
            }}
          />

          {/* Send Button */}
          <div className="flex justify-end w-full pt-2">
            <button
              onClick={handleSend}
              disabled={!canSend}
              className={cn(
                "bg-[#e9edf6] border border-[#dee4f0] rounded-lg p-2 shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)] transition-all duration-200",
                canSend
                  ? "hover:bg-[#d1d9e6] active:scale-95 cursor-pointer"
                  : "opacity-50 cursor-not-allowed"
              )}
            >
              <div className="w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center">
                <img
                  src={send}
                  alt="Send"
                  className={cn(
                    "size-6 sm:w-5 sm:h-5 transition-colors",
                    canSend ? "opacity-100" : "opacity-50"
                  )}
                />
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatInput;
