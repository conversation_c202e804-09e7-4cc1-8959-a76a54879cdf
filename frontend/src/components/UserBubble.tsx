import React from "react";
import { cn } from "../utils/cn";
import Avatar from "./Avatar";

interface UserBubbleProps {
  message: string;
  className?: string;
}

const UserBubble: React.FC<UserBubbleProps> = ({ message, className }) => {
  return (
    <div className={cn("flex flex-col gap-2 items-end", className)}>
      {/* Header row with "You" text and Avatar */}
      {/* <div className="flex items-center gap-2">
        <span className="font-['Inter'] text-sm text-[#43556e]">You</span>
        <Avatar
          title="User"
          size="md"
          variant="solid"
          backgroundColor="bg-gray-200"
          textColor="text-gray-600"
        />
      </div> */}

      {/* Message bubble */}
      <div
        className="bg-[#F4F6FA] text-[#181e29] rounded-[16px] rounded-tr-[4px] px-3 py-2 max-w-[85%]"
        style={{
          boxShadow: "0px 1px 2px 0px rgba(16, 24, 40, 0.05)",
        }}
      >
        <p className="font-['Inter'] text-[14px] leading-[24px] font-normal whitespace-pre-wrap">
          {message}
        </p>
      </div>
    </div>
  );
};

export default UserBubble;
