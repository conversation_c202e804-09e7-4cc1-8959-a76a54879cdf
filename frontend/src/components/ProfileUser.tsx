import React from "react";
import Card from "./Card";
import EditButton from "./EditButton";
import phoneIcon from "../assets/phone.svg";
import mailIcon from "../assets/mail.svg";
import vectorProfileCard from "../assets/vector-profile-card.svg";
import vectorProfileCard2 from "../assets/vector-profile-card-2.svg";
import { cn } from "../utils/cn";

interface ProfileUserProps {
  name: string;
  phone?: string;
  email?: string;
  avatarLetter?: string;
  onEdit?: () => void;
  className?: string;
}

const ProfileUser: React.FC<ProfileUserProps> = ({
  name,
  phone,
  email,
  avatarLetter,
  onEdit,
  className,
}) => {
  // Extract first letter from name if avatarLetter not provided
  const displayLetter = avatarLetter || name.charAt(0).toUpperCase();

  return (
    <Card
      className={cn(
        "relative border-[#bebbfe]",
        "bg-gradient-to-b from-[#efeeff99] to-[#f6eafc99]",
        className
      )}
    >
      {/* Decorative background vectors */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        {/* Top right vector */}
        <div className="w-40 h-40 -right-8 -top-8 absolute opacity-50">
          <img
            src={vectorProfileCard}
            alt=""
            className="w-full h-full object-contain"
          />
        </div>

        {/* Bottom left vector */}
        <div className="w-56 h-56 -left-12 -bottom-12 absolute origin-bottom-left rotate-[104.09deg] opacity-50">
          <img
            src={vectorProfileCard2}
            alt=""
            className="w-full h-full object-contain"
          />
        </div>
      </div>

      {/* Content */}
      <div className="relative p-3 flex flex-col gap-4">
        {/* Header with avatar and edit button */}
        <div className="flex items-start justify-between">
          <div className="flex flex-col gap-2">
            {/* Avatar */}
            <div className="relative w-10 h-10">
              <div className="w-10 h-10 rounded-full bg-gradient-to-br from-[#6b46c1] to-[#8b5cf6] flex items-center justify-center">
                <span className="text-[#f4f6fa] font-bold text-[24px] leading-8">
                  {displayLetter}
                </span>
              </div>
            </div>

            {/* User Info */}
            <div className="flex flex-col gap-1">
              <h2 className="font-semibold text-[20px] leading-7 text-[#181e29]">
                {name}
              </h2>

              {/* Contact Info */}
              <div className="flex flex-col gap-1">
                {/* Phone */}
                <div className="flex items-center gap-2">
                  <img
                    src={phoneIcon}
                    alt="Phone"
                    className="w-[18px] h-[18px] text-[#a330e5]"
                  />
                  <span className="font-normal text-base leading-6 text-[#181e29]">
                    {phone || "N/A"}
                  </span>
                </div>

                {/* Email */}
                <div className="flex items-center gap-2">
                  <img
                    src={mailIcon}
                    alt="Email"
                    className="w-[18px] h-[18px] text-[#a330e5]"
                  />
                  <span className="font-normal text-base leading-6 text-[#43556e]">
                    {email || "NA"}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Edit Button */}
          <div className="shrink-0">
            <EditButton onClick={onEdit} />
          </div>
        </div>
      </div>
    </Card>
  );
};

export default ProfileUser;

/*
 * Usage Example:
 *
 * import ProfileUser from './ProfileUser';
 *
 * const MyComponent = () => {
 *   return (
 *     <ProfileUser
 *       name="Sushmita Swain"
 *       phone="+91 - 9987548963"
 *       email="<EMAIL>"
 *       avatarLetter="S"
 *       onEdit={() => console.log('Edit profile')}
 *     />
 *   );
 * };
 *
 * Design Variables (from Figma):
 * - Text Primary: #181e29
 * - Text Secondary: #43556e
 * - Text Inverse: #f4f6fa
 * - Button Text: #5c54fd
 * - Border: #bebbfe
 * - Gradient: from-[#efeeff99] to-[#f6eafc99]
 * - Icon Color: #a330e5 (purple accent)
 */
