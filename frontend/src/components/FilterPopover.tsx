import React, { useState } from "react";
import * as Accordion from "@radix-ui/react-accordion";
import * as Checkbox from "@radix-ui/react-checkbox";
import * as Popover from "@radix-ui/react-popover";
import { cn } from "../utils/cn";
import CTAButton from "./CTAButton";
import FilterIcon from "../assets/filter-lines.svg";
import ChevronDownIcon from "../assets/chevron-down.svg";

interface FilterPopoverProps {
  selectedStatuses: string[];
  selectedOrderTypes: string[];
  onApplyFilters: (statuses: string[], orderTypes: string[]) => void;
  className?: string;
}

const FilterPopover: React.FC<FilterPopoverProps> = ({
  selectedStatuses,
  selectedOrderTypes,
  onApplyFilters,
  className,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [tempStatuses, setTempStatuses] = useState<string[]>(selectedStatuses);
  const [tempOrderTypes, setTempOrderTypes] =
    useState<string[]>(selectedOrderTypes);

  const statusOptions = [
    { value: "pending", label: "Pending" },
    { value: "executed", label: "Executed" },
    { value: "cancelled", label: "Cancelled" },
  ];

  const orderTypeOptions = [
    { value: "buy", label: "Buy" },
    { value: "sell", label: "Sell" },
    { value: "LIMIT", label: "Limit" },
    { value: "MARKET", label: "Market" },
    { value: "SL-L", label: "Stop-loss Limit" },
  ];

  const handleStatusChange = (status: string, checked: boolean) => {
    if (checked) {
      setTempStatuses([...tempStatuses, status]);
    } else {
      setTempStatuses(tempStatuses.filter((s) => s !== status));
    }
  };

  const handleOrderTypeChange = (orderType: string, checked: boolean) => {
    if (checked) {
      setTempOrderTypes([...tempOrderTypes, orderType]);
    } else {
      setTempOrderTypes(tempOrderTypes.filter((t) => t !== orderType));
    }
  };

  const handleApply = () => {
    console.log("FilterPopover - Applying filters:", {
      statuses: tempStatuses,
      orderTypes: tempOrderTypes,
    });
    onApplyFilters(tempStatuses, tempOrderTypes);
    setIsOpen(false);
  };

  const handleCancel = () => {
    console.log("FilterPopover - Cancelling filters");
    setTempStatuses(selectedStatuses);
    setTempOrderTypes(selectedOrderTypes);
    setIsOpen(false);
  };

  const getActiveFiltersCount = () => {
    return selectedStatuses.length + selectedOrderTypes.length;
  };

  return (
    <Popover.Root open={isOpen} onOpenChange={setIsOpen}>
      <Popover.Trigger asChild>
        <button
          className={cn(
            "bg-white border border-[#dee4f0] rounded-lg px-3 py-2 flex items-center gap-2 relative",
            "hover:bg-gray-50 transition-colors",
            className
          )}
        >
          <img src={FilterIcon} alt="filter" className="w-5 h-5" />
          <span className="text-[#181e29] text-sm font-medium">Filter By</span>
          <img src={ChevronDownIcon} alt="chevron down" className="w-5 h-5" />
          {getActiveFiltersCount() > 0 && (
            <div className="absolute -top-2 -right-2 bg-[#5c54fd] text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
              {getActiveFiltersCount()}
            </div>
          )}
        </button>
      </Popover.Trigger>

      <Popover.Portal>
        <Popover.Content
          className="bg-white border border-[#dee4f0] rounded-lg shadow-lg p-0 w-[280px] z-50"
          sideOffset={5}
          align="start"
        >
          <div className="p-4">
            {/* Header */}
            <div className="text-[#181e29] text-sm font-semibold mb-4 border-b border-[#dee4f0] pb-3">
              FILTER BY
            </div>

            {/* Accordion */}
            <Accordion.Root type="multiple" className="space-y-0">
              {/* Status Section */}
              <Accordion.Item
                value="status"
                className="border-b border-[#dee4f0]"
              >
                <Accordion.Header>
                  <Accordion.Trigger className="flex items-center justify-between w-full py-3 text-left">
                    <span className="text-[#181e29] text-sm font-medium">
                      Status
                    </span>
                    <img
                      src={ChevronDownIcon}
                      alt="chevron down"
                      className="w-4 h-4 transition-transform duration-200 data-[state=open]:rotate-180"
                    />
                  </Accordion.Trigger>
                </Accordion.Header>
                <Accordion.Content className="pb-3">
                  <div className="space-y-3">
                    {statusOptions.map((option) => (
                      <div
                        key={option.value}
                        className="flex items-center gap-3"
                      >
                        <Checkbox.Root
                          id={`status-${option.value}`}
                          className="w-4 h-4 border border-[#dee4f0] rounded flex items-center justify-center data-[state=checked]:bg-[#5c54fd] data-[state=checked]:border-[#5c54fd]"
                          checked={tempStatuses.includes(option.value)}
                          onCheckedChange={(checked) =>
                            handleStatusChange(option.value, checked as boolean)
                          }
                        >
                          <Checkbox.Indicator>
                            <svg
                              width="10"
                              height="8"
                              viewBox="0 0 10 8"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M9 1L3.5 6.5L1 4"
                                stroke="white"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          </Checkbox.Indicator>
                        </Checkbox.Root>
                        <label
                          htmlFor={`status-${option.value}`}
                          className="text-[#181e29] text-sm font-normal cursor-pointer"
                        >
                          {option.label}
                        </label>
                      </div>
                    ))}
                  </div>
                </Accordion.Content>
              </Accordion.Item>

              {/* Order Type Section */}
              <Accordion.Item value="order-type">
                <Accordion.Header>
                  <Accordion.Trigger className="flex items-center justify-between w-full py-3 text-left">
                    <span className="text-[#181e29] text-sm font-medium">
                      Order Type
                    </span>
                    <img
                      src={ChevronDownIcon}
                      alt="chevron down"
                      className="w-4 h-4 transition-transform duration-200 data-[state=open]:rotate-180"
                    />
                  </Accordion.Trigger>
                </Accordion.Header>
                <Accordion.Content className="pb-3">
                  <div className="space-y-3">
                    {orderTypeOptions.map((option) => (
                      <div
                        key={option.value}
                        className="flex items-center gap-3"
                      >
                        <Checkbox.Root
                          id={`order-type-${option.value}`}
                          className="w-4 h-4 border border-[#dee4f0] rounded flex items-center justify-center data-[state=checked]:bg-[#5c54fd] data-[state=checked]:border-[#5c54fd]"
                          checked={tempOrderTypes.includes(option.value)}
                          onCheckedChange={(checked) =>
                            handleOrderTypeChange(
                              option.value,
                              checked as boolean
                            )
                          }
                        >
                          <Checkbox.Indicator>
                            <svg
                              width="10"
                              height="8"
                              viewBox="0 0 10 8"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M9 1L3.5 6.5L1 4"
                                stroke="white"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          </Checkbox.Indicator>
                        </Checkbox.Root>
                        <label
                          htmlFor={`order-type-${option.value}`}
                          className="text-[#181e29] text-sm font-normal cursor-pointer"
                        >
                          {option.label}
                        </label>
                      </div>
                    ))}
                  </div>
                </Accordion.Content>
              </Accordion.Item>
            </Accordion.Root>

            {/* Action Buttons */}
            <div className="flex gap-2 mt-4 pt-4 border-t border-[#dee4f0]">
              <button
                onClick={handleCancel}
                className="flex-1 bg-white border border-[#dee4f0] text-[#181e29] font-medium rounded-lg px-4 py-2 hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <CTAButton onClick={handleApply} className="flex-1">
                Apply
              </CTAButton>
            </div>
          </div>
        </Popover.Content>
      </Popover.Portal>
    </Popover.Root>
  );
};

export default FilterPopover;
