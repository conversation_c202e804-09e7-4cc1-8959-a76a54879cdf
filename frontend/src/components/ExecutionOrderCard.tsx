import React, { useEffect, useRef, useState } from "react";
import { cn } from "../utils/cn";
import { formatNumber } from "../utils/formatNumber";
import HourglassIcon from "../assets/hourglass-03.svg";
import CheckCircleIcon from "../assets/check-circle.svg";
import LoadingIcon from "../assets/loading-02.svg";
import ArrowUpIcon from "../assets/arrow-narrow-up.svg";
import TargetIcon from "../assets/target-04.svg";
import LineChartUpIcon from "../assets/line-chart-up-04.svg";
import ArrowRightIcon from "../assets/arrow-circle-broken-up-right.svg";

interface ExecutionOrderCardProps {
  symbol?: string;
  status?: string;
  quantity?: number | string;
  price?: string | number;
  orderType?: string;
  product?: string;
  broker?: string;
  exchange?: string;
  primitive?: string; // BUY/SELL/other
  timestamp?: string; // ISO string if available
  className?: string;
  // Monitoring specific optional fields (when card represents a monitoring alert)
  triggerPrice?: string | number;
  currentPrice?: string | number;
  isMonitoring?: boolean;
  conditionOperator?: string;
  conditionValue?: string | number;
  onTriggerAction?: string;
  onTriggerQuantity?: number;
  onTriggerSymbol?: string;
}

// Hardcoded CSS values derived from Figma variables (resolved offline)
const COLORS = {
  cardBg: "#ffffff", // Background-Surface-Neutral-Default
  cardBorder: "#dee4f0", // Border-Neutral-Primary
  textPrimary: "#181e29", // Text-Neutral-Primary
  textSecondary: "#43556e", // Text-Neutral-Secondary
  buyBg: "#efeeff", // Background-Surface-Brand-Primary-Tertiary
  buyText: "#5c54fd", // Text-Button-Tertiary-Default (brand)
  sellBg: "#feeaec",
  sellText: "#f03142",
  statusPendingBg: "#fff4e8",
  statusPendingText: "#c87012",
  statusInProgressBg: "#fff4e8",
  statusInProgressText: "#c87012",
  statusExecutedBg: "#e9f7ef",
  statusExecutedText: "#27ae60",
  statusCancelledBg: "#f4f6fa",
  statusCancelledText: "#6d82a6",
  statusStoppedBg: "#feeaec",
  statusStoppedText: "#f03142",
};

const STATUS_CONFIG: Record<
  string,
  { text: string; bg: string; color: string; icon: string }
> = {
  pending: {
    text: "PENDING",
    bg: COLORS.statusPendingBg,
    color: COLORS.statusPendingText,
    icon: HourglassIcon,
  },
  inprogress: {
    text: "IN PROGRESS",
    bg: COLORS.statusInProgressBg,
    color: COLORS.statusInProgressText,
    icon: LoadingIcon,
  },
  executed: {
    text: "EXECUTED",
    bg: COLORS.statusExecutedBg,
    color: COLORS.statusExecutedText,
    icon: CheckCircleIcon,
  },
  cancelled: {
    text: "CANCELLED",
    bg: COLORS.statusCancelledBg,
    color: COLORS.statusCancelledText,
    icon: HourglassIcon,
  },
  triggered: {
    text: "TRIGGERED",
    bg: COLORS.statusExecutedBg,
    color: COLORS.statusExecutedText,
    icon: CheckCircleIcon,
  },
  stopped: {
    text: "STOPPED",
    bg: COLORS.statusStoppedBg,
    color: COLORS.statusStoppedText,
    icon: HourglassIcon,
  },
};
// A small helper to infer whether a primitive indicates monitoring
const MONITORING_ACTIONS = new Set([
  "monitorconditionthenact",
  "monitorprofit",
  "monitorsymbolfromwatchlist",
]);
function inferIsMonitoring(primitive?: string): boolean {
  const act = String(primitive || "").toLowerCase();
  if (!act) return false;
  return MONITORING_ACTIONS.has(act);
}

function formatTime(ts?: string): string | null {
  if (!ts) return null;
  try {
    const d = new Date(ts);
    const hh = `${d.getHours()}`.padStart(2, "0");
    const mm = `${d.getMinutes()}`.padStart(2, "0");
    const ss = `${d.getSeconds()}`.padStart(2, "0");
    return `${hh}:${mm}:${ss}`;
  } catch (_) {
    return null;
  }
}

function normalizeExchange(ex?: string): string | null {
  if (!ex) return null;
  const v = ex.toUpperCase();
  if (v.includes("NSE")) return "NSE";
  if (v.includes("BSE")) return "BSE";
  return v; // fallback to provided uppercase value
}

function formatNow(): string {
  const d = new Date();
  const hh = `${d.getHours()}`.padStart(2, "0");
  const mm = `${d.getMinutes()}`.padStart(2, "0");
  const ss = `${d.getSeconds()}`.padStart(2, "0");
  return `${hh}:${mm}:${ss}`;
}

const ExecutionOrderCard: React.FC<ExecutionOrderCardProps> = ({
  symbol,
  status,
  quantity,
  price,
  orderType,
  product,
  broker,
  exchange,
  primitive,
  timestamp,
  className,
  triggerPrice,
  currentPrice,
  isMonitoring: isMonitoringProp,
  conditionOperator,
  conditionValue,
  onTriggerAction,
  onTriggerQuantity,
  onTriggerSymbol,
}) => {
  const isSell = (primitive || "").toString().toLowerCase().includes("sell");
  const tradeText =
    (primitive || "").toString().toUpperCase() || (isSell ? "SELL" : "BUY");
  const tradeBg = isSell ? COLORS.sellBg : COLORS.buyBg;
  const tradeColor = isSell ? COLORS.sellText : COLORS.buyText;

  // Determine if this card should render the monitoring variant
  const isMonitoring =
    Boolean(isMonitoringProp) || inferIsMonitoring(primitive);

  const normalizedStatusKey = (status || "").toString().toLowerCase();
  const statusInfo = STATUS_CONFIG[normalizedStatusKey] || {
    text: (status || "").toString().toUpperCase(),
    bg: COLORS.statusCancelledBg,
    color: COLORS.statusCancelledText,
    icon: HourglassIcon,
  };

  const rightTopValue = (() => {
    const ot = (orderType || "").toString().toUpperCase();
    if (ot === "MARKET") return "Market price";
    const num = Number(price);
    if (Number.isFinite(num) && num > 0) return formatNumber(num);
    if (
      price !== undefined &&
      price !== null &&
      `${price}` !== "" &&
      `${price}` !== "0"
    )
      return `${price}`;
    return "";
  })();

  // Per-card timestamp: only update when status changes
  const [displayTime, setDisplayTime] = useState<string>(() => {
    return formatTime(timestamp || undefined) || formatNow();
  });
  const prevStatusRef = useRef<string | undefined>(status);
  useEffect(() => {
    if (prevStatusRef.current !== status) {
      setDisplayTime(formatNow());
      prevStatusRef.current = status;
    }
    // else do not update time to keep it stable for this card
  }, [status]);
  const exchangeLabel = normalizeExchange(exchange || undefined);

  // Monitoring-specific computed labels
  const monitoringTriggerLabel = (() => {
    const s = String(symbol || "").toUpperCase();
    // Prefer explicit condition parts when present
    const mapOp = (op: string) => {
      const o = String(op || "").toLowerCase();
      if (o === "gte") return ">=";
      if (o === "gt") return ">";
      if (o === "ste") return "<=";
      if (o === "st") return "<";
      return o || ">=";
    };
    if (
      conditionOperator &&
      conditionValue !== undefined &&
      conditionValue !== null
    ) {
      return `${s} ${mapOp(conditionOperator)} ₹${conditionValue}`;
    }
    // If card gets a preformatted string, use it
    if (typeof triggerPrice === "string" && triggerPrice.trim().length > 0) {
      return triggerPrice;
    }
    // Else fallback to a simple symbol > ₹price
    const trig =
      triggerPrice !== undefined &&
      triggerPrice !== null &&
      `${triggerPrice}` !== ""
        ? `${triggerPrice}`
        : undefined;
    if (trig) return `${s} > ₹${trig}`;
    return undefined;
  })();
  const monitoringCurrentLabel = (() => {
    if (
      currentPrice === undefined ||
      currentPrice === null ||
      `${currentPrice}` === ""
    )
      return undefined;
    return `₹${currentPrice}`;
  })();

  return (
    <div
      className={cn(
        "self-stretch p-3 w-full rounded-2xl outline outline-1 inline-flex flex-col justify-start items-start gap-2",
        className
      )}
      style={{
        backgroundColor: COLORS.cardBg,
        outlineColor: COLORS.cardBorder,
        outlineOffset: -1,
      }}
    >
      {/* Header badges (variant by type) */}
      {isMonitoring ? (
        <div className="self-stretch flex flex-col justify-center items-start gap-1">
          <div className="self-stretch inline-flex justify-start items-center gap-4">
            <div
              className="flex-1 justify-start text-sm font-semibold font-['Inter'] leading-tight"
              style={{ color: COLORS.textPrimary }}
            >
              {String(symbol || "").toUpperCase()}
            </div>
            <div
              className="px-1 py-0.5 rounded-lg flex justify-center items-center gap-1 overflow-hidden"
              style={{ backgroundColor: statusInfo.bg }}
            >
              <img src={statusInfo.icon} alt="" className="w-4 h-4" />
              <div className="flex justify-center items-center">
                <div
                  className="justify-start text-xs font-medium font-['Inter'] leading-none"
                  style={{ color: statusInfo.color }}
                >
                  {statusInfo.text}
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="self-stretch inline-flex justify-between items-start">
          <div
            className="px-1 py-0.5 rounded-lg flex justify-center items-center gap-1 overflow-hidden"
            style={{ backgroundColor: tradeBg }}
          >
            <div className="flex justify-center items-center">
              <div className="w-4 h-4 flex items-center justify-center">
                <img
                  src={ArrowUpIcon}
                  alt=""
                  className={cn("w-4 h-4", isSell ? "rotate-180" : "")}
                />
              </div>
            </div>
            <div className="flex justify-center items-center">
              <div
                className="justify-start text-xs font-medium font-['Inter'] leading-none"
                style={{ color: tradeColor }}
              >
                {tradeText}
              </div>
            </div>
          </div>
          <div
            className="px-1 py-0.5 rounded-lg flex justify-center items-center gap-1 overflow-hidden"
            style={{ backgroundColor: statusInfo.bg }}
          >
            <img src={statusInfo.icon} alt="" className="w-4 h-4" />
            <div className="flex justify-center items-center">
              <div
                className="justify-start text-xs font-medium font-['Inter'] leading-none"
                style={{ color: statusInfo.color }}
              >
                {statusInfo.text}
              </div>
            </div>
          </div>
        </div>
      )}

      {isMonitoring ? (
        <div className="self-stretch flex flex-col justify-start items-start gap-1">
          {/* Row: Trigger condition */}
          <div className="self-stretch inline-flex justify-start items-start gap-4">
            <div className="flex-1 flex justify-start items-center gap-2">
              <img src={TargetIcon} alt="Trigger" className="w-4 h-4" />
              <div
                className="flex-1 justify-start text-xs font-normal font-['Inter'] leading-none"
                style={{ color: COLORS.textSecondary }}
              >
                Trigger condition
              </div>
            </div>
            <div
              className="flex-1 text-right justify-start text-xs font-medium font-['Inter'] leading-none"
              style={{ color: COLORS.textPrimary }}
            >
              {monitoringTriggerLabel || "—"}
            </div>
          </div>

          {/* Row: Current value */}
          <div
            className={cn(
              "self-stretch inline-flex justify-start items-start gap-4",
              monitoringCurrentLabel === "₹0" && "hidden"
            )}
          >
            <div className="flex-1 flex justify-start items-center gap-2">
              <img src={LineChartUpIcon} alt="Current" className="w-4 h-4" />
              <div
                className="flex-1 justify-start text-xs font-normal font-['Inter'] leading-none"
                style={{ color: COLORS.textSecondary }}
              >
                Current value
              </div>
            </div>
            <div
              className="flex-1 text-right justify-start text-xs font-medium font-['Inter'] leading-none"
              style={{ color: COLORS.textPrimary }}
            >
              {monitoringCurrentLabel || "—"}
            </div>
          </div>

          {/* Row: Order action (derived from primitive and quantity) */}
          <div className="self-stretch inline-flex justify-start items-start gap-4">
            <div className="flex-1 flex justify-start items-center gap-2">
              <img src={ArrowRightIcon} alt="Action" className="w-4 h-4" />
              <div
                className="flex-1 justify-start text-xs font-normal font-['Inter'] leading-none"
                style={{ color: COLORS.textSecondary }}
              >
                Order action
              </div>
            </div>
            <div
              className="flex-1 text-right justify-start text-xs font-medium font-['Inter'] leading-none"
              style={{ color: COLORS.textPrimary }}
            >
              {(() => {
                // Prefer on_trigger details
                const trigAct = (onTriggerAction || "")
                  .toString()
                  .toUpperCase();
                const trigQty = onTriggerQuantity ?? quantity;
                const trigSym = (onTriggerSymbol || symbol || "")
                  .toString()
                  .toUpperCase();
                if (trigAct || trigQty || trigSym) {
                  return [trigAct, trigQty, trigSym]
                    .filter(
                      (x) => x !== undefined && x !== null && `${x}` !== ""
                    )
                    .join(" ")
                    .trim();
                }
                // Fallback to primitive + quantity + symbol
                const qtyStr =
                  quantity !== undefined && quantity !== null
                    ? String(quantity)
                    : "";
                const sym = String(symbol || "").toUpperCase();
                if (primitive) {
                  const prim = String(primitive).toUpperCase();
                  return [prim, qtyStr, sym].filter(Boolean).join(" ").trim();
                }
                return (
                  [qtyStr && "BUY", qtyStr, sym]
                    .filter(Boolean)
                    .join(" ")
                    .trim() || "—"
                );
              })()}
            </div>
          </div>
        </div>
      ) : (
        <div className="self-stretch flex flex-col justify-start items-start gap-1">
          {/* Row 1 */}
          <div className="self-stretch inline-flex justify-start items-start gap-4">
            <div
              className="flex-1 justify-start text-sm font-semibold font-['Inter'] leading-tight"
              style={{ color: COLORS.textPrimary }}
            >
              {(symbol || "").toString().toUpperCase()}
            </div>
            <div
              className="flex-1 text-right justify-start text-sm font-medium font-['Inter'] leading-tight"
              style={{ color: COLORS.textPrimary }}
            >
              {rightTopValue}
            </div>
          </div>

          {/* Row 2 */}
          {(quantity !== undefined && quantity !== null) || displayTime ? (
            <div className="self-stretch inline-flex justify-start items-start gap-4">
              <div
                className="flex-1 justify-start text-xs font-normal font-['Inter'] leading-none"
                style={{ color: COLORS.textSecondary }}
              >
                {quantity !== undefined && quantity !== null
                  ? `${quantity}`
                  : ""}{" "}
                /{" "}
                {quantity !== undefined && quantity !== null
                  ? `${quantity}`
                  : ""}
              </div>
              <div
                className="flex-1 text-right justify-start text-xs font-normal font-['Inter'] leading-none"
                style={{ color: COLORS.textSecondary }}
              >
                {displayTime || ""}
              </div>
            </div>
          ) : null}

          {/* Row 3 */}
          {orderType || product || price ? (
            <div className="self-stretch inline-flex justify-start items-start gap-4">
              <div
                className="flex-1 justify-start text-xs font-normal font-['Inter'] leading-none"
                style={{ color: COLORS.textSecondary }}
              >
                {[exchangeLabel, product, orderType]
                  .filter(Boolean)
                  .map((s) => `${s}`.toString().toUpperCase())
                  .join("   ")}
              </div>
              <div className="flex-1 text-right justify-start">
                {price !== undefined &&
                price !== null &&
                `${price}` !== "" &&
                `${price}` !== "0" ? (
                  <>
                    <span
                      className="text-xs font-normal font-['Inter'] leading-none"
                      style={{ color: COLORS.textSecondary }}
                    >
                      LTP&nbsp;
                    </span>
                    <span
                      className="text-xs font-normal font-['Inter'] leading-none"
                      style={{ color: COLORS.textPrimary }}
                    >
                      {Number.isFinite(Number(price))
                        ? formatNumber(Number(price))
                        : `${price}`}
                    </span>
                  </>
                ) : null}
              </div>
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
};

export default ExecutionOrderCard;
