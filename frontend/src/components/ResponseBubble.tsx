import React from "react";
import { cn } from "../utils/cn";
import CTAButton from "./CTAButton";
import Avatar from "./Avatar";

interface ResponseBubbleProps {
  message: string;
  primitives?: string[]; // Now just expects transformed strings
  actions?: Array<{
    description: string;
    type: "chat" | "orders" | "monitoring";
    message: string;
  }>;
  onActionClick?: (action: {
    description: string;
    type: "chat" | "orders" | "monitoring";
    message: string;
  }) => void;
  className?: string;
}

const ResponseBubble: React.FC<ResponseBubbleProps> = ({
  message,
  primitives,
  actions,
  onActionClick,
  className,
}) => {
  return (
    <div className={cn("flex flex-col gap-2 items-start w-full", className)}>
      {/* Header row with Avatar and "OrderGPT" text */}
      <div className="flex items-center gap-2">
        <Avatar
          title="AI"
          icon="./orderGPT.svg"
          size="md"
          variant="solid"
          backgroundColor="bg-gray-800"
        />
        <span className="font-['Inter'] text-sm text-[#43556e]">OrderGPT</span>
      </div>

      {/* Message bubble */}
      <div className="bg-transparent text-[#181e29] rounded-[16px] rounded-tl-[4px] px-3 py-2 max-w-[85%] w-full">
        {/* Raw Text */}
        <div className="font-['Inter'] text-[16px] leading-[24px] font-normal whitespace-pre-wrap">
          {message}
        </div>

        {/* Primitives as Unordered List */}
        {primitives && primitives.length > 0 && (
          <div className="mt-3">
            <ul className="list-disc list-inside space-y-1">
              {primitives.map((text, index) => (
                <li
                  key={index}
                  className="font-['Inter'] text-[14px] leading-[20px] font-normal text-[#43556e] pl-1"
                >
                  {text}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Action Buttons */}
        {actions && actions.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-3">
            {actions.map((action, index) => (
              <CTAButton
                key={index}
                onClick={() => onActionClick?.(action)}
                className="text-[12px] leading-[16px] px-3 py-2 bg-white hover:bg-[#4c44ed] transition-colors border border-[#7D76FD] hover:text-white"
                style={{ color: "var(--violet-500, #8B5CF6)" }}
              >
                {action.description}
              </CTAButton>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ResponseBubble;
