import React, { useEffect, useRef } from "react";
import { useNetworkStore } from "../stores/networkStore";
import { useWebSocketStore } from "../stores/websocketStore";
import { useAuthStore, getAuthToken } from "../stores/authStore";
import type { WebSocketResponse } from "../stores/websocketStore";
import { useSidebarStore } from "../stores/sidebarStore";
import { getCurrentBroker } from "../utils/sessionManager";
import { executionPouchDBSyncService } from "../services/ExecutionPouchDBSyncService";
import {
  getTestFirebaseUID,
  shouldSkipAuthValidation,
} from "../config/testConfig";

// Chrome extension API helper (using any to bypass type issues)

type MainThreadMessage = {
  type: "CONNECTION_STATUS" | "WEBSOCKET_MESSAGE" | "ERROR";
  payload: any;
};

// 🚀 Order execution is now handled directly in the WebSocket worker

// This component handles the global WebSocket connection
const WebSocketProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { isOnline } = useNetworkStore();
  const { addMessage, setConnectionStatus, updateSessionForTab } =
    useWebSocketStore();
  const { setBrokerLoginRequired } = useWebSocketStore.getState();
  const { activeTab } = useSidebarStore();
  const {
    isAuthenticated,
    incrementConnectionAttempts,
    maxConnectionAttempts,
    connectionAttempts,
  } = useAuthStore();
  const hasInitialized = useRef(false);
  const hasShownLoginRequired = useRef(false);
  const loginResolutionPollId = useRef<number | null>(null);
  // Global monitoring watcher to keep sidebar count updated
  const hasGlobalMonitoringWatcherRef = useRef(false);
  // Track latest execution message id to update UI
  const latestExecutionMessageIdRef = useRef<string | null>(null);
  // Short-lived polling for execution updates (fallback when change feed misses)
  const executionPollIdRef = useRef<number | null>(null);
  // Track current execution id to scope updates strictly
  const latestExecutionRequestIdRef = useRef<string | null>(null);

  // Monitoring-specific refs
  const latestMonitoringMessageIdRef = useRef<string | null>(null);
  const latestMonitoringExecIdRef = useRef<string | null>(null);
  const monitoringPollIdRef = useRef<number | null>(null);

  const showLoginResolvedThankYou = (targetTab?: string) => {
    const tab: "chat" | "orders" | "monitoring" =
      targetTab === "orders" || targetTab === "monitoring"
        ? (targetTab as any)
        : "chat";
    const sysMsg = {
      id: Date.now().toString() + Math.random(),
      timestamp: Date.now(),
      data: {
        textMessage: "logged back in, thank you",
        sender: "system",
        typeOfMessage: tab,
        messageType: "chat_response",
      },
    } as any;
    addMessage(sysMsg, tab);
    // Reset flags and stop any polling
    hasShownLoginRequired.current = false;
    if (loginResolutionPollId.current) {
      try {
        clearInterval(loginResolutionPollId.current);
      } catch (_) {}
      loginResolutionPollId.current = null;
    }
  };

  const handleWebSocketMessage = (payload: WebSocketResponse) => {
    console.log("[WebSocketProvider] Handling WebSocket message:", payload);
    console.log(
      "🚨 [EXECUTOR DEBUG] WebSocketProvider updated with executor check!"
    );

    if (payload.user_id) {
      const currentUserId = localStorage.getItem("user_id");

      // Always update if backend sends a different user_id (handles ID generation/mapping)
      if (currentUserId !== payload.user_id) {
        console.log(
          "[WebSocketProvider] 🆔 Updating user_id:",
          `${currentUserId} → ${payload.user_id}`
        );
        localStorage.setItem("user_id", payload.user_id);

        // 🔧 FIX: Immediately update WebSocket store to prevent race condition
        const { setUserId } = useWebSocketStore.getState();
        setUserId(payload.user_id);
        console.log(
          "[WebSocketProvider] ✅ Updated WebSocket store user_id immediately:",
          payload.user_id
        );
      } else {
        console.log(
          "[WebSocketProvider] 🆔 User ID unchanged:",
          payload.user_id
        );
      }
    }

    if (payload.conversation_id) {
      // Determine message type from the response or current active tab
      const messageType =
        payload.typeOfMessage ||
        (activeTab === "orders" || activeTab === "monitoring"
          ? activeTab
          : "chat");
      const conversationsStr = localStorage.getItem("conversations");
      const conversations = conversationsStr
        ? JSON.parse(conversationsStr)
        : {};
      const currentBroker = getCurrentBroker();

      const currentTabConversationId =
        conversations?.[currentBroker]?.[messageType];

      if (!currentTabConversationId) {
        console.log(
          "[WebSocketProvider] Storing new conversation_id for tab:",
          messageType,
          payload.conversation_id
        );
        const updatedConversations = {
          ...conversations,
          [currentBroker]: {
            ...conversations?.[currentBroker],
            [messageType]: payload.conversation_id,
          },
        };
        localStorage.setItem(
          "conversations",
          JSON.stringify(updatedConversations)
        );
        updateSessionForTab(messageType);

        // 🔧 NOTE: We don't update the WebSocket store conversation_id here because
        // the store only holds one conversation_id, but we need type-specific ones
        // getSessionIds() will read directly from localStorage for the specific type
      }
    }

    // Add message with the appropriate type (use typeOfMessage from response, fallback to active tab)
    const messageTab =
      payload.typeOfMessage ||
      (activeTab === "orders" || activeTab === "monitoring"
        ? activeTab
        : "chat");
    console.log(
      "[WebSocketProvider] Adding message to tab:",
      messageTab,
      "payload:",
      payload
    );
    addMessage(payload, messageTab);

    // Normalize primitives: ensure each has a stable id so executor uses our ids
    const primitivesAny: any[] = Array.isArray((payload as any).primitives)
      ? ((payload as any).primitives as any[])
      : [];
    const monitoringActions = new Set([
      "monitorconditionthenact",
      "monitorprofit",
      "monitorsymbolfromwatchlist",
    ]);
    const orderActions = new Set([
      "buy",
      "sell",
      "placebuylimitorder",
      "placeselllimitorder",
      "placebuystoplossmarketorder",
      "placesellstoplossmarketorder",
      "placebuystoplosslimitorder",
      "placesellstoplosslimitorder",
    ]);
    const isMonitoringPrimitive = (p: any) =>
      monitoringActions.has(String(p?.action || "").toLowerCase());
    const isOrderPrimitive = (p: any) =>
      orderActions.has(String(p?.action || "").toLowerCase());

    const generateStableId = (p: any): string => {
      try {
        const act = String(p?.action || "").toLowerCase();
        const args = p?.arguments || {};
        if (isOrderPrimitive({ action: act })) {
          const sym = (args.symbol || args.SYMBOL || p?.symbol || "")
            .toString()
            .toLowerCase();
          const qty = Number(args.quantity ?? args.QUANTITY ?? 0) || 0;
          const product = String(
            args.productType || args.PRODUCT_TYPE || "MIS"
          ).toLowerCase();
          const ordType = String(
            p?.orderType || args.orderType || ""
          ).toLowerCase();
          return `${act}_${sym}_${qty}_${product}_${ordType}_${Date.now()}_${Math.random()
            .toString(36)
            .slice(2, 6)}`;
        }
        if (isMonitoringPrimitive({ action: act })) {
          const cond = args?.condition || p?.condition || {};
          const sym = String(cond.symbol || "").toLowerCase();
          const op = String(cond.operator || "").toLowerCase();
          const val = String(cond.value ?? "");
          return `mon_${sym}_${op}_${val}_${Date.now()}_${Math.random()
            .toString(36)
            .slice(2, 6)}`;
        }
        return `${act}_${Date.now()}_${Math.random().toString(36).slice(2, 6)}`;
      } catch (_) {
        return `node_${Date.now()}_${Math.random().toString(36).slice(2, 6)}`;
      }
    };

    const normalizedPrimitives: any[] = primitivesAny.map((p) =>
      p && p.id ? p : { ...p, id: generateStableId(p) }
    );

    const isMonitoringAction = normalizedPrimitives.some(isMonitoringPrimitive);
    const isOrderExecution = payload.messageType === "order_execution";

    // ===== ORDERS (exclusive: only when order_execution and not monitoring) =====
    if (isOrderExecution && !isMonitoringAction) {
      const { setChatInputBlocked, updateMessageById } =
        useWebSocketStore.getState();

      // Build pending placeholders from order primitives only
      const pendingFromPrimitives: Array<{
        id: string;
        symbol: string;
        status: string;
        quantity?: number;
        price?: string;
        orderType?: string;
        product?: string;
        broker?: string;
        action_id?: string;
        primitive?: string;
      }> = [];
      try {
        normalizedPrimitives
          .filter(isOrderPrimitive)
          .forEach((p: any, idx: number) => {
            const args = p?.arguments || {};
            const rawSym = (
              args.symbol ||
              args.SYMBOL ||
              p?.symbol ||
              ""
            ).toString();
            const sym = rawSym ? rawSym.toLowerCase() : "";
            if (!sym) return;
            const qty =
              Number(args.quantity ?? args.QUANTITY ?? 0) || undefined;
            const product = (args.productType || args.PRODUCT_TYPE) as
              | string
              | undefined;
            const actionName = String(p?.action || "").toLowerCase();
            let orderType: string | undefined = "MARKET";
            if (actionName.includes("limit")) orderType = "LIMIT";
            if (
              actionName.includes("stoploss") &&
              actionName.includes("market")
            )
              orderType = "SL-M";
            if (actionName.includes("stoploss") && actionName.includes("limit"))
              orderType = "SL-L";
            pendingFromPrimitives.push({
              id: `pending_${Date.now()}_${idx}`,
              symbol: sym,
              status: "pending",
              quantity: qty,
              broker: "zerodha",
              action_id: p?.id,
              primitive: String(p?.action || "").toUpperCase(),
              product,
              orderType,
            } as any);
          });
      } catch (_) {}

      // Store execution request so background starts
      latestExecutionRequestIdRef.current = null;
      try {
        if (normalizedPrimitives.length > 0) {
          storeExecutionRequestInPouchDB(normalizedPrimitives as any)
            .then((execId) => {
              if (typeof execId === "string") {
                latestExecutionRequestIdRef.current = execId;
                // Seed immediately
                executionPouchDBSyncService
                  .getOrders()
                  .then(updateFromOrders)
                  .catch(() => {});
                // Watch the execution request to unblock if needed
                executionPouchDBSyncService.watchExecutionRequestStatus(
                  execId,
                  (status) => {
                    console.log(
                      "[EXEC-REQ] status update:",
                      status,
                      "execId:",
                      execId
                    );
                    if (status === "completed" || status === "failed") {
                      setChatInputBlocked(false);
                      // Force a final refresh to capture terminal statuses
                      executionPouchDBSyncService
                        .getOrders()
                        .then(updateFromOrders)
                        .catch(() => {});
                      // Small delayed refresh to account for replication lag
                      setTimeout(async () => {
                        try {
                          updateFromOrders(
                            await executionPouchDBSyncService.getOrders()
                          );
                        } catch (_) {}
                      }, 1500);
                    }
                  }
                );
              }
            })
            .catch(() => {});
        }
      } catch (_) {}

      // Create bubble with placeholders
      const hostMessageId = addMessage(
        {
          id: Date.now().toString() + Math.random(),
          timestamp: Date.now(),
          data: {
            textMessage: "Executing your order(s)...",
            sender: "system",
            typeOfMessage: "chat",
            messageType: "chat_response",
            executionOrders: pendingFromPrimitives,
            executionCompleted: false,
          },
        } as any,
        "chat"
      );
      latestExecutionMessageIdRef.current = hostMessageId;

      // Block input immediately at start; subsequent updates will refine based on pending-only
      setChatInputBlocked(true);

      // Update UI from orders, scoped by current execution id
      const updateFromOrders = (orders: any[]) => {
        const execId = latestExecutionRequestIdRef.current;
        if (!execId) return; // keep placeholders until exec id known

        const scoped = orders.filter(
          (o: any) => (o.execution_request_id || "") === execId
        );

        try {
          console.log("[ORDERS-SCOPE] execId:", execId);
          console.log(
            "[ORDERS-SCOPE] incoming orders:",
            orders.map((o: any) => ({
              id: o.id,
              symbol: o.symbol,
              status: o.status,
              action_id: o.action_id,
              exec: o.execution_request_id,
            }))
          );
          console.log(
            "[ORDERS-SCOPE] scoped by exec:",
            scoped.map((o: any) => ({
              id: o.id,
              symbol: o.symbol,
              status: o.status,
              action_id: o.action_id,
            }))
          );
        } catch (_) {}

        // Determine completion for polling cleanup and summary
        const running = scoped.filter((o: any) =>
          ["pending", "inProgress"].includes((o.status || "").toString())
        );
        const allDone = running.length === 0 && scoped.length > 0;

        updateMessageById("chat", hostMessageId, (old: WebSocketResponse) => {
          const previous = Array.isArray((old as any).executionOrders)
            ? (old as any).executionOrders
            : [];
          try {
            const placeholderActionIds = new Set(
              previous.map((e: any) => (e?.action_id || "").toString())
            );
            const actionIdMatches = orders.filter((o: any) =>
              placeholderActionIds.has((o?.action_id || "").toString())
            );
            console.log(
              "[ORDERS-MATCH] placeholder action_ids:",
              Array.from(placeholderActionIds)
            );
            console.log(
              "[ORDERS-MATCH] orders matching placeholder action_ids:",
              actionIdMatches.map((o: any) => ({
                id: o.id,
                symbol: o.symbol,
                status: o.status,
                action_id: o.action_id,
                exec: o.execution_request_id,
              }))
            );
          } catch (_) {}
          // Build maps by stable key: action_id > id > symbol
          const makeKey = (o: any) =>
            (
              o?.action_id ||
              o?.id ||
              String(o?.symbol || "").toLowerCase()
            ).toString();
          try {
            console.log("[ORDERS-MERGE] execId:", execId);
            console.log(
              "[ORDERS-MERGE] previous (placeholders/display):",
              previous.map((e: any) => ({
                key: makeKey(e),
                id: e.id,
                symbol: e.symbol,
                action_id: e.action_id,
                status: e.status,
              }))
            );
            console.log(
              "[ORDERS-MERGE] real (scoped from DB):",
              scoped.map((o: any) => ({
                key: makeKey(o),
                id: o.id,
                symbol: o.symbol,
                action_id: o.action_id,
                status: o.status,
              }))
            );
          } catch (_) {}
          const prevByKey = new Map<string, any>(
            previous.map((e: any) => [makeKey(e), e])
          );
          const realByKey = new Map<string, any>(
            scoped.map((o: any) => [makeKey(o), o])
          );
          const targetKeys = new Set<string>([
            ...Array.from(prevByKey.keys()),
            ...Array.from(realByKey.keys()),
          ]);
          const merged: any[] = Array.from(targetKeys).map(
            (k) => realByKey.get(k) || prevByKey.get(k)
          );

          // Recompute pending using merged so placeholders influence blocking until real docs arrive
          const hasPendingMerged = merged.some(
            (o: any) => (o?.status || "").toString() === "pending"
          );
          setChatInputBlocked(hasPendingMerged);
          const display = Array.from(targetKeys).map((k) => {
            const real = realByKey.get(k) || {};
            const prev = prevByKey.get(k) || {};
            const pick = (r: any, p: any, key: string) =>
              r[key] !== undefined && r[key] !== null ? r[key] : p[key];
            return {
              id: pick(real, prev, "id"),
              symbol: String(pick(real, prev, "symbol") || ""),
              status: String(pick(real, prev, "status") || "pending"),
              quantity: pick(real, prev, "quantity"),
              price: pick(real, prev, "price"),
              orderType: pick(real, prev, "orderType"),
              product: pick(real, prev, "product"),
              broker: pick(real, prev, "broker"),
              exchange: pick(real, prev, "exchange"),
              timestamp: pick(real, prev, "timestamp"),
              action_id: pick(real, prev, "action_id"),
              primitive: (() => {
                const fromReal = (
                  real?.type ||
                  real?.primitive ||
                  ""
                ).toString();
                const fromPrev = (prev?.primitive || "").toString();
                const val = fromReal || fromPrev;
                return val ? val.toUpperCase() : "";
              })(),
            };
          });
          try {
            console.log(
              "[ORDERS-MERGE] final display:",
              display.map((d: any) => ({
                key: makeKey(d),
                id: d.id,
                symbol: d.symbol,
                action_id: d.action_id,
                status: d.status,
              }))
            );
          } catch (_) {}

          return {
            ...old,
            executionOrders: display,
            executionCompleted: allDone,
          } as any;
        });

        if (allDone) {
          if (executionPollIdRef.current) {
            try {
              clearInterval(executionPollIdRef.current);
            } catch (_) {}
            executionPollIdRef.current = null;
          }
        }
      };

      // Live updates + fallback poll
      executionPouchDBSyncService.watchOrderUpdates((orders) => {
        try {
          updateFromOrders(orders);
        } catch (e) {
          console.error("[WebSocketProvider] Update failed:", e);
        }
      });
      if (executionPollIdRef.current) {
        try {
          clearInterval(executionPollIdRef.current);
        } catch (_) {}
        executionPollIdRef.current = null;
      }
      const start = Date.now();
      const maxMs = 120000;
      const intervalMs = 1500;
      executionPollIdRef.current = setInterval(async () => {
        if (Date.now() - start > maxMs) {
          try {
            clearInterval(executionPollIdRef.current!);
          } catch (_) {}
          executionPollIdRef.current = null;
          return;
        }
        try {
          updateFromOrders(await executionPouchDBSyncService.getOrders());
        } catch (_) {}
      }, intervalMs) as unknown as number;
    }

    // ===== MONITORING (exclusive: only when order_execution and monitoring primitives present) =====
    if (isOrderExecution && isMonitoringAction) {
      const { updateMessageById } = useWebSocketStore.getState();

      // Build pending placeholders from monitoring primitives only
      const pendingFromPrimitives: Array<{
        id: string;
        symbol: string;
        status: string;
        quantity?: number;
        broker?: string;
        action_id?: string;
        primitive?: string;
        // Monitoring specific optional fields
        triggerPrice?: string | number;
        currentPrice?: string | number;
        // Marker for UI
        isMonitoring?: boolean;
        // Trigger label parts
        conditionOperator?: string;
        conditionValue?: string | number;
        // On trigger fields for order action row
        onTriggerAction?: string;
        onTriggerQuantity?: number;
        onTriggerSymbol?: string;
      }> = [];
      try {
        const tryExtractSymbol = (p: any): string => {
          const args = p?.arguments || {};
          const cond = args?.condition || p?.condition || {};
          const onTrig = args?.on_trigger || p?.on_trigger || {};
          const raw = (
            args.symbol ||
            args.SYMBOL ||
            cond.symbol ||
            onTrig.symbol ||
            onTrig.arguments?.symbol ||
            p?.symbol ||
            ""
          ).toString();
          return raw ? raw.toLowerCase() : "";
        };
        const tryExtractCondition = (
          p: any
        ): { operator?: string; value?: string | number } => {
          try {
            const args = p?.arguments || {};
            const cond = args?.condition || p?.condition || {};
            const op = (cond?.operator || "").toString();
            const val = cond?.value;
            return { operator: op, value: val };
          } catch (_) {
            return {};
          }
        };
        const tryExtractQty = (p: any): number | undefined => {
          const args = p?.arguments || {};
          const onTrig = args?.on_trigger || p?.on_trigger || {};
          const q = Number(
            args.quantity ??
              args.QUANTITY ??
              onTrig.quantity ??
              onTrig.QUANTITY ??
              onTrig.arguments?.quantity ??
              onTrig.arguments?.QUANTITY
          );
          return Number.isFinite(q) && q > 0 ? q : undefined;
        };
        normalizedPrimitives
          .filter(isMonitoringPrimitive)
          .forEach((p: any, idx: number) => {
            const sym = tryExtractSymbol(p);
            if (!sym) return;
            const qty = tryExtractQty(p);
            const { operator, value } = tryExtractCondition(p);
            const onTrig =
              (p?.arguments || {})?.on_trigger || p?.on_trigger || {};
            const onTrigAction = String(onTrig?.action || "").toUpperCase();
            const onTrigQty = Number(
              onTrig?.quantity ??
                onTrig?.QUANTITY ??
                onTrig?.arguments?.quantity ??
                onTrig?.arguments?.QUANTITY
            );
            const onTrigSym = (
              onTrig?.symbol ??
              onTrig?.arguments?.symbol ??
              ""
            )
              .toString()
              .toLowerCase();
            pendingFromPrimitives.push({
              id: `mon_pending_${Date.now()}_${idx}`,
              symbol: sym,
              status: "pending",
              quantity: qty,
              broker: "zerodha",
              action_id: p?.id,
              primitive: onTrigAction || String(p?.action || "").toUpperCase(),
              // Unknown on first render; filled by alerts later
              triggerPrice: undefined,
              currentPrice: undefined,
              isMonitoring: true,
              conditionOperator: operator,
              conditionValue: value,
              onTriggerAction: onTrigAction || undefined,
              onTriggerQuantity: Number.isFinite(onTrigQty)
                ? onTrigQty
                : undefined,
              onTriggerSymbol: onTrigSym || undefined,
            } as any);
          });
      } catch (_) {}

      // Additionally, build order placeholders (if any) for mixed scenarios
      const pendingOrderPlaceholders: Array<{
        id: string;
        symbol: string;
        status: string;
        quantity?: number;
        price?: string;
        orderType?: string;
        product?: string;
        broker?: string;
        action_id?: string;
        primitive?: string;
      }> = [];
      try {
        normalizedPrimitives
          .filter(isOrderPrimitive)
          .forEach((p: any, idx: number) => {
            const args = p?.arguments || {};
            const rawSym = (
              args.symbol ||
              args.SYMBOL ||
              p?.symbol ||
              ""
            ).toString();
            const sym = rawSym ? rawSym.toLowerCase() : "";
            if (!sym) return;
            const qty =
              Number(args.quantity ?? args.QUANTITY ?? 0) || undefined;
            const product = (args.productType || args.PRODUCT_TYPE) as
              | string
              | undefined;
            const actionName = String(p?.action || "").toLowerCase();
            let orderType: string | undefined = "MARKET";
            if (actionName.includes("limit")) orderType = "LIMIT";
            if (
              actionName.includes("stoploss") &&
              actionName.includes("market")
            )
              orderType = "SL-M";
            if (actionName.includes("stoploss") && actionName.includes("limit"))
              orderType = "SL-L";
            pendingOrderPlaceholders.push({
              id: `ord_pending_${Date.now()}_${idx}`,
              symbol: sym,
              status: "pending",
              quantity: qty,
              broker: "zerodha",
              action_id: p?.id,
              primitive: String(p?.action || "").toUpperCase(),
              product,
              orderType,
            } as any);
          });
      } catch (_) {}

      // Create monitoring bubble in chat with placeholders
      const monitoringMessageId = addMessage(
        {
          id: Date.now().toString() + Math.random(),
          timestamp: Date.now(),
          data: {
            textMessage: "Setting up a conditional order...",
            sender: "system",
            typeOfMessage: "chat",
            messageType: "chat_response",
            executionOrders: pendingFromPrimitives,
            executionCompleted: false,
            monitoringStarted: false,
          },
        } as any,
        "chat"
      );
      latestMonitoringMessageIdRef.current = monitoringMessageId;

      // Provisional bump: reflect new monitoring primitives in sidebar count immediately
      try {
        const monCount = pendingFromPrimitives.length;
        if (monCount > 0) {
          const { monitoringAlerts, setMonitoringAlertsCount } =
            useSidebarStore.getState();
          setMonitoringAlertsCount(monitoringAlerts + monCount);
        }
      } catch (_) {}

      // If there are order primitives, create an orders bubble too (no input blocking)
      const hasOrderPrims = pendingOrderPlaceholders.length > 0;
      const ordersMessageId = hasOrderPrims
        ? addMessage(
            {
              id: Date.now().toString() + Math.random(),
              timestamp: Date.now(),
              data: {
                textMessage: "Executing your order(s)...",
                sender: "system",
                typeOfMessage: "chat",
                messageType: "chat_response",
                executionOrders: pendingOrderPlaceholders,
                executionCompleted: false,
              },
            } as any,
            "chat"
          )
        : null;

      const updateFromAlerts = (alerts: any[]) => {
        const execId = latestMonitoringExecIdRef.current;
        const scoped = execId
          ? alerts.filter((a: any) => (a.execution_request_id || "") === execId)
          : [];
        updateMessageById(
          "chat",
          monitoringMessageId,
          (old: WebSocketResponse) => {
            const previous = Array.isArray((old as any).executionOrders)
              ? (old as any).executionOrders
              : [];
            const makeKey = (o: any) =>
              (
                o?.action_id ||
                o?.id ||
                String(o?.symbol || "").toLowerCase()
              ).toString();
            const prevByKey = new Map<string, any>(
              previous.map((e: any) => [makeKey(e), e])
            );
            const realByKey = new Map<string, any>(
              scoped.map((a: any) => [makeKey(a), a])
            );
            const targetKeys = new Set<string>([
              ...prevByKey.keys(),
              ...realByKey.keys(),
            ]);
            const merged = Array.from(targetKeys).map(
              (k) => realByKey.get(k) || prevByKey.get(k)
            );
            const display = Array.from(targetKeys).map((k) => {
              const real = realByKey.get(k) || {};
              const prev = prevByKey.get(k) || {};
              const pick = (r: any, p: any, key: string) =>
                r[key] !== undefined && r[key] !== null ? r[key] : p[key];
              const primitiveFromReal = (() => {
                const desc = (real?.description || "").toString();
                if (desc) return desc.split(" ")[0].toUpperCase();
                const ot = (real?.orderType || "").toString();
                return ot ? ot.toUpperCase() : "";
              })();
              const primitive =
                primitiveFromReal ||
                String(prev?.primitive || "").toUpperCase();
              return {
                id: pick(real, prev, "id"),
                symbol: String(pick(real, prev, "symbol") || ""),
                status: String(pick(real, prev, "status") || "pending"),
                quantity: pick(real, prev, "quantity"),
                price: pick(real, prev, "price"),
                orderType: pick(real, prev, "orderType"),
                product: pick(real, prev, "product"),
                broker: pick(real, prev, "broker"),
                action_id: pick(real, prev, "action_id"),
                primitive,
                // Monitoring specific fields from alerts
                triggerPrice: pick(real, prev, "triggerPrice"),
                currentPrice: pick(real, prev, "currentPrice"),
                isMonitoring: true,
                conditionOperator: pick(real, prev, "conditionOperator"),
                conditionValue: pick(real, prev, "conditionValue"),
                onTriggerAction: pick(real, prev, "onTriggerAction"),
                onTriggerQuantity: pick(real, prev, "onTriggerQuantity"),
                onTriggerSymbol: pick(real, prev, "onTriggerSymbol"),
              };
            });
            const statuses = scoped.map((a: any) =>
              String(a?.status || "").toLowerCase()
            );
            const hasPending = statuses.some((s: string) => s === "pending");
            const allInProgress =
              scoped.length > 0 &&
              statuses.every((s: string) => s === "inprogress");
            const hasStarted = allInProgress && !hasPending;
            return {
              ...old,
              textMessage: hasStarted
                ? "Set up done! Monitoring started."
                : "Setting up a conditional order...",
              executionOrders: display,
              executionCompleted: false,
              monitoringStarted: hasStarted,
            } as any;
          }
        );
      };

      // Orders updater (no input blocking)
      const updateFromOrdersInMonitoring = (orders: any[]) => {
        if (!ordersMessageId) return;
        const execId = latestExecutionRequestIdRef.current;
        if (!execId) return;
        const scoped = orders.filter(
          (o: any) => (o.execution_request_id || "") === execId
        );
        updateMessageById("chat", ordersMessageId, (old: WebSocketResponse) => {
          const previous = Array.isArray((old as any).executionOrders)
            ? (old as any).executionOrders
            : [];
          try {
            const placeholderActionIds = new Set(
              previous.map((e: any) => (e?.action_id || "").toString())
            );
            const actionIdMatches = scoped.filter((o: any) =>
              placeholderActionIds.has((o?.action_id || "").toString())
            );
            console.log("[ORDERS-MON-SCOPE] execId:", execId);
            console.log(
              "[ORDERS-MON-SCOPE] scoped orders:",
              scoped.map((o: any) => ({
                id: o.id,
                symbol: o.symbol,
                status: o.status,
                action_id: o.action_id,
                exec: o.execution_request_id,
              }))
            );
            console.log(
              "[ORDERS-MON-MATCH] orders matching placeholder action_ids:",
              actionIdMatches.map((o: any) => ({
                id: o.id,
                symbol: o.symbol,
                status: o.status,
                action_id: o.action_id,
                exec: o.execution_request_id,
              }))
            );
          } catch (_) {}
          // Build maps by stable key: action_id > id > symbol
          const makeKey = (o: any) =>
            (
              o?.action_id ||
              o?.id ||
              String(o?.symbol || "").toLowerCase()
            ).toString();
          try {
            console.log("[ORDERS-MERGE-MON] execId:", execId);
            console.log(
              "[ORDERS-MERGE-MON] previous (placeholders/display):",
              previous.map((e: any) => ({
                key: makeKey(e),
                id: e.id,
                symbol: e.symbol,
                action_id: e.action_id,
                status: e.status,
              }))
            );
            console.log(
              "[ORDERS-MERGE-MON] real (scoped from DB):",
              scoped.map((o: any) => ({
                key: makeKey(o),
                id: o.id,
                symbol: o.symbol,
                action_id: o.action_id,
                status: o.status,
              }))
            );
          } catch (_) {}
          const prevByKey = new Map<string, any>(
            previous.map((e: any) => [makeKey(e), e])
          );
          const realByKey = new Map<string, any>(
            scoped.map((o: any) => [makeKey(o), o])
          );
          const targetKeys = new Set<string>([
            ...prevByKey.keys(),
            ...realByKey.keys(),
          ]);
          const display = Array.from(targetKeys).map((k) => {
            const real = realByKey.get(k) || {};
            const prev = prevByKey.get(k) || {};
            const pick = (r: any, p: any, key: string) =>
              r[key] !== undefined && r[key] !== null ? r[key] : p[key];
            return {
              id: pick(real, prev, "id"),
              symbol: String(pick(real, prev, "symbol") || ""),
              status: String(pick(real, prev, "status") || "pending"),
              quantity: pick(real, prev, "quantity"),
              price: pick(real, prev, "price"),
              orderType: pick(real, prev, "orderType"),
              product: pick(real, prev, "product"),
              broker: pick(real, prev, "broker"),
              exchange: pick(real, prev, "exchange"),
              timestamp: pick(real, prev, "timestamp"),
              action_id: pick(real, prev, "action_id"),
              primitive: (() => {
                const fromReal = (
                  real?.type ||
                  real?.primitive ||
                  ""
                ).toString();
                const fromPrev = (prev?.primitive || "").toString();
                const val = fromReal || fromPrev;
                return val ? val.toUpperCase() : "";
              })(),
              // Explicitly mark these as order cards in mixed scenario
              isMonitoring: false,
            };
          });
          try {
            console.log(
              "[ORDERS-MERGE-MON] final display:",
              display.map((d: any) => ({
                key: makeKey(d),
                id: d.id,
                symbol: d.symbol,
                action_id: d.action_id,
                status: d.status,
              }))
            );
          } catch (_) {}
          // Determine completion for this orders bubble
          const running = scoped.filter((o: any) =>
            ["pending", "inProgress"].includes((o.status || "").toString())
          );
          const allDone = running.length === 0 && scoped.length > 0;
          return {
            ...old,
            executionOrders: display,
            executionCompleted: allDone,
          } as any;
        });
      };

      // Store execution request to start monitoring (and orders if present)
      latestMonitoringExecIdRef.current = null;
      latestExecutionRequestIdRef.current = null;
      try {
        if (normalizedPrimitives.length > 0) {
          storeExecutionRequestInPouchDB(normalizedPrimitives as any)
            .then((execId) => {
              if (typeof execId === "string") {
                latestMonitoringExecIdRef.current = execId;
                latestExecutionRequestIdRef.current = execId;
                // Seed monitoring and orders
                executionPouchDBSyncService
                  .getMonitoringAlerts()
                  .then(updateFromAlerts)
                  .catch(() => {});
                if (ordersMessageId) {
                  executionPouchDBSyncService
                    .getOrders()
                    .then(updateFromOrdersInMonitoring)
                    .catch(() => {});
                }
              }
            })
            .catch(() => {});
        }
      } catch (_) {}

      // Subscribe to live monitoring updates
      executionPouchDBSyncService.watchMonitoringUpdates((alerts) => {
        try {
          updateFromAlerts(alerts);
        } catch (e) {
          console.error("[WebSocketProvider] Monitoring update failed:", e);
        }
      });

      // Subscribe to orders updates if we created an orders bubble
      if (ordersMessageId) {
        executionPouchDBSyncService.watchOrderUpdates((orders) => {
          try {
            updateFromOrdersInMonitoring(orders);
          } catch (e) {
            console.error(
              "[WebSocketProvider] Orders update (monitoring branch) failed:",
              e
            );
          }
        });
      }

      // Short-lived polling to ensure first render without dialog
      if (monitoringPollIdRef.current) {
        try {
          clearInterval(monitoringPollIdRef.current);
        } catch (_) {}
        monitoringPollIdRef.current = null;
      }
      const monStart = Date.now();
      const monMaxMs = 120000;
      const monInterval = 1500;
      monitoringPollIdRef.current = setInterval(async () => {
        if (Date.now() - monStart > monMaxMs) {
          try {
            clearInterval(monitoringPollIdRef.current!);
          } catch (_) {}
          monitoringPollIdRef.current = null;
          return;
        }
        try {
          updateFromAlerts(
            await executionPouchDBSyncService.getMonitoringAlerts()
          );
        } catch (_) {}
        if (ordersMessageId) {
          try {
            updateFromOrdersInMonitoring(
              await executionPouchDBSyncService.getOrders()
            );
          } catch (_) {}
        }
      }, monInterval) as unknown as number;
    }
  };

  // Helper function to get Firebase UID (from auth or test config)
  const getFirebaseUID = (): string | null => {
    if (shouldSkipAuthValidation()) {
      const testUID = getTestFirebaseUID();
      console.log("[WebSocketProvider] 🧪 Using test Firebase UID:", testUID);
      return testUID;
    }

    const firebaseUser =
      useAuthStore.getState().firebaseUser ?? useAuthStore.getState().user;
    console.log("AuthState contains:", useAuthStore.getState());
    return firebaseUser?.uid || null;
  };

  // Store execution request in PouchDB instead of Chrome API
  const storeExecutionRequestInPouchDB = async (
    primitives: any[]
  ): Promise<string | undefined> => {
    try {
      console.log(
        "[WebSocketProvider] 💾 Storing execution request in PouchDB"
      );

      const firebaseUID = getFirebaseUID();
      if (!firebaseUID) {
        console.error(
          "[WebSocketProvider] ❌ No Firebase UID found for execution request"
        );
        return undefined;
      }

      // Get current conversation ID
      const conversationsStr = localStorage.getItem("conversations");
      const conversations = conversationsStr
        ? JSON.parse(conversationsStr)
        : {};
      const currentBroker = getCurrentBroker();
      const messageType =
        activeTab === "orders" || activeTab === "monitoring"
          ? activeTab
          : "chat";
      const conversationId = conversations?.[currentBroker]?.[messageType];

      const executionRequest = {
        type: "execution_request" as const,
        firebase_uid: firebaseUID,
        conversation_id: conversationId || "unknown",
        status: "pending" as const,
        primitives, // Pass primitives as-is - no conversion needed
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Store in execution PouchDB
      const result =
        await executionPouchDBSyncService.storeExecutionRequest(
          executionRequest
        );
      console.log(
        `[WebSocketProvider] ✅ Execution request stored successfully: ${result}`
      );
      const createdId = typeof result === "string" ? result : undefined;

      // Wake up the service worker by sending a test message
      try {
        if ((globalThis as any).chrome?.runtime?.sendMessage) {
          console.log(
            "🚀 [DEBUG] Sending wake-up message to executor service worker..."
          );
          (globalThis as any).chrome.runtime.sendMessage({
            type: "WAKE_UP_SERVICE_WORKER",
            timestamp: Date.now(),
          });
        }
      } catch (error) {
        console.log(
          "🔧 [DEBUG] Chrome runtime not available (expected in web context)"
        );
      }
      return createdId;
    } catch (error) {
      console.error(
        "[WebSocketProvider] ❌ Error storing execution request:",
        error
      );
      return undefined;
    }
  };

  useEffect(() => {
    if (!window.worker && !hasInitialized.current) {
      console.log("[WebSocketProvider] Creating worker");
      const worker = new Worker(
        new URL("../workers/websocket.worker.ts", import.meta.url),
        { type: "module" }
      );
      window.worker = worker;
      hasInitialized.current = true;

      // Initialize execution PouchDB service
      const initializeExecutionPouchDB = async () => {
        try {
          const firebaseUID = getFirebaseUID();

          if (firebaseUID) {
            console.log(
              "[WebSocketProvider] 🔧 Initializing execution PouchDB service with Firebase UID:",
              firebaseUID
            );
            // No auth token needed for local-only PouchDB
            try {
              console.log(
                "[WebSocketProvider] 🚀 About to call executionPouchDBSyncService.initialize()"
              );
              await executionPouchDBSyncService.initialize(firebaseUID);
              console.log(
                "[WebSocketProvider] ✅ executionPouchDBSyncService.initialize() completed successfully"
              );
              // Set up a global monitoring updates watcher (once) to update sidebar badge
              if (!hasGlobalMonitoringWatcherRef.current) {
                try {
                  executionPouchDBSyncService.watchMonitoringUpdates(
                    (alerts) => {
                      try {
                        const {
                          lastSeenMonitoringAt,
                          setMonitoringAlertsCount,
                        } = useSidebarStore.getState();
                        const baseline = lastSeenMonitoringAt ?? 0;
                        const newCount = (alerts || []).filter((a: any) => {
                          const tsStr =
                            a?.updated_at ||
                            a?.updatedAt ||
                            a?.created_at ||
                            a?.createdAt ||
                            "";
                          const ts = tsStr ? new Date(tsStr).getTime() : 0;
                          return ts > baseline;
                        }).length;
                        setMonitoringAlertsCount(newCount);
                      } catch (_) {}
                    }
                  );
                  hasGlobalMonitoringWatcherRef.current = true;
                } catch (e) {
                  console.warn(
                    "[WebSocketProvider] Failed to set global monitoring watcher:",
                    e
                  );
                }
              }
            } catch (error) {
              console.error(
                "[WebSocketProvider] ❌ Error during executionPouchDBSyncService.initialize():",
                error
              );
              throw error;
            }

            // Setup execution callback
            executionPouchDBSyncService.setExecutionCallback((request) => {
              console.log(
                "🎯 [WebSocketProvider] Execution request received:",
                request._id
              );
              // Handle execution request changes here
            });

            console.log(
              "[WebSocketProvider] ✅ Execution PouchDB service initialized (local only)"
            );
          } else {
            console.log(
              "[WebSocketProvider] ⚠️ Skipping execution PouchDB initialization - no Firebase UID"
            );
          }
        } catch (error) {
          console.error(
            "[WebSocketProvider] ❌ Failed to initialize execution PouchDB:",
            error
          );
        }
      };

      initializeExecutionPouchDB();

      worker.onmessage = (event: MessageEvent<MainThreadMessage>) => {
        const { type, payload } = event.data;
        console.log("[WebSocketProvider] Received message from worker:", {
          type,
          payload,
        });

        switch (type) {
          case "CONNECTION_STATUS":
            setConnectionStatus(payload.isConnected);
            break;
          case "WEBSOCKET_MESSAGE":
            handleWebSocketMessage(payload);
            break;
          case "ERROR":
            console.log("[WebSocketProvider] Setting error:", payload.message);
            // Create a system error message instead of setting banner error
            const errorTab = payload.tab || "chat"; // Use the tab from the error or default to chat
            const errorMessage = {
              id: Date.now().toString() + Math.random(),
              timestamp: Date.now(),
              data: {
                textMessage: `Error: ${payload.message || "Unknown error occurred"}`,
                sender: "system",
                typeOfMessage: errorTab,
                messageType: "chat_response",
              },
            };
            // addMessage(errorMessage, errorTab); // Add as system message to the correct tab
            break;
        }
      };

      const userId = localStorage.getItem("user_id") || "";

      // Get Firebase token for authentication
      const connectWithAuth = async () => {
        try {
          const firebaseToken = await getAuthToken();
          console.log(
            "[WebSocketProvider] Sending CONNECT message to worker with userId and token:",
            { userId, hasToken: !!firebaseToken }
          );

          window.worker.postMessage({
            type: "CONNECT",
            payload: {
              userId,
              token: firebaseToken,
            },
          });
        } catch (error) {
          console.error(
            "[WebSocketProvider] Failed to get Firebase token:",
            error
          );

          // Increment connection attempts
          incrementConnectionAttempts();

          // If we've exceeded max attempts, redirect to login
          if (connectionAttempts >= maxConnectionAttempts) {
            console.warn(
              "[WebSocketProvider] Max connection attempts exceeded, redirecting to login"
            );
            window.location.href = "/login";
            return;
          }

          // Connect without token as fallback (for development)
          window.worker.postMessage({
            type: "CONNECT",
            payload: { userId },
          });
        }
      };

      if (isAuthenticated) {
        connectWithAuth();
      } else {
        // For development/testing - connect without auth
        console.log(
          "[WebSocketProvider] Connecting without authentication (development mode)"
        );
        window.worker.postMessage({ type: "CONNECT", payload: { userId } });
      }
    }

    // Don't terminate worker on cleanup - let it persist
    // The worker will be terminated when the page is actually closed
    return () => {
      console.log(
        "[WebSocketProvider] Component cleanup - keeping worker alive"
      );
    };
  }, []);

  // 🚀 FIX: Reset WebSocket connection when authentication state changes
  useEffect(() => {
    console.log("[WebSocketProvider] 🔄 Auth state changed:", {
      isAuthenticated,
    });

    // If user just logged in or out, reset the WebSocket connection
    if (window.worker && hasInitialized.current) {
      console.log(
        "[WebSocketProvider] 🔄 Resetting WebSocket connection due to auth change"
      );

      // Disconnect current connection
      window.worker.postMessage({ type: "DISCONNECT" });

      // Get current user ID
      const userId = localStorage.getItem("user_id") || "";

      // Reconnect with new auth state
      if (isAuthenticated) {
        console.log("[WebSocketProvider] 🔑 Reconnecting with authentication");
        const connectWithAuth = async () => {
          try {
            const firebaseToken = await getAuthToken();
            console.log("[WebSocketProvider] 🔄 Reconnecting with fresh token");

            window.worker.postMessage({
              type: "CONNECT",
              payload: {
                userId,
                token: firebaseToken,
              },
            });
          } catch (error) {
            console.error(
              "[WebSocketProvider] ❌ Failed to reconnect with auth:",
              error
            );
            // Fallback to connection without token
            window.worker.postMessage({
              type: "CONNECT",
              payload: { userId },
            });
          }
        };
        connectWithAuth();
      } else {
        console.log(
          "[WebSocketProvider] 🔓 User logged out - not reconnecting"
        );
        setConnectionStatus(false);
      }
    }
  }, [isAuthenticated]); // Dependency on auth state

  // On mount: query cached login status from background to handle fresh loads
  useEffect(() => {
    const chromeAPI = (globalThis as any).chrome;
    if (!chromeAPI || !chromeAPI.runtime || !chromeAPI.runtime.sendMessage) {
      return;
    }
    try {
      chromeAPI.runtime.sendMessage(
        { type: "GET_LOGIN_STATUS" },
        (resp: any) => {
          try {
            if (
              resp?.success &&
              resp?.data?.required === true &&
              !hasShownLoginRequired.current
            ) {
              setBrokerLoginRequired(true);
              const brokerName = resp?.data?.brokerName || "your broker";
              const tab: "chat" | "orders" | "monitoring" = "chat";
              const sysMsg = {
                id: Date.now().toString() + Math.random(),
                timestamp: Date.now(),
                data: {
                  textMessage: `Please log in to ${brokerName} to continue`,
                  sender: "system",
                  typeOfMessage: tab,
                  messageType: "chat_response",
                },
              } as any;
              addMessage(sysMsg, tab);
              hasShownLoginRequired.current = true;
            }
          } catch (_) {}
        }
      );
    } catch (_) {}
  }, [addMessage, activeTab]);

  // Listen for executor runtime messages (e.g., LOGIN_REQUIRED/LOGIN_RESOLVED) and surface as system messages
  useEffect(() => {
    const chromeAPI = (globalThis as any).chrome;
    if (!chromeAPI || !chromeAPI.runtime || !chromeAPI.runtime.onMessage) {
      return;
    }

    const listener = (message: any, _sender: any, sendResponse: any) => {
      try {
        if (message?.type === "LOGIN_REQUIRED") {
          if (hasShownLoginRequired.current) {
            try {
              sendResponse?.({ received: true });
            } catch (_) {}
            return true;
          }
          setBrokerLoginRequired(true);
          const brokerName = message?.brokerName || "your broker";
          const targetTab = message?.targetTab;
          const tab: "chat" | "orders" | "monitoring" =
            targetTab === "orders" || targetTab === "monitoring"
              ? targetTab
              : "chat";
          const sysMsg = {
            id: Date.now().toString() + Math.random(),
            timestamp: Date.now(),
            data: {
              textMessage: `Please log in to ${brokerName} to continue`,
              sender: "system",
              typeOfMessage: tab,
              messageType: "chat_response",
            },
          };
          addMessage(sysMsg, tab);
          hasShownLoginRequired.current = true;
          // Start a short polling loop to detect resolution in case the broadcast is missed
          const chromeAPI = (globalThis as any).chrome;
          if (
            chromeAPI?.runtime?.sendMessage &&
            !loginResolutionPollId.current
          ) {
            loginResolutionPollId.current = setInterval(() => {
              try {
                chromeAPI.runtime.sendMessage(
                  { type: "GET_LOGIN_STATUS" },
                  (resp: any) => {
                    try {
                      if (
                        resp?.success &&
                        resp?.data?.required === false &&
                        hasShownLoginRequired.current
                      ) {
                        setBrokerLoginRequired(false);
                        showLoginResolvedThankYou(targetTab);
                      }
                    } catch (_) {}
                  }
                );
              } catch (_) {}
            }, 1000) as unknown as number;
          }
          try {
            sendResponse?.({ received: true });
          } catch (_) {}
          return true;
        }
        if (message?.type === "LOGIN_RESOLVED") {
          setBrokerLoginRequired(false);
          const targetTab = message?.targetTab;
          showLoginResolvedThankYou(targetTab);
          try {
            sendResponse?.({ received: true });
          } catch (_) {}
          return true;
        }
      } catch (_) {}
    };

    chromeAPI.runtime.onMessage.addListener(listener);
    return () => {
      try {
        chromeAPI.runtime.onMessage.removeListener(listener);
      } catch (_) {}
    };
  }, [addMessage, activeTab]);

  useEffect(() => {
    if (window.worker) {
      console.log("[WebSocketProvider] Sending network status to worker:", {
        isOnline,
      });
      window.worker.postMessage({
        type: "NETWORK_STATUS_CHANGE",
        payload: { isOnline },
      });
    }
  }, [isOnline]);

  return <>{children}</>;
};

export default WebSocketProvider;
