import React, { useEffect, useRef } from "react";
import { useNetworkStore } from "../stores/networkStore";
import { useWebSocketStore } from "../stores/websocketStore";
import { useAuthStore, getAuthToken } from "../stores/authStore";
import type { WebSocketResponse } from "../stores/websocketStore";
import { useSidebarStore } from "../stores/sidebarStore";
import { getCurrentBroker } from "../utils/sessionManager";

// Chrome extension API helper (using any to bypass type issues)

type MainThreadMessage = {
  type: "CONNECTION_STATUS" | "WEBSOCKET_MESSAGE" | "ERROR";
  payload: any;
};

// 🚀 Order execution is now handled directly in the WebSocket worker

// This component handles the global WebSocket connection
const WebSocketProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { isOnline } = useNetworkStore();
  const { addMessage, setConnectionStatus, updateSessionForTab } =
    useWebSocketStore();
  const { activeTab } = useSidebarStore();
  const {
    isAuthenticated,
    incrementConnectionAttempts,
    maxConnectionAttempts,
    connectionAttempts,
  } = useAuthStore();
  const hasInitialized = useRef(false);

  const handleWebSocketMessage = (payload: WebSocketResponse) => {
    console.log("[WebSocketProvider] Handling WebSocket message:", payload);
    console.log(
      "🚨 [EXECUTOR DEBUG] WebSocketProvider updated with executor check!"
    );

    if (payload.user_id) {
      const currentUserId = localStorage.getItem("user_id");

      // Always update if backend sends a different user_id (handles ID generation/mapping)
      if (currentUserId !== payload.user_id) {
        console.log(
          "[WebSocketProvider] 🆔 Updating user_id:",
          `${currentUserId} → ${payload.user_id}`
        );
        localStorage.setItem("user_id", payload.user_id);

        // 🔧 FIX: Immediately update WebSocket store to prevent race condition
        const { setUserId } = useWebSocketStore.getState();
        setUserId(payload.user_id);
        console.log(
          "[WebSocketProvider] ✅ Updated WebSocket store user_id immediately:",
          payload.user_id
        );
      } else {
        console.log(
          "[WebSocketProvider] 🆔 User ID unchanged:",
          payload.user_id
        );
      }
    }

    if (payload.conversation_id) {
      // Determine message type from the response or current active tab
      const messageType =
        payload.typeOfMessage ||
        (activeTab === "orders" || activeTab === "monitoring"
          ? activeTab
          : "chat");
      const conversationsStr = localStorage.getItem("conversations");
      const conversations = conversationsStr
        ? JSON.parse(conversationsStr)
        : {};
      const currentBroker = getCurrentBroker();

      const currentTabConversationId =
        conversations?.[currentBroker]?.[messageType];

      if (!currentTabConversationId) {
        console.log(
          "[WebSocketProvider] Storing new conversation_id for tab:",
          messageType,
          payload.conversation_id
        );
        const updatedConversations = {
          ...conversations,
          [currentBroker]: {
            ...conversations?.[currentBroker],
            [messageType]: payload.conversation_id,
          },
        };
        localStorage.setItem(
          "conversations",
          JSON.stringify(updatedConversations)
        );
        updateSessionForTab(messageType);

        // 🔧 NOTE: We don't update the WebSocket store conversation_id here because
        // the store only holds one conversation_id, but we need type-specific ones
        // getSessionIds() will read directly from localStorage for the specific type
      }
    }

    // Add message with the appropriate type (use typeOfMessage from response, fallback to active tab)
    const messageTab =
      payload.typeOfMessage ||
      (activeTab === "orders" || activeTab === "monitoring"
        ? activeTab
        : "chat");
    console.log(
      "[WebSocketProvider] Adding message to tab:",
      messageTab,
      "payload:",
      payload
    );
    addMessage(payload, messageTab);

    // 🚀 EXECUTOR LOGIC: Check for order execution and trigger Chrome extension
    console.log("🚨🚨🚨 [EXECUTOR] About to check for order_execution!");
    console.log(
      "[WebSocketProvider] 🔍 Checking messageType:",
      payload.messageType,
      "=== 'order_execution'?",
      payload.messageType === "order_execution"
    );
    if (payload.messageType === "order_execution") {
      console.log(
        "[WebSocketProvider] 🚀 ORDER EXECUTION detected! Primitives:",
        payload.primitives
      );
      if (payload.primitives && payload.primitives.length > 0) {
        executeOrderViaChrome(payload.primitives);
      } else {
        console.warn(
          "[WebSocketProvider] ⚠️ ORDER EXECUTION message has no primitives"
        );
      }
    }
  };

  const executeOrderViaChrome = (primitives: any[]) => {
    try {
      console.log(
        "[WebSocketProvider] 🚀 Executing order via Chrome extension"
      );

      // Check if we're in extension context
      if (
        typeof (globalThis as any).chrome === "undefined" ||
        !(globalThis as any).chrome.runtime
      ) {
        console.warn(
          "[WebSocketProvider] ⚠️ Chrome extension APIs not available"
        );
        return;
      }

      // Pass primitives as-is - no conversion needed
      const actions = primitives.map((primitive) => {
        return {
          action: primitive.action.toUpperCase(), // Just uppercase the action
          arguments: primitive.arguments, // Pass arguments as-is
        };
      });

      console.log(
        "[WebSocketProvider] 🔄 Passing actions to executor as-is:",
        actions
      );
      console.log(
        "[WebSocketProvider] 🌐 Using background mode - will create new Zerodha tab if needed"
      );

      // Send to Chrome extension background script
      ((globalThis as any).chrome as any).runtime.sendMessage(
        {
          type: "RULE_ENGINE_EXECUTE_ACTIONS", // MESSAGE_TYPES.EXECUTE_ACTIONS
          actions: actions,
          siteId: "kiteByZerodha", // Default to Zerodha for now
          tabId: null, // Not needed for background mode
          automationMode: "background", // Create new Zerodha tab in background
        },
        (response: any) => {
          console.log("[WebSocketProvider] ✅ Executor response:", response);

          if (response?.success) {
            console.log(
              "[WebSocketProvider] 🎉 Order executed successfully:",
              response.message
            );
            // TODO: Show success notification to user
          } else {
            console.error(
              "[WebSocketProvider] ❌ Order execution failed:",
              response?.message
            );
            // TODO: Show error notification to user
          }
        }
      );
    } catch (error) {
      console.error("[WebSocketProvider] ❌ Error executing order:", error);
    }
  };

  useEffect(() => {
    if (!window.worker && !hasInitialized.current) {
      console.log("[WebSocketProvider] Creating worker");
      const worker = new Worker(
        new URL("../workers/websocket.worker.ts", import.meta.url),
        { type: "module" }
      );
      window.worker = worker;
      hasInitialized.current = true;

      worker.onmessage = (event: MessageEvent<MainThreadMessage>) => {
        const { type, payload } = event.data;
        console.log("[WebSocketProvider] Received message from worker:", {
          type,
          payload,
        });

        switch (type) {
          case "CONNECTION_STATUS":
            setConnectionStatus(payload.isConnected);
            break;
          case "WEBSOCKET_MESSAGE":
            handleWebSocketMessage(payload);
            break;
          case "ERROR":
            console.log("[WebSocketProvider] Setting error:", payload.message);
            // Create a system error message instead of setting banner error
            const errorTab = payload.tab || "chat"; // Use the tab from the error or default to chat
            const errorMessage = {
              id: Date.now().toString() + Math.random(),
              timestamp: Date.now(),
              data: {
                textMessage: `Error: ${payload.message || "Unknown error occurred"}`,
                sender: "system",
                typeOfMessage: errorTab,
                messageType: "chat_response",
              },
            };
            addMessage(errorMessage, errorTab); // Add as system message to the correct tab
            break;
        }
      };

      const userId = localStorage.getItem("user_id") || "";

      // Get Firebase token for authentication
      const connectWithAuth = async () => {
        try {
          const firebaseToken = await getAuthToken();
          console.log(
            "[WebSocketProvider] Sending CONNECT message to worker with userId and token:",
            { userId, hasToken: !!firebaseToken }
          );

          window.worker.postMessage({
            type: "CONNECT",
            payload: {
              userId,
              token: firebaseToken,
            },
          });
        } catch (error) {
          console.error(
            "[WebSocketProvider] Failed to get Firebase token:",
            error
          );

          // Increment connection attempts
          incrementConnectionAttempts();

          // If we've exceeded max attempts, redirect to login
          if (connectionAttempts >= maxConnectionAttempts) {
            console.warn(
              "[WebSocketProvider] Max connection attempts exceeded, redirecting to login"
            );
            window.location.href = "/login";
            return;
          }

          // Connect without token as fallback (for development)
          window.worker.postMessage({
            type: "CONNECT",
            payload: { userId },
          });
        }
      };

      if (isAuthenticated) {
        connectWithAuth();
      } else {
        // For development/testing - connect without auth
        console.log(
          "[WebSocketProvider] Connecting without authentication (development mode)"
        );
        window.worker.postMessage({ type: "CONNECT", payload: { userId } });
      }
    }

    // Don't terminate worker on cleanup - let it persist
    // The worker will be terminated when the page is actually closed
    return () => {
      console.log(
        "[WebSocketProvider] Component cleanup - keeping worker alive"
      );
    };
  }, []);

  // 🚀 FIX: Reset WebSocket connection when authentication state changes
  useEffect(() => {
    console.log("[WebSocketProvider] 🔄 Auth state changed:", {
      isAuthenticated,
    });

    // If user just logged in or out, reset the WebSocket connection
    if (window.worker && hasInitialized.current) {
      console.log(
        "[WebSocketProvider] 🔄 Resetting WebSocket connection due to auth change"
      );

      // Disconnect current connection
      window.worker.postMessage({ type: "DISCONNECT" });

      // Get current user ID
      const userId = localStorage.getItem("user_id") || "";

      // Reconnect with new auth state
      if (isAuthenticated) {
        console.log("[WebSocketProvider] 🔑 Reconnecting with authentication");
        const connectWithAuth = async () => {
          try {
            const firebaseToken = await getAuthToken();
            console.log("[WebSocketProvider] 🔄 Reconnecting with fresh token");

            window.worker.postMessage({
              type: "CONNECT",
              payload: {
                userId,
                token: firebaseToken,
              },
            });
          } catch (error) {
            console.error(
              "[WebSocketProvider] ❌ Failed to reconnect with auth:",
              error
            );
            // Fallback to connection without token
            window.worker.postMessage({
              type: "CONNECT",
              payload: { userId },
            });
          }
        };
        connectWithAuth();
      } else {
        console.log(
          "[WebSocketProvider] 🔓 User logged out - not reconnecting"
        );
        setConnectionStatus(false);
      }
    }
  }, [isAuthenticated]); // Dependency on auth state

  useEffect(() => {
    if (window.worker) {
      console.log("[WebSocketProvider] Sending network status to worker:", {
        isOnline,
      });
      window.worker.postMessage({
        type: "NETWORK_STATUS_CHANGE",
        payload: { isOnline },
      });
    }
  }, [isOnline]);

  return <>{children}</>;
};

export default WebSocketProvider;
