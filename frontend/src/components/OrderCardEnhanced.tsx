import React from "react";
import { cn } from "../utils/cn";
import { formatNumber } from "../utils/formatNumber";
import Card from "./Card";
import ArrowUpIcon from "../assets/arrow-narrow-up.svg";
import LoadingIcon from "../assets/loading-02.svg";
import HourglassIcon from "../assets/hourglass-03.svg";
import CheckCircleIcon from "../assets/check-circle.svg";

import StarIcon from "../assets/star-06.svg";

interface OrderCardProps {
  stockName: string;
  value: string | number;
  quantity: string;
  time: string;
  exchange: string;
  orderType: string;
  ltp: string | number;
  tradeType: "BUY" | "SELL";
  status: "pending" | "executed" | "cancelled";
  onChatClick?: () => void;
  className?: string;
}

const statusConfig = {
  pending: {
    icon: LoadingIcon,
    text: "PENDING",
    bgColor: "#fff4e8",
    textColor: "#c87012",
    iconColor: "#c87012",
  },
  executed: {
    icon: CheckCircleIcon,
    text: "EXECUTED",
    bgColor: "#e9f7ef",
    textColor: "#27ae60",
    iconColor: "#27ae60",
  },
  cancelled: {
    icon: HourglassIcon,
    text: "CANCELLED",
    bgColor: "#f4f6fa",
    textColor: "#6d82a6",
    iconColor: "#6d82a6",
  },
};

const OrderCardEnhanced: React.FC<OrderCardProps> = ({
  stockName,
  value,
  quantity,
  time,
  exchange,
  orderType,
  ltp,
  tradeType,
  status,
  onChatClick,
  className,
}) => {
  const statusInfo = statusConfig[status];
  const tradeTypeConfig = {
    BUY: {
      icon: ArrowUpIcon,
      text: "BUY",
      bgColor: "#efeeff",
      textColor: "#5c54fd",
      iconColor: "#5c54fd",
    },
    SELL: {
      icon: ArrowUpIcon,
      text: "SELL",
      bgColor: "#feeaec",
      textColor: "#f03142",
      iconColor: "#f03142",
      iconRotation: "rotate-180",
    },
  };

  const tradeInfo = tradeTypeConfig[tradeType];

  return (
    <Card className={cn("p-3 border-[#dee4f0]", className)}>
      <div className="flex flex-col gap-2">
        {/* Status badges */}
        <div className="flex items-center justify-between">
          <div
            className="flex items-center gap-1 px-2 py-1 rounded-lg"
            style={{ backgroundColor: tradeInfo.bgColor }}
          >
            <img
              src={tradeInfo.icon}
              alt=""
              className={cn("w-4 h-4", (tradeInfo as any).iconRotation || "")}
              style={{
                filter: `hue-rotate(${tradeType === "BUY" ? "0deg" : "0deg"})`,
              }}
            />
            <span
              className="text-xs font-medium leading-[16px]"
              style={{ color: tradeInfo.textColor }}
            >
              {tradeInfo.text}
            </span>
          </div>
          <div
            className="flex items-center gap-1 px-2 py-1 rounded-lg"
            style={{ backgroundColor: statusInfo.bgColor }}
          >
            <img src={statusInfo.icon} alt="" className="w-4 h-4" />
            <span
              className="text-xs font-medium leading-[16px]"
              style={{ color: statusInfo.textColor }}
            >
              {statusInfo.text}
            </span>
          </div>
        </div>

        {/* Order details */}
        <div className="flex flex-col gap-1">
          <div className="flex justify-between items-center">
            <span className="text-[#181e29] text-sm font-medium leading-[20px]">
              {stockName}
            </span>
            <span className="text-[#181e29] text-sm font-medium leading-[20px]">
              {formatNumber(value)}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-[#43556e] text-xs font-normal leading-[16px]">
              {quantity}
            </span>
            <span className="text-[#43556e] text-xs font-normal leading-[16px]">
              {time}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-[#43556e] text-xs font-normal leading-[16px]">
              {exchange} CNC {orderType}
            </span>
            <span className="text-[#43556e] text-xs font-normal leading-[16px]">
              LTP <span className="text-[#181e29]">{formatNumber(ltp)}</span>
            </span>
          </div>
        </div>

        {/* Divider */}
        <div className="w-full h-px bg-[#dee4f0]" />

        {/* Chat button */}
        <div className="flex justify-end pt-1">
          <button
            onClick={onChatClick}
            className="flex items-center gap-1 text-[#5c54fd] text-sm font-medium leading-[20px] hover:bg-gray-50 px-2 py-1 rounded-lg transition-colors"
          >
            <img src={StarIcon} alt="" className="w-[18px] h-[18px]" />
            Chat Now
          </button>
        </div>
      </div>
    </Card>
  );
};

export default OrderCardEnhanced;
