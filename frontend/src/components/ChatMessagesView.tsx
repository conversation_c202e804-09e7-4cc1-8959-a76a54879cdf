import React, { useEffect, useRef } from "react";
import { cn } from "../utils/cn";
import UserBubble from "./UserBubble";
import ResponseBubble from "./ResponseBubble";
import LoadingBars from "./LoadingBars";
import type {
  MessageHistory,
  WebSocketResponse,
} from "../stores/websocketStore";
import { transformPrimitivesForDisplay } from "../utils/messageUtils";

interface ChatMessagesViewProps {
  messages: MessageHistory[];
  isWaitingForResponse?: boolean;
  onActionClick?: (action: {
    description: string;
    type: "chat" | "orders" | "monitoring";
    message: string;
  }) => void;
  className?: string;
}

const ChatMessagesView: React.FC<ChatMessagesViewProps> = ({
  messages,
  isWaitingForResponse = false,
  onActionClick,
  className,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when messages change or loading state changes
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages, isWaitingForResponse]);

  // Helper to determine if message is from user
  const isUserMessage = (message: MessageHistory): boolean => {
    return message.data.sender === "user";
  };

  // Helper to get system message data
  const getSystemMessageData = (message: MessageHistory): WebSocketResponse => {
    return message.data as WebSocketResponse;
  };

  // Helper to transform primitives based on message type
  const transformPrimitives = (systemData: WebSocketResponse): string[] => {
    return transformPrimitivesForDisplay(
      systemData.primitives,
      systemData.messageType
    );
  };

  return (
    <div className={cn("flex-1 overflow-y-auto", className)}>
      <div className="space-y-4 p-4">
        {/* Render all messages */}
        {messages.map((message) => {
          if (isUserMessage(message)) {
            // Render UserBubble
            return (
              <UserBubble
                key={message.id}
                message={message.data.message || message.data.textMessage}
              />
            );
          } else {
            // Render ResponseBubble
            const systemData = getSystemMessageData(message);
            const transformedPrimitives = transformPrimitives(systemData);

            return (
              <ResponseBubble
                key={message.id}
                message={systemData.textMessage}
                primitives={transformedPrimitives}
                actions={systemData.actions}
                onActionClick={onActionClick}
              />
            );
          }
        })}

        {/* Show LoadingBars when waiting for response */}
        {isWaitingForResponse && (
          <div className="flex justify-start w-full">
            <div className="flex flex-col items-start gap-1 max-w-[80%]">
              <div className="bg-white border border-[#dee4f0] rounded-[20px] rounded-bl-[4px] px-4 py-3">
                <LoadingBars className="w-48" />
              </div>
            </div>
          </div>
        )}

        {/* Invisible element for auto-scrolling */}
        <div ref={messagesEndRef} />
      </div>
    </div>
  );
};

export default ChatMessagesView;
