import React, {
  useState,
  useRef,
  useEffect,
  type KeyboardEvent,
  type ClipboardEvent,
} from "react";
import { cn } from "../utils/cn";

interface OTPScreenProps {
  /** Optional title - when not provided, title row is hidden */
  title?: string;
  /** Number of OTP input boxes to render */
  otpLength?: number;
  /** Error message - when provided, shows red borders and error text */
  errorMessage?: string;
  /** Callback when OTP is complete */
  onComplete?: (otp: string) => void;
  /** Callback when OTP value changes */
  onChange?: (otp: string) => void;
  /** Initial OTP value */
  value?: string;
  /** Whether the OTP input is disabled */
  disabled?: boolean;
  /** Custom class name */
  className?: string;
}

const OTPScreen: React.FC<OTPScreenProps> = ({
  title = "Verification code",
  otpLength = 6,
  errorMessage,
  onComplete,
  onChange,
  value = "",
  disabled = false,
  className,
}) => {
  const [otp, setOtp] = useState<string[]>(
    Array.from({ length: otpLength }, (_, i) => value[i] || "")
  );
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Update OTP when value prop changes
  useEffect(() => {
    if (value !== undefined) {
      setOtp(Array.from({ length: otpLength }, (_, i) => value[i] || ""));
    }
  }, [value, otpLength]);

  // Focus management
  useEffect(() => {
    if (inputRefs.current[activeIndex] && !disabled) {
      inputRefs.current[activeIndex]?.focus();
    }
  }, [activeIndex, disabled]);

  const handleChange = (index: number, digit: string) => {
    if (disabled) return;

    // Only allow single digits
    if (digit.length > 1) {
      digit = digit.slice(-1);
    }

    // Only allow numbers
    if (digit && !/^\d$/.test(digit)) {
      return;
    }

    const newOtp = [...otp];
    newOtp[index] = digit;
    setOtp(newOtp);

    const otpString = newOtp.join("");
    onChange?.(otpString);

    // Move to next input if digit entered
    if (digit && index < otpLength - 1) {
      setActiveIndex(index + 1);
    }

    // Call onComplete if OTP is full
    if (otpString.length === otpLength) {
      onComplete?.(otpString);
    }
  };

  const handleKeyDown = (index: number, e: KeyboardEvent<HTMLInputElement>) => {
    if (disabled) return;

    if (e.key === "Backspace") {
      if (otp[index]) {
        // Clear current digit
        handleChange(index, "");
      } else if (index > 0) {
        // Move to previous input and clear it
        setActiveIndex(index - 1);
        handleChange(index - 1, "");
      }
    } else if (e.key === "ArrowLeft" && index > 0) {
      setActiveIndex(index - 1);
    } else if (e.key === "ArrowRight" && index < otpLength - 1) {
      setActiveIndex(index + 1);
    }
  };

  const handlePaste = (e: ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    if (disabled) return;

    const pastedText = e.clipboardData.getData("text").replace(/\D/g, "");
    const newOtp = [...otp];

    for (let i = 0; i < Math.min(pastedText.length, otpLength); i++) {
      newOtp[i] = pastedText[i];
    }

    setOtp(newOtp);
    const otpString = newOtp.join("");
    onChange?.(otpString);

    // Focus on the next empty input or last input
    const nextIndex = Math.min(pastedText.length, otpLength - 1);
    setActiveIndex(nextIndex);

    if (otpString.length === otpLength) {
      onComplete?.(otpString);
    }
  };

  const handleClick = (index: number) => {
    if (!disabled) {
      setActiveIndex(index);
    }
  };

  const handleFocus = (index: number) => {
    if (!disabled) {
      setActiveIndex(index);
    }
  };

  const isError = Boolean(errorMessage);

  return (
    <div className={cn("flex justify-center", className)}>
      <div className="flex flex-col gap-1.5 items-start">
        {/* Title - only render if provided */}
        {title && (
          <div className="font-medium text-sm leading-5 text-[#43556e] text-nowrap">
            {title}
          </div>
        )}

        {/* OTP Input Boxes */}
        <div className="flex gap-2">
          {Array.from({ length: otpLength }, (_, index) => (
            <input
              key={index}
              ref={(el) => {
                inputRefs.current[index] = el;
              }}
              type="text"
              inputMode="numeric"
              maxLength={1}
              value={otp[index]}
              disabled={disabled}
              className={cn(
                // Base styles
                "w-[54px] h-[60px] rounded-[10px] bg-white",
                "text-[#181e29] text-xl font-semibold text-center",
                "border transition-all duration-200",
                "shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]",
                "focus:outline-none focus:ring-0",

                // Border states
                isError
                  ? "border-red-500 focus:border-red-500"
                  : activeIndex === index
                    ? "border-2 border-[#4a43ca]"
                    : "border-[#dee4f0] focus:border-[#4a43ca] focus:border-2",

                // Disabled state
                disabled && "opacity-50 cursor-not-allowed bg-gray-50",

                // Hover state
                !disabled && !isError && "hover:border-[#4a43ca]/50"
              )}
              onChange={(e) => handleChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              onPaste={handlePaste}
              onClick={() => handleClick(index)}
              onFocus={() => handleFocus(index)}
              aria-label={`Digit ${index + 1} of ${otpLength}`}
              autoComplete="one-time-code"
            />
          ))}
        </div>

        {/* Error Message */}
        {isError && (
          <div className="text-red-500 text-sm leading-5 mt-1">
            {errorMessage}
          </div>
        )}
      </div>
    </div>
  );
};

export default OTPScreen;

/*
 * Usage Examples:
 *
 * import OTPScreen from './OTPScreen';
 *
 * // Basic usage with default 6 digits
 * <OTPScreen
 *   title="Enter verification code"
 *   onComplete={(otp) => console.log('OTP completed:', otp)}
 *   onChange={(otp) => console.log('OTP changed:', otp)}
 * />
 *
 * // Custom length without title
 * <OTPScreen
 *   otpLength={4}
 *   onComplete={(otp) => console.log('4-digit OTP:', otp)}
 * />
 *
 * // With error state
 * <OTPScreen
 *   title="Verification code"
 *   otpLength={6}
 *   errorMessage="Invalid verification code. Please try again."
 *   onChange={(otp) => console.log('OTP:', otp)}
 * />
 *
 * // Controlled component
 * const [otpValue, setOtpValue] = useState('');
 * <OTPScreen
 *   title="Enter OTP"
 *   value={otpValue}
 *   onChange={setOtpValue}
 *   onComplete={(otp) => submitOTP(otp)}
 * />
 *
 * // Without title (title row hidden)
 * <OTPScreen
 *   otpLength={5}
 *   onComplete={(otp) => verifyOTP(otp)}
 * />
 *
 * Design Variables (from Figma):
 * - Text Primary: #181e29
 * - Text Secondary: #43556e
 * - Border Default: #dee4f0
 * - Border Active: #4a43ca
 * - Border Error: #ef4444 (red-500)
 * - Background: #ffffff
 * - Font: Inter Semibold 20px/28px for digits
 * - Font: Inter Medium 14px/20px for title
 * - Height: 60px per input box
 * - Border Radius: 10px
 * - Gap: 8px between boxes
 */
