import React from "react";
import { cn } from "../utils/cn";

interface PillProps extends React.HTMLAttributes<HTMLDivElement> {
  // You can add any other props you need here
}

const Pill: React.FC<PillProps> = ({ className, children, ...props }) => {
  return (
    <div
      className={cn(
        "inline-flex w-fit items-center justify-start gap-1 rounded-2xl border border-[#a9dfbf] bg-[#e9f7ef] py-0.5 pl-1.5 pr-2",
        className
      )}
      {...props}
    >
      <div className="relative h-2 w-2">
        <div className="absolute left-[1px] top-[1px] h-1.5 w-1.5 rounded-full bg-[#1f8b4d]"></div>
      </div>
      <div className="text-center text-xs font-normal text-[#1f8b4d]">
        {children}
      </div>
    </div>
  );
};

export default Pill;

/*
 * Usage Example:
 *
 * import Pill from './Pill';
 *
 * const MyComponent = () => {
 *   return (
 *     <Pill>
 *       Zerodha
 *     </Pill>
 *   );
 * };
 *
 * Note: The colors and styles are based on the Figma design.
 * You can further customize them by extending your Tailwind CSS theme.
 *
 * Design Variables:
 * Text Color: #1f8b4d
 * Background Color: #e9f7ef
 * Border Color: #a9dfbf
 * Dot Color: #1f8b4d
 */
