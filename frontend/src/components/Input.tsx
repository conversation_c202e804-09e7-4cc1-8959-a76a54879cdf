import React, { useState } from "react";
import * as Popover from "@radix-ui/react-popover";
import { cn } from "../utils/cn";
import ChevronDown from "../assets/chevron-down.svg";

interface Country {
  code: string;
  name: string;
  dial_code: string;
}

const countries: Country[] = [
  { code: "IN", name: "India", dial_code: "+91" },
  { code: "US", name: "United States", dial_code: "+1" },
  { code: "GB", name: "United Kingdom", dial_code: "+44" },
  { code: "CA", name: "Canada", dial_code: "+1" },
];

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  variant?: "text" | "phone";
  label?: string;
  inputClassName?: string;
}

const Input: React.FC<InputProps> = ({
  className,
  children,
  variant = "text",
  label,
  inputClassName,
  ...props
}) => {
  const [selectedCountry, setSelectedCountry] = useState<Country>(countries[0]);

  const textInput = (
    <input
      className={cn(
        "self-stretch px-4 py-3 bg-white rounded-lg shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)] border border-gray-300 inline-flex justify-start items-center gap-2 placeholder:text-grey-400 focus:outline-none focus:ring-2 focus:ring-blue-500",
        inputClassName
      )}
      placeholder="Enter your full name"
      {...props}
    />
  );

  const phoneInput = (
    <div
      className={cn(
        "flex items-center bg-white rounded-lg border border-[#dee4f0] shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]",
        inputClassName
      )}
    >
      <Popover.Root>
        <Popover.Trigger asChild>
          <button className="group flex items-center pl-4 pr-1 py-3 focus:outline-none">
            <span className="text-[#181e29] font-medium">
              {selectedCountry.code}
            </span>
            <img
              src={ChevronDown}
              alt="chevron down"
              className="w-5 h-5 transition-transform duration-200 group-data-[state=open]:rotate-180"
            />
          </button>
        </Popover.Trigger>
        <Popover.Portal>
          <Popover.Content
            className="w-56 bg-white rounded-lg shadow-lg border border-gray-200"
            sideOffset={5}
            onOpenAutoFocus={(e) => e.preventDefault()}
          >
            <div className="p-2 max-h-60 overflow-y-auto">
              {countries.map((country) => (
                <Popover.Close
                  key={country.code}
                  className="w-full text-left"
                  asChild
                >
                  <button
                    onClick={() => setSelectedCountry(country)}
                    className={cn(
                      "w-full flex items-center justify-between px-3 py-2 text-sm rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500",
                      selectedCountry.code === country.code && "bg-gray-100"
                    )}
                  >
                    <span>{country.name}</span>
                    <span className="text-gray-500">{country.dial_code}</span>
                  </button>
                </Popover.Close>
              ))}
            </div>
          </Popover.Content>
        </Popover.Portal>
      </Popover.Root>

      <input
        className="flex-grow px-4 py-3 bg-transparent placeholder:text-grey-400 focus:outline-none"
        placeholder="9975846515"
        {...props}
      />
    </div>
  );

  return (
    <div
      className={cn(
        "self-stretch inline-flex flex-col justify-start items-start gap-2",
        className
      )}
    >
      {label && (
        <div className="inline-flex justify-start items-start gap-0.5">
          <p className="justify-start text-[#43556e] text-sm font-medium leading-tight">
            {label}
          </p>
        </div>
      )}
      {variant === "text" ? textInput : phoneInput}
    </div>
  );
};

export default Input;
