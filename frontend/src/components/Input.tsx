import React, { useState } from "react";
import * as Popover from "@radix-ui/react-popover";
import { cn } from "../utils/cn";
import ChevronDown from "../assets/chevron-down.svg";

interface Country {
  code: string;
  name: string;
  dial_code: string;
}

const countries: Country[] = [
  { code: "IN", name: "India", dial_code: "+91" },
  { code: "US", name: "United States", dial_code: "+1" },
  { code: "GB", name: "United Kingdom", dial_code: "+44" },
  { code: "CA", name: "Canada", dial_code: "+1" },
  { code: "AU", name: "Australia", dial_code: "+61" },
  { code: "DE", name: "Germany", dial_code: "+49" },
  { code: "FR", name: "France", dial_code: "+33" },
  { code: "JP", name: "Japan", dial_code: "+81" },
  { code: "SG", name: "Singapore", dial_code: "+65" },
  { code: "AE", name: "United Arab Emirates", dial_code: "+971" },
  { code: "SA", name: "Saudi Arabia", dial_code: "+966" },
  { code: "BR", name: "Brazil", dial_code: "+55" },
  { code: "MX", name: "Mexico", dial_code: "+52" },
  { code: "IT", name: "Italy", dial_code: "+39" },
  { code: "ES", name: "Spain", dial_code: "+34" },
  { code: "NL", name: "Netherlands", dial_code: "+31" },
  { code: "CH", name: "Switzerland", dial_code: "+41" },
  { code: "SE", name: "Sweden", dial_code: "+46" },
  { code: "NO", name: "Norway", dial_code: "+47" },
  { code: "DK", name: "Denmark", dial_code: "+45" },
];

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  variant?: "text" | "phone";
  label?: string;
  inputClassName?: string;
  errorMessage?: string;
}

const Input: React.FC<InputProps> = ({
  className,
  children,
  variant = "text",
  label,
  inputClassName,
  errorMessage,
  ...props
}) => {
  const [selectedCountry, setSelectedCountry] = useState<Country>(countries[0]);
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);

  const textInput = (
    <input
      className={cn(
        "self-stretch px-4 py-3 bg-white rounded-lg shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)] border inline-flex justify-start items-center gap-2 placeholder:text-grey-400 focus:outline-none focus:ring-2 focus:ring-blue-500",
        errorMessage
          ? "border-red-500 focus:border-red-500"
          : "border-gray-300 focus:border-blue-500",
        inputClassName
      )}
      placeholder="Enter your full name"
      {...props}
    />
  );

  const handlePhoneInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;

    // Extract digits, but handle case where country code might already be present
    let digitsOnly = inputValue.replace(/\D/g, "");

    // If the digits start with the country code digits, remove them to avoid duplication
    const countryCodeDigits = selectedCountry.dial_code.replace(/\D/g, "");
    if (digitsOnly.startsWith(countryCodeDigits)) {
      digitsOnly = digitsOnly.slice(countryCodeDigits.length);
    }

    // Combine country code with user input (without duplicating country code)
    const fullPhoneNumber = selectedCountry.dial_code + digitsOnly;

    // Create a new event with the full phone number
    const syntheticEvent = {
      ...e,
      target: {
        ...e.target,
        value: fullPhoneNumber,
      },
    };

    // Call the original onChange handler with the full phone number
    if (props.onChange) {
      props.onChange(syntheticEvent as React.ChangeEvent<HTMLInputElement>);
    }
  };

  const handleCountryChange = (country: Country) => {
    setSelectedCountry(country);
    setIsPopoverOpen(false);

    // If there's already a value, update it with the new country code
    if (props.value) {
      const currentValue = props.value.toString();

      // Extract digits, but properly remove the existing country code to avoid duplication
      let digitsOnly = currentValue.replace(/\D/g, "");

      // Remove the current selected country's digits if they exist at the start
      const currentCountryCodeDigits = selectedCountry.dial_code.replace(
        /\D/g,
        ""
      );
      if (digitsOnly.startsWith(currentCountryCodeDigits)) {
        digitsOnly = digitsOnly.slice(currentCountryCodeDigits.length);
      }

      const fullPhoneNumber = country.dial_code + digitsOnly;

      // Create a synthetic event to update the parent
      const syntheticEvent = {
        target: {
          value: fullPhoneNumber,
        },
      } as React.ChangeEvent<HTMLInputElement>;

      if (props.onChange) {
        props.onChange(syntheticEvent);
      }
    }
  };

  // Extract just the digits from the current value for display
  const getDisplayValue = () => {
    if (!props.value) return "";
    const currentValue = props.value.toString();

    // If the value starts with the selected country's dial code, remove it for display
    if (currentValue.startsWith(selectedCountry.dial_code)) {
      return currentValue.slice(selectedCountry.dial_code.length);
    }

    // Otherwise, just show digits
    return currentValue.replace(/\D/g, "");
  };

  const phoneInput = (
    <div
      className={cn(
        "flex items-center w-full rounded-lg border shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]",
        props.disabled
          ? "bg-gray-50 border-gray-200 cursor-not-allowed"
          : errorMessage
            ? "bg-white border-red-500"
            : "bg-white border-[#dee4f0]",
        inputClassName
      )}
    >
      <Popover.Root
        open={props.disabled ? false : isPopoverOpen}
        onOpenChange={props.disabled ? () => {} : setIsPopoverOpen}
      >
        <Popover.Trigger asChild disabled={props.disabled}>
          <button
            type="button"
            disabled={props.disabled}
            className={cn(
              "flex items-center pl-4 pr-1 py-3 focus:outline-none transition-colors",
              props.disabled
                ? "cursor-not-allowed opacity-50"
                : "hover:bg-gray-50 data-[state=open]:bg-gray-50"
            )}
          >
            <span
              className={cn(
                "font-medium mr-1",
                props.disabled ? "text-gray-400" : "text-[#181e29]"
              )}
            >
              {selectedCountry.dial_code}
            </span>
            <img
              src={ChevronDown}
              alt="chevron down"
              className={cn(
                "w-5 h-5 transition-transform duration-200",
                props.disabled ? "opacity-50" : "",
                !props.disabled && isPopoverOpen && "rotate-180"
              )}
            />
          </button>
        </Popover.Trigger>
        <Popover.Portal>
          <Popover.Content
            className="w-56 bg-white rounded-lg shadow-lg border border-gray-200 z-50 p-0"
            sideOffset={5}
            align="start"
            onOpenAutoFocus={(e) => e.preventDefault()}
          >
            <div
              className="h-48 overflow-y-auto p-2"
              style={{
                height: "192px",
                overflowY: "auto",
              }}
              onWheel={(e) => {
                e.stopPropagation();
              }}
            >
              {countries.map((country) => (
                <button
                  key={country.code}
                  type="button"
                  onClick={() => handleCountryChange(country)}
                  className={cn(
                    "w-full flex items-center justify-between px-3 py-2 text-sm rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors mb-1 last:mb-0",
                    selectedCountry.code === country.code && "bg-gray-100"
                  )}
                >
                  <span className="text-[#181e29] font-medium">
                    {country.name}
                  </span>
                  <span className="text-gray-500 text-xs">
                    {country.dial_code}
                  </span>
                </button>
              ))}
            </div>
          </Popover.Content>
        </Popover.Portal>
      </Popover.Root>

      <input
        className={cn(
          "flex-grow px-4 py-3 bg-transparent focus:outline-none",
          props.disabled
            ? "cursor-not-allowed text-gray-400 placeholder:text-gray-300"
            : "placeholder:text-grey-400"
        )}
        placeholder="9975846515"
        inputMode="numeric"
        pattern="[0-9]*"
        value={getDisplayValue()}
        onChange={handlePhoneInputChange}
        onBlur={props.onBlur}
        onFocus={props.onFocus}
        disabled={props.disabled}
        autoFocus={true}
      />
    </div>
  );

  return (
    <div
      className={cn(
        "self-stretch inline-flex flex-col justify-start items-start gap-2",
        className
      )}
    >
      {label && (
        <div className="inline-flex justify-start items-start gap-0.5">
          <p className="justify-start text-[#43556e] text-sm font-medium leading-tight">
            {label}
          </p>
        </div>
      )}
      {variant === "text" ? textInput : phoneInput}
      {errorMessage && (
        <p className="text-red-500 text-sm mt-1">{errorMessage}</p>
      )}
    </div>
  );
};

export default Input;
