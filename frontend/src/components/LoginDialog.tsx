/**
 * ===============================================================================
 * LEGACY LOGIN DIALOG COMPONENT - CURRENTLY COMMENTED OUT
 * ===============================================================================
 *
 * This component implements the original extension login flow using a modal dialog.
 *
 * ORIGINAL FLOW:
 * - Modal dialog opens within the extension
 * - User enters phone → OTP → name (if new user)
 * - Firebase operations happen in dialog context
 * - Authentication completes and dialog closes
 *
 * REPLACED BY: Secure PostMessage-based Web Login Flow
 * - Extension opens web tab for login UI
 * - All Firebase operations happen in secure extension context
 * - PostMessage communication for UI updates and user input
 * - Web tab closes after completion, extension navigates to ChatHomePage
 *
 * SECURITY BENEFITS OF NEW APPROACH:
 * - Firebase tokens never leave extension context
 * - Isolated authentication environment
 * - Better popup handling and user experience
 * - Cross-tab communication with origin validation
 *
 * STATUS: Preserved for reference and potential future use
 * DATE: January 2025
 * REASON: Enhanced security and user experience requirements
 *
 * ===============================================================================
 */

import React, { useState, useEffect } from "react";
import {
  CenteredDialog,
  CenteredDialogTrigger,
  CenteredDialogContent,
  CenteredDialogClose,
} from "./CenteredDialog";
import { useNavStore } from "../stores/navStore";
import { useAuthStore } from "../stores/authStore";
import { apiClient } from "../utils/apiClient";
import { navigate } from "../navigation/pageNavigator";
import { renderLoginStep, type LoginStep } from "./login/LoginStepRenderer";
import { getUserFriendlyErrorMessage } from "../utils/errorUtils";

interface LoginDialogProps {
  children: React.ReactNode;
}

// Firebase Authentication Functions
const sendOTP = async (phone: string): Promise<void> => {
  const authStore = useAuthStore.getState();

  try {
    // The Input component now provides the full phone number with country code
    // Basic validation: should start with + and have reasonable length
    const digitsOnly = phone.replace(/\D/g, "");
    if (
      !phone.startsWith("+") ||
      digitsOnly.length < 10 ||
      digitsOnly.length > 15
    ) {
      throw new Error("Please enter a valid phone number");
    }

    console.log("Sending OTP to:", phone);

    // Send OTP directly without reCAPTCHA (reCAPTCHA will be handled during verification if needed)
    await authStore.sendOTPWithoutRecaptcha(phone);
    console.log("Firebase OTP sent to", phone);
  } catch (error) {
    console.error("Firebase OTP send error:", error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error("Failed to send OTP. Please try again.");
  }
};

const verifyOTP = async (
  phone: string,
  otp: string
): Promise<{ user: any; userExists: boolean; sessionUserId?: string }> => {
  const authStore = useAuthStore.getState();

  try {
    // Verify OTP with Firebase
    const firebaseUser = await authStore.verifyOTP(otp);

    // Get Firebase ID token
    const idToken = await firebaseUser.getIdToken();

    // Check if user exists in our database
    const userCheckResponse = await apiClient.auth.checkUser({
      firebase_token: idToken,
    });

    console.log("User check response:", userCheckResponse);

    return {
      user: firebaseUser,
      userExists: userCheckResponse.exists,
      sessionUserId: userCheckResponse.user_id,
    };
  } catch (error) {
    console.error("Firebase OTP verification error:", error);
    throw new Error("Invalid verification code. Please try again.");
  }
};

const submitRegistration = async (
  phone: string,
  name: string
): Promise<{ sessionUserId: string }> => {
  const authStore = useAuthStore.getState();

  try {
    // Get current Firebase user and token
    const firebaseUser = authStore.firebaseUser;
    if (!firebaseUser) {
      throw new Error("No authenticated Firebase user");
    }

    const idToken = await firebaseUser.getIdToken();

    // Create user profile in our database
    const signupResponse = await apiClient.auth.signup({
      firebase_token: idToken,
      name,
      phone,
    });

    console.log("User registration successful:", signupResponse);

    return {
      sessionUserId: signupResponse.user_id || undefined, // Handle null from backend
    };
  } catch (error) {
    console.error("Registration error:", error);
    throw new Error("Registration failed. Please try again.");
  }
};

const LoginDialog: React.FC<LoginDialogProps> = ({ children }) => {
  const { setAuthenticated } = useNavStore();
  const authStore = useAuthStore();
  const [isOpen, setIsOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState<LoginStep>("phone");

  // Form data
  const [phoneNumber, setPhoneNumber] = useState("");
  const [otpValue, setOtpValue] = useState("");
  const [fullName, setFullName] = useState("");

  // Firebase user data
  const [sessionUserId, setSessionUserId] = useState<string>("");

  // Loading states
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);

  // Error states
  const [errorMessage, setErrorMessage] = useState("");

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (!isOpen) {
      setCurrentStep("phone");
      setPhoneNumber("");
      setOtpValue("");
      setFullName("");
      setSessionUserId("");
      setErrorMessage("");
      setIsLoading(false);
      setIsResending(false);
      // Cleanup Firebase auth state
      authStore.cleanupRecaptcha();
    }
  }, [isOpen]);

  // Clear error when user starts typing
  const handlePhoneNumberChange = (value: string) => {
    setPhoneNumber(value);
    if (errorMessage) setErrorMessage("");
  };

  const handleOtpChange = (value: string) => {
    setOtpValue(value);
    if (errorMessage) setErrorMessage("");
  };

  const handleNameChange = (value: string) => {
    setFullName(value);
    if (errorMessage) setErrorMessage("");
  };

  // Step 1: Send OTP
  const handleContinue = async () => {
    setIsLoading(true);
    setErrorMessage("");

    try {
      await sendOTP(phoneNumber);
      setCurrentStep("otp");
    } catch (error) {
      // Convert Firebase errors to user-friendly messages
      const errorMessage = getUserFriendlyErrorMessage(error);
      setErrorMessage(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Step 2: Verify OTP
  const handleVerify = async () => {
    setIsLoading(true);
    setErrorMessage("");

    try {
      const result = await verifyOTP(phoneNumber, otpValue);

      if (result.userExists && result.sessionUserId) {
        // Existing user - complete authentication and go to ChatHomePage
        setSessionUserId(result.sessionUserId);
        localStorage.setItem("sessionUserId", result.sessionUserId);

        // Complete authentication
        authStore.setAuthenticated(true);
        setAuthenticated(true);
        authStore.setLoading(false);

        // Close dialog and navigate to ChatHomePage
        setIsOpen(false);
        navigate();
      } else {
        // New user - go to name input step
        setCurrentStep("name");
      }
    } catch (error) {
      // Convert Firebase errors to user-friendly messages
      const errorMessage = getUserFriendlyErrorMessage(error);
      setErrorMessage(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Step 2: Resend OTP
  const handleResend = async () => {
    setIsResending(true);
    setErrorMessage("");

    try {
      await sendOTP(phoneNumber);
    } catch (error) {
      // Convert Firebase errors to user-friendly messages
      const errorMessage = getUserFriendlyErrorMessage(error);
      setErrorMessage(errorMessage);
    } finally {
      setIsResending(false);
    }
  };

  // Step 3: Submit registration (name input for new users)
  const handleSubmit = async () => {
    setIsLoading(true);
    setErrorMessage("");

    try {
      const result = await submitRegistration(phoneNumber, fullName);

      // Only store sessionUserId if it exists (existing users have it, new users get it later from WebSocket)
      if (result.sessionUserId) {
        setSessionUserId(result.sessionUserId);
        localStorage.setItem("sessionUserId", result.sessionUserId);
      }

      // Complete authentication
      authStore.setAuthenticated(true);
      setAuthenticated(true);
      authStore.setLoading(false);

      // Close dialog and navigate to ChatHomePage
      setIsOpen(false);
      navigate();
    } catch (error) {
      // Convert Firebase errors to user-friendly messages
      const errorMessage = getUserFriendlyErrorMessage(error);
      setErrorMessage(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Note: Extension flow no longer uses broker selection step

  const renderCurrentScreen = () => {
    return renderLoginStep({
      currentStep,
      isDialog: true,
      phoneNumber,
      otpValue,
      fullName,
      isLoading,
      isResending,
      errorMessage,
      onPhoneNumberChange: handlePhoneNumberChange,
      onOtpChange: handleOtpChange,
      onNameChange: handleNameChange,
      onContinue: handleContinue,
      onVerify: handleVerify,
      onSubmit: handleSubmit,
      onBrokerSubmit: () => {}, // Extension flow doesn't use broker selection
      onResend: handleResend,
    });
  };

  return (
    <>
      <CenteredDialog open={isOpen} onOpenChange={setIsOpen}>
        <CenteredDialogTrigger asChild>{children}</CenteredDialogTrigger>
        <CenteredDialogContent>{renderCurrentScreen()}</CenteredDialogContent>
      </CenteredDialog>

      {/* reCAPTCHA container - positioned to be clickable above dialog */}
      <div
        id="recaptcha-container"
        style={{
          position: "fixed",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          zIndex: 1000000, // Very high z-index to appear above everything
          pointerEvents: "auto",
          // Ensure it's always clickable
          isolation: "isolate",
        }}
      ></div>

      {/* CSS to ensure reCAPTCHA is always clickable */}
      <style>{`
        /* Force reCAPTCHA elements to be clickable even when body has pointer-events: none */
        #recaptcha-container,
        #recaptcha-container *,
        div[style*="z-index: 2000000000"],
        div[style*="z-index: 2000000000"] *,
        iframe[src*="recaptcha"],
        iframe[name*="recaptcha"],
        iframe[title*="recaptcha"],
        .grecaptcha-badge,
        .grecaptcha-badge * {
          pointer-events: auto !important;
          position: relative !important;
        }
        
        /* Specific targeting for reCAPTCHA challenge iframe */
        div[style*="position: absolute"][style*="width: 400px"][style*="height: 580px"] {
          pointer-events: auto !important;
          z-index: 1000001 !important;
        }
        
        div[style*="position: absolute"][style*="width: 400px"][style*="height: 580px"] * {
          pointer-events: auto !important;
        }
      `}</style>
    </>
  );
};

export default LoginDialog;
