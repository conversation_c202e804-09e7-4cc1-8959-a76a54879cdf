import React from "react";
import * as Accordion from "@radix-ui/react-accordion";
import { cn } from "../utils/cn";
import type { MonitoringAlert } from "../stores/dialogStore";
// import { useDialogStore } from "../stores/dialogStore"; // Commented out with stop functionality

// Import icons - these should match your assets
import TargetIcon from "../assets/target-04.svg";
import LineChartUpIcon from "../assets/line-chart-up-04.svg";
import ChevronDownIcon from "../assets/chevron-down.svg";
// import CloseIcon from "../assets/x.svg"; // Commented out with stop functionality

interface MonitoringAlertCardProps {
  alert: MonitoringAlert;
  className?: string;
}

const MonitoringAlertCard: React.FC<MonitoringAlertCardProps> = ({
  alert,
  className,
}) => {
  // const { stopAlert } = useDialogStore(); // Commented out with stop functionality

  // const handleStopAlert = () => {
  //   stopAlert(alert.id);
  // };

  const getBadgeClasses = (status: string) => {
    switch (status) {
      case "triggered":
        return "bg-green-50 text-green-600 border-green-200";
      case "stopped":
        return "bg-red-50 text-red-600 border-red-200";
      default:
        return "bg-[#f6eafc] text-[#a330e5] border-[#daacf5]";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "triggered":
        return "Triggered";
      case "stopped":
        return "Stopped";
      default:
        return `${alert.progressPercent}% to trigger reached`;
    }
  };

  return (
    <Accordion.Root
      type="single"
      collapsible
      className={cn("w-full", className)}
    >
      <Accordion.Item value={alert.id} className="border-none">
        <div className="bg-white rounded-2xl border border-[#dee4f0] overflow-hidden">
          {/* Header Section */}
          <div className="bg-[#f4f6fa] px-3 py-3">
            <p className="text-sm text-[#181e29] leading-5">
              {alert.description}
            </p>
          </div>

          {/* Main Content */}
          <div className="px-3 py-2">
            {/* Stock Symbol and Progress */}
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-semibold text-sm text-[#181e29]">
                {alert.symbol}
              </h3>
              <span className="text-xs text-[#43556e]">{alert.progress}</span>
            </div>

            {/* Price Information */}
            <div className="space-y-1 mb-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <img
                    src={TargetIcon}
                    alt="Target"
                    className="w-[18px] h-[18px]"
                  />
                  <span className="text-sm text-[#43556e]">Trigger Price</span>
                </div>
                <span className="text-sm font-medium text-[#181e29]">
                  {alert.triggerPrice}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <img
                    src={LineChartUpIcon}
                    alt="Chart"
                    className="w-[18px] h-[18px]"
                  />
                  <span className="text-sm text-[#43556e]">Current Price</span>
                </div>
                <span className="text-sm font-medium text-[#181e29]">
                  {alert.currentPrice}
                </span>
              </div>
            </div>
          </div>

          {/* Status Bar */}
          <div className="border-t border-[#dee4f0] px-3 py-3">
            <div className="flex items-center justify-between">
              <div
                className={cn(
                  "px-2 py-1 rounded-md border text-xs",
                  getBadgeClasses(alert.status)
                )}
              >
                {getStatusText(alert.status)}
              </div>

              <div className="flex items-center gap-2">
                {/* TODO: need to discuss this functionality */}
                {/* {alert.status === "pending" && (
                  <button
                    onClick={handleStopAlert}
                    className="bg-white rounded-lg border border-[#f03142] px-3 py-1 flex items-center gap-1 hover:bg-red-50 transition-colors"
                  >
                    <img src={CloseIcon} alt="Stop" className="w-4 h-4" />
                    <span className="text-sm font-medium text-[#f03142]">
                      Stop
                    </span>
                  </button>
                )} */}

                <Accordion.Trigger className="bg-white rounded-lg px-3 py-1 flex items-center gap-1 hover:bg-gray-50 transition-colors group">
                  <span className="text-sm font-medium text-[#5c54fd] group-data-[state=open]:text-[#5c54fd]">
                    <span className="group-data-[state=open]:hidden">
                      View Detail
                    </span>
                    <span className="group-data-[state=closed]:hidden">
                      Hide Detail
                    </span>
                  </span>
                  <img
                    src={ChevronDownIcon}
                    alt="Expand"
                    className="w-6 h-6 transition-transform group-data-[state=open]:rotate-180"
                  />
                </Accordion.Trigger>
              </div>
            </div>
          </div>

          {/* Expandable Content */}
          <Accordion.Content className="overflow-hidden data-[state=open]:animate-slideDown data-[state=closed]:animate-slideUp">
            <div className="px-3 py-2">
              <div className="bg-white rounded-2xl border border-[#dee4f0] p-3">
                <h4 className="font-medium text-sm text-[#181e29] mb-2">
                  Others Info
                </h4>
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-[#43556e]">Order Type</span>
                    <span className="text-sm font-medium text-[#181e29]">
                      {alert.orderType}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-[#43556e]">Stop Loss</span>
                    <span className="text-sm font-medium text-[#181e29]">
                      {alert.stopLoss}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-[#43556e]">Product</span>
                    <span className="text-sm font-medium text-[#181e29]">
                      {alert.product}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </Accordion.Content>
        </div>
      </Accordion.Item>
    </Accordion.Root>
  );
};

export default MonitoringAlertCard;
