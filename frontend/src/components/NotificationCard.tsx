import React from "react";
import { cn } from "../utils/cn";
import Card from "./Card";

// Assuming these icons exist in your assets folder.
import BookClosedIcon from "../assets/book-closed.svg";
import XIcon from "../assets/x.svg";

interface NotificationCardProps {
  variant: "positive" | "negative";
  title: string;
  time: string;
  description: string;
  actionText: string;
  onActionClick: () => void;
  className?: string;
}

const NotificationCard: React.FC<NotificationCardProps> = ({
  variant,
  title,
  time,
  description,
  actionText,
  onActionClick,
  className,
}) => {
  const isPositive = variant === "positive";

  const iconContainerClass = isPositive ? "bg-[#f6eafc]" : "bg-red-100";

  const iconClass = isPositive ? "text-[#a330e5]" : "text-red-500";

  return (
    <Card
      className={cn(
        "w-full p-0 border-0",
        isPositive ? "bg-[rgba(239,238,255,0.4)]" : "bg-white",
        className
      )}
    >
      <div className="flex gap-4 p-4">
        <div
          className={cn(
            "flex h-8 w-8 items-center justify-center rounded-full",
            iconContainerClass
          )}
        >
          <img
            src={isPositive ? BookClosedIcon : XIcon}
            alt={variant}
            className={cn("h-4 w-4", iconClass)}
            // A trick to color the SVG via filter.
            // This is not perfect but works for simple icons.
            style={{
              filter: isPositive
                ? "invert(24%) sepia(91%) saturate(4683%) hue-rotate(265deg) brightness(91%) contrast(92%)"
                : "invert(39%) sepia(74%) saturate(6015%) hue-rotate(341deg) brightness(96%) contrast(85%)",
            }}
          />
        </div>

        <div className="flex flex-1 flex-col gap-4">
          <div className="flex flex-col gap-1">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-semibold text-gray-900">{title}</h3>
              <p className="text-xs text-gray-500">{time}</p>
            </div>
            <div className="flex items-start gap-1">
              <div className="w-1 self-stretch rounded-full bg-gray-300"></div>
              <p className="flex-1 text-sm text-gray-600 truncate">
                {description}
              </p>
            </div>
          </div>
          <button
            onClick={onActionClick}
            className="text-left text-sm font-medium text-[#5c54fd] hover:underline"
          >
            {actionText}
          </button>
        </div>
      </div>
    </Card>
  );
};

export default NotificationCard;

/*
 * Usage Example:
 *
 * import NotificationCard from './NotificationCard';
 *
 * const MyComponent = () => {
 *   return (
 *     <div className="space-y-4 p-4">
 *       <NotificationCard
 *         variant="positive"
 *         title="Infosys order executed"
 *         time="14h"
 *         description="NIFTY touched 25,000. We have successfully placed 100 shares..."
 *         actionText="View Order"
 *         onActionClick={() => alert('View positive order')}
 *       />
 *       <NotificationCard
 *         variant="negative"
 *         title="Infosys order failed"
 *         time="14h"
 *         description="NIFTY touched 25,000. We have successfully placed 100 shares..."
 *         actionText="View Order"
 *         onActionClick={() => alert('View negative order')}
 *       />
 *     </div>
 *   );
 * };
 */
