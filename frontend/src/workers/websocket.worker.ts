type WorkerMessage = {
  type: "CONNECT" | "DISCONNECT" | "SEND_MESSAGE" | "NETWORK_STATUS_CHANGE";
  payload: any;
};

type MainThreadMessage = {
  type: "CONNECTION_STATUS" | "WEBSOCKET_MESSAGE" | "ERROR";
  payload: any;
};

// Declare Vite-defined variables
declare const __WS_URL__: string;
declare const __WS_CHAT_ENDPOINT__: string;

// Read from Vite-defined variables
const wsUrl = __WS_URL__;
const wsEndpoint = __WS_CHAT_ENDPOINT__;

// Check if we're in mock mode - FORCE TO FALSE FOR DEBUGGING
const isMockMode = false; // Always use real WebSocket, never mock

class WebSocketWorker {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectTimeout = 1000;
  private isReconnecting = false;
  private userId: string = "";
  private firebaseToken: string | null = null;
  private isConnected = false;
  private messageQueue: any[] = [];
  private lastMessageTime: any | null = null;
  private lastNetworkStatus: boolean | null = null;
  private currentTab: "chat" | "orders" | "monitoring" = "chat";

  constructor() {
    this.lastNetworkStatus = navigator.onLine;
    self.onmessage = this.handleMessage.bind(this);
    console.log("🚀 [Worker] WebSocket Worker STARTED successfully!");
    console.log("[Worker] Initialized with:", { wsUrl, wsEndpoint });
    console.log("[Worker] Ready to receive messages from main thread");
  }

  private handleMessage(event: MessageEvent<WorkerMessage>) {
    const { type, payload } = event.data;
    console.log("[Worker] Received message:", { type, payload });

    switch (type) {
      case "CONNECT":
        this.userId = payload.userId;
        this.firebaseToken = payload.token || null;
        console.log("[Worker] Connecting with userId and token:", {
          userId: this.userId,
          hasToken: !!this.firebaseToken,
        });
        this.connect();
        break;

      case "DISCONNECT":
        console.log("[Worker] Disconnecting");
        this.disconnect();
        break;

      case "SEND_MESSAGE":
        console.log("[Worker] Sending message:", payload);
        // Update current tab for error tracking
        if (payload.typeOfMessage) {
          this.currentTab = payload.typeOfMessage;
        }
        if (!this.isConnected) {
          console.log("[Worker] Not connected, queueing message");
          this.messageQueue.push(payload);
          this.connect(); // Try to reconnect
        } else {
          this.sendMessage(payload);
        }
        break;

      case "NETWORK_STATUS_CHANGE":
        console.log("[Worker] Network status changed:", payload.isOnline);

        // Only process if the status actually changed
        if (this.lastNetworkStatus === payload.isOnline) {
          console.log("[Worker] Network status unchanged, skipping");
          return;
        }

        this.lastNetworkStatus = payload.isOnline;

        if (
          payload.isOnline &&
          !this.isConnected &&
          !this.isReconnecting &&
          this.ws?.readyState !== WebSocket.CONNECTING
        ) {
          console.log(
            "[Worker] Network is back online, attempting to reconnect"
          );
          this.connect();
        } else if (!payload.isOnline) {
          console.log("[Worker] Network is offline");
          this.isConnected = false;
        }
        break;
    }
  }

  private connect() {
    // If in mock mode, don't attempt WebSocket connection
    if (isMockMode) {
      console.log("[Worker] Mock mode enabled, skipping WebSocket connection");
      this.postMessage({
        type: "CONNECTION_STATUS",
        payload: { isConnected: false, mockMode: true },
      });
      return;
    }

    try {
      if (this.ws) {
        console.log("[Worker] WebSocket already exists, checking state...");
        if (this.ws.readyState === WebSocket.OPEN && this.isConnected) {
          console.log("[Worker] WebSocket already connected");
          return;
        }
        if (this.ws.readyState === WebSocket.CONNECTING) {
          console.log("[Worker] WebSocket is already connecting, skipping");
          return;
        }
        // If not open, close it so we can create a new one
        this.ws.close();
        this.ws = null;
      }

      // Build WebSocket URL with Firebase token if available
      let fullUrl = `${wsUrl}${wsEndpoint}?user_id=${this.userId}`;
      if (this.firebaseToken) {
        fullUrl += `&token=${encodeURIComponent(this.firebaseToken)}`;
      }
      console.log(
        "[Worker] Connecting to:",
        fullUrl.replace(/token=[^&]+/, "token=***")
      );

      this.ws = new WebSocket(fullUrl);

      this.ws.onopen = () => {
        console.log("[Worker] WebSocket connected");
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.reconnectTimeout = 1000;
        this.isReconnecting = false;

        this.postMessage({
          type: "CONNECTION_STATUS",
          payload: { isConnected: true },
        });

        // Process any queued messages
        while (this.messageQueue.length > 0) {
          const message = this.messageQueue.shift();
          if (message) {
            this.sendMessage(message);
          }
        }
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log("[Worker] Received WebSocket message:", data);

          // Check if this is an error response from the backend
          if (data.error || data.status === "error") {
            console.log("[Worker] Backend error detected:", data);
            this.postMessage({
              type: "ERROR",
              payload: {
                message: data.error || "Backend error occurred",
                details: data.details || data,
                tab: this.currentTab, // Include current tab for error routing
              },
            });
          } else {
            // Include the current tab information in the response
            this.postMessage({
              type: "WEBSOCKET_MESSAGE",
              payload: {
                ...data,
                typeOfMessage: this.currentTab,
              },
            });
          }
        } catch (error) {
          console.error("[Worker] Failed to parse message:", error);
          this.postMessage({
            type: "ERROR",
            payload: {
              message: "Failed to parse message",
              tab: this.currentTab,
            },
          });
        }
      };

      this.ws.onclose = (event) => {
        console.log("[Worker] WebSocket closed:", event);
        this.isConnected = false;

        this.postMessage({
          type: "CONNECTION_STATUS",
          payload: { isConnected: false, code: event.code },
        });

        // Only attempt to reconnect if it wasn't a clean close
        if (!event.wasClean && !this.isReconnecting) {
          this.attemptReconnect();
        }
      };

      this.ws.onerror = (error) => {
        console.error("[Worker] WebSocket error:", error);

        // Only send error if we're not already connected or connecting
        if (!this.isConnected && this.ws?.readyState !== WebSocket.OPEN) {
          this.isConnected = false;
          this.postMessage({
            type: "ERROR",
            payload: {
              message: "WebSocket connection failed",
              url: `${wsUrl}${wsEndpoint}`,
              timestamp: Date.now(),
              connectionAttempt: this.reconnectAttempts + 1,
              tab: this.currentTab,
              details: {
                readyState: this.ws?.readyState,
                readyStateText: this.ws
                  ? this.ws.readyState === WebSocket.CONNECTING
                    ? "CONNECTING"
                    : this.ws.readyState === WebSocket.OPEN
                      ? "OPEN"
                      : this.ws.readyState === WebSocket.CLOSING
                        ? "CLOSING"
                        : this.ws.readyState === WebSocket.CLOSED
                          ? "CLOSED"
                          : "UNKNOWN"
                  : "UNKNOWN",
                isOnline: navigator.onLine,
                userId: this.userId || "",
                lastMessageTime: this.lastMessageTime || null,
              },
            },
          });
        }
      };
    } catch (error) {
      console.error("[Worker] Connection error:", error);
      this.isConnected = false;
      this.postMessage({
        type: "ERROR",
        payload: {
          message:
            error instanceof Error
              ? error.message
              : "Connection error occurred",
          timestamp: Date.now(),
          tab: this.currentTab,
        },
      });
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log("[Worker] Max reconnection attempts reached");
      this.postMessage({
        type: "CONNECTION_STATUS",
        payload: {
          isConnected: false,
          message: "Max reconnection attempts reached",
        },
      });
      return;
    }

    this.isReconnecting = true;
    this.reconnectAttempts++;

    console.log(
      `[Worker] Attempting reconnect ${this.reconnectAttempts}/${this.maxReconnectAttempts}`
    );
    this.postMessage({
      type: "CONNECTION_STATUS",
      payload: {
        isConnected: false,
        attempt: this.reconnectAttempts,
      },
    });

    setTimeout(() => {
      this.connect();
      this.reconnectTimeout *= 2; // Exponential backoff
    }, this.reconnectTimeout);
  }

  private sendMessage(message: any) {
    if (!this.ws) {
      console.log("[Worker] No WebSocket instance");
      this.messageQueue.push(message);
      this.connect();
      return;
    }

    if (!this.isConnected || this.ws.readyState !== WebSocket.OPEN) {
      console.log(
        "[Worker] WebSocket not connected, readyState:",
        this.ws.readyState,
        "isConnected:",
        this.isConnected
      );

      // Queue the message
      console.log("[Worker] Queueing message");
      this.messageQueue.push(message);

      // If we're CONNECTING, just wait for onopen
      if (this.ws.readyState === WebSocket.CONNECTING) {
        console.log("[Worker] WebSocket is connecting, message queued");
        return;
      }

      // If CLOSED or CLOSING, try to reconnect
      if (this.ws.readyState >= WebSocket.CLOSING) {
        console.log(
          "[Worker] WebSocket is closed/closing, attempting to reconnect..."
        );
        this.connect();
      }
      return;
    }

    try {
      console.log("[Worker] Sending WebSocket message:", message);
      this.ws.send(JSON.stringify(message));
    } catch (error) {
      console.error("[Worker] Send error:", error);
      this.messageQueue.push(message);
      this.postMessage({
        type: "ERROR",
        payload: {
          message:
            error instanceof Error ? error.message : "Send error occurred",
          timestamp: Date.now(),
          tab: this.currentTab,
        },
      });
    }
  }

  private disconnect() {
    if (this.ws) {
      console.log("[Worker] Closing WebSocket connection");
      this.ws.close(1000, "Normal closure");
      this.ws = null;
    }
    this.isConnected = false;
    this.isReconnecting = false;
    this.userId = "";
    this.messageQueue = [];
  }

  private postMessage(message: MainThreadMessage) {
    console.log("[Worker] Posting message to main thread:", message);
    (self as any).postMessage(message);
  }
}

// Initialize the worker
new WebSocketWorker();

// Export empty object to make it a module
export {};
