// Testing Configuration
// Configuration for testing the PouchDB sync system

export interface TestConfig {
  // Test Firebase UID for testing
  testFirebaseUID: string;

  // Single testing flag
  useTestUID: boolean;
}

// Test Firebase UID - Replace this with your actual test UID
export const TEST_FIREBASE_UID = "test_user_1234567890abcdef";

// Default testing configuration
export const defaultTestConfig: TestConfig = {
  testFirebaseUID: TEST_FIREBASE_UID,
  useTestUID: false, // Set to true to use test Firebase UID
};

// Simple test configuration
export const testConfig: TestConfig = {
  testFirebaseUID: TEST_FIREBASE_UID,
  useTestUID: false, // Set to true to use test Firebase UID
};

// Helper functions for testing
export const getTestFirebaseUID = (): string => {
  if (testConfig.useTestUID) {
    console.log(
      "🧪 [TEST_CONFIG] Using test Firebase UID:",
      testConfig.testFirebaseUID
    );
    return testConfig.testFirebaseUID;
  }
  return "";
};

export const shouldSkipAuthValidation = (): boolean => {
  return testConfig.useTestUID;
};

export const logTestConfig = (): void => {
  if (testConfig.useTestUID) {
    console.log(
      "🧪 [TEST_CONFIG] Using test Firebase UID:",
      testConfig.testFirebaseUID
    );
  }
};
