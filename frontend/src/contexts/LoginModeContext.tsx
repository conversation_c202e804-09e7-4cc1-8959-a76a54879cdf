import React, { createContext, useContext } from "react";
import { getLoginMode, type LoginMode } from "../utils/extensionDetector";

const LoginModeContext = createContext<LoginMode>("extension");

export const LoginModeProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const mode = getLoginMode();
  return (
    <LoginModeContext.Provider value={mode}>
      {children}
    </LoginModeContext.Provider>
  );
};

export const useLoginMode = () => useContext(LoginModeContext);
