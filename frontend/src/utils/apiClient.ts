import { getAuthToken } from "../stores/authStore";

// API Configuration
const API_CONFIG = {
  baseUrl: __USE_MOCK__
    ? "/assets/mock-data"
    : import.meta.env.VITE_API_BASE_URL || "http://localhost:8000",

  endpoints: {
    notifications:
      import.meta.env.VITE_NOTIFICATIONS_ENDPOINT || "/api/v1/notifications",
    profile: import.meta.env.VITE_PROFILE_ENDPOINT || "/api/v1/profile",
    monitoring:
      import.meta.env.VITE_MONITORING_ENDPOINT ||
      "/api/v1/monitoring/instances",
    orders: import.meta.env.VITE_ORDERS_ENDPOINT || "/api/v1/orders",
    chat: import.meta.env.VITE_CHAT_ENDPOINT || "/api/v1/chatHistory",
    health: import.meta.env.VITE_HEALTH_ENDPOINT || "/api/v1/health",
    // Auth endpoints
    auth: {
      signup: "/api/v1/auth/signup",
      checkUser: "/api/v1/auth/check-user",
      profile: "/api/v1/auth/profile",
    },
  },
};

// Helper function to build endpoint URL
function buildEndpointUrl(endpointPath: string): string {
  const baseUrl = API_CONFIG.baseUrl;

  if (__USE_MOCK__) {
    // For mock mode, append .json extension
    return `${baseUrl}${endpointPath}.json`;
  } else {
    // For real API, use the endpoint as-is
    return `${baseUrl}${endpointPath}`;
  }
}

// Helper function to get authenticated headers
async function getAuthHeaders(): Promise<HeadersInit> {
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  };

  if (!__USE_MOCK__) {
    try {
      const token = await getAuthToken();
      if (token) {
        headers["Authorization"] = `Bearer ${token}`;
      }
    } catch (error) {
      console.warn("Failed to get auth token:", error);
    }
  }

  return headers;
}

// Generic fetch function
export async function fetchData(endpointPath: string) {
  const fullUrl = buildEndpointUrl(endpointPath);
  console.log(`Fetching data from: ${fullUrl} (Mock: ${__USE_MOCK__})`);

  try {
    const res = await fetch(fullUrl);
    if (!res.ok) {
      throw new Error(`HTTP error! status: ${res.status}`);
    }
    return res.json();
  } catch (error) {
    console.error(`Failed to fetch from ${fullUrl}:`, error);
    throw error;
  }
}

// Generic POST function
export async function postData<T = any>(
  endpoint: string,
  data: any,
  requireAuth: boolean = true
): Promise<T> {
  const fullUrl = buildEndpointUrl(endpoint);
  console.log(`Posting data to: ${fullUrl} (Mock: ${__USE_MOCK__})`);

  if (__USE_MOCK__) {
    console.log("Mock: POST request", { endpoint, data });
    // For mock mode, return a mock response
    return Promise.resolve({
      success: true,
      user_id: "mock-user-id",
      conversation_id: "mock-conversation-id",
      history: [],
    } as T);
  }

  try {
    const headers = requireAuth
      ? await getAuthHeaders()
      : { "Content-Type": "application/json" };

    const res = await fetch(fullUrl, {
      method: "POST",
      headers,
      body: JSON.stringify(data),
    });

    // Handle 403 Forbidden - redirect to login
    if (res.status === 403) {
      console.warn("Authentication failed, redirecting to login");
      // Clear auth state and redirect
      window.location.href = "/login";
      throw new Error("Authentication failed");
    }

    if (!res.ok) {
      throw new Error(`HTTP error! status: ${res.status}`);
    }

    return res.json();
  } catch (error) {
    console.error(`Failed to post to ${fullUrl}:`, error);
    throw error;
  }
}

// Specific API functions
export const apiClient = {
  // Generic POST method
  post: postData,

  // Authentication
  auth: {
    signup: (data: { firebase_token: string; name: string; phone?: string }) =>
      postData(API_CONFIG.endpoints.auth.signup, data, false),

    checkUser: (data: { firebase_token: string }) =>
      postData(API_CONFIG.endpoints.auth.checkUser, data, false),

    getProfile: () => fetchData(API_CONFIG.endpoints.auth.profile),

    updateProfile: (data: { name?: string; phone?: string }) =>
      postData(API_CONFIG.endpoints.auth.profile, data, true),
  },

  // Notifications
  getNotifications: () => fetchData(API_CONFIG.endpoints.notifications),

  // Profile
  getProfile: () => fetchData(API_CONFIG.endpoints.profile),
  updateProfile: (data: any) => {
    if (__USE_MOCK__) {
      console.log("Mock: Profile update", data);
      return Promise.resolve({ success: true });
    }
    return fetch(buildEndpointUrl(API_CONFIG.endpoints.profile), {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(data),
    }).then((res) => res.json());
  },

  // Monitoring
  getMonitoring: () => fetchData(API_CONFIG.endpoints.monitoring),

  // Orders
  getOrders: () => fetchData(API_CONFIG.endpoints.orders),

  // Chat
  sendChatMessage: (message: string) => {
    if (__USE_MOCK__) {
      console.log("Mock: Chat message", message);
      return Promise.resolve({ success: true, message: "Message sent" });
    }
    return fetch(buildEndpointUrl(API_CONFIG.endpoints.chat), {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ message }),
    }).then((res) => res.json());
  },

  // Health check
  healthCheck: () => fetchData(API_CONFIG.endpoints.health),
};

// Export individual functions for backward compatibility
export { fetchData as default };
