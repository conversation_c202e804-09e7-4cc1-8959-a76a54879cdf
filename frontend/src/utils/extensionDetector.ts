export function isRunningInExtension(): boolean {
  // Check for extension-specific APIs
  if (
    typeof (window as any).chrome !== "undefined" &&
    (window as any).chrome.runtime &&
    (window as any).chrome.runtime.id
  ) {
    return true;
  }

  // Check for extension-specific URL patterns
  const url = window.location.href;
  if (
    url.startsWith("chrome-extension://") ||
    url.startsWith("moz-extension://")
  ) {
    return true;
  }

  // Check for extension-injected variables
  if ((window as any).__EXTENSION_CONTEXT__) {
    return true;
  }

  return false;
}

export type LoginMode = "extension" | "web";

export function getLoginMode(): LoginMode {
  // PRIORITY 1: Check URL parameter for extension mode override
  // When extension opens a web tab with ?mode=extension, we want to show WebLoginPage
  const urlParams = new URLSearchParams(window.location.search);
  const urlMode = urlParams.get("mode");

  if (urlMode === "extension") {
    console.log(
      "[LoginMode] URL parameter ?mode=extension detected → using web mode for WebLoginPage"
    );
    return "web"; // Show WebLoginPage when opened from extension
  }

  // PRIORITY 2: Check if we have a global login mode variable (from vite config)
  const mode = (globalThis as any).__LOGIN_MODE__;

  switch (mode) {
    case "extension":
      return "extension";
    case "web":
      return "web";
    case "auto":
      return isRunningInExtension() ? "extension" : "web";
    default:
      return "extension"; // Default to extension mode
  }
}
