// Session management utility for handling user sessions across different brokers and types
import { apiClient } from "./apiClient";
import {
  mapBackendChatHistoryResponse,
  type BackendChatHistoryResponse,
} from "./apiMappers";
import { useWebSocketStore } from "../stores/websocketStore";

export interface SessionData {
  user_id: string;
  conversations: {
    [brokerName: string]: {
      [type: string]: string; // conversation_id
    };
  };
}

export interface ChatHistoryRequest {
  user_id: string | null;
  conversation_id: string | null;
  type: "chat" | "orders" | "monitoring";
  brokerName: "zerodha" | "groww" | "upstox";
}

export interface ChatHistoryResponse {
  user_id: string;
  conversation_id: string;
  history: Array<{
    id: string;
    timestamp: number;
    data: any;
  }>;
}

// Check if we have valid session IDs for a specific broker + type
export function hasValidSession(
  brokerName: string,
  type: "chat" | "orders" | "monitoring"
): boolean {
  const user_id = localStorage.getItem("user_id");
  if (!user_id) {
    return false;
  }

  // Get conversations from localStorage
  const conversationsStr = localStorage.getItem("conversations");
  const conversations = conversationsStr ? JSON.parse(conversationsStr) : {};

  // Check if conversation_id exists in nested structure
  const conversation_id = conversations?.[brokerName]?.[type];
  return !!conversation_id;
}

// Get session IDs for a specific broker + type
export function getSessionIds(
  brokerName: string,
  type: "chat" | "orders" | "monitoring"
): { user_id: string; conversation_id: string } | null {
  // 🔧 FIX: Read from WebSocket store first for user_id (immediate updates)
  const wsStore = useWebSocketStore.getState();
  let user_id = wsStore.user_id;

  console.log(
    `[getSessionIds] 🔍 Reading user_id from WebSocket store for ${brokerName}/${type}:`,
    user_id || "(empty)"
  );
  console.log(
    `[getSessionIds] 🔍 WebSocket store full state:`,
    JSON.stringify(wsStore, null, 2)
  );

  // Fallback to localStorage if store user_id is empty
  if (!user_id) {
    user_id = localStorage.getItem("user_id") || "";
    console.log(
      `[getSessionIds] 📦 Fallback user_id from localStorage:`,
      user_id || "(empty)"
    );
  }

  console.log(
    `[getSessionIds] 🔍 Final user_id being used:`,
    user_id || "(empty)"
  );

  // 🔧 FIX: Always get conversation_id from localStorage for the specific type
  // The WebSocket store only holds one conversation_id, but we need type-specific ones
  const conversationsStr = localStorage.getItem("conversations");
  const conversations = conversationsStr ? JSON.parse(conversationsStr) : {};
  const conversation_id = conversations?.[brokerName]?.[type] || "";

  console.log(
    `[getSessionIds] 🗃️ Reading conversation_id from localStorage for ${brokerName}/${type}:`,
    conversation_id || "(empty)"
  );
  console.log(
    `[getSessionIds] 📋 Full conversations structure:`,
    JSON.stringify(conversations, null, 2)
  );

  if (!user_id || !conversation_id) {
    console.log(
      `[getSessionIds] Missing session data for ${brokerName}/${type} - user_id: ${user_id}, conversation_id: ${conversation_id}`
    );
    return null;
  }

  console.log(
    `[getSessionIds] ✅ Retrieved session IDs for ${brokerName}/${type} - user_id: ${user_id}, conversation_id: ${conversation_id}`
  );

  const result = {
    user_id,
    conversation_id,
  };

  console.log(
    `[getSessionIds] 🚀 Final return value:`,
    JSON.stringify(result, null, 2)
  );
  return result;
}

// Load chat history for existing sessions (only when both user_id and conversation_id exist)
export async function loadChatHistory(
  brokerName: "zerodha" | "groww" | "upstox",
  type: "chat" | "orders" | "monitoring"
): Promise<{
  user_id: string;
  conversation_id: string;
  history: any[];
} | null> {
  const sessionIds = getSessionIds(brokerName, type);

  // Only proceed if both user_id and conversation_id exist
  if (!sessionIds || !sessionIds.user_id || !sessionIds.conversation_id) {
    console.log(
      `No existing session found for ${brokerName} ${type}, skipping chat history load`
    );
    return null;
  }

  // Prepare request with existing session IDs
  const request: ChatHistoryRequest = {
    user_id: sessionIds.user_id,
    conversation_id: sessionIds.conversation_id,
    type,
    brokerName,
  };

  try {
    // Make API call to load chat history using configured chat endpoint
    const chatEndpoint =
      import.meta.env.VITE_CHAT_ENDPOINT || "/api/v1/chatHistory";
    const backendResponse = await apiClient.post<BackendChatHistoryResponse>(
      chatEndpoint,
      request
    );

    // Map backend response to frontend format
    const response = mapBackendChatHistoryResponse(backendResponse);

    console.log(`Chat history loaded for ${brokerName} ${type}:`, {
      historyCount: response.history?.length || 0,
    });

    return {
      user_id: response.user_id,
      conversation_id: response.conversation_id,
      history: response.history || [],
    };
  } catch (error) {
    console.error(
      `Error loading chat history for ${brokerName} ${type}:`,
      error
    );
    throw error;
  }
}

// Get current broker (this should be enhanced to get from user preferences)
export function getCurrentBroker(): "zerodha" | "groww" | "upstox" {
  // For now, default to zerodha - this should be configurable
  return "zerodha";
}
