// Simple UUID generator for message IDs
function generateId(): string {
  return "msg_" + Date.now() + "_" + Math.random().toString(36).substr(2, 9);
}

// Backend API Types
export interface BackendWebSocketResponse {
  textMessage: string;
  messageType:
    | "order_confirmation"
    | "monitor_order"
    | "chat_response"
    | "order_planning";
  primitives: Array<{
    action?: string;
    arguments?: Record<string, any>;
    human_friendly_explanation?: string;
    need_more_info?: string[];
    clarification?: string;
  }>;
  sender: "system";
  actions: Array<{
    description: string;
    type: "orders" | "monitoring" | "chat";
    message: string;
  }>;
  user_id: string;
  conversation_id: string;
}

export interface BackendChatHistoryResponse {
  user_id: string;
  conversation_id: string;
  type: "orders" | "monitoring" | "chat";
  brokerName: "zerodha" | "upstox" | "angelone" | "fyers" | "aliceblue";
  history: Array<{
    sender: "user" | "system";
    textMessage: string;
    timestamp: string; // ISO string
    messageType?: "order_confirmation" | "monitor_order" | "chat_response";
    primitives?: Array<{
      action?: string;
      arguments?: Record<string, any>;
      human_friendly_explanation?: string;
      need_more_info?: string[];
      clarification?: string;
    }>;
    actions?: Array<{
      description: string;
      type: "orders" | "monitoring" | "chat";
      message: string;
    }>;
  }>;
}

// Frontend Types (existing)
export interface WebSocketResponse {
  message: string;
  sender: "system";
  user_id?: string;
  conversation_id?: string;
  messageType?:
    | "order_confirmation"
    | "monitor_order"
    | "chat_response"
    | "order_planning";
  primitives?: Array<{
    action?: string;
    arguments?: Record<string, any>;
    human_friendly_explanation?: string;
    need_more_info?: string[];
    clarification?: string;
  }>;
  actions?: Array<{
    description: string;
    type: "orders" | "monitoring" | "chat";
    message: string;
  }>;
}

export interface ChatHistoryResponse {
  user_id: string;
  conversation_id: string;
  history: Array<{
    id: string;
    timestamp: number;
    data: {
      message: string;
      sender: "user" | "system";
      messageType?: string;
      primitives?: string[];
      actions?: Array<{
        description: string;
        type: "orders" | "monitoring" | "chat";
        message: string;
      }>;
    };
  }>;
}

// Mappers
export function mapBackendWebSocketResponse(
  backendResponse: BackendWebSocketResponse
): WebSocketResponse {
  return {
    message: backendResponse.textMessage,
    sender: backendResponse.sender,
    user_id: backendResponse.user_id,
    conversation_id: backendResponse.conversation_id,
    messageType: backendResponse.messageType,
    primitives: backendResponse.primitives,
    actions: backendResponse.actions,
  };
}

export function mapBackendChatHistoryResponse(
  backendResponse: BackendChatHistoryResponse
): ChatHistoryResponse {
  console.log("Mapping chat history response:", backendResponse);
  return {
    user_id: backendResponse.user_id,
    conversation_id: backendResponse.conversation_id,
    history: backendResponse.history.map((historyItem) => {
      // Process primitives with the same logic as WebSocket response
      const processedPrimitives = (historyItem.primitives || []).map(
        (primitive) => {
          if (typeof primitive === "object" && primitive !== null) {
            // Always prefer human_friendly_explanation if available, fallback to clarification
            const explanation = (
              primitive as { human_friendly_explanation?: string }
            ).human_friendly_explanation;
            if (explanation) return explanation;

            const clarification = (primitive as { clarification?: string })
              .clarification;
            if (clarification) return clarification;

            return "";
          }
          return String(primitive);
        }
      );

      // Filter out empty strings from primitives
      const filteredPrimitives = processedPrimitives.filter(
        (str) => str.length > 0
      );

      const mappedMessage = {
        id: generateId(),
        timestamp: new Date(historyItem.timestamp || Date.now()).getTime(),
        data: {
          message: historyItem.textMessage, // For UI compatibility
          textMessage: historyItem.textMessage, // Original field
          sender: historyItem.sender,
          messageType: historyItem.messageType,
          primitives:
            filteredPrimitives.length > 0 ? filteredPrimitives : undefined,
          actions: historyItem.actions,
        },
      };
      console.log("Mapped message:", mappedMessage);
      return mappedMessage;
    }),
  };
}

// WebSocket URL builder for backend format
export function buildWebSocketURL(
  baseUrl: string,
  endpoint: string,
  userId?: string
): string {
  const wsUrl = `${baseUrl}${endpoint}`;
  return userId ? `${wsUrl}?user_id=${userId}` : wsUrl;
}
