// Navigation manager that handles session initialization for tabs
import {
  hasValidSession,
  loadChatHistory,
  getCurrentBroker,
} from "./sessionManager";
import { useWebSocketStore } from "../stores/websocketStore";
import { useSidebarStore } from "../stores/sidebarStore";

export interface NavigationResult {
  success: boolean;
  error?: string;
  sessionInitialized?: boolean;
}

// Navigate to a tab and ensure session is initialized
export async function navigateToTab(
  tab: "chat" | "orders" | "monitoring"
): Promise<NavigationResult> {
  // Set active tab first
  const sidebarStore = useSidebarStore.getState();
  sidebarStore.setActiveTab(tab);

  // Return success without initializing session (handled by ChatHomePage)
  return { success: true, sessionInitialized: false };
}

// Check if all required sessions are initialized
export function checkSessionStatus(): {
  chat: boolean;
  orders: boolean;
  monitoring: boolean;
} {
  const currentBroker = getCurrentBroker();

  return {
    chat: hasValidSession(currentBroker, "chat"),
    orders: hasValidSession(currentBroker, "orders"),
    monitoring: hasValidSession(currentBroker, "monitoring"),
  };
}

// Preload sessions for all tabs (optional optimization)
export async function preloadAllSessions(): Promise<{
  chat: NavigationResult;
  orders: NavigationResult;
  monitoring: NavigationResult;
}> {
  const [chatResult, ordersResult, monitoringResult] = await Promise.all([
    initializeTabSession("chat"),
    initializeTabSession("orders"),
    initializeTabSession("monitoring"),
  ]);

  return {
    chat: chatResult,
    orders: ordersResult,
    monitoring: monitoringResult,
  };
}

// Handle new chat functionality - clears current tab messages and conversation_id
export async function handleNewChat(): Promise<NavigationResult> {
  const { activeTab } = useSidebarStore.getState();

  // Determine the current tab type
  const tabType =
    activeTab === "orders"
      ? "orders"
      : activeTab === "monitoring"
        ? "monitoring"
        : "chat";

  try {
    console.log(`Starting new chat for ${tabType} tab`);

    const websocketStore = useWebSocketStore.getState();

    // Clear messages for current tab and conversation_id from localStorage
    websocketStore.clearCurrentTabMessages(tabType);

    console.log(`New chat initialized for ${tabType}:`, {
      messagesCleared: true,
      conversationIdCleared: true,
      userIdPreserved: true,
      readyForFirstMessage: true,
    });

    return {
      success: true,
      sessionInitialized: false, // Will be created on next WebSocket message
    };
  } catch (error) {
    console.error(`Failed to start new chat for ${tabType}:`, error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to start new chat",
      sessionInitialized: false,
    };
  }
}

// Load chat history for a tab if session exists (no session creation)
export async function initializeTabSession(
  tab: "chat" | "orders" | "monitoring"
): Promise<NavigationResult> {
  const currentBroker = getCurrentBroker();

  try {
    // Check if session exists and load history
    if (hasValidSession(currentBroker, tab)) {
      console.log(
        `[NavigationManager] Loading chat history for existing session: ${currentBroker} ${tab}`
      );

      // Update WebSocket store with existing session
      const websocketStore = useWebSocketStore.getState();
      websocketStore.updateSessionForTab(tab);

      // Clear existing messages before loading history
      websocketStore.clearMessages(tab);

      // Load chat history if both IDs exist
      try {
        const historyResult = await loadChatHistory(currentBroker, tab);

        if (historyResult) {
          // Convert history to MessageHistory format
          const convertedHistory = historyResult.history.map((item: any) => ({
            id: item.id,
            timestamp: item.timestamp,
            data: item.data,
          }));

          // Update WebSocket store with chat history
          convertedHistory.forEach((message) => {
            websocketStore.addMessage(message, tab);
          });

          console.log(
            `[NavigationManager] Chat history loaded for ${currentBroker} ${tab}:`,
            {
              historyCount: convertedHistory.length,
            }
          );
        }
      } catch (error) {
        console.warn(
          `[NavigationManager] Failed to load chat history for ${currentBroker} ${tab}:`,
          error
        );
        // Continue anyway - user can start fresh conversation
      }

      return { success: true, sessionInitialized: false };
    }

    // No session exists - user will get session IDs from WebSocket when they send first message
    console.log(
      `[NavigationManager] No session found for ${currentBroker} ${tab} - will be created on first message`
    );
    return { success: true, sessionInitialized: false };
  } catch (error) {
    console.error(
      `[NavigationManager] Failed to initialize tab session for ${currentBroker} ${tab}:`,
      error
    );
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      sessionInitialized: false,
    };
  }
}
