import { useDialogStore, type Dialog<PERSON>age<PERSON>ey } from "../stores/dialogStore";

// Result returned by each dialog navigation case
interface DialogNavResult {
  actions?: Array<() => void>;
  target: DialogPageKey;
}

// Evaluate current dialog page and decide next based on conditions
export function getDialogNavigationOutcome(): DialogNavResult | null {
  const dialog = useDialogStore.getState();
  const current = dialog.currentPage;

  if (!current) return null;

  switch (current) {
    case "all-monitoring":
      // All Monitoring page - let the component handle its own PouchDB data loading
      return {
        actions: [
          () => {
            console.log(
              "Opening monitoring dialog - component will handle PouchDB data loading"
            );
          },
        ],
        target: "all-monitoring",
      };

    case "all-orders":
      // All Orders page - let the component handle its own PouchDB data loading
      return {
        actions: [
          () => {
            console.log(
              "Opening orders dialog - component will handle PouchDB data loading"
            );
          },
        ],
        target: "all-orders",
      };

    case "edit-profile":
      // Edit Profile page - no additional data loading needed
      return {
        actions: [
          () => {
            console.log("Opening edit profile page");
          },
        ],
        target: "edit-profile",
      };

    default:
      return null;
  }
}

// Helper function to execute data loading for the current page
function executePageActions(): void {
  const outcome = getDialogNavigationOutcome();
  if (outcome) {
    outcome.actions?.forEach((fn) => fn());
  }
}

// Open dialog with specific page and load data
export function openDialog(page: DialogPageKey): void {
  const dialog = useDialogStore.getState();
  dialog.openDialog(page);
  executePageActions();
}

// Close dialog
export function closeDialog(): void {
  const dialog = useDialogStore.getState();
  dialog.closeDialog();
}
