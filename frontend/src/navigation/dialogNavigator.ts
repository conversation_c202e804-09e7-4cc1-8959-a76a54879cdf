import { useDialogStore, type Dialog<PERSON>age<PERSON><PERSON> } from "../stores/dialogStore";
import { apiClient } from "../utils/apiClient";

// Result returned by each dialog navigation case
interface DialogNavResult {
  actions?: Array<() => void>;
  target: DialogPageKey;
}

// Evaluate current dialog page and decide next based on conditions
export function getDialogNavigationOutcome(): DialogNavResult | null {
  const dialog = useDialogStore.getState();
  const current = dialog.currentPage;

  if (!current) return null;

  switch (current) {
    case "all-monitoring":
      // All Monitoring page - fetch monitoring alerts and store in store
      return {
        actions: [
          async () => {
            try {
              console.log("Fetching monitoring alerts...");
              dialog.setLoading(true);
              const data = await apiClient.getMonitoring();
              const alerts = data.alerts || [];
              dialog.setMonitoringAlerts(alerts);
              console.log("Monitoring alerts loaded:", alerts.length);
            } catch (error) {
              console.error("Failed to fetch monitoring alerts:", error);
              // Fallback data
              const fallbackAlerts = [
                {
                  id: "alert-1",
                  description:
                    "Buy 100 shares of Infosys when the NIFTY touches 25,000.",
                  symbol: "INFOSYS",
                  triggerPrice: "25,000",
                  currentPrice: "26,540",
                  progress: "0/100",
                  progressPercent: 94,
                  status: "pending" as const,
                  orderType: "Limit @ ₹1,490",
                  stopLoss: "₹1,450",
                  product: "Intraday",
                },
                {
                  id: "alert-2",
                  description:
                    "Buy 100 shares of Reliance when the NIFTY touches 25,000.",
                  symbol: "RELIANCE",
                  triggerPrice: "25,000",
                  currentPrice: "25,540",
                  progress: "0/100",
                  progressPercent: 94,
                  status: "pending" as const,
                  orderType: "Limit @ ₹2,890",
                  stopLoss: "₹2,750",
                  product: "Intraday",
                },
              ];
              dialog.setMonitoringAlerts(fallbackAlerts);
            } finally {
              dialog.setLoading(false);
            }
          },
        ],
        target: "all-monitoring",
      };

    case "all-orders":
      // All Orders page - fetch orders and store in store
      return {
        actions: [
          async () => {
            try {
              console.log("Fetching orders...");
              dialog.setLoading(true);
              const data = await apiClient.getOrders();
              const orders = data.orders || [];
              dialog.setOrders(orders);
              console.log("Orders loaded:", orders.length);
            } catch (error) {
              console.error("Failed to fetch orders:", error);
              // Fallback data
              const fallbackOrders = [
                {
                  id: "order-1",
                  symbol: "INFOSYS",
                  type: "buy" as const,
                  quantity: 100,
                  price: "₹1,490",
                  status: "pending" as const,
                  timestamp: "2024-01-15T10:30:00Z",
                  orderType: "Limit",
                  product: "Intraday",
                },
                {
                  id: "order-2",
                  symbol: "RELIANCE",
                  type: "buy" as const,
                  quantity: 50,
                  price: "₹2,890",
                  status: "executed" as const,
                  timestamp: "2024-01-15T09:45:00Z",
                  orderType: "Market",
                  product: "Delivery",
                },
              ];
              dialog.setOrders(fallbackOrders);
            } finally {
              dialog.setLoading(false);
            }
          },
        ],
        target: "all-orders",
      };

    case "edit-profile":
      // Edit Profile page - no additional data loading needed
      return {
        actions: [
          () => {
            console.log("Opening edit profile page");
          },
        ],
        target: "edit-profile",
      };

    default:
      return null;
  }
}

// Helper function to execute data loading for the current page
function executePageActions(): void {
  const outcome = getDialogNavigationOutcome();
  if (outcome) {
    outcome.actions?.forEach((fn) => fn());
  }
}

// Open dialog with specific page and load data
export function openDialog(page: DialogPageKey): void {
  const dialog = useDialogStore.getState();
  dialog.openDialog(page);
  executePageActions();
}

// Close dialog
export function closeDialog(): void {
  const dialog = useDialogStore.getState();
  dialog.closeDialog();
}
