import { useNavStore } from "../stores/navStore";
import type { PageKey } from "../stores/navStore";

// Result returned by each navigation case
interface NavResult {
  actions?: Array<() => void>;
  target: PageKey;
}

// Evaluate current page and decide next
export function getNavigationOutcome(): NavResult | null {
  const nav = useNavStore.getState();
  const current = nav.stack[nav.stack.length - 1];

  switch (current) {
    case "home":
      // If authenticated, go to chat; otherwise, profile
      if (nav.isAuthenticated) {
        return {
          actions: [() => console.log("Navigating to chat")],
          target: "chat",
        };
      }
      return { target: "profile" };

    case "chat":
      // If unread messages exist, clear them and stay; else, go home
      if (nav.unreadCount > 0) {
        return {
          actions: [() => nav.clearUnread()],
          target: "chat",
        };
      }
      return { target: "home" };

    case "profile":
      // Always navigate to settings
      return { target: "settings" };

    case "settings":
      // Return to home after settings
      return { target: "home" };

    default:
      return null;
  }
}

// Trigger navigation: perform actions, update stack, update URL
export function navigate(): void {
  const outcome = getNavigationOutcome();
  if (!outcome) return;
  outcome.actions?.forEach((fn) => fn());
  const nav = useNavStore.getState();
  nav.push(outcome.target);
  window.history.pushState({}, "", `/${outcome.target}`);
}

// Go back: pop stack and update URL
export function goBack(): void {
  const nav = useNavStore.getState();
  nav.pop();
  const prev = nav.stack[nav.stack.length - 1] || "home";
  window.history.pushState({}, "", `/${prev}`);
} 