// Simple Execution PouchDB Service
// Minimal service for storing execution requests in PouchDB

import PouchDB from "pouchdb";
import PouchDBFind from "pouchdb-find";
import {
  shouldSkipAuthValidation,
  TEST_FIREBASE_UID,
} from "../config/testConfig";
import { isRunningInExtension } from "../utils/extensionDetector";

// Register the find plugin
PouchDB.plugin(PouchDBFind);

export interface ExecutionRequest {
  _id?: string;
  type: string;
  firebase_uid: string;
  conversation_id: string;
  status: string;
  primitives: any[];
  created_at: string;
  updated_at: string;
}

export interface Order {
  id: string;
  symbol: string;
  type: "buy" | "sell";
  quantity: number;
  price: string;
  status: "pending" | "inProgress" | "executed" | "cancelled" | (string & {});
  broker_status?: string;
  timestamp: string;
  orderType: string;
  product: string;
  broker: string;
  exchange?: string;
  execution_request_id?: string;
  action_id?: string;
}

export interface MonitoringAlert {
  id: string;
  description: string;
  symbol: string;
  triggerPrice: string;
  currentPrice: string;
  progress: string;
  progressPercent: number;
  status: "pending" | "inProgress" | "triggered" | "stopped" | (string & {});
  orderType: string;
  stopLoss: string;
  product: string;
  execution_request_id?: string;
  action_id?: string;
  // Structured fields for UI
  onTriggerAction?: string;
  onTriggerQuantity?: number;
  onTriggerSymbol?: string;
  conditionOperator?: string;
  conditionValue?: string | number;
  limitPrice?: string;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * SHA-256 hash generator
 */
async function generateHash(message: string): Promise<string> {
  // Convert the string to a Uint8Array
  const msgUint8 = new TextEncoder().encode(message);
  // Hash the message
  const hashBuffer = await crypto.subtle.digest("SHA-256", msgUint8);
  // Convert the buffer to a hex string
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray
    .map((b) => b.toString(16).padStart(2, "0"))
    .join("");
  return hashHex;
}

/**
 * Simple execution PouchDB service
 */
class ExecutionPouchDBSyncService {
  private localDB: PouchDB.Database | null = null;
  private remoteDB: PouchDB.Database | null = null;
  private firebaseUID: string = "";
  private executionCallback: ((request: ExecutionRequest) => void) | null =
    null;

  constructor() {
    console.log("🔧 ExecutionPouchDBSyncService constructor called");
  }

  /**
   * Initialize PouchDB with Firebase UID
   */
  async initialize(firebaseUID: string): Promise<void> {
    console.log(
      "🚀 [INIT] BasePouchDBSyncService.initialize() called with Firebase UID:",
      firebaseUID
    );

    // Use test Firebase UID if provided
    const actualFirebaseUID = shouldSkipAuthValidation()
      ? TEST_FIREBASE_UID
      : firebaseUID;

    if (shouldSkipAuthValidation()) {
      console.log(
        "🧪 [TEST_CONFIG] Using test Firebase UID:",
        actualFirebaseUID
      );
    }

    this.firebaseUID = actualFirebaseUID;

    // Generate database names
    const localDBName = `execution_${actualFirebaseUID}_local`;
    const hash = await generateHash(actualFirebaseUID);

    console.log("🔧 Creating local PouchDB instance:", localDBName);
    this.localDB = new PouchDB(localDBName);
    console.log("✅ Local PouchDB instance created successfully");

    if (isRunningInExtension()) {
      const manifest = chrome.runtime.getManifest();
      const couchdbUrl = manifest.aagman_config.couchdb_url;
      const couchdbDbPrefix = manifest.aagman_config.couchdb_db_prefix;
      const remoteDBName = `${couchdbDbPrefix}${hash}`;
      console.log(
        "🔧 Creating remote PouchDB instance:",
        `${couchdbUrl}/${remoteDBName}`
      );
      this.remoteDB = new PouchDB(`${couchdbUrl}/${remoteDBName}`);
      (globalThis as any).chrome.runtime.sendMessage({
        type: "CONNECT_TO_COUCHDB",
        firebaseUid: actualFirebaseUID,
        success: true,
      });
    } else {
      const remoteDBName = `${import.meta.env.VITE_COUCHDB_DB_PREFIX}${hash}`;
      console.log(
        "🔧 Creating remote PouchDB instance:",
        `${import.meta.env.VITE_COUCHDB_URL}/${remoteDBName}`
      );
      this.remoteDB = new PouchDB(
        `${import.meta.env.VITE_COUCHDB_URL}/${remoteDBName}`
      );
    }
    console.log("✅ Remote PouchDB instance created successfully");

    // Test connection
    console.log("🔧 [INIT] Testing connection to CouchDB...");
    try {
      await this.remoteDB!.info();
      console.log("✅ [INIT] Connection test successful");
    } catch (error) {
      console.log(
        "⚠️ [INIT] Connection test failed, continuing with local-only mode"
      );
    }

    // Setup sync
    console.log("🔄 [SYNC] Setting up sync with mode: live");
    console.log("🔄 [SYNC] Initializing live sync...");

    try {
      const syncOptions = {
        live: true,
        retry: true,
        filter: (doc: any) => {
          console.log(
            "🔄 [SYNC] Firebase UID for filtering:",
            actualFirebaseUID
          );
          return doc.firebase_uid === actualFirebaseUID;
        },
      };

      console.log("🔄 [SYNC] Starting live sync with options:", syncOptions);
      const sync = this.localDB!.sync(this.remoteDB!, syncOptions);

      sync
        .on("change", (info) => {
          console.log("🔄 [SYNC] Change detected:", info);
        })
        .on("paused", () => {
          console.log("⏸️ [SYNC] Sync paused");
        })
        .on("active", () => {
          console.log("▶️ [SYNC] Sync active");
        })
        .on("error", (err) => {
          console.log("❌ [SYNC] Sync error:", err);
        });

      console.log("✅ [SYNC] Live sync initialized");
    } catch (error) {
      console.log(
        "⚠️ [SYNC] Sync setup failed, continuing with local-only mode"
      );
    }

    console.log(
      "🎉 [INIT] BasePouchDBSyncService initialization completed successfully!"
    );
  }

  /**
   * Store execution request in PouchDB
   */
  async storeExecutionRequest(
    request: Omit<ExecutionRequest, "_id" | "_rev">
  ): Promise<string> {
    if (!this.localDB) {
      throw new Error("PouchDB not initialized");
    }

    // Clear previous requests for this Firebase UID (like the original)
    console.log(
      `[ExecutionPouchDB] 🧹 Clearing previous execution requests for user: ${request.firebase_uid}`
    );
    const clearedCount = await this.clearPreviousRequests(request.firebase_uid);
    console.log(
      `[ExecutionPouchDB] ✅ Cleared ${clearedCount} previous execution requests`
    );

    // Create document with _id (like the original ExecutionDocumentHandler)
    const doc: ExecutionRequest = {
      _id: `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...request,
    };

    // Add Firebase UID context if not present
    if (!doc.firebase_uid && this.firebaseUID) {
      doc.firebase_uid = this.firebaseUID;
    }

    // Add timestamps (like the original storeDocument)
    const now = new Date().toISOString();
    if (!doc.created_at) {
      doc.created_at = now;
    }
    doc.updated_at = now;

    const result = await this.localDB.put(doc);
    return typeof result === "string" ? result : result.id || doc._id!;
  }

  /**
   * Clear previous execution requests for a user (like the original)
   */
  private async clearPreviousRequests(firebaseUID: string): Promise<number> {
    if (!this.localDB) {
      console.warn(
        "[ExecutionPouchDB] LocalDB not initialized, skipping clear"
      );
      return 0;
    }

    // Check if find method is available (requires pouchdb-find plugin)
    if (typeof this.localDB.find !== "function") {
      console.error(
        "[ExecutionPouchDB] CRITICAL: PouchDB find method not available! This will cause execution request accumulation!"
      );
      // Try alternative cleanup method using allDocs
      return await this.clearPreviousRequestsAlternative(firebaseUID);
    }

    try {
      // Find all execution requests for this user
      const result = await this.localDB.find({
        selector: {
          type: "execution_request",
          firebase_uid: firebaseUID,
        },
      });

      // Delete them
      let deleteCount = 0;
      for (const doc of result.docs) {
        try {
          await this.localDB.remove(doc._id, doc._rev);
          deleteCount++;
        } catch (error) {
          // Ignore delete errors (document might already be deleted)
        }
      }

      return deleteCount;
    } catch (error) {
      console.warn("Error clearing previous requests:", error);
      return 0;
    }
  }

  /**
   * Alternative method to clear previous requests using allDocs (fallback when find() not available)
   */
  private async clearPreviousRequestsAlternative(
    firebaseUID: string
  ): Promise<number> {
    if (!this.localDB) {
      return 0;
    }

    try {
      console.log(
        "[ExecutionPouchDB] Using alternative cleanup method with allDocs"
      );

      // Get all documents
      const result = await this.localDB.allDocs({ include_docs: true });

      // Filter execution requests for this user
      const toDelete = result.rows.filter((row) => {
        const doc = row.doc as any; // Cast to any to access custom fields
        return (
          doc &&
          doc.type === "execution_request" &&
          doc.firebase_uid === firebaseUID
        );
      });

      // Delete them
      let deleteCount = 0;
      for (const row of toDelete) {
        try {
          if (row.doc) {
            // Type guard
            await this.localDB.remove(row.doc._id, row.doc._rev);
            deleteCount++;
            console.log(
              `[ExecutionPouchDB] Deleted execution request: ${row.doc._id}`
            );
          }
        } catch (error) {
          console.warn(
            `[ExecutionPouchDB] Failed to delete ${row.doc?._id}:`,
            error
          );
        }
      }

      console.log(
        `[ExecutionPouchDB] Alternative cleanup completed: ${deleteCount} requests deleted`
      );
      return deleteCount;
    } catch (error) {
      console.error("[ExecutionPouchDB] Alternative cleanup failed:", error);
      return 0;
    }
  }

  /**
   * Set callback for execution changes
   */
  setExecutionCallback(callback: (request: ExecutionRequest) => void): void {
    this.executionCallback = callback;
    // Note: Change monitoring would be implemented here if needed
  }

  /**
   * Get orders from PouchDB
   */
  async getOrders(): Promise<Order[]> {
    console.log("🚀 [ORDERS] ===== STARTING ORDER RETRIEVAL =====");

    if (!this.localDB) {
      console.warn(
        "⚠️ [ORDERS] PouchDB not initialized, returning empty orders array"
      );
      return [];
    }

    console.log("📋 [ORDERS] Firebase UID:", this.firebaseUID);
    console.log("📋 [ORDERS] Querying for order_result documents...");

    try {
      const result = await this.localDB.find({
        selector: {
          type: "order_result",
          firebase_uid: this.firebaseUID,
        },
      });

      console.log(`📊 [ORDERS] Found ${result.docs.length} order documents`);
      console.log(
        "📊 [ORDERS] Raw documents:",
        JSON.stringify(result.docs, null, 2)
      );

      const mappedOrders = result.docs.map((doc: any) => {
        const mappedOrder: Order = {
          id: doc.id,
          symbol: doc.symbol,
          type:
            doc.tradeType?.toLowerCase() === "buy"
              ? ("buy" as const)
              : ("sell" as const),
          quantity: parseInt(doc.quantity) || 0,
          price: doc.price,
          status: doc.status,
          broker_status: doc.broker_status,
          timestamp: doc.timestamp,
          orderType: doc.orderType,
          product: doc.product,
          broker: doc.broker || "zerodha", // Default to zerodha if not specified
          exchange: doc.exchange || doc.market || undefined,
          execution_request_id: doc.execution_request_id,
          action_id: doc.action_id,
        };
        console.log(
          `🔄 [ORDERS] Mapped order:`,
          JSON.stringify(mappedOrder, null, 2)
        );
        return mappedOrder;
      });

      // Sort by timestamp (newest first) - client-side sorting is more reliable than PouchDB sorting
      mappedOrders.sort((a, b) => {
        const timeA = new Date(a.timestamp).getTime();
        const timeB = new Date(b.timestamp).getTime();
        return timeB - timeA; // Descending order (newest first)
      });

      console.log(
        `✅ [ORDERS] Successfully mapped and sorted ${mappedOrders.length} orders`
      );
      console.log("🎉 [ORDERS] ===== ORDER RETRIEVAL COMPLETED =====");
      return mappedOrders;
    } catch (error) {
      console.error("❌ [ORDERS] ===== ORDER RETRIEVAL FAILED =====");
      console.error("❌ [ORDERS] Error details:", error);
      console.error("❌ [ORDERS] Error stack:", (error as Error).stack);
      return [];
    }
  }

  /**
   * Get monitoring alerts from PouchDB
   */
  async getMonitoringAlerts(): Promise<MonitoringAlert[]> {
    console.log("🚀 [MONITORING] ===== STARTING MONITORING RETRIEVAL =====");

    if (!this.localDB) {
      console.warn(
        "⚠️ [MONITORING] PouchDB not initialized, returning empty monitoring array"
      );
      return [];
    }

    console.log("📋 [MONITORING] Firebase UID:", this.firebaseUID);
    console.log("📋 [MONITORING] Querying for monitoring_alert documents...");

    try {
      const result = await this.localDB.find({
        selector: {
          type: "monitoring_alert",
          firebase_uid: this.firebaseUID,
        },
      });

      console.log(
        `📊 [MONITORING] Found ${result.docs.length} monitoring documents`
      );
      console.log(
        "📊 [MONITORING] Raw documents:",
        JSON.stringify(result.docs, null, 2)
      );

      const mappedAlerts = result.docs.map((doc: any) => {
        const condition = doc.condition || {};
        const onTrigger = doc.onTrigger || {};
        const onTrigArgs = onTrigger?.arguments || {};
        const condOperator = (condition.operator || "").toString();
        const condValue = condition.value;
        const otcActionRaw = (onTrigger.action || "").toString();
        const actionName = otcActionRaw.toLowerCase();
        const normalizedVerb = actionName.includes("sell")
          ? "SELL"
          : actionName.includes("buy")
            ? "BUY"
            : otcActionRaw.toUpperCase();
        const normalizedOrderType = (() => {
          if (actionName.includes("stoploss") && actionName.includes("limit"))
            return "SL-L";
          if (actionName.includes("stoploss") && actionName.includes("market"))
            return "SL-M";
          if (actionName.includes("limit")) return "LIMIT";
          return "MARKET";
        })();
        const otcQty =
          onTrigger.quantity ??
          onTrigger.QUANTITY ??
          onTrigArgs.quantity ??
          onTrigArgs.QUANTITY;
        const otcSym =
          onTrigger.symbol || onTrigArgs.symbol || doc.symbol || "";
        const productRaw =
          doc.product ||
          onTrigArgs.productType ||
          onTrigArgs.PRODUCT_TYPE ||
          "";
        const mappedAlert: MonitoringAlert = {
          id: doc.id,
          description: doc.description,
          symbol: doc.symbol,
          triggerPrice: doc.triggerPrice,
          currentPrice: doc.currentPrice,
          progress: doc.progress,
          progressPercent: doc.progressPercent,
          status: doc.status,
          orderType: doc.orderType || normalizedOrderType,
          stopLoss: doc.stopLoss,
          product: productRaw ? String(productRaw).toUpperCase() : "",
          execution_request_id: doc.execution_request_id,
          action_id: doc.action_id,
          // Structured fields (computed from original fields if available)
          onTriggerAction: normalizedVerb || undefined,
          onTriggerQuantity: Number.isFinite(Number(otcQty))
            ? Number(otcQty)
            : undefined,
          onTriggerSymbol: otcSym ? String(otcSym).toUpperCase() : undefined,
          conditionOperator: condOperator || undefined,
          conditionValue: condValue !== undefined ? condValue : undefined,
          limitPrice: (() => {
            const lp =
              onTrigArgs.limitPrice ||
              onTrigArgs.LIMIT_PRICE ||
              onTrigArgs.price ||
              onTrigArgs.PRICE;
            return lp !== undefined && lp !== null ? String(lp) : undefined;
          })(),
          createdAt: doc.created_at,
          updatedAt: doc.updated_at,
        };
        console.log(
          `🔄 [MONITORING] Mapped alert:`,
          JSON.stringify(mappedAlert, null, 2)
        );
        return mappedAlert;
      });

      // Sort by timestamp (newest first) - client-side sorting is more reliable than PouchDB sorting
      mappedAlerts.sort((a, b) => {
        // Use created_at from the original document for sorting
        const docA = result.docs.find((doc: any) => doc.id === a.id);
        const docB = result.docs.find((doc: any) => doc.id === b.id);
        const timeA = (docA as any)?.created_at
          ? new Date((docA as any).created_at).getTime()
          : 0;
        const timeB = (docB as any)?.created_at
          ? new Date((docB as any).created_at).getTime()
          : 0;
        return timeB - timeA; // Descending order (newest first)
      });

      console.log(
        `✅ [MONITORING] Successfully mapped and sorted ${mappedAlerts.length} alerts`
      );
      console.log("🎉 [MONITORING] ===== MONITORING RETRIEVAL COMPLETED =====");
      return mappedAlerts;
    } catch (error) {
      console.error("❌ [MONITORING] ===== MONITORING RETRIEVAL FAILED =====");
      console.error("❌ [MONITORING] Error details:", error);
      console.error("❌ [MONITORING] Error stack:", (error as Error).stack);
      return [];
    }
  }

  /**
   * Watch for real-time order updates
   */
  watchOrderUpdates(callback: (orders: Order[]) => void): void {
    console.log("🚀 [ORDERS-WATCH] ===== SETTING UP ORDER WATCHER =====");

    if (!this.localDB) {
      console.warn(
        "⚠️ [ORDERS-WATCH] PouchDB not initialized, cannot watch order updates"
      );
      return;
    }

    console.log("👀 [ORDERS-WATCH] Setting up real-time order updates watcher");
    console.log("📋 [ORDERS-WATCH] Firebase UID filter:", this.firebaseUID);

    this.localDB
      .changes({
        since: "now",
        live: true,
        include_docs: true,
        filter: (doc: any) =>
          doc.type === "order_result" && doc.firebase_uid === this.firebaseUID,
      })
      .on("change", (change: any) => {
        console.log("🔄 [ORDERS-WATCH] ===== ORDER CHANGE DETECTED =====");
        console.log(
          "🔄 [ORDERS-WATCH] Change details:",
          JSON.stringify(change, null, 2)
        );
        console.log("🔄 [ORDERS-WATCH] Refreshing order data...");
        this.getOrders().then((orders) => {
          console.log(
            `🔄 [ORDERS-WATCH] Calling callback with ${orders.length} orders`
          );
          callback(orders);
          console.log("✅ [ORDERS-WATCH] Callback completed");
        });
      })
      .on("error", (err: any) => {
        console.error("❌ [ORDERS-WATCH] ===== ORDER WATCH ERROR =====");
        console.error("❌ [ORDERS-WATCH] Error details:", err);
      });

    console.log("✅ [ORDERS-WATCH] Order watcher setup completed");
  }

  /**
   * Watch for real-time monitoring updates
   */
  watchMonitoringUpdates(callback: (alerts: MonitoringAlert[]) => void): void {
    console.log(
      "🚀 [MONITORING-WATCH] ===== SETTING UP MONITORING WATCHER ====="
    );

    if (!this.localDB) {
      console.warn(
        "⚠️ [MONITORING-WATCH] PouchDB not initialized, cannot watch monitoring updates"
      );
      return;
    }

    console.log(
      "👀 [MONITORING-WATCH] Setting up real-time monitoring updates watcher"
    );
    console.log("📋 [MONITORING-WATCH] Firebase UID filter:", this.firebaseUID);

    this.localDB
      .changes({
        since: "now",
        live: true,
        include_docs: true,
        filter: (doc: any) =>
          doc.type === "monitoring_alert" &&
          doc.firebase_uid === this.firebaseUID,
      })
      .on("change", (change: any) => {
        console.log(
          "🔄 [MONITORING-WATCH] ===== MONITORING CHANGE DETECTED ====="
        );
        console.log(
          "🔄 [MONITORING-WATCH] Change details:",
          JSON.stringify(change, null, 2)
        );
        console.log("🔄 [MONITORING-WATCH] Refreshing monitoring data...");
        this.getMonitoringAlerts().then((alerts) => {
          console.log(
            `🔄 [MONITORING-WATCH] Calling callback with ${alerts.length} alerts`
          );
          callback(alerts);
          console.log("✅ [MONITORING-WATCH] Callback completed");
        });
      })
      .on("error", (err: any) => {
        console.error(
          "❌ [MONITORING-WATCH] ===== MONITORING WATCH ERROR ====="
        );
        console.error("❌ [MONITORING-WATCH] Error details:", err);
      });

    console.log("✅ [MONITORING-WATCH] Monitoring watcher setup completed");
  }

  /**
   * Watch a specific execution_request document for status updates
   */
  watchExecutionRequestStatus(
    executionRequestId: string,
    callback: (status: string, doc?: any) => void
  ): void {
    if (!this.localDB) {
      console.warn(
        "⚠️ [EXEC-REQ-WATCH] PouchDB not initialized, cannot watch execution request"
      );
      return;
    }
    try {
      // Immediately fetch once
      this.localDB
        .get(executionRequestId)
        .then((doc: any) => {
          if (doc?.status) callback(doc.status, doc);
        })
        .catch(() => {});

      this.localDB
        .changes({ since: "now", live: true, include_docs: true })
        .on("change", (change: any) => {
          const doc = change?.doc;
          if (
            doc &&
            doc._id === executionRequestId &&
            doc.type === "execution_request"
          ) {
            callback(doc.status, doc);
          }
        })
        .on("error", (err: any) => {
          console.error("❌ [EXEC-REQ-WATCH] Watch error:", err);
        });
    } catch (err) {
      console.error("❌ [EXEC-REQ-WATCH] Failed to set watch:", err);
    }
  }
}

// Export singleton instance
export const executionPouchDBSyncService = new ExecutionPouchDBSyncService();
