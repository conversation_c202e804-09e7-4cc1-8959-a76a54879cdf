// Firebase configuration and initialization
import { initializeApp } from "firebase/app";
import {
  getAuth,
  connectAuthEmulator,
  setPersistence,
  browserLocalPersistence,
} from "firebase/auth";
import { getAnalytics } from "firebase/analytics";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCteF_PO5x6t041DoftbG_I3UM0a7iDx_w",
  authDomain: "smartagent-9f971.firebaseapp.com",
  projectId: "smartagent-9f971",
  storageBucket: "smartagent-9f971.firebasestorage.app",
  messagingSenderId: "1032540951900",
  appId: "1:1032540951900:web:c077c0466caf6343861d99",
  measurementId: "G-YS2NJE2HNH",
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);

// Configure Firebase Auth persistence for extension context
if (typeof window !== "undefined") {
  setPersistence(auth, browserLocalPersistence)
    .then(() => {
      console.log("🔐 Firebase auth persistence configured for extension");
    })
    .catch((error) => {
      console.warn("⚠️ Firebase auth persistence setup failed:", error);
    });
}

// Initialize Analytics (optional)
let analytics;
if (typeof window !== "undefined") {
  analytics = getAnalytics(app);
}

export { analytics };

// Configure auth emulator for development (optional)
if (process.env.NODE_ENV === "development" && typeof window !== "undefined") {
  // Only connect to emulator if not already connected
  try {
    // Check if we're running on localhost and emulator is available
    if (window.location.hostname === "localhost") {
      // Uncomment the line below if you want to use Firebase Auth Emulator
      // connectAuthEmulator(auth, "http://localhost:9099");
    }
  } catch (error) {
    console.warn("Firebase Auth Emulator connection failed:", error);
  }
}

export default app;
