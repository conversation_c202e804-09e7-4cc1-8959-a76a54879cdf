/// <reference types="vite/client" />
/// <reference types="chrome" />

interface Window {
  worker: Worker;
  __EXTENSION_CONTEXT__?: boolean;
}

declare const __USE_MOCK__: boolean;
declare const __LOGIN_MODE__: string;

declare module "*.svg" {
  const content: string;
  export default content;
}

// Chrome Extension API type declarations for build-time compatibility
declare global {
  const chrome: typeof chrome | undefined;
}
