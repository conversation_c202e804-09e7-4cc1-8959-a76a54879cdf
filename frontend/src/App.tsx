import { useEffect } from 'react';
import { useNavStore, type <PERSON><PERSON>ey } from './stores/navStore';
import HomePage from './pages/HomePage';
import ChatPage from './pages/ChatPage';
import ProfilePage from './pages/ProfilePage';
import SettingsPage from './pages/SettingsPage';
import ComponentsDemo from './pages/ComponentsDemo';

function App() {
  const { stack, push, pop } = useNavStore();
  const currentPage = stack[stack.length - 1];

  // Effect for Initial URL Load
  useEffect(() => {
    const path = window.location.pathname.substring(1) as PageKey;
    const validPages: PageKey[] = ['home', 'chat', 'profile', 'settings', 'demo'];
    // Only push if the stack is in its initial state, to avoid double-adding on hot-reloads.
    if (path && validPages.includes(path) && stack.length === 1 && stack[0] === 'home') {
        push(path);
    }
  }, [push, stack]);

  // Effect for Browser Back/Forward Navigation
  useEffect(() => {
    const handlePopState = () => {
      const newPath = window.location.pathname.substring(1) as Page<PERSON>ey;
      const stackIndex = stack.lastIndexOf(newPath);

      if (stackIndex !== -1) {
        // If the page is in our history, pop until we get to it.
        while (stack[stack.length - 1] !== newPath && stack.length > 1) {
          pop();
        }
      } else {
        // If the page isn't in our known stack (e.g., browser forward),
        // a simple push is a reasonable default.
        push(newPath);
      }
    };

    window.addEventListener('popstate', handlePopState);
    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [stack, push, pop]);

  const renderPage = () => {
    switch (currentPage) {
      case 'home':
        return <HomePage />;
      case 'chat':
        return <ChatPage />;
      case 'profile':
        return <ProfilePage />;
      case 'settings':
        return <SettingsPage />;
      case 'demo':
        return <ComponentsDemo />;
      default:
        return <HomePage />;
    }
  };

  return (
    <div>
      {renderPage()}
    </div>
  );
}

export default App;
