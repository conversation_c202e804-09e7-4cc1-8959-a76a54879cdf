import { create } from 'zustand';

export type PageKey = 'home' | 'chat' | 'profile' | 'settings' | 'demo';

interface NavState {
  stack: <PERSON><PERSON>ey[];
  isAuthenticated: boolean;
  unreadCount: number;
  push: (page: <PERSON><PERSON>ey) => void;
  pop: () => void;
  clearUnread: () => void;
  setAuthenticated: (isAuth: boolean) => void;
}

export const useNavStore = create<NavState>((set) => ({
  stack: ['home'],
  isAuthenticated: false,
  unreadCount: 0,
  push: (page: <PERSON><PERSON><PERSON>) => set((state) => ({ stack: [...state.stack, page] })),
  pop: () => set((state) => ({ stack: state.stack.slice(0, state.stack.length - 1) })),
  clearUnread: () => set({ unreadCount: 0 }),
  setAuthenticated: (isAuth: boolean) => set({ isAuthenticated: isAuth }),
}));
