import { create } from "zustand";
import { persist } from "zustand/middleware";
import {
  type User,
  onAuthStateChanged,
  signOut as firebaseSignOut,
  getIdToken,
  RecaptchaVerifier,
  signInWithPhoneNumber,
  type ConfirmationResult,
  getAuth,
} from "firebase/auth";
import { auth } from "../services/firebase";

export interface AuthUser {
  uid: string;
  email?: string | null;
  phoneNumber?: string | null;
  emailVerified: boolean;
  displayName?: string | null;
}

interface AuthState {
  // Auth state
  user: AuthUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  preventAutoRedirect: boolean; // Flag to prevent automatic redirect to ChatHomePage (for web flow)

  // Firebase specific
  firebaseUser: User | null;
  idToken: string | null;

  // User profile data (from our database)
  userProfile: {
    name?: string;
    phone?: string;
  } | null;

  // Connection state
  connectionAttempts: number;
  maxConnectionAttempts: number;

  // Phone auth state
  recaptchaVerifier: RecaptchaVerifier | null;
  confirmationResult: ConfirmationResult | null;

  // Actions
  setUser: (user: User | null) => void;
  setIdToken: (token: string | null) => void;
  setUserProfile: (profile: { name?: string; phone?: string } | null) => void;
  setLoading: (loading: boolean) => void;
  setAuthenticated: (authenticated: boolean) => void;
  setPreventAutoRedirect: (prevent: boolean) => void;
  incrementConnectionAttempts: () => void;
  resetConnectionAttempts: () => void;
  signOut: () => Promise<void>;
  refreshToken: () => Promise<string | null>;

  // Phone authentication
  setupRecaptcha: (containerId: string) => void;
  sendOTP: (phoneNumber: string) => Promise<void>;
  sendOTPWithoutRecaptcha: (phoneNumber: string) => Promise<void>;
  verifyOTP: (code: string) => Promise<User>;
  cleanupRecaptcha: () => void;

  // Initialization
  initialize: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      isLoading: true,
      preventAutoRedirect: false,
      firebaseUser: null,
      idToken: null,
      userProfile: null,
      connectionAttempts: 0,
      maxConnectionAttempts: 3,
      recaptchaVerifier: null,
      confirmationResult: null,

      // Actions
      setUser: (firebaseUser: User | null) => {
        if (firebaseUser) {
          const authUser: AuthUser = {
            uid: firebaseUser.uid,
            email: firebaseUser.email,
            phoneNumber: firebaseUser.phoneNumber,
            emailVerified: firebaseUser.emailVerified,
            displayName: firebaseUser.displayName,
          };

          // 🆕 CHECK PREVENT AUTO REDIRECT FLAG
          const currentState = get();
          const shouldPreventAuth = currentState.preventAutoRedirect;

          if (shouldPreventAuth) {
            console.log(
              "🔒 Firebase user detected but preventAutoRedirect is active - not setting authenticated state yet"
            );
            console.log(
              "🔒 User will be authenticated after completing signup flow"
            );

            set({
              user: authUser,
              firebaseUser,
              isAuthenticated: false, // 🔒 Keep false until signup complete
              isLoading: false,
            });
          } else {
            console.log(
              "🔐 Firebase user detected, setting authenticated state to true"
            );

            set({
              user: authUser,
              firebaseUser,
              isAuthenticated: true, // ✅ Set authenticated for existing users
              isLoading: false,
            });
          }

          // Get initial ID token
          getAuth().currentUser
            ?.getIdToken()
            .then((token) => {
              get().setIdToken(token);
            })
            .catch((error) => {
              console.error("Failed to get initial ID token:", error);
            });
        } else {
          // Firebase user is null, but check if we have persisted auth state
          const currentState = get();
          const wasAuthenticated = currentState.isAuthenticated;

          console.log("🔍 Firebase user is null, checking persistence:", {
            wasAuthenticated,
            hasPersistedUser: !!currentState.user,
          });

          // If we had a persisted authenticated state, don't immediately sign out
          // This handles cases where Firebase auth doesn't persist in extension context
          // but our Zustand store has the user data
          if (wasAuthenticated && currentState.user) {
            console.log("🔐 Preserving authentication state from persistence");
            set({
              firebaseUser: null, // Firebase user is null, but keep our auth state
              isLoading: false,
              // Keep user, isAuthenticated, and other persisted data
            });
          } else {
            console.log("🚪 No persisted auth state, signing out");
            set({
              user: null,
              firebaseUser: null,
              isAuthenticated: false,
              isLoading: false,
              idToken: null,
              userProfile: null,
            });
          }
        }
      },

      setIdToken: (token: string | null) => {
        set({ idToken: token });
      },

      setUserProfile: (profile: { name?: string; phone?: string } | null) => {
        set({ userProfile: profile });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setAuthenticated: (authenticated: boolean) => {
        set({ isAuthenticated: authenticated });
      },

      setPreventAutoRedirect: (prevent: boolean) => {
        set({ preventAutoRedirect: prevent });
      },

      incrementConnectionAttempts: () => {
        set((state) => ({
          connectionAttempts: state.connectionAttempts + 1,
        }));
      },

      resetConnectionAttempts: () => {
        set({ connectionAttempts: 0 });
      },

      signOut: async () => {
        try {
          await firebaseSignOut(auth);
          set({
            user: null,
            firebaseUser: null,
            isAuthenticated: false,
            idToken: null,
            userProfile: null,
            connectionAttempts: 0,
          });
        } catch (error) {
          console.error("Sign out error:", error);
          throw error;
        }
      },

      refreshToken: async (): Promise<string | null> => {
        try {
          const { firebaseUser } = get();
          if (!firebaseUser) {
            return null;
          }

          // Force refresh the token
          const token = await getIdToken(firebaseUser, true);
          get().setIdToken(token);
          return token;
        } catch (error) {
          console.error("Token refresh error:", error);
          return null;
        }
      },

      // Phone authentication methods
      setupRecaptcha: (containerId: string) => {
        try {
          // Clean up any existing reCAPTCHA instance
          const { recaptchaVerifier: existingVerifier } = get();
          if (existingVerifier) {
            try {
              existingVerifier.clear();
            } catch (e) {
              console.log("Previous reCAPTCHA instance cleared");
            }
          }

          // Ensure the container is empty
          const container = document.getElementById(containerId);
          if (container) {
            container.innerHTML = "";
          }

          const recaptchaVerifier = new RecaptchaVerifier(auth, containerId, {
            size: "invisible",
            callback: () => {
              console.log("reCAPTCHA solved");
              // reCAPTCHA solved - Firebase will handle cleanup
              console.log("reCAPTCHA solved - cleanup handled by Firebase");
            },
            "expired-callback": () => {
              console.log("reCAPTCHA expired");
            },
            "error-callback": (error: any) => {
              console.log("reCAPTCHA error:", error);
              // Firebase will handle cleanup on error
            },
          });

          // Don't watch for changes - Firebase will handle showing reCAPTCHA
          // The container will be shown by Firebase when needed

          set({ recaptchaVerifier });
        } catch (error) {
          console.error("Error setting up reCAPTCHA:", error);
          throw error;
        }
      },

      sendOTP: async (phoneNumber: string): Promise<void> => {
        try {
          const { recaptchaVerifier } = get();
          if (!recaptchaVerifier) {
            throw new Error("reCAPTCHA not initialized");
          }

          const confirmationResult = await signInWithPhoneNumber(
            auth,
            phoneNumber,
            recaptchaVerifier
          );

          set({ confirmationResult });
          console.log("OTP sent successfully");
        } catch (error) {
          console.error("Error sending OTP:", error);
          throw error;
        }
      },

      sendOTPWithoutRecaptcha: async (phoneNumber: string): Promise<void> => {
        try {
          // Setup invisible reCAPTCHA automatically if needed
          const actions = get();
          if (!actions.recaptchaVerifier) {
            actions.setupRecaptcha("recaptcha-container");
          }

          const { recaptchaVerifier } = get();
          if (!recaptchaVerifier) {
            throw new Error("Failed to initialize reCAPTCHA");
          }

          const confirmationResult = await signInWithPhoneNumber(
            auth,
            phoneNumber,
            recaptchaVerifier
          );

          set({ confirmationResult });
          console.log("OTP sent successfully without manual reCAPTCHA setup");
        } catch (error) {
          console.error("Error sending OTP:", error);
          throw error;
        }
      },

      verifyOTP: async (code: string): Promise<User> => {
        try {
          const { confirmationResult } = get();
          if (!confirmationResult) {
            throw new Error("No confirmation result available");
          }

          const result = await confirmationResult.confirm(code);
          const user = result.user;

          if (!user) {
            throw new Error("No user returned from confirmation");
          }

          console.log("OTP verified successfully");
          return user;
        } catch (error) {
          console.error("Error verifying OTP:", error);
          throw error;
        }
      },

      cleanupRecaptcha: () => {
        const { recaptchaVerifier } = get();
        if (recaptchaVerifier) {
          try {
            recaptchaVerifier.clear();
          } catch (error) {
            console.error("Error clearing reCAPTCHA:", error);
          }
        }

        // Firebase will handle container cleanup
        console.log("Cleaning up reCAPTCHA - Firebase handles visibility");

        set({ recaptchaVerifier: null, confirmationResult: null });
      },

      initialize: () => {
        // Set up Firebase auth state listener
        const unsubscribe = onAuthStateChanged(auth, (user) => {
          console.log(
            "Firebase auth state changed:",
            user?.uid || "signed out"
          );
          get().setUser(user);
        });

        // Set up token refresh interval (refresh every 50 minutes)
        const tokenRefreshInterval = setInterval(
          async () => {
            const { isAuthenticated } = get();
            if (isAuthenticated) {
              try {
                await get().refreshToken();
                console.log("Token refreshed automatically");
              } catch (error) {
                console.error("Automatic token refresh failed:", error);
              }
            }
          },
          50 * 60 * 1000
        ); // 50 minutes

        // Cleanup function (not used in this implementation but good practice)
        return () => {
          unsubscribe();
          clearInterval(tokenRefreshInterval);
        };
      },
    }),
    {
      name: "auth-storage",
      // Only persist non-sensitive data
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        userProfile: state.userProfile,
        // Don't persist tokens or Firebase user objects
      }),
    }
  )
);

// Initialize auth store on module load
if (typeof window !== "undefined") {
  useAuthStore.getState().initialize();
}

// Utility functions
export const getAuthToken = async (): Promise<string | null> => {
  const { firebaseUser, refreshToken } = useAuthStore.getState();

  if (!firebaseUser) {
    return null;
  }

  try {
    // Try to get current token
    const token = await getIdToken(getAuth().currentUser as User);
    return token;
  } catch (error) {
    console.error("Failed to get auth token:", error);
    // Try to refresh token
    return await refreshToken();
  }
};

export const isTokenExpired = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split(".")[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  } catch (error) {
    console.error("Failed to parse token:", error);
    return true;
  }
};

export const shouldRefreshToken = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split(".")[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    // Refresh if token expires in less than 5 minutes
    return payload.exp - currentTime < 300;
  } catch (error) {
    console.error("Failed to parse token:", error);
    return true;
  }
};
