/**
 * ===============================================================================
 * WEB LOGIN PAGE - DUAL MODE: STANDARD WEB + EXTENSION UI COLLECTOR
 * ===============================================================================
 *
 * This component now operates in two modes:
 *
 * 1. STANDARD WEB MODE (default):
 *    - Full-page login experience
 *    - Direct Firebase operations
 *    - Complete 4-step flow: phone → OTP → name → broker selection
 *    - Stays on page after completion for broker selection
 *
 * 2. EXTENSION UI MODE (?mode=extension):
 *    - Acts as UI collector for extension's secure login flow
 *    - Sends user input to extension via postMessage
 *    - <PERSON> handles all Firebase operations securely
 *    - SKIPS broker selection step (extension mode only needs 3 steps)
 *    - Closes tab after authentication completion
 *
 * SECURITY ARCHITECTURE (Extension Mode):
 * - Extension context: Secure Firebase auth operations, token storage
 * - Web tab context: UI collection only, no sensitive operations
 * - PostMessage communication: Origin-validated, secure data exchange
 * - Auto-closure: Tab closes after completion, extension navigates to ChatHomePage
 *
 * COMMUNICATION FLOW (Extension Mode):
 * Extension ←→ WebTab
 *   SEND_OTP ←→ SET_LOADING, OTP_SENT, SET_ERROR
 *   VERIFY_OTP ←→ SET_LOADING, OTP_VERIFIED, SET_ERROR
 *   COMPLETE_REGISTRATION ←→ SET_LOADING, LOGIN_COMPLETE, SET_ERROR
 *   CLOSE_LOGIN ←→ [Tab closure cleanup]
 *
 * MODE DETECTION: URL parameter ?mode=extension
 * FALLBACK: Standard web mode if parameter missing
 *
 * ===============================================================================
 */

import React, { useState, useEffect } from "react";
import { useNavStore } from "../stores/navStore";
import { navigate } from "../navigation/pageNavigator";
import {
  renderLoginStep,
  type LoginStep,
} from "../components/login/LoginStepRenderer";
import { brokers, getBrokerUrl } from "../data/brokers";
import { useAuthStore } from "../stores/authStore";
import { apiClient } from "../utils/apiClient";
import { getUserFriendlyErrorMessage } from "../utils/errorUtils";
import { auth } from "../services/firebase";

// Real API functions (same as LoginDialog)
const sendOTP = async (phone: string): Promise<void> => {
  const authStore = useAuthStore.getState();
  // Send OTP directly without reCAPTCHA (reCAPTCHA will be handled during verification if needed)
  await authStore.sendOTPWithoutRecaptcha(phone);
};

const verifyOTP = async (
  phone: string,
  otp: string
): Promise<{ user: any; userExists: boolean; sessionUserId?: string }> => {
  const authStore = useAuthStore.getState();

  try {
    // Verify OTP with Firebase
    const firebaseUser = await authStore.verifyOTP(otp);

    // Get Firebase ID token
    const idToken = await firebaseUser.getIdToken();

    // Check if user exists in our database
    const userCheckResponse = await apiClient.auth.checkUser({
      firebase_token: idToken,
    });

    console.log("User check response:", userCheckResponse);

    return {
      user: firebaseUser,
      userExists: userCheckResponse.exists,
      sessionUserId: userCheckResponse.user_id,
    };
  } catch (error) {
    console.error("Firebase OTP verification error:", error);
    throw new Error("Invalid verification code. Please try again.");
  }
};

const submitRegistration = async (
  phone: string,
  name: string
): Promise<{ sessionUserId?: string }> => {
  const authStore = useAuthStore.getState();

  try {
    // Get current Firebase user and token
    const firebaseUser = authStore.firebaseUser;
    if (!firebaseUser) {
      throw new Error("No authenticated Firebase user");
    }

    const idToken = await firebaseUser.getIdToken();

    // Create user profile in our database
    const signupResponse = await apiClient.auth.signup({
      firebase_token: idToken,
      name,
      phone,
    });

    console.log("User registration successful:", signupResponse);

    return {
      sessionUserId: signupResponse.user_id || undefined, // Handle null from backend
    };
  } catch (error) {
    console.error("Registration error:", error);
    throw new Error("Registration failed. Please try again.");
  }
};

const WebLoginPage: React.FC = () => {
  const { setAuthenticated } = useNavStore();
  const [currentStep, setCurrentStep] = useState<LoginStep>("phone");

  // ===================================================================
  // EXTENSION MODE DETECTION AND POSTMESSAGE COMMUNICATION
  // ===================================================================

  // Extension mode detection from URL parameter
  const [isExtensionMode, setIsExtensionMode] = useState(false);

  // 🆕 NEW: Check auth state and sync with extension immediately on mount
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const mode = urlParams.get("mode");
    const action = urlParams.get("action");

    if (mode === "extension") {
      console.log(
        `[WebTab] 🔍 Extension mode detected, checking authentication state...`
      );

      // 🆕 AUTH CLEARING: Now handled in App.tsx before routing
      // The auth state has already been cleared by App.tsx if action was fresh_login or logout
      if (action === "fresh_login") {
        console.log(
          `[WebTab] 🔄 Fresh login flow - auth state cleared by App.tsx`
        );
        console.log(`[WebTab] 🔄 Proceeding with clean login flow...`);

        return; // Skip the auth state check below since auth is already cleared
      }

      if (action === "logout") {
        console.log(
          `[WebTab] 🚪 Logout flow - auth state cleared by App.tsx and tab will close`
        );
        return; // Don't continue with any other logic - tab will close
      }

      // Get current auth state from auth store (only for normal extension mode)
      const authStore = useAuthStore.getState();
      const isAuthenticated = authStore.isAuthenticated;
      const user = authStore.user;

      console.log(`[WebTab] 🔍 Auth state check:`, {
        isAuthenticated,
        hasUser: !!user,
        userUid: user?.uid,
        userPhone: user?.phoneNumber,
        userEmail: user?.email,
      });

      if (isAuthenticated && user) {
        console.log(
          `[WebTab] ✅ User already authenticated in extension mode!`
        );
        console.log(
          `[WebTab] 🔐 User: ${user.uid} (${user.phoneNumber || user.email})`
        );
        console.log(
          `[WebTab] 📨 Sending ALREADY_AUTHENTICATED message to extension...`
        );

        // Send message to extension that user is already logged in
        // Use timeout to ensure the content script bridge is ready
        setTimeout(() => {
          window.postMessage(
            {
              type: "EXTENSION_ALREADY_AUTHENTICATED",
              payload: {
                message:
                  "User already authenticated, extension can navigate to ChatHomePage",
                user: {
                  uid: user.uid,
                  email: user.email,
                  phoneNumber: user.phoneNumber,
                },
                userExists: true,
                firebaseUid: user.uid,
              },
            },
            window.location.origin
          );

          console.log(
            `[WebTab] ✅ ALREADY_AUTHENTICATED message sent to extension`
          );
        }, 100);

        return; // Don't continue with normal flow
      } else {
        console.log(
          `[WebTab] 🔓 User not authenticated, continuing with normal login flow`
        );
      }
    }
  }, []); // Run once on mount

  // Form data
  const [phoneNumber, setPhoneNumber] = useState("");
  const [otpValue, setOtpValue] = useState("");
  const [fullName, setFullName] = useState("");

  // Loading states
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);

  // Error states
  const [errorMessage, setErrorMessage] = useState("");

  // Extension mode setup and postMessage communication
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const mode = urlParams.get("mode");

    if (mode === "extension") {
      console.log(
        "[WebTab] Extension mode detected - setting up postMessage communication"
      );
      setIsExtensionMode(true);
      setupExtensionCommunication();
    } else {
      console.log(
        "[WebTab] Standard web mode - using direct Firebase operations"
      );

      // 🔧 SET preventAutoRedirect TRUE for web flow to stay on WebLoginPage for all 4 steps
      const authStore = useAuthStore.getState();
      authStore.setPreventAutoRedirect(true);
      console.log(
        "[WebTab] 🔒 Set preventAutoRedirect=true for web flow to stay for Step 4 (broker selection)"
      );

      // 🚀 USE FIREBASE BUILT-IN AUTH PERSISTENCE
      const unsubscribe = auth.onAuthStateChanged((user) => {
        console.log("[WebTab] 🔥 Firebase auth state changed:", !!user);

        if (user) {
          // User is authenticated - check if they completed the flow
          const authToken = localStorage.getItem("authToken");

          if (authToken === "authenticated") {
            console.log(
              "[WebTab] ✅ Authenticated user found - restoring to Step 4"
            );

            // Restore auth state
            authStore.setUser(user);
            authStore.setAuthenticated(true);
            authStore.setLoading(false);

            // Skip to Step 4 (broker selection)
            setCurrentStep("broker");
          } else {
            console.log(
              "[WebTab] 🔄 Firebase user found but flow not completed - starting from Step 1"
            );
            authStore.setLoading(false);
          }
        } else {
          console.log(
            "[WebTab] 🔓 No authenticated user - starting from Step 1"
          );
          authStore.setLoading(false);
        }
      });

      // Cleanup listener on unmount
      return () => {
        unsubscribe();
      };
    }

    // Cleanup reCAPTCHA on component unmount
    return () => {
      // Cleanup reCAPTCHA when component unmounts
      const authStore = useAuthStore.getState();
      authStore.cleanupRecaptcha();
    };
  }, []);

  /**
   * Setup DOM event communication with extension via Content Script Bridge
   * This handles messages from the extension containing UI updates and responses
   */
  const setupExtensionCommunication = () => {
    const handleDOMMessage = (event: MessageEvent) => {
      // Security: Only accept messages from same origin
      if (event.origin !== window.location.origin) {
        console.warn(
          `[WebTab] Rejected message from different origin:`,
          event.origin
        );
        return;
      }

      // Only handle messages from extension (forwarded by content script)
      // Ignore our own messages sent to the bridge (echo prevention)
      if (!event.data || !event.data.type || !event.data.fromBridge) {
        return;
      }

      console.log(`[WebTab] DOM message received:`, event.data);

      // Handle messages from extension
      switch (event.data.type) {
        case "HANDLE_OTP_REQUEST":
          console.log(
            `[WebTab] Extension requested OTP handling for:`,
            event.data.phone
          );
          handleOTPRequestFromExtension(event.data.phone);
          break;

        case "HANDLE_OTP_VERIFICATION":
          console.log(
            `[WebTab] Extension requested OTP verification for:`,
            event.data.phone,
            `OTP:`,
            event.data.otp
          );
          handleOTPVerificationForExtension(event.data.phone, event.data.otp);
          break;

        case "SET_LOADING":
          setIsLoading(event.data.isLoading);
          if (!event.data.isLoading) {
            setIsResending(false); // Clear resending state when loading stops
          }
          if (event.data.isLoading) {
            setErrorMessage(""); // Clear errors when loading starts
          }
          break;

        case "SET_ERROR":
          setErrorMessage(event.data.error);
          setIsLoading(false);
          setIsResending(false); // Clear resending state on error
          console.error(`[WebTab] Error from extension:`, event.data.error);
          break;

        case "OTP_SENT":
          setCurrentStep("otp");
          setErrorMessage("");
          setIsLoading(false);
          setIsResending(false); // Also clear resending state
          console.log(`[WebTab] OTP sent successfully, moving to OTP step`);
          break;

        case "OTP_VERIFIED":
          setErrorMessage("");
          setIsLoading(false);
          if (event.data.userExists) {
            // Existing user - extension will handle completion, close tab
            console.log(`[WebTab] Existing user verified, closing tab`);

            // 🆕 ALLOW AUTHENTICATION FOR EXISTING USERS
            const authStore = useAuthStore.getState();
            authStore.setPreventAutoRedirect(false);
            authStore.setAuthenticated(true);

            window.close();
          } else {
            // New user - set preventAutoRedirect and go to name step
            console.log(`[WebTab] New user verified, moving to name step`);
            console.log(
              `[WebTab] 🔒 Setting preventAutoRedirect for new user signup flow`
            );

            const authStore = useAuthStore.getState();
            authStore.setPreventAutoRedirect(true);

            setCurrentStep("name");
          }
          break;

        case "LOGIN_COMPLETE":
          console.log(`[WebTab] Login completed successfully, closing tab`);
          // Notify extension we're closing
          sendToExtension("CLOSE_LOGIN", {});
          window.close();
          break;

        default:
          console.warn(`[WebTab] Unknown message type: ${event.data.type}`);
      }
    };

    // Add DOM event listener for bridge communication
    window.addEventListener("message", handleDOMMessage);
    console.log(
      `[WebTab] ✅ DOM message listener attached for bridge communication`
    );

    // Cleanup function
    return () => {
      console.log(`[WebTab] Cleaning up extension communication`);
      window.removeEventListener("message", handleDOMMessage);
    };
  };

  /**
   * Send messages to extension via Content Script Bridge
   * This sends user input and requests to the extension for secure processing
   */
  const sendToExtension = (type: string, data: any = {}) => {
    const message = {
      type: `EXTENSION_${type}`, // Prefix for content script filtering
      ...data,
    };

    console.log(`[WebTab] Sending message via bridge:`, message);

    // Send to content script bridge via DOM events
    window.postMessage(message, window.location.origin);
  };

  /**
   * Handle OTP request from extension by setting up reCAPTCHA and sending OTP
   * This allows the web tab to handle all UI while keeping the extension secure
   */
  const handleOTPRequestFromExtension = async (phone: string) => {
    try {
      console.log(
        `[WebTab] Setting up reCAPTCHA and sending OTP for: ${phone}`
      );

      // Get auth store for Firebase operations
      const authStore = useAuthStore.getState();

      // Setup reCAPTCHA if not already set up
      if (!authStore.recaptchaVerifier) {
        console.log(`[WebTab] Setting up reCAPTCHA container`);
        authStore.setupRecaptcha("recaptcha-container");
      }

      // Send OTP using Firebase
      await authStore.sendOTP(phone);

      console.log(`[WebTab] OTP sent successfully, notifying extension`);

      // Notify extension of success
      sendToExtension("OTP_SENT", { success: true });

      // Update local UI state
      setCurrentStep("otp");
      setErrorMessage("");
      setIsLoading(false);
      setIsResending(false);
    } catch (error: any) {
      console.error(`[WebTab] Failed to send OTP:`, error);

      // Notify extension of error
      sendToExtension("OTP_ERROR", {
        error: error.message || "Failed to send OTP. Please try again.",
      });

      // Update local UI state
      setErrorMessage(error.message || "Failed to send OTP. Please try again.");
      setIsLoading(false);
      setIsResending(false);
    }
  };

  /**
   * Handle OTP verification request from extension
   * This allows the web tab to verify OTP using its Firebase context (where confirmationResult exists)
   */
  const handleOTPVerificationForExtension = async (
    phone: string,
    otp: string
  ): Promise<void> => {
    try {
      console.log(
        `[WebTab] 🔍 VERIFICATION ATTEMPT: phone="${phone}", otp="${otp}"`
      );
      console.log(`[WebTab] Verifying OTP using web tab's Firebase context`);

      // Get auth store for Firebase operations
      const authStore = useAuthStore.getState();

      // 🆕 PREVENT AUTOMATIC AUTHENTICATION FOR NEW USERS
      // Set preventAutoRedirect to prevent navigation until we know if user exists
      authStore.setPreventAutoRedirect(true);

      // Verify OTP with Firebase (using the confirmationResult from this context)
      const firebaseUser = await authStore.verifyOTP(otp);
      console.log(
        `[WebTab] Firebase OTP verified for user:`,
        firebaseUser?.uid
      );

      // Get Firebase ID token for backend authentication
      const idToken = await firebaseUser.getIdToken();

      // Check if user exists in our database
      const userCheckResponse = await apiClient.auth.checkUser({
        firebase_token: idToken,
      });

      console.log(`[WebTab] User check response:`, userCheckResponse);

      // Notify extension of successful verification
      // ✅ ENHANCED SYNC: Include Firebase ID token for extension database operations
      sendToExtension("OTP_VERIFIED", {
        success: true,
        userExists: userCheckResponse.exists,
        sessionUserId: userCheckResponse.user_id,
        firebaseUid: firebaseUser?.uid,
        email: firebaseUser?.email,
        phoneNumber: firebaseUser?.phoneNumber,
        // 🆕 CRITICAL: Sync Firebase context to extension
        firebaseIdToken: idToken,
        firebaseUser: {
          uid: firebaseUser?.uid,
          email: firebaseUser?.email,
          phoneNumber: firebaseUser?.phoneNumber,
          emailVerified: firebaseUser?.emailVerified,
          displayName: firebaseUser?.displayName,
        },
      });

      console.log(
        `[WebTab] OTP verification successful - userExists: ${userCheckResponse.exists}`
      );

      // Update local UI state based on user existence
      if (userCheckResponse.exists) {
        // Existing user - allow authentication and extension will handle completion
        console.log(
          `[WebTab] Existing user verified, extension will handle completion`
        );

        // 🆕 ALLOW AUTHENTICATION FOR EXISTING USERS
        authStore.setPreventAutoRedirect(false);
        authStore.setAuthenticated(true);

        // 🆕 Tell extension that authentication is complete
        console.log(
          `[WebTab] 🔐 Notifying extension that authentication is complete`
        );
        sendToExtension("LOGIN_COMPLETE", {
          success: true,
          userExists: true,
          firebaseUid: firebaseUser?.uid,
          email: firebaseUser?.email,
          phoneNumber: firebaseUser?.phoneNumber,
          // 🆕 SYNC: Also include Firebase context for existing users
          firebaseIdToken: idToken,
          firebaseUser: {
            uid: firebaseUser?.uid,
            email: firebaseUser?.email,
            phoneNumber: firebaseUser?.phoneNumber,
            emailVerified: firebaseUser?.emailVerified,
            displayName: firebaseUser?.displayName,
          },
        });

        console.log(`[WebTab] 🗑️ Web tab will be closed by extension in 500ms`);

        // Optional: Show a brief success message before tab closes
        setErrorMessage("");
        setIsLoading(false);
      } else {
        // New user - keep preventAutoRedirect active and proceed to name step
        console.log(`[WebTab] New user verified, moving to name step`);
        console.log(
          `[WebTab] 🔒 Keeping preventAutoRedirect active until signup complete`
        );

        setCurrentStep("name");
      }

      setErrorMessage("");
      setIsLoading(false);
    } catch (error: any) {
      console.error(`[WebTab] OTP verification failed:`, error);

      // Reset preventAutoRedirect on error
      const authStore = useAuthStore.getState();
      authStore.setPreventAutoRedirect(false);

      // Notify extension of error
      sendToExtension("OTP_ERROR", {
        error: error.message || "Invalid verification code. Please try again.",
      });

      // Update local UI state
      setErrorMessage(
        error.message || "Invalid verification code. Please try again."
      );
      setIsLoading(false);
    }
  };

  // Clear error when user starts typing
  const handlePhoneNumberChange = (value: string) => {
    setPhoneNumber(value);
    if (errorMessage) setErrorMessage("");
  };

  const handleOtpChange = (value: string) => {
    setOtpValue(value);
    if (errorMessage) setErrorMessage("");
  };

  const handleNameChange = (value: string) => {
    setFullName(value);
    if (errorMessage) setErrorMessage("");
  };

  // Step 1: Send OTP
  const handleContinue = async () => {
    setErrorMessage("");

    if (isExtensionMode) {
      // EXTENSION MODE: Send request to extension for secure processing
      console.log(
        `[WebTab] Sending OTP request to extension for phone: ${phoneNumber}`
      );
      sendToExtension("SEND_OTP", { phone: phoneNumber });
      return;
    }

    // STANDARD WEB MODE: Handle Firebase operations directly
    setIsLoading(true);

    try {
      await sendOTP(phoneNumber);
      setCurrentStep("otp");
    } catch (error) {
      // Convert Firebase errors to user-friendly messages
      const errorMessage = getUserFriendlyErrorMessage(error);
      setErrorMessage(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Step 2: Verify OTP
  const handleVerify = async () => {
    setErrorMessage("");

    if (isExtensionMode) {
      // EXTENSION MODE: Send request to extension for secure processing
      console.log(
        `[WebTab] Sending OTP verification to extension: ${phoneNumber} / ${otpValue}`
      );
      sendToExtension("VERIFY_OTP", { phone: phoneNumber, otp: otpValue });
      return;
    }

    // STANDARD WEB MODE: Handle Firebase operations directly
    setIsLoading(true);

    try {
      const result = await verifyOTP(phoneNumber, otpValue);

      if (result.userExists && result.sessionUserId) {
        // Existing user - store session and go to broker selection (web flow includes broker step)
        localStorage.setItem("sessionUserId", result.sessionUserId);

        // 🔧 SET AUTHENTICATION STATE FOR EXISTING USERS IN WEB MODE
        const authStore = useAuthStore.getState();
        console.log(
          `[WebTab] 🔐 Existing user verified - enabling authentication for web flow`
        );

        // Keep preventAutoRedirect true so we stay on WebLoginPage for Step 4
        // authStore.setPreventAutoRedirect(true); // Already set from earlier, don't need to set again
        authStore.setAuthenticated(true);
        console.log(
          `[WebTab] 🔒 Keeping preventAutoRedirect=true for existing user to stay for Step 4`
        );

        // Go to broker selection (web flow requires broker selection even for existing users)
        setCurrentStep("broker");
      } else {
        // New user - go to name input step
        setCurrentStep("name");
      }
    } catch (error) {
      // Convert Firebase errors to user-friendly messages
      const errorMessage = getUserFriendlyErrorMessage(error);
      setErrorMessage(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Step 2: Resend OTP
  const handleResend = async () => {
    setErrorMessage("");

    if (isExtensionMode) {
      // EXTENSION MODE: Send resend request to extension
      console.log(
        `[WebTab] Sending OTP resend request to extension for phone: ${phoneNumber}`
      );
      setIsResending(true); // Set resending state for UI feedback
      sendToExtension("SEND_OTP", { phone: phoneNumber });
      return;
    }

    // STANDARD WEB MODE: Handle Firebase operations directly
    setIsResending(true);

    try {
      await sendOTP(phoneNumber);
    } catch (error) {
      // Convert Firebase errors to user-friendly messages
      const errorMessage = getUserFriendlyErrorMessage(error);
      setErrorMessage(errorMessage);
    } finally {
      setIsResending(false);
    }
  };

  // Step 3: Submit name and proceed to broker selection (web flow includes broker step)
  const handleSubmit = async () => {
    setErrorMessage("");

    if (isExtensionMode) {
      // EXTENSION MODE: Send registration completion to extension (SKIP BROKER SELECTION)
      console.log(
        `[WebTab] Sending registration completion to extension: ${fullName}`
      );
      sendToExtension("COMPLETE_REGISTRATION", {
        phone: phoneNumber,
        name: fullName,
      });

      // Extension will handle authentication and navigation, then close this tab
      return;
    }

    // STANDARD WEB MODE: Handle registration and continue to broker selection
    setIsLoading(true);

    try {
      const result = await submitRegistration(phoneNumber, fullName);

      // Store sessionUserId if it exists (though it will be null for new users until first WebSocket message)
      if (result.sessionUserId) {
        localStorage.setItem("sessionUserId", result.sessionUserId);
      }

      // 🆕 COMPLETE AUTHENTICATION FOR NEW USERS IN WEB MODE
      const authStore = useAuthStore.getState();
      console.log(
        `[WebTab] 🔐 Registration complete - enabling authentication for new user`
      );

      // 🔧 KEEP preventAutoRedirect TRUE for web flow to stay on WebLoginPage for Step 4
      // DO NOT reset preventAutoRedirect - we need it to stay true so App.tsx doesn't navigate to ChatHomePage
      // authStore.setPreventAutoRedirect(false); // ❌ REMOVED - this was causing navigation away
      console.log(
        `[WebTab] 🔒 Keeping preventAutoRedirect=true to stay for Step 4 (broker selection)`
      );

      authStore.setAuthenticated(true);

      // Move to broker selection step
      setCurrentStep("broker");
    } catch (error) {
      // Convert Firebase errors to user-friendly messages
      const errorMessage = getUserFriendlyErrorMessage(error);
      setErrorMessage(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Step 4: Final submission with broker selection
  const handleFinalSubmit = async (brokerId: string) => {
    // Save auth token and broker selection
    localStorage.setItem("authToken", "authenticated");
    localStorage.setItem("selectedBroker", brokerId);

    // Complete authentication but ALWAYS prevent redirect for pure web flow
    const authStore = useAuthStore.getState();
    authStore.setAuthenticated(true);

    // 🚀 FIX: Pure web flow NEVER goes to ChatHomePage - always stays on Step 4
    const urlParams = new URLSearchParams(window.location.search);
    const mode = urlParams.get("mode");

    if (mode !== "extension") {
      // Pure web flow - ALWAYS stay on Step 4, never go to ChatHomePage
      console.log(
        "[WebTab] 🔒 Step 4 completed for pure web flow - staying on broker selection page"
      );
      authStore.setPreventAutoRedirect(true); // Always prevent navigation for web users
    } else {
      // Extension flow - keep preventing redirect as extension handles navigation
      console.log(
        "[WebTab] 🔒 Step 4 completed for extension flow - preventing web navigation"
      );
      authStore.setPreventAutoRedirect(true);
    }

    authStore.setLoading(false);

    // Also set nav store authentication
    setAuthenticated(true);
    const navStore = useNavStore.getState();
    navStore.setSuppressAutoNavigation(true);

    // Get broker URL and open in new tab
    const broker = brokers.find((b) => b.id === brokerId);
    if (broker) {
      window.open(getBrokerUrl(brokerId), "_blank");
      console.log(`Opened ${broker.name} in new tab`);
    }

    // Pure web flow always stays on this page - users can select multiple brokers
    // Extension flow also stays on this page
    console.log(
      `[WebTab] 🎉 Broker selection completed: ${brokerId} - staying on Step 4`
    );
  };

  return (
    <div className="min-h-screen bg-[#F4F6FA] flex items-center justify-center py-8">
      {renderLoginStep({
        currentStep,
        isDialog: false, // Web card version
        phoneNumber,
        otpValue,
        fullName,
        isLoading,
        isResending,
        errorMessage,
        onPhoneNumberChange: handlePhoneNumberChange,
        onOtpChange: handleOtpChange,
        onNameChange: handleNameChange,
        onContinue: handleContinue,
        onVerify: handleVerify,
        onSubmit: handleSubmit,
        onBrokerSubmit: handleFinalSubmit,
        onResend: handleResend,
      })}

      {/* reCAPTCHA container - positioned to be clickable above content */}
      <div
        id="recaptcha-container"
        style={{
          position: "fixed",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          zIndex: 1000000, // Very high z-index to appear above everything
          pointerEvents: "auto",
          // Ensure it's always clickable
          isolation: "isolate",
        }}
      ></div>

      {/* CSS to ensure reCAPTCHA is always clickable */}
      <style>{`
        /* Force reCAPTCHA elements to be clickable even when body has pointer-events: none */
        #recaptcha-container,
        #recaptcha-container *,
        div[style*="z-index: 2000000000"],
        div[style*="z-index: 2000000000"] *,
        iframe[src*="recaptcha"],
        iframe[name*="recaptcha"],
        iframe[title*="recaptcha"],
        .grecaptcha-badge,
        .grecaptcha-badge * {
          pointer-events: auto !important;
          position: relative !important;
        }
        
        /* Specific targeting for reCAPTCHA challenge iframe */
        div[style*="position: absolute"][style*="width: 400px"][style*="height: 580px"] {
          pointer-events: auto !important;
          z-index: 1000001 !important;
        }
      `}</style>
    </div>
  );
};

export default WebLoginPage;
