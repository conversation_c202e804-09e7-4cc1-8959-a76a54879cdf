import React, { useState } from 'react';
import CTAButton from '../components/CTAButton';
import Card from '../components/Card';
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '../components/Dialog';
import Input from '../components/Input';
import LoadingBars from '../components/LoadingBars';
import MonitoringCard from '../components/MonitoringCard';
import NotificationCard from '../components/NotificationCard';
import {
  NotificationPanel,
  NotificationPanelTrigger,
  NotificationPanelContent,
  NotificationPanelClose,
  OrderGptLogo,
} from '../components/NotificationPanel';
import OrderCard from '../components/OrderCard';
import Pill from '../components/Pill';
import Sidebar from '../components/Sidebar';

// Import icons
import ChevronDownIcon from '../assets/chevron-down.svg';
import BellIcon from '../assets/bell-01.svg';
import XIcon from '../assets/x.svg';

const ComponentsDemo: React.FC = () => {
  const [inputValue, setInputValue] = useState('');
  const [phoneValue, setPhoneValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleLoadingToggle = () => {
    setIsLoading(!isLoading);
  };

  return (
    <div className="flex min-h-screen bg-gray-50">
      {/* Sidebar */}
      <Sidebar />
      
      {/* Main Content */}
      <div className="flex-1 p-8 overflow-y-auto">
        <div className="max-w-6xl mx-auto space-y-12">
          
          {/* Header */}
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold text-gray-900">Components Demo</h1>
            <p className="text-lg text-gray-600">
              Showcase of all available UI components with interactive examples
            </p>
          </div>

          {/* Buttons Section */}
          <section className="space-y-6">
            <h2 className="text-2xl font-semibold text-gray-800">Buttons</h2>
            <Card className="p-6">
              <div className="space-y-4">
                <div className="flex flex-wrap gap-4">
                  <CTAButton onClick={() => alert('Primary button clicked!')}>
                    Continue
                  </CTAButton>
                  <CTAButton onClick={() => alert('Secondary action!')}>
                    Place Order
                  </CTAButton>
                  <CTAButton 
                    onClick={handleLoadingToggle}
                    className="bg-green-600 border-green-700"
                  >
                    Toggle Loading
                  </CTAButton>
                </div>
                <p className="text-sm text-gray-600">
                  Primary action buttons with hover and active states
                </p>
              </div>
            </Card>
          </section>

          {/* Inputs Section */}
          <section className="space-y-6">
            <h2 className="text-2xl font-semibold text-gray-800">Input Fields</h2>
            <Card className="p-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <Input
                    variant="text"
                    label="Full Name"
                    placeholder="Enter your full name"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                  />
                  <Input
                    variant="phone"
                    label="Phone Number"
                    placeholder="9975846515"
                    value={phoneValue}
                    onChange={(e) => setPhoneValue(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-700">Current Values:</p>
                  <p className="text-sm text-gray-600">Name: {inputValue || 'Empty'}</p>
                  <p className="text-sm text-gray-600">Phone: {phoneValue || 'Empty'}</p>
                </div>
              </div>
            </Card>
          </section>

          {/* Loading States */}
          <section className="space-y-6">
            <h2 className="text-2xl font-semibold text-gray-800">Loading States</h2>
            <Card className="p-6">
              <div className="space-y-4">
                {isLoading ? (
                  <LoadingBars className="w-full max-w-md" />
                ) : (
                  <div className="w-full max-w-md p-4 bg-gray-100 rounded-lg">
                    <p className="text-gray-600">Content loaded! Click "Toggle Loading" to see shimmer effect.</p>
                  </div>
                )}
                <p className="text-sm text-gray-600">
                  Shimmer loading animation for content placeholders
                </p>
              </div>
            </Card>
          </section>

          {/* Pills Section */}
          <section className="space-y-6">
            <h2 className="text-2xl font-semibold text-gray-800">Status Pills</h2>
            <Card className="p-6">
              <div className="flex flex-wrap gap-3">
                <Pill>Zerodha</Pill>
                <Pill>Active</Pill>
                <Pill>Connected</Pill>
                <Pill>Verified</Pill>
              </div>
              <p className="text-sm text-gray-600 mt-4">
                Status indicators with green styling for broker connections
              </p>
            </Card>
          </section>

          {/* Trading Cards Section */}
          <section className="space-y-6">
            <h2 className="text-2xl font-semibold text-gray-800">Trading Cards</h2>
            <div className="grid md:grid-cols-2 gap-6">
              
              {/* Order Cards */}
              <Card className="p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Order Cards</h3>
                <div className="space-y-3">
                  <OrderCard
                    stockName="INFOSYS"
                    value="25000.55"
                    details="BSE | 100/100"
                    percentageChange={5.4}
                    onClick={() => alert('INFOSYS order clicked')}
                  />
                  <OrderCard
                    stockName="TECHM"
                    value="12345"
                    details="NSE | 50/50"
                    percentageChange={-2.1}
                    onClick={() => alert('TECHM order clicked')}
                  />
                  <OrderCard
                    stockName="TCS"
                    value="45678.90"
                    details="BSE | 75/100"
                    percentageChange={1.8}
                    onClick={() => alert('TCS order clicked')}
                  />
                </div>
              </Card>

              {/* Monitoring Cards */}
              <Card className="p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Monitoring Cards</h3>
                <div className="space-y-3">
                  <MonitoringCard
                    stockName="INFOSYS"
                    status="0/100"
                    triggerPrice="25000"
                    currentPrice="26540"
                  />
                  <MonitoringCard
                    stockName="RELIANCE"
                    status="50/200"
                    triggerPrice="2800"
                    currentPrice="2750"
                  />
                </div>
              </Card>
            </div>
          </section>

          {/* Notifications Section */}
          <section className="space-y-6">
            <h2 className="text-2xl font-semibold text-gray-800">Notifications</h2>
            <div className="grid md:grid-cols-2 gap-6">
              
              {/* Notification Cards */}
              <Card className="p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Notification Cards</h3>
                <div className="space-y-4">
                  <NotificationCard
                    variant="positive"
                    title="Infosys order executed"
                    time="14h"
                    description="NIFTY touched 25,000. We have successfully placed 100 shares..."
                    actionText="View Order"
                    onActionClick={() => alert('View positive order')}
                  />
                  <NotificationCard
                    variant="negative"
                    title="Infosys order failed"
                    time="2h"
                    description="Order execution failed due to insufficient funds..."
                    actionText="Retry Order"
                    onActionClick={() => alert('Retry failed order')}
                  />
                </div>
              </Card>

              {/* Notification Panel */}
              <Card className="p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Notification Panel</h3>
                <div className="space-y-4">
                  <NotificationPanel>
                    <NotificationPanelTrigger asChild>
                      <button className="flex items-center gap-2 rounded-lg bg-blue-500 px-4 py-2 text-white hover:bg-blue-600">
                        <img src={BellIcon} alt="Open Notifications" className="h-5 w-5" />
                        Open Notification Panel
                      </button>
                    </NotificationPanelTrigger>
                    <NotificationPanelContent>
                      <div className="flex flex-col gap-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-1.5">
                            <OrderGptLogo />
                            <p className="text-xs text-[#665beb]">OrderGPT</p>
                            <p className="text-xs text-gray-500">now</p>
                          </div>
                          <NotificationPanelClose>
                            <img src={ChevronDownIcon} alt="Close" className="h-4 w-4 rotate-180" />
                          </NotificationPanelClose>
                        </div>
                        <div className="flex flex-col gap-1">
                          <p className="text-[15px] font-bold text-gray-800">Infosys order executed</p>
                          <p className="text-[13px] text-gray-600">
                            NIFTY touched 25,000. We have successfully placed 100 shares of Infosys as per your setup.
                          </p>
                        </div>
                      </div>
                    </NotificationPanelContent>
                  </NotificationPanel>
                  <p className="text-sm text-gray-600">
                    Click the button above to see the notification panel in action
                  </p>
                </div>
              </Card>
            </div>
          </section>

          {/* Dialog Section */}
          <section className="space-y-6">
            <h2 className="text-2xl font-semibold text-gray-800">Dialogs</h2>
            <Card className="p-6">
              <div className="space-y-4">
                <Dialog>
                  <DialogTrigger asChild>
                    <CTAButton>Open Dialog</CTAButton>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Profile Edit</DialogTitle>
                      <DialogClose>
                        <img src={XIcon} alt="Close" className="h-6 w-6" />
                      </DialogClose>
                    </DialogHeader>
                    <div className="p-4">
                      <p className="text-gray-600 mb-4">This is the main content of the bottom sheet dialog.</p>
                      <div className="space-y-4">
                        <Input
                          variant="text"
                          label="Update Name"
                          placeholder="Enter new name"
                        />
                        <div className="flex gap-3">
                          <CTAButton className="flex-1">Save Changes</CTAButton>
                          <DialogClose asChild>
                            <button className="flex-1 rounded-lg border border-gray-300 px-4 py-3 text-gray-700 hover:bg-gray-50">
                              Cancel
                            </button>
                          </DialogClose>
                        </div>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
                <p className="text-sm text-gray-600">
                  Bottom sheet modal dialog with overlay and slide-up animation
                </p>
              </div>
            </Card>
          </section>

        </div>
      </div>
    </div>
  );
};

export default ComponentsDemo;
