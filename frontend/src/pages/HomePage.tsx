import React from "react";
import { navigate, goBack } from "../navigation/pageNavigator";
import { useNavStore } from "../stores/navStore";
import Card from "../components/Card";
import CTAButton from "../components/CTAButton";
import Input from "../components/Input";
import Pill from "../components/Pill";
import Sidebar from "../components/Sidebar";
import LoadingBars from "../components/LoadingBars";
import MonitoringCard from "../components/MonitoringCard";
import OrderCard from "../components/OrderCard";
import BellIcon from "../assets/bell-01.svg";
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "../components/Dialog";
import CloseIcon from "../assets/x.svg"; // Make sure you have this icon

import {
  NotificationPanel,
  NotificationPanelTrigger,
  NotificationPanelContent,
  NotificationPanelClose,
  OrderGptLogo,
} from "../components/NotificationPanel";
import ChevronDownIcon from "../assets/chevron-down.svg";
import NotificationCard from "../components/NotificationCard";

const HomePage: React.FC = () => {
  const { isAuthenticated, setAuthenticated } = useNavStore();

  return (
    <div>
      <h1>Home Page</h1>
      <div style={{ display: "flex", gap: "10px", marginTop: "10px" }}>
        <button onClick={() => navigate()}>Navigate</button>
        <button onClick={() => goBack()}>Go Back</button>
      </div>
      <Card>card</Card>

      <CTAButton>Click Here</CTAButton>

      <Input variant="text" label="Full Name" />
      <Input variant="phone" label="Phone Number" />

      <Pill>Zerodha</Pill>

      <Sidebar />

      <LoadingBars />

      <MonitoringCard
        className="m-4"
        stockName="INFY"
        status="0/100"
        triggerPrice="10000000.3444"
        currentPrice="10000000.4333"
      />

      <OrderCard
        stockName="INFY"
        value="10000000.4333"
        details="BSE | 100/100"
        percentageChange={2}
        onClick={() => {}}
      />

      <Dialog>
        <DialogTrigger asChild>
          <button className="rounded-md bg-blue-500 px-4 py-2 text-white">
            Open Dialog
          </button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Profile Edit</DialogTitle>
            <DialogClose>
              <img src={CloseIcon} alt="Close" className="h-6 w-6" />
            </DialogClose>
          </DialogHeader>
          <div className="p-4">
            <p>This is the main content of the bottom sheet dialog.</p>
            <p>You can add any form elements or other components here.</p>
          </div>
        </DialogContent>
      </Dialog>

      <NotificationPanel>
        <NotificationPanelTrigger asChild>
          <button className="rounded-full p-2 hover:bg-gray-100">
            <img src={BellIcon} alt="Open Notifications" className="h-6 w-6" />
          </button>
        </NotificationPanelTrigger>
        <NotificationPanelContent>
          <div className="flex flex-col gap-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1.5">
                <OrderGptLogo />
                <p className="text-xs text-[#665beb]">OrderGPT</p>
                <p className="text-xs text-gray-500">now</p>
              </div>
              <NotificationPanelClose>
                <img
                  src={ChevronDownIcon}
                  alt="Close"
                  className="h-4 w-4 rotate-180"
                />
              </NotificationPanelClose>
            </div>
            <div className="flex flex-col gap-1">
              <p className="text-[15px] font-bold text-gray-800">
                Infosys order executed
              </p>
              <p className="text-[13px] text-gray-600">
                NIFTY touched 25,000. We have successfully placed 100 shares of
                Infosys as per your setup.
              </p>
            </div>
          </div>
        </NotificationPanelContent>
      </NotificationPanel>

      <NotificationCard
        variant="positive"
        title="Infosys order executed"
        time="14h"
        description="NIFTY touched 25,000. We have successfully placed 100 shares..."
        actionText="View Order"
        onActionClick={() => {}}
      />

      <div style={{ marginTop: "20px" }}>
        <label>
          <input
            type="checkbox"
            checked={isAuthenticated}
            onChange={(e) => setAuthenticated(e.target.checked)}
          />
          Set Authenticated
        </label>
      </div>
    </div>
  );
};

export default HomePage;
