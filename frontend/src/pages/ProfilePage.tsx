import React, { useEffect, useState } from 'react';
import { navigate, goBack } from '../navigation/pageNavigator';
import { fetchData } from '../utils/apiClient';

interface UserProfile {
  username: string;
  email: string;
}

const ProfilePage: React.FC = () => {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchData('/profile.json')
      .then(data => {
        setProfile(data);
      })
      .catch(error => {
        console.error("Failed to fetch profile:", error);
        setProfile(null);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  return (
    <div>
      <h1>Profile Page</h1>
      <div>
        {loading ? (
          <p>Loading profile...</p>
        ) : profile ? (
          <div>
            <p><strong>Username:</strong> {profile.username}</p>
            <p><strong>Email:</strong> {profile.email}</p>
          </div>
        ) : (
          <p>Could not load profile data.</p>
        )}
      </div>
      <div style={{ display: 'flex', gap: '10px', marginTop: '10px' }}>
        <button onClick={() => navigate()}>Navigate</button>
        <button onClick={() => goBack()}>Go Back</button>
      </div>
    </div>
  );
};

export default ProfilePage; 