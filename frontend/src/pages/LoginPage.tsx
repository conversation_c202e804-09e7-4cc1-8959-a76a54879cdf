/**
 * LoginPage Component
 *
 * This component implements the login interface for OrderGPT based on the actual design.
 * Features:
 * - Purple starburst logo with OrderGPT branding
 * - Dashed purple border container
 * - Feature cards with bar chart icons
 * - Purple login button with diagonal stripes
 * - Authentication state management
 * - Navigation to ChatHomePage after authentication
 */

import React, { useEffect, useState } from "react";
import { useNavStore } from "../stores/navStore";
import { useAuthStore } from "../stores/authStore";
import { navigate } from "../navigation/pageNavigator";
import { useLoginMode } from "../contexts/LoginModeContext";
import WebLoginPage from "./WebLoginPage";
import Card from "../components/Card";
import Pill from "../components/Pill";
import CTAButton from "../components/CTAButton";
import LoginDialog from "../components/LoginDialog";
import { apiClient } from "../utils/apiClient";
import { getUserFriendlyErrorMessage } from "../utils/errorUtils";

// Chrome Extension API usage with proper type safety

declare global {
  interface Window {
    otpPromiseResolvers?: {
      resolve: (value: void) => void;
      reject: (reason: any) => void;
    };
  }
}

// Utility function to safely access Chrome APIs
const getChromeAPI = () => {
  if (typeof chrome !== "undefined" && chrome?.tabs && chrome?.runtime) {
    return chrome;
  }
  return null;
};
import OrderGPTIcon from "../assets/orderGPT.svg";
import PinIcon from "../assets/pin.svg";
import LoginIcon from "../assets/loginIcon.svg";

// Figma Design Tokens (from OrderGPT-variables-full.json)
const designTokens = {
  // Brand Colors from Figma
  brand900: "rgb(18, 18, 51)", // Darkest purple
  brand800: "rgb(38, 33, 102)", // Very dark purple
  brand700: "rgb(56, 51, 153)", // Dark purple
  brand600: "rgb(74, 66, 201)", // Medium dark purple
  brand500: "rgb(92, 84, 252)", // Primary brand purple
  brand400: "rgb(125, 117, 252)", // Light purple
  brand300: "rgb(158, 153, 255)", // Lighter purple
  brand200: "rgb(191, 186, 255)", // Very light purple
  brand100: "rgb(222, 222, 255)", // Lightest purple
  brand50: "rgb(240, 237, 255)", // Background purple

  // Semantic Colors
  success: "rgb(31, 139, 77)", // Green for Zerodha pill
  successBg: "rgb(233, 247, 239)", // Light green background
  successBorder: "rgb(169, 223, 191)", // Light green border

  // Neutral Colors
  white: "rgb(255, 255, 255)",
  gray50: "rgb(248, 250, 252)",
  gray100: "rgb(241, 245, 249)",
  gray200: "rgb(226, 232, 240)",
  gray300: "rgb(203, 213, 225)",
  gray400: "rgb(148, 163, 184)",
  gray500: "rgb(100, 116, 139)",
  gray600: "rgb(71, 85, 105)",
  gray700: "rgb(51, 65, 85)",
  gray800: "rgb(30, 41, 59)",
  gray900: "rgb(15, 23, 42)",

  // Text Colors
  textPrimary: "rgb(15, 23, 42)", // gray900
  textSecondary: "rgb(100, 116, 139)", // gray500
  textMuted: "rgb(148, 163, 184)", // gray400
};

// Feature data
const features = [
  {
    id: 1,
    text: "Get daily stock suggestions tailored to your goals, behavior, and market trends.",
  },
  {
    id: 2,
    text: "Know what's working, what's risky, and how to optimize your investments.",
  },
  {
    id: 3,
    text: "Stay ahead of the curve with real-time market movement forecasts.",
  },
  {
    id: 4,
    text: "Get notified the moment key events impact your trades.",
  },
];

// Sub-component: Header
const Header: React.FC = () => {
  return (
    <Card className="border-b-2 border-white bg-[#f4f6fa]">
      <div className="flex flex-row items-center p-4 gap-4">
        {/* Logo Section */}
        <div className="flex-1 flex flex-row items-start gap-2">
          {/* OrderGPT Logo with Gradient Background */}
          <div className="relative w-8 h-8 rounded-lg bg-gradient-to-r from-[#5c54fd] to-[#a330e5] flex items-center justify-center shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)] border-2 border-[rgba(255,255,255,0.12)]">
            <div className="absolute inset-0 rounded-lg shadow-[0px_0px_0px_1px_inset_rgba(16,24,40,0.18),0px_-2px_0px_0px_inset_rgba(16,24,40,0.05)]" />
            <img
              src={OrderGPTIcon}
              alt="OrderGPT"
              className="w-[21.333px] h-[21.333px] relative z-10"
            />
          </div>

          {/* Text and Pill Section */}
          <div className="flex flex-col gap-1 justify-center">
            <h1 className="font-bold text-[18px] leading-[26px] text-[#181e29] whitespace-nowrap">
              OrderGPT
            </h1>
            <Pill className="text-[12px] leading-[16px]">Zerodha</Pill>
          </div>
        </div>

        {/* Pin Icon Section */}
        <div className="flex flex-row gap-4">
          {/* Pin Icon */}
          <button className="w-6 h-6 flex items-center justify-center hover:bg-gray-100 rounded transition-colors relative">
            <img src={PinIcon} alt="Pin" className="w-6 h-6" />
          </button>
        </div>
      </div>
    </Card>
  );
};

// Sub-component: Feature Card
interface FeatureCardProps {
  feature: (typeof features)[0];
}

const FeatureCard: React.FC<FeatureCardProps> = ({ feature }) => {
  return (
    <div className="self-stretch p-4 bg-white rounded-2xl outline outline-1 outline-offset-[-1px] outline-gray-200 inline-flex justify-start items-center gap-4">
      {/* Icon Section */}
      <img src={LoginIcon} alt="Feature" className="w-10 h-10" />

      {/* Content Section */}
      <div className="flex-1 justify-start text-gray-900 text-base font-medium font-['Inter'] leading-normal">
        {feature.text}
      </div>
    </div>
  );
};

// ===================================================================
// PHASE 2 & 3: SECURE EXTENSION LOGIN WITH POSTMESSAGE COMMUNICATION
// ===================================================================

/**
 * Firebase Authentication Functions for Extension Context
 * These handle secure authentication operations within the extension,
 * keeping sensitive Firebase tokens isolated from the web tab.
 */

// Send OTP via Firebase (Extension context only)
// This is now just a placeholder - actual OTP sending is handled by the web tab
const sendOTP = async (phone: string): Promise<void> => {
  // This function is no longer used in the new flow
  // OTP sending is handled directly by the web tab via handleOTPRequestFromExtension
  console.log(
    `[Extension] sendOTP called - this should not happen in the new flow`
  );
  throw new Error("sendOTP should not be called in the new flow");
};

// Verify OTP and check user existence (Extension context only)
// This is now just a placeholder - actual OTP verification is handled by the web tab
const verifyOTP = async (
  phone: string,
  otp: string
): Promise<{ user: any; userExists: boolean; sessionUserId?: string }> => {
  console.log(
    `[Extension] 🔍 VERIFICATION ATTEMPT: phone=${phone}, otp=${otp}`
  );
  console.log(
    `[Extension] verifyOTP called - this should not happen in the new flow`
  );
  console.log(
    `[Extension] OTP verification is now handled by the web tab via handleOTPVerificationForExtension`
  );
  throw new Error(
    "verifyOTP should not be called in the new flow - verification handled by web tab"
  );
};

// Submit registration for new users (Extension context only)
const submitRegistration = async (
  phone: string,
  name: string
): Promise<{ success: boolean; sessionUserId?: string }> => {
  const authStore = useAuthStore.getState();

  try {
    let firebaseUser = authStore.firebaseUser;

    // 🆕 FALLBACK: If no Firebase user in memory, try to restore from localStorage
    if (!firebaseUser) {
      console.log(
        `[Extension] 🔄 No Firebase user in memory, attempting localStorage restore`
      );
      const storedToken = localStorage.getItem("firebase_id_token");
      const storedUid = localStorage.getItem("firebase_uid");

      if (storedToken && storedUid) {
        console.log(
          `[Extension] 📱 Restoring Firebase context from localStorage`
        );
        firebaseUser = {
          uid: storedUid,
          getIdToken: () => Promise.resolve(storedToken),
        } as any;

        // Update authStore with restored context
        authStore.firebaseUser = firebaseUser;
        console.log(
          `[Extension] ✅ Firebase context restored from localStorage`
        );
      }
    }

    if (!firebaseUser) {
      throw new Error("No authenticated user found");
    }

    // Get Firebase ID token
    const idToken = await firebaseUser.getIdToken();

    // Register user with our backend
    const registrationResponse = await apiClient.auth.signup({
      firebase_token: idToken,
      name: name,
      phone: phone,
    });

    return {
      success: true,
      sessionUserId: registrationResponse.user_id,
    };
  } catch (error) {
    console.error("Registration error:", error);
    throw new Error("Registration failed. Please try again.");
  }
};

/**
 * PostMessage Communication Functions
 * These handle secure communication between extension and web login tab
 */

// Send messages to web login tab
const sendToWebTab = (loginWindow: Window, type: string, data: any = {}) => {
  if (loginWindow && !loginWindow.closed) {
    loginWindow.postMessage({ type, ...data }, window.location.origin);
    console.log(`[Extension] Sent message to web tab:`, { type, ...data });
  } else {
    console.warn(`[Extension] Cannot send message - web tab is closed`);
  }
};

/**
 * Firebase Operation Handlers
 * These execute secure Firebase operations and communicate results to web tab
 */

// Handle OTP sending (Secure - Extension context only)
const handleSendOTP = async (phone: string, loginWindow: Window) => {
  console.log(`[Extension] Handling OTP send for phone: ${phone}`);
  sendToWebTab(loginWindow, "SET_LOADING", { isLoading: true });

  try {
    await sendOTP(phone);
    sendToWebTab(loginWindow, "OTP_SENT", { success: true });
    console.log(`[Extension] OTP sent successfully`);
  } catch (error: any) {
    const errorMessage = getUserFriendlyErrorMessage(error);
    sendToWebTab(loginWindow, "SET_ERROR", { error: errorMessage });
    console.error(`[Extension] OTP send failed:`, error);
  } finally {
    sendToWebTab(loginWindow, "SET_LOADING", { isLoading: false });
  }
};

// Handle OTP verification (Secure - Extension context only)
const handleVerifyOTP = async (
  phone: string,
  otp: string,
  loginWindow: Window
) => {
  console.log(`[Extension] Handling OTP verification for phone: ${phone}`);
  sendToWebTab(loginWindow, "SET_LOADING", { isLoading: true });

  try {
    const result = await verifyOTP(phone, otp);
    sendToWebTab(loginWindow, "OTP_VERIFIED", {
      success: true,
      userExists: result.userExists,
      sessionUserId: result.sessionUserId,
    });
    console.log(`[Extension] OTP verified successfully:`, {
      userExists: result.userExists,
    });
  } catch (error: any) {
    const errorMessage = getUserFriendlyErrorMessage(error);
    sendToWebTab(loginWindow, "SET_ERROR", { error: errorMessage });
    console.error(`[Extension] OTP verification failed:`, error);
  } finally {
    sendToWebTab(loginWindow, "SET_LOADING", { isLoading: false });
  }
};

// Handle registration completion (Secure - Extension context only)
const handleCompleteRegistration = async (
  phone: string,
  name: string,
  loginWindow: Window
) => {
  console.log(`[Extension] Handling registration completion for: ${name}`);
  sendToWebTab(loginWindow, "SET_LOADING", { isLoading: true });

  try {
    const result = await submitRegistration(phone, name);

    // Store session data securely in extension context
    if (result.sessionUserId) {
      localStorage.setItem("sessionUserId", result.sessionUserId);
      console.log(`[Extension] Session ID stored: ${result.sessionUserId}`);
    }

    // Complete authentication in extension context (SECURE)
    const authStore = useAuthStore.getState();
    const { setAuthenticated } = useNavStore.getState();

    authStore.setAuthenticated(true);
    setAuthenticated(true);
    authStore.setLoading(false);

    console.log(`[Extension] Authentication completed successfully`);

    // Notify web tab to close
    sendToWebTab(loginWindow, "LOGIN_COMPLETE", { success: true });

    // Navigate to ChatHomePage in extension
    navigate();
    console.log(`[Extension] Navigated to ChatHomePage`);
  } catch (error: any) {
    const errorMessage = getUserFriendlyErrorMessage(error);
    sendToWebTab(loginWindow, "SET_ERROR", { error: errorMessage });
    console.error(`[Extension] Registration completion failed:`, error);
  } finally {
    sendToWebTab(loginWindow, "SET_LOADING", { isLoading: false });
  }
};

/**
 * ===============================================================================
 * CHROME RUNTIME MESSAGING HANDLERS (New secure approach)
 * ===============================================================================
 */

// Send messages to web login tab via Content Script Bridge
const sendToWebTabRuntime = (
  tabId: number | undefined,
  type: string,
  data: any = {}
) => {
  if (!tabId) {
    console.warn(`[Extension] Cannot send message - no tab ID provided`);
    return;
  }

  const message = { type, ...data };

  // Use chrome.tabs.sendMessage to send to the content script in the specific tab
  const chromeAPI = getChromeAPI();
  if (!chromeAPI) {
    console.warn(`[Extension] Chrome API not available`);
    return;
  }

  chromeAPI.tabs.sendMessage(tabId, message, (response: any) => {
    if (chromeAPI.runtime.lastError) {
      console.warn(
        `[Extension] Failed to send message to tab ${tabId}:`,
        chromeAPI.runtime.lastError
      );
    } else {
      console.log(`[Extension] ✅ Sent message to tab ${tabId}:`, message);
      console.log(`[Extension] Response:`, response);
    }
  });
};

// Handle OTP sending via Chrome Runtime (Secure - Extension context only)
const handleSendOTPRuntime = async (
  phone: string,
  tabId: number | undefined,
  sendResponse: (response?: any) => void
) => {
  console.log(`[Extension] Runtime: Handling OTP send for phone: ${phone}`);
  console.log(`[Extension] Telling web tab to handle OTP sending directly`);

  // Send loading state to web tab
  sendToWebTabRuntime(tabId, "SET_LOADING", { isLoading: true });

  // Tell the web tab to handle the OTP sending (reCAPTCHA setup + Firebase OTP)
  // The web tab will call handleOTPRequestFromExtension which will:
  // 1. Setup reCAPTCHA in web tab context
  // 2. Send OTP via Firebase
  // 3. Send back EXTENSION_OTP_SENT or EXTENSION_OTP_ERROR
  sendToWebTabRuntime(tabId, "HANDLE_OTP_REQUEST", { phone: phone });

  console.log(
    `[Extension] Sent HANDLE_OTP_REQUEST to web tab for phone: ${phone}`
  );
  sendResponse({ success: true });
};

// Handle OTP verification via Chrome Runtime (Secure - Extension context only)
const handleVerifyOTPRuntime = async (
  phone: string,
  otp: string,
  tabId: number | undefined,
  sendResponse: (response?: any) => void
) => {
  console.log(
    `[Extension] Runtime: Handling OTP verification for phone: ${phone}, OTP: ${otp}`
  );
  console.log(
    `[Extension] 🔍 VERIFICATION DETAILS: phone="${phone}", otp="${otp}", tabId=${tabId}`
  );
  console.log(
    `[Extension] Telling web tab to handle OTP verification directly`
  );

  // Send loading state to web tab
  sendToWebTabRuntime(tabId, "SET_LOADING", { isLoading: true });

  // Tell the web tab to handle the OTP verification (Firebase verification + user check)
  // The web tab will call handleOTPVerificationForExtension which will:
  // 1. Verify OTP with Firebase using the confirmationResult from the web tab's context
  // 2. Check if user exists in our database
  // 3. Send back EXTENSION_OTP_VERIFIED or EXTENSION_OTP_ERROR
  sendToWebTabRuntime(tabId, "HANDLE_OTP_VERIFICATION", {
    phone: phone,
    otp: otp,
  });

  console.log(
    `[Extension] Sent HANDLE_OTP_VERIFICATION to web tab for phone: ${phone}, OTP: ${otp}`
  );
  sendResponse({ success: true });
};

// Handle registration completion via Chrome Runtime (Secure - Extension context only)
const handleCompleteRegistrationRuntime = async (
  phone: string,
  name: string,
  tabId: number | undefined,
  sendResponse: (response?: any) => void
) => {
  console.log(
    `[Extension] Runtime: Handling registration completion for: ${name}`
  );
  sendToWebTabRuntime(tabId, "SET_LOADING", { isLoading: true });

  try {
    const result = await submitRegistration(phone, name);

    // Store session data securely in extension context
    if (result.sessionUserId) {
      localStorage.setItem("sessionUserId", result.sessionUserId);
      console.log(
        `[Extension] Runtime: Session ID stored: ${result.sessionUserId}`
      );
    }

    console.log(
      `[Extension] Runtime: Database registration completed successfully`
    );

    // Notify web tab to close (authentication and navigation handled by message handler)
    sendToWebTabRuntime(tabId, "LOGIN_COMPLETE", { success: true });

    sendResponse({ success: true });
  } catch (error: any) {
    const errorMessage = getUserFriendlyErrorMessage(error);
    sendToWebTabRuntime(tabId, "SET_ERROR", { error: errorMessage });
    console.error(
      `[Extension] Runtime: Registration completion failed:`,
      error
    );
    sendResponse({ error: errorMessage });
  } finally {
    sendToWebTabRuntime(tabId, "SET_LOADING", { isLoading: false });
  }
};

/**
 * Setup secure Chrome Runtime Messaging with web login tab
 * This creates a message handler that processes authentication requests from the web tab
 */
const setupChromeRuntimeCommunication = (tabId: number) => {
  console.log(`[Extension] 🔗 Setting up Chrome Runtime communication`);
  console.log(`[Extension] 🆔 Target tab ID:`, tabId);
  console.log(`[Extension] 📡 Ready to receive Chrome runtime messages`);

  const handleRuntimeMessage = (
    message: any,
    sender: any,
    sendResponse: (response?: any) => void
  ) => {
    console.log(`[Extension] 📨 Runtime message received:`, {
      message,
      sender,
      tabId: sender.tab?.id,
      expectedTabId: tabId,
    });

    // SECURITY: Verify message comes from our login tab
    if (sender.tab?.id !== tabId) {
      console.warn(
        `[Extension] ❌ Rejected message from wrong tab. Expected: ${tabId}, Got: ${sender.tab?.id}`
      );
      return;
    }

    console.log(`[Extension] ✅ Tab ID validated`);
    console.log(`[Extension] ✅ Processing message:`, message);

    // Route messages to appropriate secure handlers
    switch (message.type) {
      case "EXTENSION_BRIDGE_READY":
        console.log(`[Extension] Bridge ready for tab:`, sender.tab?.id);
        sendResponse({ success: true });
        break;

      case "EXTENSION_SEND_OTP":
        handleSendOTPRuntime(message.phone, sender.tab?.id, sendResponse);
        break;

      case "EXTENSION_OTP_SENT":
        console.log(`[Extension] Web tab confirmed OTP sent successfully`);
        if (window.otpPromiseResolvers) {
          window.otpPromiseResolvers.resolve();
          window.otpPromiseResolvers = undefined;
        }
        sendResponse({ success: true });
        break;

      case "EXTENSION_OTP_ERROR":
        console.log(`[Extension] Web tab reported OTP error:`, message.error);
        if (window.otpPromiseResolvers) {
          window.otpPromiseResolvers.reject(new Error(message.error));
          window.otpPromiseResolvers = undefined;
        }
        sendResponse({ success: false });
        break;

      case "EXTENSION_OTP_VERIFIED":
        console.log(
          `[Extension] Web tab confirmed OTP verification successful:`,
          {
            userExists: message.userExists,
            sessionUserId: message.sessionUserId,
            firebaseUid: message.firebaseUid,
          }
        );

        // 🆕 SYNC FIREBASE CONTEXT TO EXTENSION AUTHSTORE
        console.log(`[Extension] 🔄 Syncing Firebase context from web tab...`);
        const authStore = useAuthStore.getState();

        // Store Firebase user data in extension's authStore
        if (message.firebaseUser && message.firebaseIdToken) {
          console.log(
            `[Extension] 🔑 Storing Firebase user context for database operations`
          );

          // 🆕 PERSIST ID TOKEN IN LOCALSTORAGE FOR CROSS-CONTEXT RELIABILITY
          localStorage.setItem("firebase_id_token", message.firebaseIdToken);
          localStorage.setItem("firebase_uid", message.firebaseUser.uid);
          console.log(
            `[Extension] 💾 Firebase ID token persisted to localStorage`
          );

          // 🔧 CREATE COMPLETE FIREBASE USER OBJECT WITH ALL REQUIRED METHODS
          const syncedFirebaseUser = {
            uid: message.firebaseUser.uid,
            email: message.firebaseUser.email,
            phoneNumber: message.firebaseUser.phoneNumber,
            emailVerified: message.firebaseUser.emailVerified,
            displayName: message.firebaseUser.displayName,
            // Required Firebase User methods
            getIdToken: (forceRefresh = false) => {
              const storedToken = localStorage.getItem("firebase_id_token");
              return Promise.resolve(message.firebaseIdToken || storedToken);
            },
            getIdTokenResult: (forceRefresh = false) => {
              const storedToken = localStorage.getItem("firebase_id_token");
              return Promise.resolve({
                token: message.firebaseIdToken || storedToken,
                claims: {},
                authTime: new Date().toISOString(),
                issuedAtTime: new Date().toISOString(),
                expirationTime: new Date(Date.now() + 3600000).toISOString(), // 1 hour
              });
            },
            refreshToken: "mock_refresh_token",
            providerId: "firebase",
            isAnonymous: false,
            metadata: {
              creationTime: new Date().toISOString(),
              lastSignInTime: new Date().toISOString(),
            },
            providerData: [],
            reload: () => Promise.resolve(),
            toJSON: () => ({}),
            delete: () => Promise.resolve(),
          };

          // Store in authStore for database operations (but don't call setUser yet)
          authStore.firebaseUser = syncedFirebaseUser as any;
          console.log(`[Extension] ✅ Firebase context synced successfully`);
        }

        // 🔧 FIXED NAVIGATION LOGIC: Only navigate for EXISTING users, not new users
        if (message.userExists) {
          console.log(
            `[Extension] ✅ Existing user verified successfully! User will be logged in.`
          );

          // Set authentication state in extension for EXISTING users only
          console.log(`[Extension] 🔑 Setting authentication state to true...`);
          const navStore = useNavStore.getState();

          // Set user in authStore (this will work now with complete Firebase user object)
          if (message.firebaseUser) {
            authStore.setUser(message.firebaseUser as any);
          }

          // Update both stores to ensure UI updates correctly
          authStore.setAuthenticated(true);
          navStore.setAuthenticated(true);
          console.log(
            `[Extension] ✅ Authentication state updated in both stores`
          );

          // Navigate to ChatHomePage in extension for EXISTING users
          console.log(`[Extension] 🔄 Navigating to ChatHomePage...`);
          navigate();
          console.log(`[Extension] ✅ Navigation completed`);

          // Close the login tab after a short delay to ensure message response is sent
          setTimeout(() => {
            if (tabId) {
              console.log(`[Extension] 🗑️ Closing login tab: ${tabId}`);
              const chromeAPI = getChromeAPI();
              if (chromeAPI) {
                chromeAPI.tabs.remove(tabId, () => {
                  console.log(`[Extension] ✅ Login tab closed successfully`);
                });
              }
            }
          }, 500);
        } else {
          console.log(
            `[Extension] 👤 New user verified - waiting for Step 3 (name entry) completion`
          );
          console.log(
            `[Extension] 🔒 Extension will NOT navigate until registration completes`
          );
          // Web tab will continue with name collection (Step 3)
          // Extension will wait for COMPLETE_REGISTRATION message before navigating
        }
        sendResponse({ success: true });
        break;

      case "EXTENSION_VERIFY_OTP":
        handleVerifyOTPRuntime(
          message.phone,
          message.otp,
          sender.tab?.id,
          sendResponse
        );
        break;

      case "EXTENSION_COMPLETE_REGISTRATION":
        handleCompleteRegistrationRuntime(
          message.phone,
          message.name,
          sender.tab?.id,
          sendResponse
        );

        // 🆕 COMPLETE AUTHENTICATION AND NAVIGATION FOR NEW USERS (after Step 3)
        console.log(
          `[Extension] 🎉 Step 3 completed - new user registration successful`
        );

        // Now that registration is complete, set authentication state and navigate
        const authStoreReg = useAuthStore.getState();
        const navStoreReg = useNavStore.getState();

        console.log(
          `[Extension] 🔑 Setting authentication state for new user after registration...`
        );

        // Set user in authStore if Firebase context is available
        const storedUid = localStorage.getItem("firebase_uid");
        if (storedUid) {
          authStoreReg.setUser({
            uid: storedUid,
            email: null,
            phoneNumber: message.phone,
            emailVerified: true,
            displayName: message.name,
          } as any);
        }

        // Complete authentication
        authStoreReg.setAuthenticated(true);
        navStoreReg.setAuthenticated(true);
        console.log(`[Extension] ✅ Authentication completed for new user`);

        // Navigate to ChatHomePage for new user
        console.log(
          `[Extension] 🔄 Navigating new user to ChatHomePage after registration...`
        );
        navigate();
        console.log(`[Extension] ✅ New user navigation completed`);

        break;

      case "EXTENSION_LOGIN_COMPLETE":
        console.log(`[Extension] 🎉 Web tab confirmed login complete:`, {
          userExists: message.userExists,
          firebaseUid: message.firebaseUid,
          email: message.email,
          phoneNumber: message.phoneNumber,
        });

        // 🆕 SYNC FIREBASE CONTEXT FOR EXISTING USERS TOO
        console.log(
          `[Extension] 🔄 Syncing Firebase context for existing user...`
        );
        const authStoreComplete = useAuthStore.getState();

        // Sync Firebase context if provided
        if (message.firebaseUser && message.firebaseIdToken) {
          console.log(
            `[Extension] 🔑 Storing Firebase context for existing user`
          );

          // 🆕 PERSIST ID TOKEN IN LOCALSTORAGE FOR CROSS-CONTEXT RELIABILITY
          localStorage.setItem("firebase_id_token", message.firebaseIdToken);
          localStorage.setItem("firebase_uid", message.firebaseUser.uid);
          console.log(
            `[Extension] 💾 Firebase ID token persisted to localStorage for existing user`
          );

          // Create synced Firebase user object for database operations
          const syncedFirebaseUser = {
            uid: message.firebaseUser.uid,
            email: message.firebaseUser.email,
            phoneNumber: message.firebaseUser.phoneNumber,
            emailVerified: message.firebaseUser.emailVerified,
            displayName: message.firebaseUser.displayName,
            getIdToken: () => {
              // First try memory, then fallback to localStorage
              const storedToken = localStorage.getItem("firebase_id_token");
              return Promise.resolve(message.firebaseIdToken || storedToken);
            },
          };

          // Store in authStore
          authStoreComplete.firebaseUser = syncedFirebaseUser as any;
          console.log(
            `[Extension] ✅ Firebase context synced for existing user`
          );
        }

        // Set authentication state in both stores
        console.log(
          `[Extension] 🔐 Setting authentication state for login completion...`
        );
        const navStoreComplete = useNavStore.getState();

        // Set user information in auth store
        if (message.firebaseUid) {
          authStoreComplete.setUser({
            uid: message.firebaseUid,
            email: message.email,
            phoneNumber: message.phoneNumber,
            emailVerified: true, // Assume verified since they completed phone auth
            displayName: null,
          } as any); // Using 'as any' to bypass full Firebase User interface requirements
        }

        // Set authentication states
        authStoreComplete.setAuthenticated(true);
        navStoreComplete.setAuthenticated(true);
        console.log(
          `[Extension] ✅ Authentication state updated for login completion`
        );

        // Navigate to ChatHomePage
        console.log(
          `[Extension] 🔄 Navigating to ChatHomePage after login completion...`
        );
        navigate();
        console.log(`[Extension] ✅ Navigation completed after login`);

        // Close the login tab after a short delay
        setTimeout(() => {
          if (tabId) {
            console.log(
              `[Extension] 🗑️ Closing login tab after login completion: ${tabId}`
            );
            const chromeAPI = getChromeAPI();
            if (chromeAPI) {
              chromeAPI.tabs.remove(tabId, () => {
                console.log(
                  `[Extension] ✅ Login tab closed successfully after login completion`
                );
              });
            }
          }
        }, 500);

        sendResponse({ success: true });
        break;

      case "EXTENSION_ALREADY_AUTHENTICATED":
        console.log(
          `[Extension] 📨 ALREADY_AUTHENTICATED received from web tab:`,
          message
        );
        console.log(
          `[Extension] ✅ Web tab detected user is already authenticated!`
        );

        // Set authentication state in both stores
        console.log(
          `[Extension] 🔐 Setting authentication state for already authenticated user...`
        );
        const authStoreAlready = useAuthStore.getState();
        const navStoreAlready = useNavStore.getState();

        // Set user information in auth store from web tab response
        if (message.user) {
          authStoreAlready.setUser({
            ...message.user, 
            emailVerified: true, 
            displayName: null
          } as any);
          console.log(
            `[Extension] 🔐 User data set: ${message.user.uid} (${message.user.phoneNumber || message.user.email})`
          );
        }

        // Set authentication states
        authStoreAlready.setAuthenticated(true);
        navStoreAlready.setAuthenticated(true);
        console.log(
          `[Extension] ✅ Authentication state updated for already authenticated user`
        );

        // Navigate to ChatHomePage
        console.log(
          `[Extension] 🔄 Navigating to ChatHomePage for already authenticated user...`
        );
        navigate();
        console.log(
          `[Extension] ✅ Navigation completed for already authenticated user`
        );

        // Close the login tab immediately (no delay needed since no OTP flow)
        if (tabId) {
          console.log(
            `[Extension] 🗑️ Closing login tab for already authenticated user: ${tabId}`
          );
          const chromeAPI = getChromeAPI();
          if (chromeAPI) {
            chromeAPI.tabs.remove(tabId, () => {
              console.log(
                `[Extension] ✅ Login tab closed successfully for already authenticated user`
              );
            });
          }
        }

        sendResponse({ success: true });
        break;

      case "EXTENSION_CLOSE_LOGIN":
        console.log(`[Extension] Login tab requested closure - cleaning up`);
        const chromeAPI = getChromeAPI();
        if (chromeAPI) {
          chromeAPI.runtime.onMessage.removeListener(handleRuntimeMessage);
        }
        sendResponse({ success: true });
        break;

      default:
        console.warn(`[Extension] Unknown message type: ${message.type}`);
        sendResponse({ error: "Unknown message type" });
    }

    // Return true to indicate we will send a response asynchronously
    return true;
  };

  // Add Chrome Runtime message listener
  const chromeAPI = getChromeAPI();
  if (chromeAPI) {
    chromeAPI.runtime.onMessage.addListener(handleRuntimeMessage);
    console.log(`[Extension] ✅ Chrome Runtime message listener attached`);
    console.log(`[Extension] Communication setup complete`);
  }
};

// Sub-component: Login Button
const LoginButton: React.FC = () => {
  /* 
  === COMMENTED OUT: Old Dialog-Based Login Flow ===
  This was the original extension login flow using a modal dialog.
  Kept for reference and potential future use.
  
  return (
    <LoginDialog>
      <CTAButton className="w-full py-4 px-6 text-lg font-semibold">
        Login
      </CTAButton>
    </LoginDialog>
  );
  */

  // NEW: Extension Web Login Flow - Opens web tab for secure authentication
  const handleExtensionLogin = () => {
    // 🆕 Check if user is already authenticated from persistence
    const authStore = useAuthStore.getState();
    const navStore = useNavStore.getState();

    console.log(`[Extension] 🔍 Checking authentication state before login:`, {
      isAuthenticated: authStore.isAuthenticated,
      hasUser: !!authStore.user,
      userUid: authStore.user?.uid,
    });

    if (authStore.isAuthenticated && authStore.user) {
      console.log(
        `[Extension] ✅ User already authenticated, navigating directly to ChatHomePage`
      );
      console.log(
        `[Extension] 🔐 User: ${authStore.user.uid} (${authStore.user.phoneNumber || authStore.user.email})`
      );

      // Set nav store authentication state to match
      navStore.setAuthenticated(true);

      // Navigate directly to ChatHomePage
      navigate();
      console.log(`[Extension] ✅ Navigation completed - skipped web login`);
      return; // Exit early, don't open web tab
    }

    console.log(
      `[Extension] 🔓 User not authenticated, proceeding with web login flow`
    );

    // Clear any existing completion flags to start fresh
    localStorage.removeItem("extension_login_completed");

    // Get the correct web server URL (not extension URL)
    // In development: use localhost web server
    // In production: use the actual domain
    const getWebServerUrl = () => {
      // Check if we're in development mode
      const isDevelopment =
        process.env.NODE_ENV === "development" ||
        window.location.hostname === "localhost" ||
        window.location.protocol === "chrome-extension:";

      if (isDevelopment) {
        // Development: Use Vite dev server (usually port 5173)
        return "http://localhost:5173";
      } else {
        // Production: Use the actual domain
        // This would be your production domain, e.g., 'https://yourdomain.com'
        return "https://yourdomain.com"; // TODO: Replace with actual production URL
      }
    };

    // Open web login in new TAB (not popup window) with extension mode parameter
    // This ensures the web flow knows it's being used by the extension
    const webLoginUrl = `${getWebServerUrl()}/?mode=extension&action=fresh_login`;
    console.log(`[Extension] Opening web login at: ${webLoginUrl}`);
    console.log(
      `[Extension] 🔄 Using fresh_login flag to ensure clean auth state in web tab`
    );

    // Use Chrome Extension APIs for proper tab management and communication
    console.log(`[Extension] 🚀 Using Chrome Extension API to create new tab`);

    // Create new tab using Chrome Extension API (instead of window.open)
    const chromeAPI = getChromeAPI();
    if (chromeAPI) {
      chromeAPI.tabs.create({ url: webLoginUrl }, (tab) => {
        console.log(`[Extension] ✅ New tab created:`, tab);
        console.log(`[Extension] 🆔 Tab ID:`, tab.id);

        // Setup Chrome Runtime Messaging (instead of postMessage)
        if (tab.id) {
          setupChromeRuntimeCommunication(tab.id);
        }
      });
    }
  };

  return (
    <CTAButton
      className="w-full py-4 px-6 text-lg font-semibold"
      onClick={handleExtensionLogin}
    >
      Login
    </CTAButton>
  );
};

// Main LoginPage Component
const LoginPage: React.FC = () => {
  const { isAuthenticated, setAuthenticated } = useNavStore();
  const loginMode = useLoginMode();

  // Check authentication status on mount
  useEffect(() => {
    // Mock mode: always authenticated (skip login)
    if (import.meta.env.VITE_USE_MOCK === "true") {
      setAuthenticated(true);
      return;
    }

    // Local mode: check localStorage for auth status
    const authToken = localStorage.getItem("authToken");
    if (authToken) {
      setAuthenticated(true);
    }
  }, [setAuthenticated]);

  // Handle login button click - now handled by LoginDialog
  const handleLogin = () => {
    // This is now handled by the LoginDialog component
  };

  // If authenticated, navigate to chat (handled by pageNavigator)
  useEffect(() => {
    if (isAuthenticated) {
      navigate();
    }
  }, [isAuthenticated]);

  // If web mode, render full-page login flow
  if (loginMode === "web") {
    return <WebLoginPage />;
  }

  // Extension mode - render existing dialog-based login
  return (
    <div
      className="min-h-screen flex flex-col"
      style={{ backgroundColor: designTokens.gray50 }}
    >
      {/* Header - Fixed at top */}
      <Header />

      {/* Main Content - Takes remaining space */}
      <div className="flex-1 flex items-center justify-center py-20">
        <div className="w-full max-w-md">
          {/* Dashed Border Container */}
          <div className="rounded-lg p-8">
            {/* Welcome Section */}
            <div className="text-center mb-8">
              <h1 className="font-['Inter:Bold',_sans-serif] font-bold text-[#181e29] text-3xl sm:text-3xl lg:text-4xl leading-tight mb-2">
                Hi,
              </h1>
              <h2
                className="font-['Inter:Bold',_sans-serif] font-bold text-3xl leading-tight bg-clip-text text-transparent"
                style={{
                  backgroundImage:
                    "linear-gradient(100.076deg, rgb(92, 84, 253) 9.8213%, rgb(163, 48, 229) 80.799%)",
                  textShadow: "rgba(16,24,40,0.05) 0px 1px 2px",
                  WebkitTextFillColor: "transparent",
                }}
              >
                Let's get started with OrderGPT.
              </h2>
            </div>

            {/* Feature Cards */}
            <div className="space-y-6 mb-10">
              {features.map((feature) => (
                <FeatureCard key={feature.id} feature={feature} />
              ))}
            </div>

            {/* Login Button */}
            <LoginButton />
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
