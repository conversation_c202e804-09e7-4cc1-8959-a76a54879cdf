import React, { useState, useEffect } from "react";
import RadioButton from "../components/RadioButton";
import Dropdown from "../components/Dropdown";
import OrderCardEnhanced from "../components/OrderCardEnhanced";
import EmptyState from "../components/EmptyState";
import { apiClient } from "../utils/apiClient";

import CalendarIcon from "../assets/calendar.svg";
import ZerodhaIcon from "../assets/zerodha.svg";
import GrowwIcon from "../assets/groww.svg";
import UpstoxIcon from "../assets/upstox.svg";
import FilterIcon from "../assets/filter-lines.svg";

interface Order {
  id: string;
  symbol: string;
  tradeType: "BUY" | "SELL";
  quantity: string;
  price: string;
  status: "pending" | "executed" | "cancelled";
  timestamp: string;
  broker: string;
  product: string;
  orderType: string;
}

const AllOrdersPage: React.FC = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [selectedTab, setSelectedTab] = useState<string>("open");
  const [selectedBroker, setSelectedBroker] = useState<string>("zerodha");
  const [selectedFilter, setSelectedFilter] = useState<string>("all");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>("last_month");

  // Load orders from API
  useEffect(() => {
    const loadOrders = async () => {
      try {
        const data = await apiClient.getOrders();
        setOrders(data.orders || []);
      } catch (error) {
        console.error("Failed to load orders:", error);
      }
    };
    loadOrders();
  }, []);

  // Filter orders based on selected criteria
  const filteredOrders = orders.filter((order) => {
    const isOpenOrder = order.status === "pending";
    const tabMatch =
      selectedTab === "open" ? isOpenOrder : order.status === "executed";
    const brokerMatch =
      selectedBroker === "all" || order.broker === selectedBroker;
    const filterMatch =
      selectedFilter === "all" || order.tradeType === selectedFilter;

    return tabMatch && brokerMatch && filterMatch;
  });

  // Radio button options
  const tabOptions = [
    { value: "open", label: "Open" },
    { value: "executed", label: "Executed" },
  ];

  // Dropdown options
  const brokerOptions = [
    { value: "zerodha", label: "Zerodha", icon: ZerodhaIcon },
    { value: "groww", label: "Groww", icon: GrowwIcon },
    { value: "upstox", label: "Upstox", icon: UpstoxIcon },
    { value: "all", label: "All Brokers" },
  ];

  const filterOptions = [
    { value: "all", label: "All Orders" },
    { value: "BUY", label: "Buy Orders" },
    { value: "SELL", label: "Sell Orders" },
  ];

  const dateRangeOptions = [
    { value: "today", label: "Today" },
    { value: "last_week", label: "Last Week" },
    { value: "last_month", label: "Last Month" },
    { value: "last_3_months", label: "Last 3 Months" },
  ];

  const handleChatClick = (orderId: string) => {
    console.log(`Chat clicked for order ${orderId}`);
    // Implement chat navigation logic here
  };

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header */}
      <div className="flex flex-col gap-4 p-4">
        {/* Radio button tabs */}
        <div className="w-full">
          <RadioButton
            options={tabOptions}
            value={selectedTab}
            onValueChange={setSelectedTab}
            className="w-full"
          />
        </div>

        {/* Controls row */}
        <div className="flex items-center justify-between">
          {/* Broker dropdown */}
          <Dropdown
            options={brokerOptions}
            value={selectedBroker}
            onSelect={setSelectedBroker}
            placeholder="Select Broker"
            className="min-w-[140px]"
          />

          {/* Filter and date controls */}
          <div className="flex items-center gap-2">
            <Dropdown
              options={filterOptions}
              value={selectedFilter}
              onSelect={setSelectedFilter}
              placeholder="Filter By"
              icon={FilterIcon}
              className="min-w-[100px]"
            />
            <Dropdown
              options={dateRangeOptions}
              value={selectedDateRange}
              onSelect={setSelectedDateRange}
              placeholder="Date Range"
              icon={CalendarIcon}
              className="min-w-[120px]"
            />
          </div>
        </div>
      </div>

      {/* Orders list */}
      <div className="flex-1 overflow-y-auto">
        {filteredOrders.length === 0 ? (
          <EmptyState
            type="orders"
            className="h-full"
            title={`No ${selectedTab} orders found`}
            description={`You don't have any ${selectedTab} orders with the current filters.`}
          />
        ) : (
          <div className="px-4 pb-4">
            <div className="space-y-3">
              {filteredOrders.map((order) => (
                <OrderCardEnhanced
                  key={order.id}
                  stockName={order.symbol}
                  value={order.price}
                  quantity={order.quantity}
                  time={order.timestamp}
                  exchange={order.broker}
                  orderType={order.orderType}
                  ltp={order.price}
                  tradeType={order.tradeType}
                  status={order.status}
                  onChatClick={() => handleChatClick(order.id)}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AllOrdersPage;
