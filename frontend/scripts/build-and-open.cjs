#!/usr/bin/env node

const { execSync } = require("child_process");

function runCommand(command, description) {
  console.log(`🔄 ${description}...`);
  try {
    execSync(command, { stdio: "inherit", cwd: process.cwd() });
    console.log(`✅ ${description} completed`);
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    process.exit(1);
  }
}

const mode = process.argv[2] || "executor";

console.log("🚀 Building extension for development...");
console.log(`📝 Mode: ${mode}`);
console.log("");

// Build executor
runCommand(`node scripts/build-executor.cjs ${mode}`, "Building executor");

console.log("");
console.log("🎉 Extension built successfully!");
console.log(`📁 Location: ${require('path').join(__dirname, '..', '..', 'executor')}`);
console.log("");
console.log("🔄 Next: Refresh extension in Chrome (if already loaded)");
console.log("💡 Or run 'npm run extension:open' for setup instructions");
