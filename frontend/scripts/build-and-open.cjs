#!/usr/bin/env node

const { execSync } = require("child_process");

function runCommand(command, description) {
  console.log(`🔄 ${description}...`);
  try {
    execSync(command, { stdio: "inherit", cwd: process.cwd() });
    console.log(`✅ ${description} completed`);
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    process.exit(1);
  }
}

const mode = process.argv[2] || "executor";

console.log("🚀 Building extension for development...");
console.log(`📝 Mode: ${mode}`);
console.log("");

// Step 1: Set up environment file
runCommand(`node scripts/setup-env.cjs ${mode}`, "Setting up environment");

// Step 2: Build executor
runCommand("node build-executor.cjs", "Building executor");

console.log("");
console.log("🎉 Extension built successfully!");
console.log("📁 Location: /Users/<USER>/smart-agent/executor");
console.log("");
console.log("🔄 Next: Refresh extension in Chrome (if already loaded)");
console.log("💡 Or run 'npm run extension:open' for setup instructions");
