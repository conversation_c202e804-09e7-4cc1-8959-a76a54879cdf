#!/usr/bin/env node

const fs = require("fs");
const path = require("path");

const envConfigs = {
  executor: {
    VITE_USE_MOCK: "false",
    VITE_LOGIN_MODE: "extension",
    VITE_API_BASE_URL: "http://localhost:8000",
    VITE_WS_URL: "ws://localhost:8000",
    VITE_NOTIFICATIONS_ENDPOINT: "/api/notifications",
    VITE_PROFILE_ENDPOINT: "/api/profile",
    VITE_ORDERS_ENDPOINT: "/api/v1/orders",
    VITE_MONITORING_ENDPOINT: "/api/v1/monitoring/instances",
    VITE_CHAT_ENDPOINT: "/api/v1/chatHistory",
    VITE_HEALTH_ENDPOINT: "/health",
    VITE_WS_CHAT_ENDPOINT: "/api/v1/ws/chat",
  },
  development: {
    VITE_USE_MOCK: "true",
    VITE_LOGIN_MODE: "auto",
    VITE_API_BASE_URL: "http://localhost:8000",
    VITE_WS_URL: "ws://localhost:8000",
    VITE_NOTIFICATIONS_ENDPOINT: "/api/notifications",
    VITE_PROFILE_ENDPOINT: "/api/profile",
    VITE_ORDERS_ENDPOINT: "/api/v1/orders",
    VITE_MONITORING_ENDPOINT: "/api/v1/monitoring/instances",
    VITE_CHAT_ENDPOINT: "/api/v1/chatHistory",
    VITE_HEALTH_ENDPOINT: "/health",
    VITE_WS_CHAT_ENDPOINT: "/api/v1/ws/chat",
  },
  dev: {
    VITE_USE_MOCK: "false",
    VITE_LOGIN_MODE: "auto",
    VITE_API_BASE_URL: "http://localhost:8000",
    VITE_WS_URL: "ws://localhost:8000",
    VITE_NOTIFICATIONS_ENDPOINT: "/api/notifications",
    VITE_PROFILE_ENDPOINT: "/api/profile",
    VITE_ORDERS_ENDPOINT: "/api/v1/orders",
    VITE_MONITORING_ENDPOINT: "/api/v1/monitoring/instances",
    VITE_CHAT_ENDPOINT: "/api/v1/chatHistory",
    VITE_HEALTH_ENDPOINT: "/health",
    VITE_WS_CHAT_ENDPOINT: "/api/v1/ws/chat",
  },
};

function createEnvFile(mode) {
  const config = envConfigs[mode];
  if (!config) {
    console.error(`Unknown mode: ${mode}`);
    process.exit(1);
  }

  const envFileName = `.env.${mode}`;
  const envContent = Object.entries(config)
    .map(([key, value]) => `${key}=${value}`)
    .join("\n");

  fs.writeFileSync(envFileName, envContent);
  console.log(`✅ Created ${envFileName}`);
}

const mode = process.argv[2];
if (!mode) {
  console.error("Usage: node setup-env.js <mode>");
  console.error("Available modes: executor, development, dev");
  process.exit(1);
}

createEnvFile(mode);
