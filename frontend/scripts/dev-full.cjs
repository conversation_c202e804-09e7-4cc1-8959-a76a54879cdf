#!/usr/bin/env node

const { spawn } = require("child_process");
const path = require("path");

const mode = process.argv[2] || "dev";

console.log(`🚀 Starting full development environment for mode: ${mode}`);
console.log(`📱 Extension watcher + 🌐 Web dev server`);
console.log(`============================================`);

// Colors for different processes
const colors = {
  reset: "\x1b[0m",
  red: "\x1b[31m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  magenta: "\x1b[35m",
  cyan: "\x1b[36m",
};

// Helper function to prefix output with colors
function prefixOutput(prefix, color, data) {
  const lines = data
    .toString()
    .split("\n")
    .filter((line) => line.trim());
  lines.forEach((line) => {
    console.log(`${color}[${prefix}]${colors.reset} ${line}`);
  });
}

// Start the extension watcher
console.log(`${colors.cyan}Starting extension watcher...${colors.reset}`);
const extensionWatcher = spawn("node", ["scripts/watch-and-build.cjs", mode], {
  stdio: "pipe",
  cwd: process.cwd(),
});

// Start the web dev server
console.log(`${colors.green}Starting web dev server...${colors.reset}`);

// Map mode to correct web script name
const webScriptMap = {
  development: "dev:mock-web",
  dev: "dev:local-web",
};

const webScript = webScriptMap[mode] || "dev:local-web";
console.log(`${colors.green}Running: npm run ${webScript}${colors.reset}`);

const webServer = spawn("npm", ["run", webScript], {
  stdio: "pipe",
  cwd: process.cwd(),
  shell: true,
});

// Handle extension watcher output
extensionWatcher.stdout.on("data", (data) => {
  prefixOutput("EXT", colors.cyan, data);
});

extensionWatcher.stderr.on("data", (data) => {
  prefixOutput("EXT-ERR", colors.red, data);
});

// Handle web server output
webServer.stdout.on("data", (data) => {
  prefixOutput("WEB", colors.green, data);
});

webServer.stderr.on("data", (data) => {
  prefixOutput("WEB-ERR", colors.yellow, data);
});

// Handle process exits
extensionWatcher.on("close", (code) => {
  console.log(
    `${colors.red}Extension watcher exited with code ${code}${colors.reset}`
  );
  if (webServer && !webServer.killed) {
    console.log(`${colors.yellow}Terminating web server...${colors.reset}`);
    webServer.kill();
  }
  process.exit(code);
});

webServer.on("close", (code) => {
  console.log(
    `${colors.red}Web server exited with code ${code}${colors.reset}`
  );
  if (extensionWatcher && !extensionWatcher.killed) {
    console.log(
      `${colors.yellow}Terminating extension watcher...${colors.reset}`
    );
    extensionWatcher.kill();
  }
  process.exit(code);
});

// Handle Ctrl+C gracefully
process.on("SIGINT", () => {
  console.log(
    `\n${colors.yellow}Received SIGINT, shutting down gracefully...${colors.reset}`
  );

  if (extensionWatcher && !extensionWatcher.killed) {
    extensionWatcher.kill("SIGINT");
  }

  if (webServer && !webServer.killed) {
    webServer.kill("SIGINT");
  }

  process.exit(0);
});

console.log(`${colors.magenta}✨ Both processes started!${colors.reset}`);
console.log(`${colors.blue}🔌 Extension: Auto-reload enabled`);
console.log(`🌐 Web server: http://localhost:5173`);
console.log(`🧪 Test login: Open extension → Click Login → New tab opens`);
console.log(
  `${colors.yellow}Press Ctrl+C to stop both processes${colors.reset}`
);
