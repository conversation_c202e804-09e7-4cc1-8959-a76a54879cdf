#!/usr/bin/env node

const { execSync } = require("child_process");
const path = require("path");

console.log("🔍 CHROME EXTENSION DEBUGGING");
console.log("================================");

// Check Chrome installation
console.log("\n1. Chrome Installation:");
try {
  const chromeVersion = execSync(
    "/Applications/Google\\ Chrome.app/Contents/MacOS/Google\\ Chrome --version",
    { encoding: "utf8" }
  );
  console.log("✅ Chrome found:", chromeVersion.trim());
} catch (error) {
  console.log("❌ Chrome not found at expected location");
}

// Check extension directory
const extensionPath = path.resolve(__dirname, "../../executor");
console.log("\n2. Extension Directory:");
console.log("📁 Path:", extensionPath);

const fs = require("fs");
if (fs.existsSync(extensionPath)) {
  console.log("✅ Extension directory exists");

  // Check manifest
  const manifestPath = path.join(extensionPath, "manifest.json");
  if (fs.existsSync(manifestPath)) {
    console.log("✅ manifest.json exists");
    try {
      const manifest = JSON.parse(fs.readFileSync(manifestPath, "utf8"));
      console.log("✅ Manifest is valid JSON");
      console.log("   Name:", manifest.name);
      console.log("   Version:", manifest.version);
    } catch (error) {
      console.log("❌ manifest.json is invalid:", error.message);
    }
  } else {
    console.log("❌ manifest.json missing");
  }

  // Check UI files
  const uiPath = path.join(extensionPath, "ui");
  if (fs.existsSync(uiPath)) {
    console.log("✅ ui directory exists");
    const indexPath = path.join(uiPath, "index.html");
    if (fs.existsSync(indexPath)) {
      console.log("✅ ui/index.html exists");
    } else {
      console.log("❌ ui/index.html missing");
    }
  } else {
    console.log("❌ ui directory missing");
  }
} else {
  console.log("❌ Extension directory does not exist");
}

console.log("\n3. Testing Chrome Extensions Page:");
console.log("Opening Chrome to extensions page...");

try {
  execSync('open -a "Google Chrome" "chrome://extensions/"');
  console.log("✅ Chrome command executed");
  console.log("\n🔍 WHAT TO CHECK IN CHROME:");
  console.log("1. Did Chrome open to chrome://extensions/ ?");
  console.log('2. Do you see "Developer mode" toggle in top-right?');
  console.log('3. Is "Developer mode" currently ON or OFF?');
  console.log('4. After turning ON, do you see "Load unpacked" button?');
  console.log("\n📋 Extension path for manual loading:");
  console.log(extensionPath);
} catch (error) {
  console.log("❌ Failed to open Chrome:", error.message);
}
