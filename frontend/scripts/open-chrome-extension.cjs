#!/usr/bin/env node

const path = require("path");

const extensionPath = path.resolve(__dirname, "../../executor");

console.log("✅ Extension built and ready!");
console.log("");
console.log("📁 Extension location:", extensionPath);
console.log("");
console.log("🔧 TO LOAD EXTENSION IN CHROME:");
console.log("1️⃣  Open Chrome → chrome://extensions/");
console.log("2️⃣  Enable 'Developer mode' (top-right toggle)");
console.log("3️⃣  Click 'Load unpacked'");
console.log("4️⃣  Navigate to and select the executor folder");
console.log("5️⃣  Extension loads → Click puzzle piece icon 🧩");
console.log("");
console.log("🔄 AFTER CODE CHANGES:");
console.log("• Run npm run dev (or VITE_USE_MOCK=true npm run dev) to rebuild");
console.log("• Click refresh icon on extension card in chrome://extensions/");
console.log("• Or press Ctrl+R in the extension's side panel");
