#!/usr/bin/env node

const { execSync } = require("child_process");
const fs = require("fs");
const path = require("path");

let isBuilding = false;
let buildTimeout = null;

function runCommand(command, description) {
  console.log(`🔄 ${description}...`);
  try {
    execSync(command, { stdio: "inherit", cwd: process.cwd() });
    console.log(`✅ ${description} completed`);
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
  }
}

function debouncedBuild(mode) {
  if (buildTimeout) {
    clearTimeout(buildTimeout);
  }

  buildTimeout = setTimeout(() => {
    if (isBuilding) return;

    isBuilding = true;
    console.log("");
    console.log("🔄 File change detected, rebuilding...");

    // Step 1: Set up environment
    runCommand(`node scripts/setup-env.cjs ${mode}`, "Setting up environment");

    // Step 2: Build executor
    runCommand("node build-executor.cjs", "Building executor");

    console.log("✅ Rebuild complete! Extension updated.");
    console.log(
      "🔄 Auto-reload enabled - extension will refresh automatically!"
    );
    console.log("👀 Watching for changes...");
    console.log("");

    isBuilding = false;
  }, 500); // Debounce for 500ms
}

const mode = process.argv[2] || "executor";

console.log("🚀 Starting file watcher for extension development...");
console.log(`📝 Mode: ${mode}`);
console.log("");

// Initial build
runCommand(`node scripts/setup-env.cjs ${mode}`, "Initial environment setup");
runCommand("node build-executor.cjs", "Initial build");

console.log("");
console.log("✅ Initial build complete!");
console.log("📁 Extension location: /Users/<USER>/smart-agent/executor");
console.log("");
console.log("👀 Now watching for file changes...");
console.log("🔄 Any changes to src/ will automatically rebuild the extension");
console.log("💡 Use Ctrl+C to stop watching");
console.log("");

// Watch for changes in src directory
const srcDir = path.resolve(__dirname, "../src");

try {
  fs.watch(srcDir, { recursive: true }, (eventType, filename) => {
    if (
      filename &&
      (filename.endsWith(".tsx") ||
        filename.endsWith(".ts") ||
        filename.endsWith(".css"))
    ) {
      console.log(`📝 Changed: ${filename}`);
      debouncedBuild(mode);
    }
  });

  console.log("🔍 Watching directory:", srcDir);
  console.log(
    "📱 Ready! Make changes to your React components and see them in the extension."
  );
} catch (error) {
  console.error("❌ Failed to start file watcher:", error.message);
  process.exit(1);
}
