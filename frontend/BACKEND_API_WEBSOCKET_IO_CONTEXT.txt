=== REST API INPUT/OUTPUT TYPES ===

1. CHAT HISTORY ENDPOINT
POST /api/v1/chatHistory

INPUT:
{
  "user_id": "string",
  "conversation_id": "string", 
  "type": "orders" | "monitoring" | "chat",
  "brokerName": "zerodha" | "upstox" | "angelone" | "fyers" | "aliceblue"
}

OUTPUT:
{
  "user_id": "string",
  "conversation_id": "string",
  "type": "orders" | "monitoring" | "chat", 
  "brokerName": "zerodha" | "upstox" | "angelone" | "fyers" | "aliceblue",
  "history": [
    {
      "sender": "user" | "system",
      "message": "string",
      "timestamp": "2025-01-19T20:33:51.153332",
      "message_type": "order_confirmation" | "monitor_order" | "chat_response",
      "actions": [
        {
          "description": "string",
          "type": "orders" | "monitoring" | "chat",
          "message": "string"
        }
      ]
    }
  ]
}

2. ORDERS ENDPOINT
POST /api/v1/orders

INPUT:
{
  "user_id": "string",
  "broker": "string" (optional),
  "status": "string" (optional)
}

OUTPUT:
{
  "orders": [
    {
      "order_id": "string",
      "symbol": "string",
      "quantity": number,
      "price": number,
      "order_type": "MARKET" | "LIMIT" | "STOP_LOSS" | "STOP_LIMIT",
      "transaction_type": "BUY" | "SELL",
      "status": "PENDING" | "COMPLETED" | "CANCELLED" | "OPEN" | "FILLED",
      "created_at": "2025-01-19T20:33:51.153332"
    }
  ]
}

3. MONITORING INSTANCES ENDPOINT
POST /api/v1/monitoring/instances

INPUT:
{
  "user_id": "string"
}

OUTPUT:
{
  "monitoring_instances": [
    {
      "instance_id": "string",
      "broker": "string",
      "name": "string",
      "target": "string",
      "status": "active" | "inactive" | "completed" | "filled",
      "created_at": "2025-01-19T20:33:51.153332"
    }
  ]
}

=== WEBSOCKET CHAT INPUT/OUTPUT TYPES ===

WEBSOCKET CONNECTION: ws://localhost:8000/api/v1/ws/chat?user_id={user_id}

SEND MESSAGE (Frontend → Backend):
{
  "user_id": "string",
  "conversation_id": "string",
  "message": "string",
  "sender": "user",
  "brokerName": "zerodha" | "upstox" | "angelone" | "fyers" | "aliceblue",
  "typeOfMessage": "orders" | "monitoring" | "chat",
  "modelId": "mock-llm-v1"
}

RECEIVE MESSAGE (Backend → Frontend):
{
  "textMessage": "string",
  "messageType": "order_confirmation" | "monitor_order" | "chat_response",
  "primitives": [],
  "sender": "system",
  "actions": [
    {
      "description": "string",
      "type": "orders" | "monitoring" | "chat",
      "message": "string"
    }
  ],
  "user_id": "string",
  "conversation_id": "string"
}