# Extension Development Guide

This guide explains how to develop and test the OrderGPT Chrome extension with automatic rebuilding.

## Quick Start

### Extension Mode (Recommended)

**One-time build:**

```bash
# Development with mock data
npm run dev:mock

# Development with local backend
npm run dev:local
```

**Auto-rebuild on file changes (Best for Development):**

```bash
# Development with mock data + file watching
npm run dev:mock-watch

# Development with local backend + file watching
npm run dev:local-watch
```

### Traditional Web Development

If you want to develop in the traditional web browser (not extension):

```bash
# Development with mock data + Web browser
npm run dev:mock-web

# Development with local backend + Web browser
npm run dev:local-web
```

## What Happens When You Run Commands

### `npm run dev:mock` or `npm run dev:local` (One-time build)

1. **Environment Setup**: Creates appropriate `.env` file for the mode
2. **Build Executor**: Runs `build-executor.cjs` to compile and copy files to `executor/ui/`
3. **Ready**: Shows extension location and manual loading instructions

### `npm run dev:mock-watch` or `npm run dev:local-watch` (Auto-rebuild)

1. **Initial Build**: Same as above
2. **File Watching**: Monitors `src/` for `.tsx`, `.ts`, `.css` changes
3. **Auto-Rebuild**: Automatically rebuilds when files change (debounced 500ms)
4. **Keeps Running**: Continues watching until you press Ctrl+C

## Manual Commands

### Just Open Chrome with Extension

```bash
npm run extension:open
```

### Just Setup Environment

```bash
npm run env:setup executor   # For extension mode
npm run env:setup development # For mock mode
npm run env:setup dev        # For local backend mode
```

### Clean Environment Files

```bash
npm run env:clean
```

## Extension Development Workflow

### First Time Setup (One-time only)

1. **Start Development**:

   ```bash
   npm run dev:mock-watch    # or dev:local-watch for auto-rebuild
   ```

2. **Load Extension in Chrome** (manual - one time only):
   - Open Chrome → `chrome://extensions/`
   - Enable "Developer mode" (top-right toggle)
   - Click "Load unpacked"
   - Navigate to: `/Users/<USER>/smart-agent/executor`
   - Click "Select" to load extension

3. **Open Extension**: Click the puzzle piece icon (🧩) in Chrome toolbar

### Development Loop (Auto-rebuild)

1. **Keep Running**: `npm run dev:mock-watch` should still be running
2. **Make Changes**: Edit files in `src/` directory
3. **Auto-Rebuild**: Files automatically rebuild when saved!
4. **Refresh Extension**: Click refresh icon on extension card in `chrome://extensions/` or press Ctrl+R in the extension panel
5. **Test**: See your changes immediately in the extension

### Development Loop (Manual rebuild)

1. **Make Changes**: Edit files in `src/` directory
2. **Rebuild**: Run `npm run dev:mock` (or `dev:local`)
3. **Refresh Extension**: Click refresh icon on extension card in `chrome://extensions/` or press Ctrl+R in the extension panel
4. **Test**: See your changes in the extension

## Environment Files

The system automatically creates these environment files:

- `.env.executor`: For extension builds (connects to real API)
- `.env.development`: For mock mode (uses mock data)
- `.env.dev`: For local backend mode (connects to local backend)

## Troubleshooting

### Chrome Doesn't Open Automatically

If Chrome doesn't open automatically, follow these manual steps:

1. Open Chrome
2. Go to `chrome://extensions/`
3. Enable "Developer mode" (top right toggle)
4. Click "Load unpacked" and select the `executor` directory
5. Click the extension icon to open the side panel

### Extension Not Loading

- Make sure the `executor/ui/` directory has built files
- Check if `executor/manifest.json` exists
- Try reloading the extension in Chrome

### Build Errors

- Make sure you're in the `frontend/` directory
- Check if all dependencies are installed: `npm install`
- Clean and rebuild: `npm run env:clean && npm run dev:mock`

## Directory Structure

```
frontend/
├── scripts/
│   ├── setup-env.js           # Creates environment files
│   ├── open-chrome-extension.js # Opens Chrome with extension
│   └── build-and-open.js      # Main orchestration script
├── build-executor.cjs          # Builds extension files
└── package.json               # Updated with new commands

executor/
├── ui/                        # Built extension files (auto-generated)
├── manifest.json             # Extension manifest
└── ...                       # Other extension files
```

## Command Reference

| Command                    | Description                                       |
| -------------------------- | ------------------------------------------------- |
| `npm run dev:mock`         | Build extension once (mock data)                  |
| `npm run dev:local`        | Build extension once (local backend)              |
| `npm run dev:mock-watch`   | Build extension + auto-rebuild on changes (mock)  |
| `npm run dev:local-watch`  | Build extension + auto-rebuild on changes (local) |
| `npm run dev:mock-web`     | Traditional web dev (mock data)                   |
| `npm run dev:local-web`    | Traditional web dev (local backend)               |
| `npm run extension:open`   | Show extension loading instructions               |
| `npm run env:setup <mode>` | Setup environment for specific mode               |
| `npm run env:clean`        | Remove all environment files                      |
| `npm run build:executor`   | Just build executor (no opening)                  |
