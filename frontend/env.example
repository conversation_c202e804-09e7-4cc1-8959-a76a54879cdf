# OrderGPT Frontend Environment Variables
# Copy this file to .env.local, .env.development, or .env.production and update the values

# ================================
# CORE CONFIGURATION
# ================================

# Mock Mode - Enable to use mock data instead of real API calls
# Set to 'true' for development with mock data, 'false' for real API integration
VITE_USE_MOCK=false

# ================================
# API CONFIGURATION
# ================================

# Base API URL - Main backend server address
# Development: http://localhost:8000
# Production: https://api.orderGPT.com
VITE_API_BASE_URL=http://localhost:8000

# WebSocket URL - Real-time communication endpoint
# Development: ws://localhost:8000
# Production: wss://api.orderGPT.com
VITE_WS_URL=ws://localhost:8000

# ================================
# API ENDPOINTS
# ================================

# Individual API endpoints (relative to base URL)
# These can be overridden if your backend uses different paths

# Notifications API endpoint
VITE_NOTIFICATIONS_ENDPOINT=/api/notifications

# User profile API endpoint
VITE_PROFILE_ENDPOINT=/api/profile

# Trading orders API endpoint
VITE_ORDERS_ENDPOINT=/api/v1/orders

# Monitoring alerts API endpoint
VITE_MONITORING_ENDPOINT=/api/v1/monitoring/instances

# Chat history API endpoint (for session initialization)
VITE_CHAT_ENDPOINT=/api/v1/chatHistory

# Health check API endpoint
VITE_HEALTH_ENDPOINT=/health

# WebSocket chat endpoint (for real-time messaging)
VITE_WS_CHAT_ENDPOINT=/api/v1/ws/chat

# ================================
# ENVIRONMENT SPECIFIC EXAMPLES
# ================================

# --- DEVELOPMENT ENVIRONMENT ---
# VITE_USE_MOCK=true
# VITE_API_BASE_URL=http://localhost:8000
# VITE_WS_URL=ws://localhost:8000

# --- PRODUCTION ENVIRONMENT ---
# VITE_USE_MOCK=false
# VITE_API_BASE_URL=https://api.orderGPT.com
# VITE_WS_URL=wss://api.orderGPT.com

# --- STAGING ENVIRONMENT ---
# VITE_USE_MOCK=false
# VITE_API_BASE_URL=https://staging-api.orderGPT.com
# VITE_WS_URL=wss://staging-api.orderGPT.com

# ================================
# ADDITIONAL CONFIGURATION
# ================================

# Add any future environment variables here
# Examples:
# VITE_APP_VERSION=1.0.0
# VITE_BROKER_DEFAULT=zerodha
# VITE_FEATURE_FLAGS={}
# VITE_ANALYTICS_ID=your-analytics-id
# VITE_SENTRY_DSN=your-sentry-dsn
