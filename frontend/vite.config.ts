import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import { viteSingleFile } from "vite-plugin-singlefile";

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  console.log(`Running in mode: ${mode}`);

  // Load env file based on mode
  // For mode 'dev', will load .env.local (connects to real backend)
  // For mode 'development', will load .env.development (uses mock data)
  // For mode 'production', will load .env.production
  // Note: .env.local is loaded automatically for all modes except 'test'
  const env = loadEnv(mode, process.cwd(), "");

  // Determine if we should use mock based on mode or environment variable
  let useMock = false;

  if (mode === "development") {
    useMock = true;
  } else if (mode === "dev") {
    useMock = false;
  } else if (mode === "executor") {
    useMock = false; // Use real API for executor extension
  } else {
    // Fallback to environment variable
    useMock = env.VITE_USE_MOCK === "true";
  }

  // Determine login mode
  const loginMode = env.VITE_LOGIN_MODE || "extension";
  // Determine if we should use single file build
  const useSingleFile = mode === "single";

  console.log(`Using mock: ${useMock}`);
  console.log(`Login mode: ${loginMode}`);
  console.log(`Using single file: ${useSingleFile}`);
  console.log(`Base URL: ${env.VITE_API_BASE_URL || "default"}`);
  console.log(`WebSocket URL: ${env.VITE_WS_URL || "default"}`);

  const plugins = useSingleFile ? [react(), viteSingleFile()] : [react()];

  return {
    plugins,
    define: {
      __USE_MOCK__: JSON.stringify(useMock),
      __WS_URL__: JSON.stringify(env.VITE_WS_URL),
      __WS_CHAT_ENDPOINT__: JSON.stringify(env.VITE_WS_CHAT_ENDPOINT),
      __LOGIN_MODE__: JSON.stringify(loginMode),
    },
    worker: {
      format: "es",
      plugins: () => [],
    },
    server: {
      allowedHosts: true
    },
    build: {
      // Ensure proper minification and chunking for non-single builds
      minify: 'terser',
      // Use relative paths for assets and resources
      assetsDir: '',
      rollupOptions: useSingleFile ? undefined : {
        output: {
          // Create a single main chunk for the application
          manualChunks: undefined,
          // Ensure the main entry point is named index.js
          entryFileNames: 'index.js',
          // Keep worker files separate
          chunkFileNames: '[name].js',
          assetFileNames: '[name].[ext]'
        }
      }
    },
    // Configure base path for relative URLs
    base: './'
  };
});
