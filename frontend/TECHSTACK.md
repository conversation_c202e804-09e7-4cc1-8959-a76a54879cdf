# Tech Stack Overview

This document details the dependencies powering our Vite‑based front‑end application, including their versions and primary purposes. Use this as the definitive reference for adding, upgrading, or auditing packages.

---

## 🛠️ Core Dependencies

| Package                | Version   | Purpose                                                                                    |
| ---------------------- | --------- | ------------------------------------------------------------------------------------------ |
| `react`                | ^19.1.0   | Component-driven UI library                                                                |
| `react-dom`            | ^19.1.0   | React DOM renderer for mounting components                                                 |
| `typescript`           | ^5.8.3    | Type safety and improved developer ergonomics                                              |
| `vite`                 | ^7.0.2    | Next‑gen build tool with lightning-fast dev server and HMR                                 |
| `@vitejs/plugin-react` | ^4.6.0    | Enables React Fast Refresh and JSX support                                                 |
| `zustand`              | ^5.0.6    | Minimal, flexible global state management                                                  |
| `tailwindcss`          | ^3.4.17   | Utility‑first CSS framework for rapid, responsive styling                                  |
| `postcss`              | ^8.5.6    | PostCSS processor for integrating Tailwind directives                                      |
| `autoprefixer`         | ^10.4.21  | Automatic vendor-prefixing for cross-browser CSS support                                   |
| `shadcn-ui`            | ^0.9.5    | Headless, accessible UI components built on Radix primitives and Tailwind                  |
| `@radix-ui/react-*`    | ^1.1.14   | Peer dependencies supplying base functionality for shadcn-ui (Popover, Dialog, Tabs, etc.) |

---

## 🔧 Development Dependencies

| Package                     | Version   | Purpose                                                           |
| --------------------------- | --------- | ----------------------------------------------------------------- |
| `eslint`                    | ^9.30.1   | Linting JavaScript/TypeScript code for style and error prevention |
| `prettier`                  | ^3.6.2    | Opinionated code formatter to ensure consistency                  |
| `vitest`                    | ^3.2.4    | Vite-native test runner for unit and integration tests            |
| `@testing-library/react`    | ^16.3.0   | Testing utilities for React component behavior                    |
| `@testing-library/jest-dom` | ^6.6.3    | Custom Jest matchers for asserting on DOM nodes                   |

---

## 🖥️ Node & npm Requirements

- Node.js ≥ 16.x (from README.md)
- npm ≥ 8.x (from README.md)

---

## 🚀 Rationale

- **Performance**: Vite's esbuild-powered dev server and optimized production bundling minimize build times and payload sizes.
- **Developer Experience**: TypeScript, ESLint, and Prettier work together to catch errors early and enforce a unified code style.
- **Scalability**: React's component model and Zustand's lightweight store allow incremental growth from small prototypes to large applications.
- **Design Consistency**: Tailwind CSS ensures responsive layouts, while shadcn-ui provides accessible, customizable components adhering to design tokens.
- **Testability**: Vitest and Testing Library offer fast feedback loops and reliable component testing.

---

_Refer to this document when updating or auditing dependencies to maintain alignment with our project standards._
