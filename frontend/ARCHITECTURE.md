# Architecture Overview

This document outlines the architecture for our Vite‑based Single Page Application (SPA). It covers project structure, the environment‑based data source flag, and the switch‑style `pageNavigator` logic that drives navigation via a page stack.

---

## 📦 Project Structure

```text
frontend/
├── public/
│   └── assets/              # Static files: images, fonts, mock JSON (if using mock flag)
├── src/
│   ├── navigation/
│   │   └── pageNavigator.ts # Switch‑style navigator logic
│   ├── pages/               # Page components (HomePage.tsx, ChatPage.tsx, ProfilePage.tsx, SettingsPage.tsx)
│   ├── stores/              # Zustand stores (navStore.ts)
│   ├── utils/               # API client and helpers (apiClient.ts)
│   ├── App.tsx              # Root React component (renders pages)
│   └── main.tsx             # ReactDOM.render entrypoint
└── vite.config.ts           # Vite config (define __USE_MOCK__ flag)
```

Minimal folder structure ensures abstraction while keeping responsibilities separated.

---

## 🌐 Environment Flag for Data Source

Use the `VITE_USE_MOCK` environment variable to toggle between mock JSON files in `/public/assets` and real API calls:

```ts
// vite.config.ts
import { defineConfig } from "vite";

export default defineConfig({
  define: {
    __USE_MOCK__: JSON.stringify(process.env.VITE_USE_MOCK === "true"),
  },
});
```

```ts
// src/utils/apiClient.ts
const BASE_URL = __USE_MOCK__
  ? "/assets/mock-data" // serves JSON from public/assets/mock-data
  : "https://api.myapp.com";

export async function fetchData(path: string) {
  const res = await fetch(`${BASE_URL}${path}`);
  return res.json();
}
```

This approach centralizes the data source decision and keeps API calls consistent across pages.

---

## 🧭 Switch‑Style Page Navigator

The `pageNavigator` reads the current page key from the top of the navigation stack and uses a `switch` statement to determine the next page based on store state. Each case returns an object with optional `actions` and a `target` page key.

```ts
// src/navigation/pageNavigator.ts
import { useNavStore } from "../stores/navStore";
import type { PageKey } from "../stores/navStore";

// Result returned by each navigation case
interface NavResult {
  actions?: Array<() => void>;
  target: PageKey;
}

// Evaluate current page and decide next
export function getNavigationOutcome(): NavResult | null {
  const nav = useNavStore.getState();
  const current = nav.stack[nav.stack.length - 1];

  switch (current) {
    case "home":
      // If authenticated, go to chat; otherwise, profile
      if (nav.isAuthenticated) {
        return {
          actions: [() => console.log("Navigating to chat")],
          target: "chat",
        };
      }
      return { target: "profile" };

    case "chat":
      // If unread messages exist, clear them and stay; else, go home
      if (nav.unreadCount > 0) {
        return {
          actions: [() => nav.clearUnread()],
          target: "chat",
        };
      }
      return { target: "home" };

    case "profile":
      // Always navigate to settings
      return { target: "settings" };

    case "settings":
      // Return to home after settings
      return { target: "home" };

    default:
      return null;
  }
}

// Trigger navigation: perform actions, update stack, update URL
export function navigate(): void {
  const outcome = getNavigationOutcome();
  if (!outcome) return;
  outcome.actions?.forEach((fn) => fn());
  const nav = useNavStore.getState();
  nav.push(outcome.target);
  window.history.pushState({}, "", `/${outcome.target}`);
}

// Go back: pop stack and update URL
export function goBack(): void {
  const nav = useNavStore.getState();
  nav.pop();
  const prev = nav.stack[nav.stack.length - 1] || "home";
  window.history.pushState({}, "", `/${prev}`);
}
```

- **`nav.store.stack`**: Array of `PageKey` strings, with the current page at the end.
- **`nav.isAuthenticated`**, **`nav.unreadCount`**, etc.: Flags in the same store used in conditions.

---


