{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:single": "tsc -b && vite build --mode single", "build:separate": "tsc -b && vite build", "build:executor": "node build-executor.cjs", "lint": "eslint .", "preview": "vite preview", "dev:mock": "node scripts/dev-full.cjs development", "dev:local": "node scripts/dev-full.cjs dev", "dev:mock-watch": "node scripts/watch-and-build.cjs development", "dev:local-watch": "node scripts/watch-and-build.cjs dev", "dev:prod": "vite --mode production", "dev:mock-web": "vite --mode development", "dev:local-web": "vite --mode dev", "build:mock": "tsc -b && vite build --mode development", "build:local": "tsc -b && vite build --mode dev", "build:prod": "tsc -b && vite build --mode production", "extension:open": "node scripts/open-chrome-extension.cjs", "env:setup": "node scripts/setup-env.cjs", "env:clean": "rm -f .env .env.*", "env:status": "echo 'Run with --mode to see environment details in console'"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-radio-group": "^1.3.7", "firebase": "^12.0.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-tabs": "^1.1.12", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/chrome": "^0.1.1", "@types/node": "^24.0.12", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "shadcn-ui": "^0.9.5", "tailwindcss": "^3.4.17", "terser": "^5.32.0", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^5.4.19", "vite-plugin-singlefile": "^2.3.0", "vitest": "^3.2.4", "zustand": "^5.0.6"}}