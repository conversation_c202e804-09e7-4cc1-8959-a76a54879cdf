module.exports = {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      fontFamily: {
        sans: ["Inter", "sans-serif"],
      },
      keyframes: {
        shimmer: {
          "0%": { transform: "translateX(-100%)" },
          "100%": { transform: "translateX(100%)" },
        },
        "slide-in-from-bottom": {
          "0%": { transform: "translateY(100%)" },
          "100%": { transform: "translateY(0)" },
        },
        "slide-out-to-bottom": {
          "0%": { transform: "translateY(0)" },
          "100%": { transform: "translateY(100%)" },
        },
        "fade-in-from-top": {
          "0%": { transform: "translate(-50%, -100%)", opacity: "0" },
          "100%": { transform: "translate(-50%, 0)", opacity: "1" },
        },
        "fade-out-to-top": {
          "0%": { transform: "translate(-50%, 0)", opacity: "1" },
          "100%": { transform: "translate(-50%, -100%)", opacity: "0" },
        },
      },
      animation: {
        shimmer: "shimmer 1.5s ease-in-out infinite",
        "slide-in-from-bottom": "slide-in-from-bottom 0.3s ease-out",
        "slide-out-to-bottom": "slide-out-to-bottom 0.3s ease-in",
        "fade-in-from-top": "fade-in-from-top 0.3s ease-out",
        "fade-out-to-top": "fade-out-to-top 0.3s ease-in",
      },
    },
  },
  plugins: [],
};
