// This script runs in the background and listens for extension events.

// Listen for when the extension icon is clicked.
chrome.action.onClicked.addListener((tab) => {
  // Toggle the side panel.
  // The side panel will automatically show/hide based on the 'sidePanel' permission
  // and the 'default_path' defined in manifest.json.
  // No explicit chrome.sidePanel.open() call is needed here for a simple toggle.
  // If you wanted to programmatically open it on a specific tab, you would use:
  // chrome.sidePanel.open({ tabId: tab.id });
  chrome.sidePanel.open({ tabId: tab.id });
});

// Listen for messages from web login page
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === "openExtension") {
    // Open extension side panel or popup
    chrome.action
      .openPopup()
      .then(() => {
        console.log("Extension popup opened from web login");
      })
      .catch((error) => {
        console.log("Could not open popup, trying side panel:", error);
        // Fallback to side panel if popup fails
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
          if (tabs[0]) {
            chrome.sidePanel.open({ tabId: tabs[0].id });
          }
        });
      });

    sendResponse({ success: true });
  }
});

// Optional: You can also set a default side panel behavior for specific hosts or globally.
// This example uses a simple toggle from the action button.
