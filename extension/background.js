// This script runs in the background and listens for extension events.

// Listen for when the extension icon is clicked.
chrome.action.onClicked.addListener((tab) => {
  // Toggle the side panel.
  // The side panel will automatically show/hide based on the 'sidePanel' permission
  // and the 'default_path' defined in manifest.json.
  // No explicit chrome.sidePanel.open() call is needed here for a simple toggle.
  // If you wanted to programmatically open it on a specific tab, you would use:
  // chrome.sidePanel.open({ tabId: tab.id });
  chrome.sidePanel.open({ tabId: tab.id });
});

// Optional: You can also set a default side panel behavior for specific hosts or globally.
// This example uses a simple toggle from the action button.
