// Content script to inject extension context flag
// This helps the web app detect when it's running in an extension context

// Inject flag to indicate extension context
if (typeof window !== "undefined") {
  window.__EXTENSION_CONTEXT__ = true;

  // Also inject into the global scope for better detection
  const script = document.createElement("script");
  script.textContent = "window.__EXTENSION_CONTEXT__ = true;";
  (document.head || document.documentElement).appendChild(script);
  script.remove();

  console.log("Extension context injected");
}
