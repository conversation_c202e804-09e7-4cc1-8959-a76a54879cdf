@tailwind base;
@tailwind components;
@tailwind utilities;

html, body, #app {
  height: 100%;
  min-height: 0;
}
body {
  font-family: 'Inter', sans-serif;
}
html, body {
  margin: 0;
  overflow: hidden;
}
#app {
  flex-direction: column;
  height: 100%;
  min-height: 0;
}
.loading-overlay {
  position: fixed;
  inset: 0;
  z-index: 50;
  background: #fff;
  align-items: center;
  justify-content: center;
}
#chat-section {
  flex: 1 1 0%;
  flex-direction: column;
  min-height: 0;
  height: 100%;
  padding: 1rem;
}
.main-content {
  flex: 1 1 0%;
  min-height: 0;
  gap: 1rem;
}
#sessions-sidebar {
  flex-shrink: 0;
  width: 16rem;
  flex-direction: column;
  height: 100%;
}
#sessions-list {
  flex: 1 1 0%;
  overflow-y: auto;
}
.chat-area {
  flex: 1 1 0%;
  flex-direction: column;
  min-height: 0;
}
#messages-display {
  flex: 1 1 0%;
  overflow-y: auto;
  min-height: 0;
  max-height: 100%;
}
.input-area {
  flex-shrink: 0;
  background: white;
  padding-top: 0.5rem;
}
/* Custom scrollbar for message display */
#messages-display::-webkit-scrollbar {
  width: 8px;
}
#messages-display::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}
#messages-display::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}
#messages-display::-webkit-scrollbar-thumb:hover {
  background: #555;
}
#sessions-list::-webkit-scrollbar {
  width: 6px;
}
#sessions-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 8px;
}
#sessions-list::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 8px;
}
#sessions-list::-webkit-scrollbar-thumb:hover {
  background: #555;
}
.dropdown-menu {
  transform-origin: top right;
  transition: all 0.2s ease-in-out;
}
.dropdown-menu.hidden {
  opacity: 0;
  transform: scale(0.95) translateY(-10px);
  pointer-events: none;
}
.dropdown-menu:not(.hidden) {
  opacity: 1;
  transform: scale(1) translateY(0);
}
/* Responsive: mobile sessions list full width, chat area hidden */
@media (max-width: 768px) {
  body {
      padding: 0 !important;
  }
  .main-content {
      gap: 0;
  }
  #sessions-sidebar {
      width: 100%;
      max-width: 100vw;
      min-width: 0;
      left: 0;
      right: 0;
      z-index: 20;
      background: #f9fafb;
      box-sizing: border-box;
  }
  .chat-area {
      width: 100vw;
      max-width: 100vw;
      min-width: 0;
  }
}