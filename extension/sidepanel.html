<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart FinAgent</title>
    <!-- Load Tailwind CSS from local file -->
    <script src="tailwind.js"></script>
    <link href="tailwind.css" rel="stylesheet"/>
    <link rel="apple-touch-icon" sizes="180x180" href="icons/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="icons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icons/favicon-16x16.png">
    <link rel="manifest" href="icons/site.webmanifest">
</head>
<body class="bg-gray-100 text-gray-800 p-4">
    <div id="app" class="bg-white flex rounded-lg shadow-xl overflow-hidden justify-center">
        <!-- Loading Indicator -->
        <div id="loading" class="loading-overlay flex flex-col items-center justify-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
            <p class="mt-4 text-lg font-semibold text-gray-700">Loading...</p>
        </div>

        <!-- Authentication Form Section -->
        <div id="auth-section" class="hidden">
            <div class="flex-1 flex flex-col justify-center items-center p-6">
                <h2 class="text-2xl font-bold mb-6 text-center text-gray-900">Login / Sign Up</h2>
                <div class="w-full max-w-sm">
                    <button
                        id="send-otp-btn"
                        class="w-full bg-blue-600 text-white p-3 rounded-md font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 ease-in-out shadow-md hover:shadow-lg"
                    >
                        Login / Sign Up
                    </button>
                </div>
            </div>
        </div>

        <!-- Chat Section -->
        <div id="chat-section" class="flex hidden h-full min-h-0">
            <div class="flex flex-col h-full min-h-0 w-full">
                <!-- Header with Menu Button -->
                <div class="flex items-center justify-between mb-4 pb-2 border-b border-gray-200 flex-shrink-0">
                    <div class="flex items-center space-x-3">
                        <h2 class="text-xl font-bold text-gray-900">Smart FinAgent</h2>
                        <div id="current-model-indicator" class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full font-medium hidden">
                            <span id="current-model-name">Loading...</span>
                        </div>
                    </div>
                    <div class="relative">
                        <button
                            id="menu-btn"
                            class="bg-gray-600 text-white p-2 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200 ease-in-out shadow-md hover:shadow-lg"
                        >
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                            </svg>
                        </button>
                        <div id="menu-dropdown" class="dropdown-menu hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50">
                            <div class="py-1">
                                <button id="new-chat-btn" class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition-colors duration-150 flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                    </svg>
                                    New Chat
                                </button>
                                <button id="toggle-sessions-btn" class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition-colors duration-150 flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    Sessions
                                </button>
                                <div class="border-t border-gray-200 my-1"></div>
                                <div class="px-4 py-2">
                                    <label class="text-xs font-medium text-gray-500 uppercase tracking-wide">AI Model</label>
                                    <select id="model-selector" class="w-full mt-1 text-sm text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <option value="">Loading models...</option>
                                    </select>
                                </div>
                                <div class="border-t border-gray-200 my-1"></div>
                                <a id="admin-link" href="#" target="_blank" class="hidden w-full text-left px-4 py-2 text-sm text-purple-600 hover:bg-purple-50 focus:outline-none focus:bg-purple-50 transition-colors duration-150 flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    Admin Panel
                                </a>
                                <div class="border-t border-gray-200 my-1"></div>
                                <button id="logout-btn" class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 focus:outline-none focus:bg-red-50 transition-colors duration-150 flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                    </svg>
                                    Logout
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Error Message (always visible in chat) -->
                <p id="error-message" class="text-red-600 text-center mt-2 text-sm hidden"></p>
                <!-- Main Content Area -->
                <div class="main-content flex h-full min-h-0">
                    <!-- Sessions Sidebar -->
                    <div id="sessions-sidebar" class="bg-gray-50 rounded-lg border border-gray-200 hidden flex-shrink-0 flex flex-col h-full">
                        <div class="flex flex-col h-full">
                            <div class="p-3 border-b border-gray-200 flex-shrink-0">
                                <h3 class="font-semibold text-gray-800">Chat Sessions</h3>
                            </div>
                            <div id="sessions-list" class="flex-1 overflow-y-auto p-2">
                                <!-- Sessions will be populated here -->
                            </div>
                        </div>
                    </div>
                    <!-- Chat Area -->
                    <div class="chat-area flex flex-col h-full min-h-0">
                      <div class="flex items-start">
                        <div id="current-session-info" class="text-sm text-blue-600 mb-2 px-2 font-medium hidden flex-shrink-0">
                            Current Session: <span id="session-name"></span>
                        </div>
                        <button id="toggle-raw-responses-btn" class="hidden flex text-left px-4 text-sm text-orange-600 hover:bg-orange-50 focus:outline-none focus:bg-orange-50 transition-colors duration-150">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                            </svg>
                            <span id="toggle-raw-responses-text">Show All Raw Responses</span>
                        </button>
                        <div id="user-info" class="text-sm text-gray-600 mb-4 px-2 flex-shrink-0 ml-auto"></div>
                      </div>
                        <div id="messages-display" class="bg-gray-50 p-3 rounded-lg border border-gray-200 flex-1 flex flex-col space-y-2 min-h-0">
                            <p class="text-gray-500 text-center p-4" id="no-messages-placeholder">No messages yet. Type and send!</p>
                        </div>
                        <div class="input-area">
                            <div class="flex gap-2">
                                <input
                                    type="text"
                                    id="chat-input"
                                    placeholder="Type your message..."
                                    class="flex-1 p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200"
                                />
                                <button
                                    id="send-chat-btn"
                                    class="bg-blue-600 text-white px-5 py-3 rounded-md font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 ease-in-out shadow-md hover:shadow-lg"
                                >
                                    Send
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="module" src="firebase-app.js"></script>
    <script type="module" src="firebase-auth.js"></script>
    <script type="module" src="sidepanel.js"></script>

    <!-- Name Modal Overlay (hidden by default) -->
    <div id="name-modal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40 hidden">
        <div class="bg-white rounded-2xl shadow-xl w-full max-w-md p-8 flex flex-col items-center">
            <h2 class="text-2xl font-bold mb-2 text-center text-gray-900">Your stock journey starts now!</h2>
            <p class="text-gray-600 text-center mb-2">What's your full name so we can tailor your experience?</p>
            <form id="name-modal-form" class="w-full flex flex-col mt-4">
                <label for="name-modal-input" class="text-sm font-medium text-gray-700 w-full mb-2">Full Name</label>
                <input type="text" id="name-modal-input" name="name-modal-input" required placeholder="Eg. Jogn Doe" class="flex-1 p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 mb-4" />
                <button type="submit" id="name-modal-submit" class="w-full bg-violet-600 text-white p-3 rounded-md font-semibold hover:bg-violet-700 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-offset-2 transition-all duration-200 ease-in-out mt-2 disabled:opacity-50 flex items-center justify-center">Submit</button>
            </form>
        </div>
    </div>
</body>
</html>
