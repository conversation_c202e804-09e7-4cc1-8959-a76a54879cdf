# URL Hash-Based Session Tracking

This feature allows users to share, bookmark, and navigate between chat sessions using URL hashes.

## How It Works

### URL Format

Chat session URLs follow this format:

```
https://your-domain.com/#session=<sessionId>
```

Example:

```
https://smartagent.pandeyanshuman.com/#session=session_1703123456789_abc123def
```

### Features

1. **Session Sharing**: Users can copy and share URLs that link directly to specific chat sessions
2. **Page Refresh**: Refreshing the page will return to the same session
3. **Browser Navigation**: Back/forward buttons work between different sessions
4. **Bookmarking**: Users can bookmark specific chat sessions
5. **Direct Access**: Visiting a session URL directly will load that session (after authentication)

### User Experience

#### Normal Usage

- When a user creates a new chat, the URL updates with the session ID
- When a user switches between sessions, the URL updates accordingly
- The URL always reflects the currently active session

#### URL Sharing

- Users can copy the current URL to share a specific chat session
- When someone visits a shared URL, they'll see that exact chat session (if they have access)
- If the session doesn't exist or the user doesn't have access, they'll see an error and a new session will be created

#### Error Handling

- Invalid session IDs are automatically cleared from the URL
- Users get clear error messages when sessions are not found
- The system gracefully falls back to creating new sessions

### Technical Implementation

#### Key Functions

- `getSessionIdFromHash()`: Extracts session ID from URL hash
- `updateUrlHash(sessionId)`: Updates URL hash with session ID
- `loadSessionFromHash(userId)`: Loads session from URL hash
- `handleBrowserNavigation(userId)`: Handles back/forward navigation
- `handleSessionNotFound(sessionId)`: Handles missing sessions

#### Security Features

- Session IDs are validated against the server
- Users can only access their own sessions
- Invalid session IDs are automatically cleared
- Authentication is required before accessing any session

#### Browser Compatibility

- Works with all modern browsers
- Supports browser history and navigation
- Handles hash changes in real-time
- Graceful fallback for unsupported features

### Usage Examples

#### For Users

1. **Share a Chat**: Copy the URL from your browser and share it with others
2. **Bookmark a Session**: Bookmark the current URL to return to this session later
3. **Navigate Between Sessions**: Use browser back/forward buttons to move between sessions
4. **Direct Access**: Visit a shared URL to jump directly to a specific chat

#### For Developers

The system automatically handles:

- URL hash parsing and validation
- Session loading and error handling
- Browser navigation events
- Authentication state management
- Fallback behavior for missing sessions

### Error Scenarios

1. **Session Not Found**: User gets an error message and a new session is created
2. **Invalid Session ID**: Hash is cleared and a new session is created
3. **Network Error**: User gets an error message and can retry
4. **Authentication Required**: User is prompted to log in before accessing the session

### Future Enhancements

Potential improvements could include:

- Session name in URL (e.g., `#session=abc123:My%20Chat`)
- Multiple session support in URL
- Session export/import via URL
- QR code generation for mobile sharing
- Session preview in URL metadata
