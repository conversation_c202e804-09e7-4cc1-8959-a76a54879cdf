// This script manages the UI and Firebase authentication logic for the side panel.

// This script manages the UI and Firebase authentication logic for the side panel.

// Import Firebase modules
import { initializeApp } from './firebase-app.js'; // Local import
import { getAuth, signInWithCustomToken, onAuthStateChanged, signOut } from './firebase-auth.js'; // Local import
// Uncomment and import getFirestore if you plan to implement actual chat persistence
// import { getFirestore, collection, addDoc, query, orderBy, onSnapshot } from './firebase-firestore.js';

// --- Environment Detection ---
// Check if we're running in a browser extension context
const isExtension = typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id;

// --- Firebase Configuration ---
// IMPORTANT: Replace with your actual Firebase project configuration
const firebaseConfig = {
  apiKey: "AIzaSyB9T5_wqb5ySiLtvDU99w7_Otj3PgJ1gcc",
  authDomain: "tradetalk-ad365.firebaseapp.com",
  projectId: "tradetalk-ad365",
  storageBucket: "tradetalk-ad365.firebasestorage.app",
  messagingSenderId: "861501410895",
  appId: "1:861501410895:web:290fb51137647d7339243a",
  measurementId: "G-D4YJVX7S4S"
};

// Initialize Firebase for the side panel
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
// const db = getFirestore(app); // Uncomment if using Firestore

// --- DOM Elements ---
const loadingScreen = document.getElementById('loading');
const authSection = document.getElementById('auth-section');
const chatSection = document.getElementById('chat-section');

const sendOtpBtn = document.getElementById('send-otp-btn'); // This button will now open the popup
const errorMessage = document.getElementById('error-message'); // For displaying messages

const userInfoDiv = document.getElementById('user-info');
const logoutBtn = document.getElementById('logout-btn');
const messagesDisplay = document.getElementById('messages-display');
const chatInput = document.getElementById('chat-input');
const sendChatBtn = document.getElementById('send-chat-btn');

// New session-related elements
const newChatBtn = document.getElementById('new-chat-btn');
const toggleSessionsBtn = document.getElementById('toggle-sessions-btn');
const sessionsSidebar = document.getElementById('sessions-sidebar');
const sessionsList = document.getElementById('sessions-list');
const currentSessionInfo = document.getElementById('current-session-info');
const sessionName = document.getElementById('session-name');

// Menu-related elements
const menuBtn = document.getElementById('menu-btn');
const menuDropdown = document.getElementById('menu-dropdown');
const adminLink = document.getElementById('admin-link');
const modelSelector = document.getElementById('model-selector');
const toggleRawResponsesBtn = document.getElementById('toggle-raw-responses-btn');

// Model indicator elements
const currentModelIndicator = document.getElementById('current-model-indicator');
const currentModelName = document.getElementById('current-model-name');

// --- API Configuration ---
// IMPORTANT: Update this URL based on how you're serving the sidepanel
// When served from Node.js, use relative path to auth endpoint
const AUTH_POPUP_BASE_URL = isExtension ? 'https://smartagent.pandeyanshuman.com/auth' : window.location.origin + "/auth"; // Use relative path when served from Node.js

// Chat API endpoint
const CHAT_API_BASE_URL = isExtension ? 'https://smartagent.pandeyanshuman.com/api' : window.location.origin + "/api";

// --- Session Management ---
let currentSessionId = null;
let currentSessionName = null;
let sessions = [];
let pendingSessionHash = null; // Store hash for after login
let isProcessingHashChange = false; // Prevent infinite loops
let isAdminUser = false; // Track if current user is admin
let showAllRawResponses = false; // Global toggle for showing all raw responses

// --- URL Hash Management ---
/**
 * URL Hash-Based Session Tracking System
 * 
 * This system allows users to:
 * 1. Share URLs with specific chat sessions (e.g., https://example.com/#session=abc123)
 * 2. Refresh the page and return to the same session
 * 3. Use browser back/forward navigation between sessions
 * 4. Bookmark specific chat sessions
 * 
 * How it works:
 * - When a user switches to a session, the URL hash is updated: #session=<sessionId>
 * - When the page loads, it checks for a session hash and loads that session
 * - If no hash is present, a new session is created
 * - If an invalid hash is provided, an error is shown and a new session is created
 * - Browser navigation (back/forward) is handled automatically
 * 
 * Security considerations:
 * - Session IDs are validated against the server
 * - Users can only access sessions they own
 * - Invalid session IDs are cleared from the URL
 */

// Session loading result constants
const SESSION_LOAD_RESULT = {
  FOUND: 'found',
  NOT_FOUND: 'not_found',
  NO_HASH: 'no_hash'
};

/**
 * Get session ID from URL hash
 * @returns {string|null} - Session ID from hash or null if not found
 */
function getSessionIdFromHash() {
  const hash = window.location.hash;
  if (hash && hash.startsWith('#session=')) {
    return hash.substring(9); // Remove '#session=' prefix
  }
  return null;
}

/**
 * Update URL hash with current session ID
 * @param {string} sessionId - The session ID to set in hash
 */
function updateUrlHash(sessionId) {
  const currentHash = window.location.hash;
  const newHash = sessionId ? `#session=${sessionId}` : '';

  // Only update if the hash is actually different
  if (currentHash !== newHash) {
    window.location.hash = newHash;
  }
}

/**
 * Load session from URL hash if available
 * @param {string} userId - The user's ID
 * @returns {Promise<string>} - SESSION_LOAD_RESULT constant indicating the result
 */
async function loadSessionFromHash(userId) {
  const sessionIdFromHash = getSessionIdFromHash();

  if (!sessionIdFromHash) {
    return SESSION_LOAD_RESULT.NO_HASH;
  }

  try {
    // First, ensure sessions are loaded
    await loadSessions(userId);

    // Check if the session from hash exists in our sessions list
    const session = sessions.find(s => s.id === sessionIdFromHash);

    if (session) {
      // Switch to the session from hash
      await switchToSession(sessionIdFromHash);
      return SESSION_LOAD_RESULT.FOUND;
    } else {
      // Session doesn't exist, try to load it from server

      // Try to load the session messages to see if it exists on server
      const response = await authenticatedFetch(`${CHAT_API_BASE_URL}/chat/session/${sessionIdFromHash}?userId=${userId}`);

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.session) {
          // Session exists on server, add it to our sessions list
          const serverSession = {
            id: sessionIdFromHash,
            name: data.session.name || `Chat ${new Date().toLocaleDateString()}`,
            timestamp: data.session.timestamp || new Date().toISOString(),
            messageCount: data.messages ? data.messages.length : 0
          };

          // Add to sessions list if not already present
          if (!sessions.find(s => s.id === sessionIdFromHash)) {
            sessions.unshift(serverSession);
            updateSessionsList();
          }

          // Switch to the session
          await switchToSession(sessionIdFromHash);
          return SESSION_LOAD_RESULT.FOUND;
        }
      }

      // Session doesn't exist, clear the hash and create new session
      console.warn(`Session from hash not found on server: ${sessionIdFromHash}`);
      handleSessionNotFound(sessionIdFromHash);
      return SESSION_LOAD_RESULT.NOT_FOUND;
    }
  } catch (error) {
    console.error('Error loading session from hash:', error);
    // Clear invalid hash
    handleSessionNotFound(sessionIdFromHash);
    return SESSION_LOAD_RESULT.NOT_FOUND;
  }
}

/**
 * Get the current session URL for sharing
 * @returns {string} - The current URL with session hash
 */
function getCurrentSessionUrl() {
  if (currentSessionId) {
    return `${window.location.origin}${window.location.pathname}#session=${currentSessionId}`;
  }
  return window.location.href;
}

/**
 * Copy current session URL to clipboard
 */
async function copySessionUrl() {
  try {
    const url = getCurrentSessionUrl();
    await navigator.clipboard.writeText(url);
    showMessage('Session URL copied to clipboard!');
  } catch (error) {
    console.error('Failed to copy URL:', error);
    showError('Failed to copy URL to clipboard');
  }
}

/**
 * Handle browser navigation (back/forward buttons)
 * @param {string} userId - The user's ID
 */
async function handleBrowserNavigation(userId) {
  if (isProcessingHashChange) {
    return;
  }

  isProcessingHashChange = true;

  try {
    const sessionIdFromHash = getSessionIdFromHash();

    if (sessionIdFromHash) {
      // Try to load the session from hash
      const result = await loadSessionFromHash(userId);
      if (result === SESSION_LOAD_RESULT.NOT_FOUND) {
        // Session not found, create a new one
        createNewSession(true); // Skip hash update to prevent conflicts
      }
    } else {
      // Hash was cleared, create new session
      createNewSession(true); // Skip hash update to prevent conflicts
    }
  } finally {
    isProcessingHashChange = false;
  }
}

/**
 * Handle session not found scenario
 * @param {string} sessionId - The session ID that was not found
 */
function handleSessionNotFound(sessionId) {
  console.warn(`Session not found: ${sessionId}`);
  showError(`Session not found. The session may have been deleted or you may not have access to it.`);

  // Clear the invalid hash
  updateUrlHash(null);

  // Don't automatically create a new session here to prevent infinite loops
  // The calling function should handle creating a new session if needed
}

// --- Name Modal Logic ---
const nameModal = document.getElementById('name-modal');
const nameModalForm = document.getElementById('name-modal-form');
const nameModalInput = document.getElementById('name-modal-input');

let pendingUserIdMain = null;

if (nameModalForm) {
  nameModalForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    const name = nameModalInput.value.trim();
    if (!name || !pendingUserIdMain) return;
    try {
      const resp = await authenticatedFetch('/api/user', {
        method: 'POST',
        body: JSON.stringify({ userId: pendingUserIdMain, name })
      });
      const data = await resp.json();
      if (resp.ok && data.success) {
        // Hide modal and allow chat
        nameModal.classList.add('hidden');
        chatSection.classList.remove('pointer-events-none', 'opacity-50');
      } else {
        showError(data.error || 'Failed to save name');
      }
    } catch (err) {
      showError('Failed to save name: ' + err.message);
    }
  });
}

// --- Helper Functions ---
/**
 * Get the current user's ID token for authentication
 * @returns {Promise<string|null>} The ID token or null if not available
 */
async function getIdToken() {
  try {
    const user = auth.currentUser;
    if (user) {
      return await user.getIdToken();
    }
    return null;
  } catch (error) {
    console.error('Error getting ID token:', error);
    return null;
  }
}

/**
 * Create fetch request with authentication headers
 * @param {string} url - The URL to fetch
 * @param {Object} options - Fetch options
 * @returns {Promise<Response>} The fetch response
 */
async function authenticatedFetch(url, options = {}) {
  const idToken = await getIdToken();

  const headers = {
    'Content-Type': 'application/json',
    ...options.headers
  };

  if (idToken) {
    headers['Authorization'] = `Bearer ${idToken}`;
  }

  return fetch(url, {
    ...options,
    headers
  });
}

// --- Firebase Authentication Listener ---

onAuthStateChanged(auth, async (user) => {
  // Hide loading screen once auth state is determined
  loadingScreen.classList.add('hidden');

  if (user) {
    // User is logged in
    console.log("User is logged in:", user.phoneNumber || user.uid); // Use uid if phoneNumber is not available (e.g., for custom token)
    authSection.classList.add('hidden'); // Hide auth section
    chatSection.classList.remove('hidden'); // Show chat section
    userInfoDiv.textContent = `Logged in as: ${user.phoneNumber || user.uid}`;

    // Set authentication cookie for server-side auth check
    try {
      const idToken = await user.getIdToken();
      document.cookie = `idToken=${idToken}; path=/; max-age=3600; SameSite=Strict`;
    } catch (error) {
      console.error('Error setting auth cookie:', error);
    }

    // --- Name check logic ---
    try {
      const userResp = await authenticatedFetch(`/api/user/${user.uid}`);
      const userData = await userResp.json();
      if (!userData.exists || !userData.user || !userData.user.name || userData.user.name.trim() === '') {
        // Show modal and block chat
        pendingUserIdMain = user.uid;
        nameModal.classList.remove('hidden');
        chatSection.classList.add('pointer-events-none', 'opacity-50');
        return;
      }
    } catch (err) {
      showError('Failed to check user profile: ' + err.message);
    }

    // Load sessions and create a new session when user logs in
    loadSessions(user.uid);

    // Load available models for the user
    loadAvailableModels();

    // Check admin access and update admin link visibility
    const isAdmin = await checkAdminAccess(user.uid);
    updateAdminLinkVisibility(isAdmin);

    // Try to load session from URL hash first
    let sessionLoadedFromHash = false;

    // If we have a pending hash from before login, use that
    if (pendingSessionHash) {
      console.log(`Attempting to load pending session hash: ${pendingSessionHash}`);
      const result = await loadSessionFromHash(user.uid);
      sessionLoadedFromHash = (result === SESSION_LOAD_RESULT.FOUND);
      pendingSessionHash = null; // Clear pending hash
    } else {
      // Try to load session from current URL hash
      const result = await loadSessionFromHash(user.uid);
      sessionLoadedFromHash = (result === SESSION_LOAD_RESULT.FOUND);
    }

    // If no session was loaded from hash, create a new one
    if (!sessionLoadedFromHash) {
      createNewSession(true); // Skip hash update during initial load
    }
  } else {
    // User is logged out
    console.log("User is logged out.");
    chatSection.classList.add('hidden'); // Hide chat section
    authSection.classList.remove('hidden'); // Show auth section

    // Clear authentication cookie
    document.cookie = 'idToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Strict';

    // Clear URL hash when logging out
    updateUrlHash(null);

    // Hide admin link when user logs out
    updateAdminLinkVisibility(false);
  }
});

// --- Authentication Delegation (Open Popup) ---

/**
 * Handles the click event for opening the authentication popup.
 */
sendOtpBtn.addEventListener('click', () => {
  // Get the extension ID to pass to the popup (only if running in extension context)
  const extensionId = isExtension ? chrome.runtime.id : null;

  // Construct the popup URL with the extension ID as a query parameter (if available)
  let AUTH_POPUP_URL = AUTH_POPUP_BASE_URL;
  if (extensionId) {
    AUTH_POPUP_URL += `?extensionId=${extensionId}`;
  }

  // Open the external authentication page in a new popup window
  window.open(AUTH_POPUP_URL, 'firebaseAuthPopup', 'width=400,height=600,resizable=yes,scrollbars=yes');
  showMessage("Please complete authentication in the popup window.");
});

// --- Listen for Messages from Popup (using postMessage) ---
// This is how the authenticated token is received from the external popup.
window.addEventListener('message', async (event) => {
  // IMPORTANT: Verify the origin of the message for security
  // When served from Node.js, check against the server's origin
  const allowedOrigin = isExtension ? 'https://smartagent.pandeyanshuman.com' : window.location.origin;
  if (event.origin !== allowedOrigin) {
    console.warn("Message received from untrusted origin:", event.origin);
    return;
  }

  const request = event.data; // The data sent by postMessage

  if (request.type === "FIREBASE_AUTH_SUCCESS" && request.idToken) {
    console.log("Received ID Token from popup:", request.idToken);
    try {
      // Sign in the user in the extension's context using the custom token
      const credential = await signInWithCustomToken(auth, request.idToken);
      console.log("Signed in with custom token:", credential.user.uid);

      // Set a cookie with the ID token for server-side authentication
      const idToken = await credential.user.getIdToken();
      document.cookie = `idToken=${idToken}; path=/; max-age=3600; SameSite=Strict`;

      // The onAuthStateChanged listener will handle the UI update
      showMessage("Successfully logged in!");
    } catch (error) {
      console.error("Error signing in with custom token:", error);
      showError(`Failed to log in: ${error.message}`);
    }
  } else if (request.type === "FIREBASE_AUTH_ERROR" && request.errorMessage) {
    console.error("Received authentication error from popup:", request.errorMessage);
    showError(`Authentication failed: ${request.errorMessage}`);
  }
});

/**
 * Handles the click event for logging out.
 */
logoutBtn.addEventListener('click', async () => {
  try {
    await signOut(auth);

    // Clear the authentication cookie
    document.cookie = 'idToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Strict';

    // Clear URL hash when logging out
    updateUrlHash(null);

    console.log("User logged out successfully.");
  } catch (error) {
    console.error("Error logging out:", error);
    showError(`Failed to log out: ${error.message}`); // Display error in auth section
  }
});

// --- Session Management Functions ---

/**
 * Create a new chat session
 * @param {boolean} skipHashUpdate - Whether to skip updating the URL hash (default: false)
 */
function createNewSession(skipHashUpdate = false) {
  const sessionId = generateSessionId();
  const sessionName = `Chat ${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()}`;

  currentSessionId = sessionId;
  currentSessionName = sessionName;

  // Add to sessions list
  const newSession = {
    id: sessionId,
    name: sessionName,
    timestamp: new Date().toISOString(),
    messageCount: 0
  };

  sessions.unshift(newSession); // Add to beginning of array
  updateSessionsList();
  updateCurrentSessionInfo();

  // Update URL hash with new session ID (unless skipped)
  if (!skipHashUpdate) {
    updateUrlHash(sessionId);
  }

  // Clear messages display
  clearMessagesDisplay();

  console.log(`Created new session: ${sessionName} (${sessionId})`);
}

/**
 * Load sessions for a user
 * @param {string} userId - The user's ID
 */
async function loadSessions(userId) {
  try {
    const response = await authenticatedFetch(`${CHAT_API_BASE_URL}/chat/sessions/${userId}`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.success) {
      sessions = data.sessions || [];
      updateSessionsList();
    }
  } catch (error) {
    console.error('Error loading sessions:', error);
    // Initialize with empty sessions array if API fails
    sessions = [];
  }
}

/**
 * Load messages for a specific session
 * @param {string} sessionId - The session ID
 */
async function loadSessionMessages(sessionId) {
  try {
    const user = auth.currentUser;
    if (!user) return;

    const response = await authenticatedFetch(`${CHAT_API_BASE_URL}/chat/session/${sessionId}?userId=${user.uid}`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.success) {
      clearMessagesDisplay();

      if (data.messages && data.messages.length > 0) {
        data.messages.forEach(msg => {
          displayMessage('You', msg.user_message);
          displayMessage('FinAgent', msg.llm_response);
        });
        setTimeout(() => messagesDisplay.scrollTop = messagesDisplay.scrollHeight, 0);
      }
    }
  } catch (error) {
    console.error('Error loading session messages:', error);
    showError(`Failed to load session messages: ${error.message}`);
  }
}

/**
 * Update the sessions list display
 */
function updateSessionsList() {
  sessionsList.innerHTML = '';

  if (sessions.length === 0) {
    sessionsList.innerHTML = '<p class="text-gray-500 text-center p-4 text-sm">No sessions yet</p>';
    return;
  }

  sessions.forEach(session => {
    const sessionElement = document.createElement('div');
    sessionElement.className = `p-3 mb-2 rounded-lg cursor-pointer transition-all duration-200 ${session.id === currentSessionId
      ? 'bg-blue-100 border border-blue-300'
      : 'bg-white border border-gray-200 hover:bg-gray-50'
      }`;

    sessionElement.innerHTML = `
      <div class="font-medium text-sm text-gray-800 truncate">${session.name}</div>
      <div class="text-xs text-gray-500 mt-1">
        ${new Date(session.timestamp).toLocaleDateString()} • ${session.messageCount} messages
      </div>
    `;

    sessionElement.addEventListener('click', () => {
      updateUrlHash(session.id);
    });

    sessionsList.appendChild(sessionElement);
  });
}

/**
 * Switch to a specific session
 * @param {string} sessionId - The session ID to switch to
 */
async function switchToSession(sessionId) {
  const session = sessions.find(s => s.id === sessionId);
  if (!session) return;

  currentSessionId = sessionId;
  currentSessionName = session.name;

  updateCurrentSessionInfo();
  updateSessionsList();
  await loadSessionMessages(sessionId);

  // Update URL hash with current session ID
  updateUrlHash(sessionId);

  console.log(`Switched to session: ${session.name} (${sessionId})`);
  // Responsive: hide sessions list and show chat area on mobile
  if (window.innerWidth <= 768) {
    chatSection.classList.remove('mobile-sessions-open');
    sessionsSidebar.classList.add('hidden');
  }
}

/**
 * Update the current session info display
 */
function updateCurrentSessionInfo() {
  if (currentSessionName) {
    sessionName.textContent = currentSessionName;
    currentSessionInfo.classList.remove('hidden');
  } else {
    currentSessionInfo.classList.add('hidden');
  }
}

/**
 * Clear the messages display
 */
function clearMessagesDisplay() {
  messagesDisplay.innerHTML = '<p class="text-gray-500 text-center p-4" id="no-messages-placeholder">No messages yet. Type and send!</p>';
}

// --- Session UI Event Listeners ---

/**
 * Handle new chat button click
 */
newChatBtn.addEventListener('click', () => {
  createNewSession();
  closeMenu();
});

/**
 * Handle toggle sessions sidebar button click
 */
toggleSessionsBtn.addEventListener('click', () => {
  // On mobile, show sessions list full width and hide chat area
  if (window.innerWidth <= 768) {
    chatSection.classList.add('mobile-sessions-open');
  }
  // sessionsSidebar.classList.remove('hidden');
  sessionsSidebar.classList.toggle('hidden');
  closeMenu();
});

/**
 * Handle menu button click
 */
menuBtn.addEventListener('click', (e) => {
  e.stopPropagation();
  toggleMenu();
});

/**
 * Close menu when clicking outside
 */
document.addEventListener('click', (e) => {
  if (!menuBtn.contains(e.target) && !menuDropdown.contains(e.target)) {
    closeMenu();
  }
});

/**
 * Toggle menu visibility
 */
function toggleMenu() {
  menuDropdown.classList.toggle('hidden');
}

/**
 * Close menu
 */
function closeMenu() {
  menuDropdown.classList.add('hidden');
}

/**
 * Handle admin link click
 */
adminLink.addEventListener('click', (e) => {
  e.preventDefault();
  const adminUrl = adminLink.href;
  window.open(adminUrl, '_blank', 'noopener,noreferrer');
  closeMenu();
});

/**
 * Handle toggle raw responses button click
 */
toggleRawResponsesBtn.addEventListener('click', (e) => {
  e.preventDefault();
  toggleAllRawResponses();
});

// --- Chat API Integration ---

/**
 * Send a chat message to the server and get LLM response with retry logic
 * @param {string} message - The user's message
 * @param {string} userId - The user's ID
 * @param {number} retryCount - Number of retries attempted
 * @returns {Promise<Object>} - The response from the server
 */
async function sendChatMessage(message, userId, retryCount = 0) {
  const maxRetries = 2;
  const retryDelay = 1000 * (retryCount + 1); // Exponential backoff: 1s, 2s, 3s

  try {
    // Update button text to show retry status
    if (retryCount > 0) {
      sendChatBtn.textContent = `Retrying... (${retryCount}/${maxRetries})`;
    }

    const response = await authenticatedFetch(`${CHAT_API_BASE_URL}/chat/v2`, {
      method: 'POST',
      body: JSON.stringify({
        message: message,
        userId: userId,
        sessionId: currentSessionId
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error sending chat message (attempt ${retryCount + 1}):`, error);

    // Retry logic for certain types of errors
    if (retryCount < maxRetries) {
      const shouldRetry = error.message.includes('high traffic') ||
        error.message.includes('rate limit') ||
        error.message.includes('timeout') ||
        error.message.includes('network') ||
        error.message.includes('503') ||
        error.message.includes('Failed to fetch');

      if (shouldRetry) {
        console.log(`Retrying in ${retryDelay}ms... (attempt ${retryCount + 1}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        return sendChatMessage(message, userId, retryCount + 1);
      }
    }

    throw error;
  }
}

/**
 * Generate a simple session ID
 * @returns {string} - Session ID
 */
function generateSessionId() {
  return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

/**
 * Displays an error message to the user.
 * @param {string} message - The error message to display.
 */
function showError(message) {
  errorMessage.textContent = message;
  errorMessage.classList.remove('hidden', 'text-green-600');
  errorMessage.classList.add('text-red-600');
  // Hide error after a few seconds
  setTimeout(() => {
    errorMessage.classList.add('hidden');
  }, 8000); // Give user more time to read errors
}

/**
 * Displays a success/info message to the user temporarily.
 * @param {string} message - The message to display.
 */
function showMessage(message) {
  errorMessage.textContent = message;
  errorMessage.classList.remove('hidden', 'text-red-600');
  errorMessage.classList.add('text-green-600');
  setTimeout(() => {
    errorMessage.classList.add('hidden');
    errorMessage.classList.remove('text-green-600');
    errorMessage.classList.add('text-red-600'); // Revert to red for errors
  }, 8000); // Give user more time to read messages
}

/**
 * Reset the send button to its normal state
 */
function resetSendButton() {
  sendChatBtn.disabled = false;
  sendChatBtn.textContent = 'Send';
  sendChatBtn.classList.remove('bg-orange-600', 'hover:bg-orange-700');
  sendChatBtn.classList.add('bg-blue-600', 'hover:bg-blue-700');
}

/**
 * Check if the current user has admin access
 * @param {string} userId - The user's ID
 * @returns {Promise<boolean>} - True if user has admin access, false otherwise
 */
async function checkAdminAccess(userId) {
  try {
    const response = await authenticatedFetch(`${CHAT_API_BASE_URL}/admin/validate-firebase-user`, {
      method: 'POST'
    });

    if (response.ok) {
      const data = await response.json();
      return data.success && data.user;
    }
    return false;
  } catch (error) {
    console.error('Error checking admin access:', error);
    return false;
  }
}

/**
 * Update admin link visibility based on user authorization
 * @param {boolean} isAdmin - Whether the user has admin access
 */
function updateAdminLinkVisibility(isAdmin) {
  isAdminUser = isAdmin; // Set global admin flag
  if (isAdmin) {
    adminLink.classList.remove('hidden');
    toggleRawResponsesBtn.classList.remove('hidden');
    // Set the admin link URL
    const adminUrl = isExtension ? 'https://smartagent.pandeyanshuman.com/admin' : window.location.origin + '/admin';
    adminLink.href = adminUrl;
  } else {
    adminLink.classList.add('hidden');
    toggleRawResponsesBtn.classList.add('hidden');
  }
}

// --- Chat Section (Updated with Session Management) ---
sendChatBtn.addEventListener('click', async () => {
  const message = chatInput.value.trim();
  if (!message) return;

  const user = auth.currentUser;
  if (!user) {
    showError("Please log in to send messages");
    return;
  }

  if (!currentSessionId) {
    createNewSession();
  }

  // Store the original message to restore if needed
  const originalMessage = message;

  // Disable send button and show loading
  sendChatBtn.disabled = true;
  sendChatBtn.textContent = 'Sending...';

  try {
    // Display user message immediately
    displayMessage('You', message);
    chatInput.value = ''; // Clear input

    // Send message to server and get LLM response
    const response = await sendChatMessage(message, user.uid);

    if (response.success) {
      // Check if this is a plan confirmation response
      if (response.responseType === 'plan_confirmation') {
        // Handle plan confirmation response
        handlePlanConfirmation(response.intent, response.reasoning, response.originalRequest);
      } else {
        // Display normal LLM response
        displayMessage('FinAgent', response.response);
      }

      // Update session message count
      const currentSession = sessions.find(s => s.id === currentSessionId);
      if (currentSession) {
        currentSession.messageCount = (currentSession.messageCount || 0) + 1;
        updateSessionsList();
      }

      // Reset button to normal state on success
      resetSendButton();
    } else {
      // Handle different types of errors
      if (response.errorType === 'llm_error') {
        // Display LLM error as a special message
        displayErrorMessage('FinAgent', response.error);
        // Reset button to normal state for LLM errors (they're displayed in chat)
        resetSendButton();
      } else {
        throw new Error(response.error || 'Failed to get response');
      }
    }

  } catch (error) {
    console.error('Error sending message:', error);

    // Handle different error scenarios
    let errorMessage = 'Failed to send message. Please try again.';

    if (error.message.includes('high traffic') || error.message.includes('rate limit')) {
      errorMessage = 'The AI service is busy right now. Please try again in a few moments.';
    } else if (error.message.includes('timeout')) {
      errorMessage = 'Request timed out. Please check your internet connection and try again.';
    } else if (error.message.includes('network')) {
      errorMessage = 'Network error. Please check your internet connection and try again.';
    } else if (error.message.includes('400')) {
      errorMessage = 'Invalid message. Please try rephrasing your request.';
    } else if (error.message.includes('401') || error.message.includes('403')) {
      errorMessage = 'Authentication error. Please log out and log back in.';
    } else if (error.message.includes('503')) {
      errorMessage = 'Service temporarily unavailable. Please try again later.';
    } else if (error.message.includes('Failed to fetch')) {
      errorMessage = 'Unable to connect to the server. Please check your internet connection.';
    }

    showError(errorMessage);

    // Remove the user message from chat display
    const lastMessage = messagesDisplay.lastElementChild;
    if (lastMessage && lastMessage.textContent.includes('You:')) {
      lastMessage.remove();
    }

    // Restore the user's message in the input field
    chatInput.value = originalMessage;

    // Focus the input field so user can easily retry
    chatInput.focus();

    // Select all text so user can easily replace it if needed
    chatInput.select();

    // Change button text to indicate retry
    sendChatBtn.textContent = 'Retry';
    sendChatBtn.classList.add('bg-orange-600', 'hover:bg-orange-700');

  } finally {
    // Re-enable send button
    sendChatBtn.disabled = false;

    // Reset button styling if it was changed to retry
    if (sendChatBtn.textContent === 'Retry') {
      sendChatBtn.classList.remove('bg-orange-600', 'hover:bg-orange-700');
      sendChatBtn.classList.add('bg-blue-600', 'hover:bg-blue-700');
    }

    // Only reset to "Send" if it's not "Retry"
    if (sendChatBtn.textContent !== 'Retry') {
      sendChatBtn.textContent = 'Send';
    }
  }
});

/**
 * Display an error message in the chat display area
 * @param {string} sender - The sender of the message (usually 'FinAgent')
 * @param {string|Object} errorText - The error message to display
 */
function displayErrorMessage(sender, errorText) {
  // Remove the "No messages yet" placeholder if it exists
  const noMessagesPlaceholder = messagesDisplay.querySelector('#no-messages-placeholder');
  if (noMessagesPlaceholder) {
    noMessagesPlaceholder.remove();
  }

  const messageElement = document.createElement('div');
  messageElement.classList.add('mb-2', 'p-2', 'rounded-lg', 'bg-yellow-100', 'border', 'border-yellow-300', 'relative');

  // Handle JSON error responses
  let displayText = errorText;
  let rawErrorText = null;

  if (typeof errorText === 'object') {
    displayText = JSON.stringify(errorText, null, 2);
    rawErrorText = JSON.stringify(errorText, null, 2);
  } else {
    rawErrorText = errorText;
  }

  let messageContent = `
    <div class="flex items-start">
      <svg class="w-4 h-4 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
      </svg>
      <div>
        <strong class="font-semibold text-yellow-800">${sender}:</strong>
        <pre class="whitespace-pre-wrap text-sm text-yellow-700">${displayText}</pre>
      </div>
    </div>
  `;

  messageElement.innerHTML = messageContent;

  // Add admin raw response toggle for error messages
  if (isAdminUser && rawErrorText) {
    const toggleButton = document.createElement('button');
    toggleButton.className = 'absolute top-1 right-1 px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-gray-600 transition-colors z-10';
    toggleButton.innerHTML = '<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>';
    toggleButton.title = 'Toggle raw error response';
    toggleButton.setAttribute('data-raw-toggle', 'true');

    const rawResponseDiv = document.createElement('div');
    rawResponseDiv.className = 'mt-2 p-3 bg-gray-900 border border-gray-600 rounded text-xs font-mono raw-response-div hidden';
    rawResponseDiv.innerHTML = `<pre class="whitespace-pre-wrap text-gray-300 overflow-x-auto">${rawErrorText}</pre>`;

    // Apply global toggle state if enabled
    if (showAllRawResponses) {
      rawResponseDiv.classList.remove('hidden');
      toggleButton.innerHTML = '<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>';
      toggleButton.classList.remove('bg-blue-600');
      toggleButton.classList.add('bg-red-500');
    }

    toggleButton.addEventListener('click', () => {
      rawResponseDiv.classList.toggle('hidden');
      toggleButton.innerHTML = rawResponseDiv.classList.contains('hidden')
        ? '<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>'
        : '<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>';
      toggleButton.classList.toggle('bg-blue-600');
      toggleButton.classList.toggle('bg-red-500');
    });

    // Append the toggle button and raw response div directly to the message element
    messageElement.appendChild(toggleButton);
    messageElement.appendChild(rawResponseDiv);
  }

  messagesDisplay.appendChild(messageElement);
  messagesDisplay.scrollTop = messagesDisplay.scrollHeight; // Auto-scroll to latest message
}

// Allow Enter key to send messages
chatInput.addEventListener('keypress', (e) => {
  if (e.key === 'Enter' && !e.shiftKey) {
    e.preventDefault();
    sendChatBtn.click();
  }
});

// Reset button state when user starts typing
chatInput.addEventListener('input', () => {
  // If button is in "Retry" state and user modifies the message, reset to "Send"
  if (sendChatBtn.textContent === 'Retry') {
    resetSendButton();
  }
});

/**
 * Displays a message in the chat display area.
 * @param {string} sender - The sender of the message.
 * @param {string|Object} text - The message text or JSON object.
 */
function displayMessage(sender, text) {
  // Remove the "No messages yet" placeholder if it exists
  const noMessagesPlaceholder = messagesDisplay.querySelector('#no-messages-placeholder');
  if (noMessagesPlaceholder) {
    noMessagesPlaceholder.remove();
  }

  if (
    typeof text === 'object' &&
    text.intent &&
    ['agree', 'disagree', 'modify'].includes(text.intent)
  ) {
    if (text.intent === 'agree') {
      text = {
        type: 'plan_agreement',
        message: "Perfect! I'm glad the plan works for you. Let's proceed with implementing it.",
        reasoning: text.reasoning
      };
    } else if (text.intent === 'disagree') {
      text = {
        type: 'plan_disagreement',
        message: "I understand this plan doesn't meet your needs. Let me suggest some alternatives or help you refine your requirements.",
        reasoning: text.reasoning
      };
    } else if (text.intent === 'modify') {
      text = {
        type: 'plan_modification',
        message: "I'll help you modify the plan. What specific changes would you like to make?",
        reasoning: text.reasoning
      };
    }
  }

  const messageElement = document.createElement('div');
  messageElement.classList.add('mb-2', 'p-2', 'rounded-lg', 'relative');

  if (sender === 'You') {
    messageElement.classList.add('bg-blue-100', 'self-end', 'ml-auto', 'text-right');
  } else if (sender === 'FinAgent') {
    messageElement.classList.add('bg-green-100', 'self-start', 'mr-auto');
  } else {
    messageElement.classList.add('bg-gray-200', 'self-start', 'mr-auto');
  }

  // Handle different response types from FinAgent
  let displayText = text;
  let clarifications = null;
  let explanations = null;
  let rawResponse = null; // Store original response for admin view

  if (sender === 'FinAgent') {
    // Store the original response for admin viewing
    rawResponse = typeof text === 'object' ? JSON.stringify(text, null, 2) : text;

    // Check for clarifications and explanations first
    const extracted = extractClarifications(text.primitives);

    if (extracted) {
      clarifications = extracted.clarifications;
      explanations = extracted.explanations;

      if (clarifications && clarifications.length > 0) {
        // Display clarifications in a user-friendly format
        displayText = formatClarifications(clarifications);
      } else if (explanations && explanations.length > 0) {
        // Display explanations as plan of action
        displayText = formatExplanations(explanations);
      }
    } else {
      // Handle regular responses as before
      if (typeof text === 'object') {
        // Check for special plan response types
        if (text.type === 'plan_agreement') {
          displayText = formatPlanAgreement(text);
        } else if (text.type === 'plan_disagreement') {
          displayText = formatPlanDisagreement(text);
        } else if (text.type === 'plan_modification') {
          displayText = formatPlanModification(text);
        } else {
          // Regular JSON object
          displayText = JSON.stringify(text, null, 2);
        }
      } else if (typeof text === 'string') {
        // Try to parse as JSON if it looks like JSON
        try {
          if (text.trim().startsWith('{') || text.trim().startsWith('[')) {
            const parsed = JSON.parse(text);
            displayText = JSON.stringify(parsed, null, 2);
          } else {
            // Regular string response
            displayText = text;
          }
        } catch (e) {
          // Not valid JSON, treat as regular string
          displayText = text;
        }
      }
    }
  } else {
    // User messages are always strings
    displayText = text;
  }

  // Create the message content
  let messageContent = '';

  // If we have clarifications or explanations, use innerHTML to render the HTML
  if ((clarifications && clarifications.length > 0) || (explanations && explanations.length > 0)) {
    messageContent = `<strong class="font-semibold">${sender}:</strong> ${displayText}`;
  } else if (typeof text === 'object' && (text.type === 'plan_agreement' || text.type === 'plan_disagreement' || text.type === 'plan_modification')) {
    // Use innerHTML for plan response types
    messageContent = `<strong class="font-semibold">${sender}:</strong> ${displayText}`;
  } else {
    messageContent = `<strong class="font-semibold">${sender}:</strong> <pre class="whitespace-pre-wrap text-sm">${displayText}</pre>`;
  }

  messageElement.innerHTML = messageContent;

  // Add admin raw response toggle for FinAgent messages
  if (sender === 'FinAgent' && isAdminUser && rawResponse) {
    const toggleButton = document.createElement('button');
    toggleButton.className = 'absolute top-1 right-1 px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-gray-600 transition-colors z-10';
    toggleButton.innerHTML = '<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>';
    toggleButton.title = 'Toggle raw LLM response';
    toggleButton.setAttribute('data-raw-toggle', 'true');

    const rawResponseDiv = document.createElement('div');
    rawResponseDiv.className = 'mt-2 p-3 bg-gray-900 border border-gray-600 rounded text-xs font-mono raw-response-div hidden';
    rawResponseDiv.innerHTML = `<pre class="whitespace-pre-wrap text-gray-300 overflow-x-auto">${rawResponse}</pre>`;

    // Apply global toggle state if enabled
    if (showAllRawResponses) {
      rawResponseDiv.classList.remove('hidden');
      toggleButton.innerHTML = '<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>';
      toggleButton.classList.remove('bg-blue-600');
      toggleButton.classList.add('bg-red-500');
    }

    toggleButton.addEventListener('click', () => {
      rawResponseDiv.classList.toggle('hidden');
      toggleButton.innerHTML = rawResponseDiv.classList.contains('hidden')
        ? '<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>'
        : '<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>';
      toggleButton.classList.toggle('bg-blue-600');
      toggleButton.classList.toggle('bg-red-500');
    });

    // Append the toggle button and raw response div directly to the message element
    messageElement.appendChild(toggleButton);
    messageElement.appendChild(rawResponseDiv);
  }

  messagesDisplay.appendChild(messageElement);
  messagesDisplay.scrollTop = messagesDisplay.scrollHeight; // Auto-scroll to latest message
}

/**
 * Extract clarifications from a response object or array
 * @param {Object|Array|string} response - The response from the server
 * @returns {Object|null} - Object with clarifications and/or explanations, null if neither found
 */
function extractClarifications(response) {
  // If response is a string, try to parse it as JSON
  if (typeof response === 'string') {
    try {
      if (response.trim().startsWith('{') || response.trim().startsWith('[')) {
        response = JSON.parse(response);
      } else {
        return null; // Not JSON, no clarifications
      }
    } catch (e) {
      return null; // Not valid JSON, no clarifications
    }
  }

  let clarifications = [];
  let explanations = [];
  let hasNeedMoreInfo = false;

  // Helper function to check if an item needs more information
  const checkNeedMoreInfo = (item) => {
    if (item && typeof item === 'object') {
      // Check if need_more_info array exists and is not empty
      if ((item.action === "llmChat") || (item.need_more_info && Array.isArray(item.need_more_info) && item.need_more_info.length > 0)) {
        hasNeedMoreInfo = true;
        // Use clarification field for display if available, otherwise use need_more_info
        if (item.clarification && item.clarification.trim() !== '') {
          clarifications.push(item.clarification);
        } else {
          // If no clarification field, use the need_more_info items as clarifications
          item.need_more_info.forEach(info => {
            if (info && info.trim() !== '') {
              clarifications.push(info);
            }
          });
        }
      }

      // Always collect explanations if available
      if (item.human_friendly_explanation && item.human_friendly_explanation.trim() !== '') {
        explanations.push(item.human_friendly_explanation);
      }
    }
  };

  // If response is an array, check each object for clarifications and explanations
  if (Array.isArray(response)) {
    response.forEach(checkNeedMoreInfo);
  }

  // If response is an object, check multiple possible structures
  if (response && typeof response === 'object') {
    // Check if response has a response field that's an array
    if (response.response && Array.isArray(response.response)) {
      response.response.forEach(checkNeedMoreInfo);
    }

    // Also check if the response object itself has need_more_info or explanation fields
    if (response.need_more_info && Array.isArray(response.need_more_info) && response.need_more_info.length > 0) {
      hasNeedMoreInfo = true;
      if (response.clarification && response.clarification.trim() !== '') {
        clarifications.push(response.clarification);
      } else {
        response.need_more_info.forEach(info => {
          if (info && info.trim() !== '') {
            clarifications.push(info);
          }
        });
      }
    }

    if (response.human_friendly_explanation && response.human_friendly_explanation.trim() !== '') {
      explanations.push(response.human_friendly_explanation);
    }

    // Check if response has a data field that might contain clarifications/explanations
    if (response.data && Array.isArray(response.data)) {
      response.data.forEach(checkNeedMoreInfo);
    }
  }

  // Return null if neither clarifications nor explanations found
  if (clarifications.length === 0 && explanations.length === 0) {
    return null;
  }

  // If we have need_more_info, prioritize clarifications over explanations
  if (hasNeedMoreInfo && clarifications.length > 0) {
    return {
      clarifications: clarifications,
      explanations: null
    };
  }

  // Otherwise, return explanations as plan of action
  return {
    clarifications: null,
    explanations: explanations.length > 0 ? explanations : null
  };
}

/**
 * Format clarifications for display
 * @param {Array} clarifications - Array of clarification strings
 * @returns {string} - Formatted HTML string for clarifications
 */
function formatClarifications(clarifications) {
  if (!clarifications || clarifications.length === 0) {
    return '';
  }

  let html = '<div class="clarifications-section bg-blue-50 border border-blue-200 rounded-lg p-3">';
  html += '<div class="flex items-center mb-3">';
  html += '<svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
  html += '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>';
  html += '</svg>';
  html += '<span class="text-sm font-semibold text-blue-800">Clarifications needed:</span>';
  html += '</div>';
  html += '<ul class="space-y-2">';

  clarifications.forEach((clarification, index) => {
    html += '<li class="flex items-start">';
    html += '<span class="text-blue-500 mr-2 mt-0.5">•</span>';
    html += `<span class="text-sm text-blue-700 leading-relaxed">${clarification}</span>`;
    html += '</li>';
  });

  html += '</ul>';
  html += '<div class="mt-3 text-xs text-blue-600 italic">Please provide the requested information to get a more accurate response.</div>';
  html += '</div>';
  return html;
}

/**
 * Format human-friendly explanations for display
 * @param {Array} explanations - Array of explanation strings
 * @returns {string} - Formatted HTML string for explanations
 */
function formatExplanations(explanations) {
  if (!explanations || explanations.length === 0) {
    return '';
  }

  let html = '<div class="explanations-section bg-green-50 border border-green-200 rounded-lg p-3">';
  html += '<div class="flex items-center mb-3">';
  html += '<svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
  html += '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>';
  html += '</svg>';
  html += '<span class="text-sm font-semibold text-green-800">Plan of Action:</span>';
  html += '</div>';
  html += '<ul class="space-y-2">';

  explanations.forEach((explanation, index) => {
    html += '<li class="flex items-start">';
    html += '<span class="text-green-500 mr-2 mt-0.5">•</span>';
    html += `<span class="text-sm text-green-700 leading-relaxed">${explanation}</span>`;
    html += '</li>';
  });

  html += '</ul>';
  html += '<div class="mt-3 text-xs text-green-600 italic">These are the recommended steps based on your request.</div>';
  html += '<div class="mt-3 p-2 bg-green-100 rounded border border-green-300">';
  html += '<p class="text-sm text-green-800 font-medium">Does this plan look good to you?</p>';
  html += '</div>';
  html += '</div>';
  return html;
}

/**
 * Handle plan confirmation response based on user intent
 * @param {string} intent - The user's intent: 'agree', 'disagree', or 'modify'
 * @param {string} reasoning - The LLM's reasoning for the classification
 * @param {string} originalRequest - The original user request
 */
function handlePlanConfirmation(intent, reasoning, originalRequest) {
  console.log(`Plan confirmation result: ${intent} - ${reasoning}`);

  switch (intent) {
    case 'agree':
      // User agrees with the plan - show success and proceed
      displayMessage('FinAgent', {
        type: 'plan_agreement',
        message: 'Perfect! I\'m glad the plan works for you. Let\'s proceed with implementing it.',
        originalRequest: originalRequest
      });
      break;

    case 'disagree':
      // User disagrees with the plan - show alternatives
      displayMessage('FinAgent', {
        type: 'plan_disagreement',
        message: 'I understand this plan doesn\'t meet your needs. Let me suggest some alternatives or help you refine your requirements.',
        reasoning: reasoning
      });
      break;

    case 'modify':
      // User wants to modify the plan - resume normal conversation
      displayMessage('FinAgent', {
        type: 'plan_modification',
        message: 'I\'ll help you modify the plan. What specific changes would you like to make?',
        reasoning: reasoning
      });
      break;

    default:
      // Fallback - treat as normal conversation
      console.warn('Unknown plan confirmation intent:', intent);
      break;
  }
}

/**
 * Format plan agreement response for display
 * @param {Object} response - The plan agreement response object
 * @returns {string} - Formatted HTML string for plan agreement
 */
function formatPlanAgreement(response) {
  let html = '<div class="plan-agreement-section bg-blue-50 border border-blue-200 rounded-lg p-3">';
  html += '<div class="flex items-center mb-3">';
  html += '<svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
  html += '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>';
  html += '</svg>';
  html += '<span class="text-sm font-semibold text-blue-800">Great! Let\'s proceed:</span>';
  html += '</div>';
  html += `<p class="text-sm text-blue-700 mb-3">${response.message}</p>`;
  html += '<div class="mt-3 p-2 bg-blue-100 rounded border border-blue-300">';
  html += '<p class="text-sm text-blue-800 font-medium">I\'ll now help you implement this plan step by step.</p>';
  html += '</div>';
  html += '</div>';
  return html;
}

/**
 * Format plan disagreement response for display
 * @param {Object} response - The plan disagreement response object
 * @returns {string} - Formatted HTML string for plan disagreement
 */
function formatPlanDisagreement(response) {
  let html = '<div class="plan-disagreement-section bg-orange-50 border border-orange-200 rounded-lg p-3">';
  html += '<div class="flex items-center mb-3">';
  html += '<svg class="w-5 h-5 text-orange-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
  html += '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>';
  html += '</svg>';
  html += '<span class="text-sm font-semibold text-orange-800">I understand:</span>';
  html += '</div>';
  html += `<p class="text-sm text-orange-700 mb-3">${response.message}</p>`;
  if (response.reasoning) {
    html += '<div class="mt-2 p-2 bg-orange-100 rounded border border-orange-300">';
    html += `<p class="text-xs text-orange-700 italic">${response.reasoning}</p>`;
    html += '</div>';
  }
  html += '<div class="mt-3 p-2 bg-orange-100 rounded border border-orange-300">';
  html += '<p class="text-sm text-orange-800 font-medium">Let me suggest some alternatives or help you refine your requirements.</p>';
  html += '</div>';
  html += '</div>';
  return html;
}

/**
 * Format plan modification response for display
 * @param {Object} response - The plan modification response object
 * @returns {string} - Formatted HTML string for plan modification
 */
function formatPlanModification(response) {
  let html = '<div class="plan-modification-section bg-purple-50 border border-purple-200 rounded-lg p-3">';
  html += '<div class="flex items-center mb-3">';
  html += '<svg class="w-5 h-5 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
  html += '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>';
  html += '</svg>';
  html += '<span class="text-sm font-semibold text-purple-800">Let\'s modify the plan:</span>';
  html += '</div>';
  html += `<p class="text-sm text-purple-700 mb-3">${response.message}</p>`;
  if (response.reasoning) {
    html += '<div class="mt-2 p-2 bg-purple-100 rounded border border-purple-300">';
    html += `<p class="text-xs text-purple-700 italic">${response.reasoning}</p>`;
    html += '</div>';
  }
  html += '<div class="mt-3 p-2 bg-purple-100 rounded border border-purple-300">';
  html += '<p class="text-sm text-purple-800 font-medium">What specific changes would you like to make to the plan?</p>';
  html += '</div>';
  html += '</div>';
  return html;
}

// --- Model Selection Functions ---

/**
 * Load available models and populate the selector
 */
async function loadAvailableModels() {
  try {
    const response = await authenticatedFetch(`${CHAT_API_BASE_URL}/models`);

    if (response.ok) {
      const data = await response.json();
      if (data.success) {
        populateModelSelector(data.models);
      }
    }
  } catch (error) {
    console.error('Error loading available models:', error);
  }
}

/**
 * Populate the model selector dropdown
 * @param {Object} models - Object with provider as key and models array as value
 */
function populateModelSelector(models) {
  modelSelector.innerHTML = '';

  // Add default option
  const defaultOption = document.createElement('option');
  defaultOption.value = '';
  defaultOption.textContent = 'Select a model...';
  modelSelector.appendChild(defaultOption);

  // Add models grouped by provider
  Object.entries(models).forEach(([provider, modelList]) => {
    // Add provider header
    const providerOption = document.createElement('option');
    providerOption.value = '';
    providerOption.textContent = `── ${provider.toUpperCase()} ──`;
    providerOption.disabled = true;
    modelSelector.appendChild(providerOption);

    // Add models for this provider
    modelList.forEach(model => {
      const option = document.createElement('option');
      option.value = `${provider}:${model.id}`;
      option.textContent = `${model.name} - ${model.description}`;
      modelSelector.appendChild(option);
    });
  });

  // Load current user preference
  loadCurrentModelPreference();
}

/**
 * Load and set the current user's model preference
 */
async function loadCurrentModelPreference() {
  try {
    const response = await authenticatedFetch(`${CHAT_API_BASE_URL}/user/model-preference`);

    if (response.ok) {
      const data = await response.json();
      if (data.success && data.preferences) {
        const { preferred_provider, preferred_model } = data.preferences;
        const optionValue = `${preferred_provider}:${preferred_model}`;

        // Find and select the matching option
        const option = modelSelector.querySelector(`option[value="${optionValue}"]`);
        if (option) {
          modelSelector.value = optionValue;
        }

        // Update the model indicator
        updateCurrentModelIndicator(preferred_model);
      }
    }
  } catch (error) {
    console.error('Error loading current model preference:', error);
  }
}

/**
 * Update user's model preference
 * @param {string} provider - Provider name
 * @param {string} model - Model ID
 */
async function updateModelPreference(provider, model) {
  try {
    const response = await authenticatedFetch(`${CHAT_API_BASE_URL}/user/model-preference`, {
      method: 'POST',
      body: JSON.stringify({
        preferred_provider: provider,
        preferred_model: model
      })
    });

    if (response.ok) {
      const data = await response.json();
      if (data.success) {
        showMessage(`Model updated to ${model}`);
        updateCurrentModelIndicator(model);
        console.log('Model preference updated:', data.preferences);
      }
    } else {
      const errorData = await response.json();
      showError(errorData.error || 'Failed to update model preference');
    }
  } catch (error) {
    console.error('Error updating model preference:', error);
    showError('Failed to update model preference');
  }
}

/**
 * Update the current model indicator display
 * @param {string} modelName - The current model name
 */
function updateCurrentModelIndicator(modelName) {
  if (modelName) {
    currentModelName.textContent = modelName;
    currentModelIndicator.classList.remove('hidden');
  } else {
    currentModelIndicator.classList.add('hidden');
  }
}

// --- Model Selection Event Listeners ---

/**
 * Handle model selector change
 */
modelSelector.addEventListener('change', (e) => {
  const selectedValue = e.target.value;

  if (selectedValue && selectedValue.includes(':')) {
    const [provider, model] = selectedValue.split(':');
    updateModelPreference(provider, model);
  }
});

// --- Environment-specific initialization ---
window.onload = () => {
  // Capture any session hash from URL before authentication
  const initialHash = getSessionIdFromHash();
  if (initialHash) {
    pendingSessionHash = initialHash;
  }

  // Set up hash change listener for URL-based session navigation
  let hashChangeTimeout = null;
  window.addEventListener('hashchange', async () => {
    const user = auth.currentUser;
    if (user && !isProcessingHashChange) {
      // Clear any pending timeout
      if (hashChangeTimeout) {
        clearTimeout(hashChangeTimeout);
      }

      // Add a small delay to prevent rapid hash changes
      hashChangeTimeout = setTimeout(async () => {
        await handleBrowserNavigation(user.uid);
      }, 100);
    }
  });

  // Any other initial setup for the side panel can go here if needed.
  // The auth state listener will handle showing the correct section.
};

/**
 * Toggle global raw response visibility for admin users
 */
function toggleAllRawResponses() {
  showAllRawResponses = !showAllRawResponses;

  // Update all existing raw response toggles
  const rawToggles = document.querySelectorAll('[data-raw-toggle]');
  rawToggles.forEach(toggle => {
    const rawDiv = toggle.nextElementSibling;
    if (rawDiv && rawDiv.classList.contains('raw-response-div')) {
      if (showAllRawResponses) {
        rawDiv.classList.remove('hidden');
        toggle.innerHTML = '<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>';
        toggle.classList.remove('bg-blue-600');
        toggle.classList.add('bg-red-500');
      } else {
        rawDiv.classList.add('hidden');
        toggle.innerHTML = '<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>';
        toggle.classList.remove('bg-red-500');
        toggle.classList.add('bg-blue-600');
      }
    }
  });

  // Update menu item text
  const toggleText = document.getElementById('toggle-raw-responses-text');
  if (toggleText) {
    toggleText.textContent = showAllRawResponses ? 'Hide All Raw Responses' : 'Show All Raw Responses';
  }
  closeMenu();
}
