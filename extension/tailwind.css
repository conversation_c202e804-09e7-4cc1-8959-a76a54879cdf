/*! tailwindcss v4.1.10 | MIT License | https://tailwindcss.com */
@layer properties;
.invisible {
  visibility: hidden;
}
.visible {
  visibility: visible;
}
.static {
  position: static;
}
.container {
  width: 100%;
}
.mx-auto {
  margin-inline: auto;
}
.contents {
  display: contents;
}
.flex {
  display: flex;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.inline-flex {
  display: inline-flex;
}
.table {
  display: table;
}
.min-h-screen {
  min-height: 100vh;
}
.w-full {
  width: 100%;
}
.min-w-full {
  min-width: 100%;
}
.flex-1 {
  flex: 1;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.cursor-pointer {
  cursor: pointer;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.items-start {
  align-items: flex-start;
}
.justify-between {
  justify-content: space-between;
}
.justify-center {
  justify-content: center;
}
.divide-y {
  :where(& > :not(:last-child)) {
    --tw-divide-y-reverse: 0;
    border-bottom-style: var(--tw-border-style);
    border-top-style: var(--tw-border-style);
    border-top-width: calc(1px * var(--tw-divide-y-reverse));
    border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  }
}
.overflow-x-auto {
  overflow-x: auto;
}
.overflow-y-auto {
  overflow-y: auto;
}
.rounded-full {
  border-radius: calc(infinity * 1px);
}
.border {
  border-style: var(--tw-border-style);
  border-width: 1px;
}
.border-t {
  border-top-style: var(--tw-border-style);
  border-top-width: 1px;
}
.border-b {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
}
.border-b-2 {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 2px;
}
.border-transparent {
  border-color: transparent;
}
.bg-\[\#f6f7fa\] {
  background-color: #f6f7fa;
}
.bg-gradient-to-r {
  --tw-gradient-position: to right in oklab;
  background-image: linear-gradient(var(--tw-gradient-stops));
}
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.uppercase {
  text-transform: uppercase;
}
.opacity-25 {
  opacity: 25%;
}
.opacity-75 {
  opacity: 75%;
}
.opacity-80 {
  opacity: 80%;
}
.opacity-90 {
  opacity: 90%;
}
.transition {
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}
.transition-all {
  transition-property: all;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}
.transition-colors {
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}
.transition-transform {
  transition-property: transform, translate, scale, rotate;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}
.duration-200 {
  --tw-duration: 200ms;
  transition-duration: 200ms;
}
.hover\:underline {
  &:hover {
    @media (hover: hover) {
      text-decoration-line: underline;
    }
  }
}
.focus\:ring-2 {
  &:focus {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
}
.focus\:ring-offset-2 {
  &:focus {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }
}
.focus\:outline-none {
  &:focus {
    --tw-outline-style: none;
    outline-style: none;
  }
}
.disabled\:cursor-not-allowed {
  &:disabled {
    cursor: not-allowed;
  }
}
.disabled\:opacity-50 {
  &:disabled {
    opacity: 50%;
  }
}
html, body, #app {
  height: 100%;
  min-height: 0;
}
body {
  font-family: 'Inter', sans-serif;
}
html, body {
  margin: 0;
  overflow: hidden;
}
#app {
  flex-direction: column;
  height: 100%;
  min-height: 0;
}
.loading-overlay {
  position: fixed;
  inset: 0;
  z-index: 50;
  background: #fff;
  align-items: center;
  justify-content: center;
}
#chat-section {
  flex: 1 1 0%;
  flex-direction: column;
  min-height: 0;
  height: 100%;
  padding: 1rem;
}
.main-content {
  flex: 1 1 0%;
  min-height: 0;
  gap: 1rem;
}
#sessions-sidebar {
  flex-shrink: 0;
  width: 16rem;
  flex-direction: column;
  height: 100%;
}
#sessions-list {
  flex: 1 1 0%;
  overflow-y: auto;
}
.chat-area {
  flex: 1 1 0%;
  flex-direction: column;
  min-height: 0;
}
#messages-display {
  flex: 1 1 0%;
  overflow-y: auto;
  min-height: 0;
  max-height: 100%;
}
.input-area {
  flex-shrink: 0;
  background: white;
  padding-top: 0.5rem;
}
#messages-display::-webkit-scrollbar {
  width: 8px;
}
#messages-display::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}
#messages-display::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}
#messages-display::-webkit-scrollbar-thumb:hover {
  background: #555;
}
#sessions-list::-webkit-scrollbar {
  width: 6px;
}
#sessions-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 8px;
}
#sessions-list::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 8px;
}
#sessions-list::-webkit-scrollbar-thumb:hover {
  background: #555;
}
.dropdown-menu {
  transform-origin: top right;
  transition: all 0.2s ease-in-out;
}
.dropdown-menu.hidden {
  opacity: 0;
  transform: scale(0.95) translateY(-10px);
  pointer-events: none;
}
.dropdown-menu:not(.hidden) {
  opacity: 1;
  transform: scale(1) translateY(0);
}
@media (max-width: 768px) {
  body {
    padding: 0 !important;
  }
  .main-content {
    gap: 0;
  }
  #sessions-sidebar {
    width: 100%;
    max-width: 100vw;
    min-width: 0;
    left: 0;
    right: 0;
    z-index: 20;
    background: #f9fafb;
    box-sizing: border-box;
  }
  .chat-area {
    width: 100vw;
    max-width: 100vw;
    min-width: 0;
  }
}
@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-divide-y-reverse: 0;
      --tw-border-style: solid;
      --tw-duration: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
    }
  }
}
