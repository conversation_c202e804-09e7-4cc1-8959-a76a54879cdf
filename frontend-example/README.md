# Smart Agent Frontend

This is a React frontend application that integrates with the Smart Agent Backend API using Firebase Authentication.

## Features

- **Environment-Specific Authentication**: 
  - Production: Full Firebase Authentication
  - Local Development: Mock authentication for seamless development
- **Automatic Token Management**: Automatically handles Firebase ID tokens in production
- **API Integration**: Complete integration with the Smart Agent Backend API
- **TypeScript Support**: Full TypeScript support for type safety

## Environment Configuration

The application uses environment variables to control behavior:

### Local Development (APP_ENV=local)
- Authentication is bypassed
- Mock user is automatically provided
- No Firebase credentials required
- Direct API calls without authentication headers

### Production (APP_ENV=production)
- Full Firebase Authentication
- Firebase ID tokens are automatically attached to API requests
- Requires valid Firebase configuration

## Setup Instructions

### 1. Install Dependencies

```bash
npm install
```

### 2. Environment Configuration

#### For Local Development:
Copy `env.local.example` to `.env.local`:
```bash
cp env.local.example .env.local
```

The local environment will use mock authentication, so Firebase credentials are not required.

#### For Production:
Copy `env.production.example` to `.env.production`:
```bash
cp env.production.example .env.production
```

Update the Firebase configuration with your actual Firebase project credentials.

### 3. Start Development Server

```bash
npm run dev
```

The application will be available at `http://localhost:3000`.

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `VITE_APP_ENV` | Environment mode (`local` or `production`) | Yes |
| `VITE_API_BASE_URL` | Backend API base URL | Yes |
| `VITE_FIREBASE_API_KEY` | Firebase API key | Production only |
| `VITE_FIREBASE_AUTH_DOMAIN` | Firebase auth domain | Production only |
| `VITE_FIREBASE_PROJECT_ID` | Firebase project ID | Production only |
| `VITE_FIREBASE_STORAGE_BUCKET` | Firebase storage bucket | Production only |
| `VITE_FIREBASE_MESSAGING_SENDER_ID` | Firebase messaging sender ID | Production only |
| `VITE_FIREBASE_APP_ID` | Firebase app ID | Production only |

## Authentication Flow

### Local Development Mode
1. User visits the application
2. Authentication is automatically bypassed
3. Mock user is provided
4. API calls are made without authentication headers
5. Backend uses mock authentication

### Production Mode
1. User visits the application
2. User signs in/signs up using Firebase Authentication
3. Firebase ID token is automatically retrieved
4. Token is attached to all API requests
5. Backend validates Firebase token

## API Integration

The application includes a complete API service that handles:

- **Authentication**: Automatic token management
- **Chat History**: Create and retrieve chat conversations
- **Orders**: Retrieve user orders
- **Monitoring**: Get monitoring instances
- **Notifications**: Manage user notifications

## Backend Integration

This frontend is designed to work with the Smart Agent Backend API. The backend must be configured with:

- `APP_ENV=local` for local development
- `APP_ENV=production` for production
- Firebase Admin SDK configuration for production

## Development Workflow

1. **Local Development**:
   - Set `VITE_APP_ENV=local`
   - Start backend with `APP_ENV=local`
   - Authentication is automatically bypassed

2. **Production Testing**:
   - Set `VITE_APP_ENV=production`
   - Configure Firebase credentials
   - Start backend with `APP_ENV=production`
   - Use real Firebase authentication

## Building for Production

```bash
npm run build
```

The built application will be in the `dist` directory.

## Troubleshooting

### Common Issues

1. **Firebase not initialized**: Ensure Firebase credentials are correctly configured
2. **API calls failing**: Check that the backend is running and accessible
3. **Authentication errors**: Verify environment variables are set correctly

### Debug Mode

Enable debug logging by setting:
```bash
VITE_DEBUG=true
```

This will show detailed authentication and API call logs in the browser console. 