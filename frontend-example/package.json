{"name": "smart-agent-frontend", "version": "1.0.0", "description": "Frontend for Smart Agent Backend API with Firebase Authentication", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx,ts,tsx", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "firebase": "^10.7.0", "axios": "^1.6.0", "react-router-dom": "^6.20.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^5.0.0"}, "keywords": ["react", "firebase", "authentication", "trading", "smart-agent"], "author": "Smart Agent Team", "license": "MIT"}