import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { auth, isProduction, isLocal } from '../config/firebase';
import { User, signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut } from 'firebase/auth';

// API base URL
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add authentication token
apiClient.interceptors.request.use(
  async (config) => {
    if (isProduction) {
      // Production: Get Firebase ID token
      const user = auth.currentUser;
      if (user) {
        try {
          const token = await user.getIdToken();
          config.headers.Authorization = `Bearer ${token}`;
        } catch (error) {
          console.error('Failed to get Firebase ID token:', error);
        }
      }
    } else if (isLocal) {
      // Local development: No authentication header needed
      // The backend will use mock authentication
      console.log('Local development mode - no authentication header required');
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle authentication errors
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    if (error.response?.status === 401) {
      // Handle authentication errors
      if (isProduction) {
        // In production, redirect to login or refresh token
        console.error('Authentication failed in production mode');
      } else {
        // In local development, this shouldn't happen
        console.error('Authentication failed in local development mode');
      }
    }
    return Promise.reject(error);
  }
);

// Authentication service
export class AuthService {
  /**
   * Sign in with email and password
   */
  static async signIn(email: string, password: string): Promise<User> {
    if (isProduction) {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      return userCredential.user;
    } else {
      // Local development: Mock successful sign in
      console.log('Local development mode - mock sign in successful');
      return {} as User; // Mock user object
    }
  }

  /**
   * Sign up with email and password
   */
  static async signUp(email: string, password: string): Promise<User> {
    if (isProduction) {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      return userCredential.user;
    } else {
      // Local development: Mock successful sign up
      console.log('Local development mode - mock sign up successful');
      return {} as User; // Mock user object
    }
  }

  /**
   * Sign out
   */
  static async signOut(): Promise<void> {
    if (isProduction) {
      await signOut(auth);
    } else {
      // Local development: Mock sign out
      console.log('Local development mode - mock sign out successful');
    }
  }

  /**
   * Get current user
   */
  static getCurrentUser(): User | null {
    if (isProduction) {
      return auth.currentUser;
    } else {
      // Local development: Return mock user
      return {
        uid: 'mock-user-123',
        email: '<EMAIL>',
        displayName: 'Mock User',
        emailVerified: true,
      } as User;
    }
  }

  /**
   * Check if user is authenticated
   */
  static isAuthenticated(): boolean {
    if (isProduction) {
      return auth.currentUser !== null;
    } else {
      // Local development: Always return true
      return true;
    }
  }
}

// API service for backend endpoints
export class ApiService {
  /**
   * Get chat history
   */
  static async getChatHistory(conversationId: string, page = 1, pageSize = 50) {
    const response = await apiClient.get(`/api/v1/chatHistory/${conversationId}`, {
      params: { page, page_size: pageSize }
    });
    return response.data;
  }

  /**
   * Create chat history
   */
  static async createChatHistory(data: {
    user_id: string;
    conversation_id: string;
    conversation_type: string;
    broker_name: string;
  }) {
    const response = await apiClient.post('/api/v1/chatHistory', data);
    return response.data;
  }

  /**
   * Get user conversations
   */
  static async getUserConversations(userId: string, page = 1, pageSize = 20, conversationType?: string) {
    const params: any = { user_id: userId, page, page_size: pageSize };
    if (conversationType) {
      params.conversation_type = conversationType;
    }
    
    const response = await apiClient.get('/api/v1/conversations', { params });
    return response.data;
  }

  /**
   * Get user orders
   */
  static async getOrders(userId: string, brokerName: string, status?: string) {
    const params: any = { user_id: userId, broker_name: brokerName };
    if (status) {
      params.status = status;
    }
    
    const response = await apiClient.get('/api/v1/orders', { params });
    return response.data;
  }

  /**
   * Get monitoring instances
   */
  static async getMonitoringInstances(userId: string) {
    const response = await apiClient.get('/api/v1/monitoring/instances', {
      params: { user_id: userId }
    });
    return response.data;
  }

  /**
   * Get notifications
   */
  static async getNotifications(userId: string, status?: string) {
    const params: any = { user_id: userId };
    if (status) {
      params.status = status;
    }
    
    const response = await apiClient.get('/api/v1/notifications', { params });
    return response.data;
  }

  /**
   * Create notification
   */
  static async createNotification(data: {
    user_id: string;
    title: string;
    description: string;
    actions?: Array<{ type: string; label: string }>;
  }) {
    const response = await apiClient.post('/api/v1/notifications', data);
    return response.data;
  }

  /**
   * Update notification
   */
  static async updateNotification(notificationId: string, data: { status?: string }) {
    const response = await apiClient.patch(`/api/v1/notifications/${notificationId}`, data);
    return response.data;
  }
}

export default apiClient; 