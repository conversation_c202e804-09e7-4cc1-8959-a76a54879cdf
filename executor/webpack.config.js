import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export default {
  mode: 'production',
  entry: {
    'json-rule-engine-bundle': './lib/bundle-entry.js'
  },
  output: {
    path: path.resolve(__dirname, 'lib'),
    filename: '[name].bundle.js',
    library: {
      type: 'umd',
      name: 'PrimitiveEngineController',
      export: 'default'
    },
    globalObject: '(typeof globalThis !== "undefined" ? globalThis : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : this)'
  },
  target: ['web', 'es5'],
  resolve: {
    extensions: ['.js', '.json']
  },
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env']
          }
        }
      }
    ]
  }
}; 