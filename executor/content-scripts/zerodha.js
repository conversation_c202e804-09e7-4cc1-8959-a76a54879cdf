// Zerodha Kite Content Script
// Prevent multiple injections
(function() {
  console.log('🚀 SmartFinAgent content script starting...');
  
  if (window.SmartFinAgentContentScriptLoaded) {
    console.log('SmartFinAgent content script already loaded, skipping...');
    return;
  }
  window.SmartFinAgentContentScriptLoaded = true;
  
  console.log('✅ SmartFinAgent content script loaded successfully');

  // Message types for communication
  const MESSAGE_TYPES = {
    PERFORM_SITE_ACTIONS: "PERFORM_SITE_ACTIONS"
  };

    // Environment configuration
  let slowExecute = false;

  /**
   * Load timeout settings directly from shared config
   */
  async function getTimeoutSettings() {
    const defaults = {
      content_script_general_timeout_seconds: 25,
      content_script_monitoring_timeout_seconds: 300,
      monitor_max_duration_minutes: 60,
      monitor_polling_interval_seconds: 5,
      monitor_error_interval_seconds: 10,
      monitor_max_retry_attempts: 720,
      action_execution_timeout_seconds: 30,
      price_monitor_observer_timeout_minutes: 120,
      monitor_max_consecutive_errors: 5
    };

    try {
      const response = await fetch(chrome.runtime.getURL('lib/shared-config.json'));
      const config = await response.json();
      
      if (config.TIMEOUT_SETTINGS) {
        return { ...defaults, ...config.TIMEOUT_SETTINGS };
      }
      return defaults;
    } catch (error) {
      console.warn('Failed to load timeout config, using defaults:', error);
      return defaults;
    }
  }

  /**
   * Initialize environment configuration
   */
  async function initializeEnvironment() {
    try {
      const response = await fetch(chrome.runtime.getURL('lib/shared-config.json'));
      const config = await response.json();
      slowExecute = config.ENVIRONMENT?.slow_execute || false;
    } catch (error) {
      console.warn('Failed to load environment config, using default:', error);
      slowExecute = false;
    }
  }

  /**
  * Sleep function that respects the slow_execute environment variable
  * @param {number} defaultDelay - Default delay in milliseconds
  * @param {number} slowDelay - Delay when slow_execute is true (default: 1000ms)
  */
  async function smartSleep(defaultDelay = 100, slowDelay = 1000) {
    const delay = slowExecute ? slowDelay : defaultDelay;
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  // Initialize environment on script load
  initializeEnvironment();

  /**
  * Normalizes a string to uppercase for case-insensitive comparison
  * @param {string} str - String to normalize
  * @returns {string} Uppercase string
  */
  function normalizeString(str) {
    return typeof str === 'string' ? str.toUpperCase() : str;
  }

  /**
  * Enhanced function to find and select an index from search results
  * @param {string} indexName - The index name to find (e.g., "SENSEX", "NIFTY 50")
  * @param {Array} searchResults - Array of search result elements
  * @returns {Element|null} The matching search result element or null if not found
  */
  function findIndexInSearchResults(indexName, searchResults) {
    console.log(`Looking for index: ${indexName} in ${searchResults.length} search results`);
    
    for (const result of searchResults) {
      // Try multiple selectors to find the name element
      let nameElement = result.querySelector(".name span");
      if (!nameElement) {
        nameElement = result.querySelector(".name");
      }
      
      if (!nameElement) continue;
      
      const symbolName = nameElement.textContent.trim();
      const tags = Array.from(result.querySelectorAll(".tag"));
      
      console.log(`Checking result: "${symbolName}" with tags: [${tags.map(t => t.textContent).join(', ')}]`);
      
      // Normalize both names for comparison
      const normalizedSearchName = indexName.toUpperCase().replace(/\s+/g, '');
      const normalizedResultName = symbolName.toUpperCase().replace(/\s+/g, '');
      
      // Check for exact match first
      if (normalizedResultName === normalizedSearchName) {
        console.log(`Exact match found for index: ${symbolName}`);
        return result;
      }
      
      // Check for partial match (e.g., "SENSEX" in "SENSEX 5th AUG FUT")
      if (normalizedResultName.includes(normalizedSearchName) || 
          normalizedSearchName.includes(normalizedResultName)) {
        
        // For indices, prefer results with INDICES tag
        const hasIndicesTag = tags.some(tag => 
          tag.textContent.toLowerCase() === 'indices'
        );
        
        if (hasIndicesTag) {
          console.log(`Index match found with INDICES tag: ${symbolName}`);
          return result;
        }
        
        // If no INDICES tag, still accept if it's the first match
        console.log(`Index match found (no INDICES tag): ${symbolName}`);
        return result;
      }
    }
    
    console.log(`No index match found for: ${indexName}`);
    return null;
  }

  /**
  * Normalizes arguments to be case-insensitive while preserving original case for required args
  * @param {Object} args - Arguments object
  * @param {Array} requiredArgs - Array of required argument names
  * @returns {Object} Normalized arguments
  */
  function normalizeArguments(args, requiredArgs = []) {
    const normalized = {};
    
    // First handle required arguments with proper case
    for (const requiredArg of requiredArgs) {
      const foundArgKey = Object.keys(args).find(
        key => normalizeString(key) === normalizeString(requiredArg)
      );
      if (foundArgKey) {
        normalized[requiredArg] = args[foundArgKey];
      }
    }
    
    // Handle special camelCase mappings
    if (args.productType && !normalized.PRODUCT_TYPE) {
      normalized.PRODUCT_TYPE = args.productType;
    }
    if (args.triggerPrice && !normalized.TRIGGER_PRICE) {
      normalized.TRIGGER_PRICE = args.triggerPrice;
    }
    
    // Then add any additional arguments with their original case
    for (const [key, value] of Object.entries(args)) {
      const isRequiredArg = requiredArgs.some(reqArg => normalizeString(reqArg) === normalizeString(key));
      const isSpecialMapping = key === 'productType' || key === 'triggerPrice'; // Skip special mappings as they're already handled
      if (!isRequiredArg && !isSpecialMapping) {
        normalized[key] = value;
      }
    }
    
    return normalized;
  }

  /**
  * Finds an action function case-insensitively
  * @param {string} actionName - The action name to find
  * @returns {Function|null} The action function or null if not found
  */
  function findActionCaseInsensitive(actionName) {
    const normalizedActionName = normalizeString(actionName);
    const actionKey = Object.keys(siteActions).find(
      key => normalizeString(key) === normalizedActionName
    );
    return actionKey ? siteActions[actionKey] : null;
  }

  function waitForElement(selector, timeout = 10000) {
    return new Promise((resolve, reject) => {
      const el = document.querySelector(selector);
      if (el) return resolve(el);
      const start = Date.now();
      const interval = setInterval(() => {
        const el = document.querySelector(selector);
        if (el) {
          clearInterval(interval);
          resolve(el);
        } else if (Date.now() - start > timeout) {
          clearInterval(interval);
          reject(new Error("Element not found: " + selector));
        }
      }, 200);
    });
  }

  function needsLogin() {
    return document.querySelector(".login-form form") !== null;
  }

  async function searchSymbol(symbol) {
    // Validate symbol parameter
    if (!symbol || typeof symbol !== 'string') {
      throw new Error('Symbol is required and must be a string.');
    }
    
    const searchInput = document.querySelector('.search-input-field input');
    
    if (!searchInput) {
      throw new Error('Search input field not found on the page.');
    }
    
    searchInput.value = symbol;
    searchInput.dispatchEvent(new Event('input', {
      bubbles: true,
      cancelable: true
    }));
    searchInput.dispatchEvent(new KeyboardEvent('keyup', {
      key: symbol.slice(-1), // Simulate the last key press
      bubbles: true,
      cancelable: true
    }));
    
    await smartSleep(); // Wait for results to load
    
    const searchResults = Array.from(document.querySelectorAll('.omnisearch .results-item'));
    if (searchResults.length === 0) {
      throw new Error('Search was triggered, but no result elements were found.');
    }
  }

  async function clearToast() {
    if (document.querySelector(".su-toast-bottom-right .close")) {
      document.querySelector(".su-toast-bottom-right .close").click();
      await smartSleep(); // Wait for toast to close
    }
  }

  async function navigateToProfile() {
    try {
      // Find the user profile dropdown element
      const profileElement = document.querySelector('.user-nav .dropdown-label');
      
      if (!profileElement) {
        throw new Error('Profile element not found in the header');
      }
      
      // Click on the profile element to open dropdown
      profileElement.click();
      await smartSleep(); // Wait for profile dropdown to open
      
      // Now click on the profile link in the dropdown to navigate to profile page
      const profileLink = document.querySelector('#account-nav-items a[href="/profile"]');
      
      if (!profileLink) {
        throw new Error('Profile link not found in dropdown menu');
      }
      
      // Click on the profile link to navigate to profile page
      profileLink.click();
      await smartSleep(); // Wait for navigation to complete
      
      return { success: true, message: 'Successfully navigated to profile page' };
    } catch (e) {
      return { success: false, message: "NavigateToProfile failed: " + e.message };
    }
  }

  async function navigate(pageName){
    if(pageName === "dashboard"){
      document.querySelector('a[href="/dashboard"]').click();
    } else if(pageName === "orders"){
      const link = document.querySelector('a[href="/orders"]');
      if (link) {
        link.click();
      } else if (window.location && window.location.assign) {
        // Fallback in case the anchor is not present (e.g., during login or layout changes)
        window.location.assign('/orders');
      }
    } else if(pageName === "holdings"){
      document.querySelector('a[href="/holdings"]').click();
    } else if(pageName === "positions"){
      document.querySelector('a[href="/positions"]').click();
    } else if(pageName === "bids"){
      document.querySelector('a[href="/bids"]').click();
    } else if(pageName === "funds"){
      document.querySelector('a[href="/funds"]').click();
    } else if(pageName === "portfolio"){
      document.querySelector('a[href="/portfolio"]').click();
    }
    else if(pageName === "profile"){
      await navigateToProfile();
    }
    await smartSleep(); // Wait for navigation to complete
  }

  async function getProfileInfo(){
    try {
      await navigate("profile");
      await smartSleep();
      
      // Extract profile information
      const profileData = {};
      
      // Get all rows in the profile section
      const profileRows = Array.from(document.querySelectorAll('.profile .row'));
      
      for (const row of profileRows) {
        const labelElement = row.querySelector('.label');
        if (!labelElement) continue;
        
        const labelText = labelElement.textContent.trim();
        const valueElement = row.querySelector('.value');
        
        if (!valueElement) continue;
        
        // Extract email
        if (labelText === 'E-mail') {
          profileData.email = valueElement.textContent.trim();
        }
        
        // Extract Demat authorization
        if (labelText === 'Demat authorisation') {
          const authLink = valueElement.querySelector('a');
          if (authLink) {
            profileData.dematAuthorization = authLink.textContent.trim();
          }
        }
        
        // Extract segments
        if (labelText === 'Segments') {
          const segmentsLink = valueElement.querySelector('a');
          if (segmentsLink) {
            profileData.segments = segmentsLink.textContent.trim();
          }
        }
      }
      
      return { 
        success: true, 
        message: "Profile information retrieved successfully",
        data: profileData
      };
    } catch (e) {
      return { success: false, message: "GetProfileInfo failed: " + e.message };
    }
  }

  async function hover(symbol, productType, buttonToClick = null) {
    try {
      // Navigate to positions page first
      await navigate("positions");
      await smartSleep(); // Wait for positions table to load
      
      // Find all position rows
      const positionRows = Array.from(document.querySelectorAll('tbody tr'));
      
      for (const row of positionRows) {
        // Check if this row has the required symbol and product type
        const symbolElement = row.querySelector('.tradingsymbol');
        const productElement = row.querySelector('.product span');
        
        if (symbolElement && productElement) {
          const rowSymbol = symbolElement.textContent.trim();
          const rowProductType = productElement.textContent.trim();
          
          // Check if symbol and product type match (case-insensitive)
          if (rowSymbol.toLowerCase() === symbol.toLowerCase() && 
              rowProductType.toLowerCase() === productType.toLowerCase()) {
            
            // Find the menu column for this row
            const menuColumn = row.querySelector('.col-menu');
            if (menuColumn) {
              // Trigger hover event on the menu column
              menuColumn.dispatchEvent(new MouseEvent('mouseenter', {
                bubbles: true,
                cancelable: true,
                view: window
              }));
              
              await smartSleep(); // Wait for hover menu to appear
              
              // If a button to click is specified, click it
              if (buttonToClick) {
                const button = document.querySelector(`.table-menu-toolbar a[data-label="${buttonToClick}"]`);
                if (button) {
                  button.click();
                  await smartSleep(); // Wait for action to complete
                  return { success: true, message: `Hovered and clicked ${buttonToClick} for ${symbol} (${productType})` };
                } else {
                  throw new Error(`${buttonToClick} button not found in position menu`);
                }
              }
              
              return { success: true, message: `Hovered over menu for ${symbol} (${productType})` };
            }
          }
        }
      }
      
      throw new Error(`Position not found for symbol: ${symbol} and product type: ${productType}`);
    } catch (e) {
      return { success: false, message: "Hover failed: " + e.message };
    }
  }

  async function openSellModalfromPosition(symbol, productType){
    try {
      // First hover over the position menu to show the dropdown
      const hoverResult = await hover(symbol, productType);
      if (!hoverResult.success) {
        return hoverResult;
      }
      
      // Wait for the dropdown menu to appear
      await smartSleep();
      
      // Click on the hovered button (the three dots button itself)
      const menuButton = document.querySelector('.table-menu-button.icon.icon-more-vertical');
      if (menuButton) {
        menuButton.click();
        await smartSleep(); // Wait for dropdown to fully open
      }
      
      // Click on the Exit button from the dropdown menu
      const exitButton = document.querySelector('.table-menu-toolbar a[data-label="Exit"]');
      if (exitButton) {
        exitButton.click();
        await smartSleep(); // Wait for exit modal to open
        return { success: true, message: `Clicked menu button and Exit for ${symbol} (${productType})` };
      } else {
        throw new Error("Exit button not found in position menu");
      }
    } catch (e) {
      return { success: false, message: "OpenSellModalFromPosition failed: " + e.message };
    }
  }

  async function openSellModalfromHolding(symbol){
    try {
      if (needsLogin()) {
        alert("Please login first.");
        return { success: false, message: "OpenSellModalFromHolding failed: Login required" };
      }

      await navigate("holdings");
      await smartSleep();
      
      const holdingRows = Array.from(document.querySelectorAll('tbody tr'));
      
      for (const row of holdingRows) {
        const symbolElement = row.querySelector('.instrument a span');
        if (!symbolElement) continue;
        
        const rowSymbol = symbolElement.textContent.trim();
        if (rowSymbol.toLowerCase() !== symbol.toLowerCase()) continue;
        
        const menuColumn = row.querySelector('.col-menu');
        if (!menuColumn) continue;
        
        // Hover to show menu
        menuColumn.dispatchEvent(new MouseEvent('mouseenter', {
          bubbles: true,
          cancelable: true,
          view: window
        }));
        
        await smartSleep();
        
        // Click menu button
        const menuButton = document.querySelector('.table-menu-button.icon.icon-more-vertical');
        if (menuButton) {
          menuButton.click();
          await smartSleep();
        }
        
        // Click Exit button
        const exitButton = document.querySelector('.table-menu-toolbar a[data-label="Exit"]');
        if (!exitButton) {
          throw new Error("Exit button not found in holdings menu");
        }
        
        exitButton.click();
        await smartSleep();
        
        return { success: true, message: `Opened exit modal for ${symbol} from holdings` };
      }
      
      throw new Error(`Holding not found for symbol: ${symbol}`);
    } catch (e) {
      return { success: false, message: "OpenSellModalFromHolding failed: " + e.message };
    }
  }

  async function checkToast() {
    try {
        await waitForElement(".su-toast-bottom-right .title");
        
        const toastTitleEl = document.querySelector(".su-toast-bottom-right .title");
        const toastMessageEl = document.querySelector(".su-toast-bottom-right .message");
        const orderIdEl = document.querySelector(".su-toast-bottom-right .order-id");
        
        // Get status with fallback
        const status = toastTitleEl?.textContent?.trim().toLowerCase() || 'unknown';
        console.log("toast broker status => ", status);
        
        // Get message with error handling
        let message;
        try {
            message = toastMessageEl?.textContent?.trim() || 'unable to fetch';
        } catch (error) {
            message = 'unable to fetch';
        }
        
        // Get order ID with error handling
        let orderId;
        try {
            orderId = orderIdEl?.textContent?.trim() || null;
        } catch (error) {
            orderId = null;
        }
        
        // Check if status is error and throw if needed
        if (status === 'error') {
            throw new Error(message);
        }
        
        return {
            status,
            message,
            orderId
        };
        
    } catch (error) {
        // If it's already an Error object from the status check, re-throw it
        if (error instanceof Error && error.message !== 'unable to fetch') {
            throw error;
        }
        
        // For any other errors, return a safe fallback
        return {
            status: 'unknown',
            message: 'unable to fetch',
            orderId: null
        };
    }
}

  async function openBuyModal(symbol, exchange) {
    const searchResults = Array.from(document.querySelectorAll('.omnisearch .results-item'));
    if (searchResults.length === 0) {
      throw new Error('Search was triggered, but no result elements were found.');
    }
    
    let targetResult = null;
    
    if (isIndex(symbol)) {
      // Use enhanced index finding function
      targetResult = findIndexInSearchResults(symbol, searchResults);
    } else {
      // For regular stocks, use the existing logic
      for (let i = 0; i < searchResults.length; i++) {
        // Try multiple selectors to find the name element
        let nameElement = searchResults[i].querySelector(".name span");
        if (!nameElement) {
          nameElement = searchResults[i].querySelector(".name");
        }
        
        if (nameElement && nameElement.textContent.trim().toLowerCase() === symbol.toLowerCase()) {
          const tags = Array.from(searchResults[i].querySelectorAll(".tag"));
          for (let j = 0; j < tags.length; j++) {
            if (tags[j].textContent.toLowerCase() === exchange.toLowerCase()) {
              targetResult = searchResults[i];
              break;
            }
          }
          if (targetResult) break;
        }
      }
    }
    
    if (targetResult) {
      targetResult.dispatchEvent(new MouseEvent('mouseenter', {
        bubbles: true,
        cancelable: true,
        view: window
      }));
      await smartSleep(); // Wait for hover to take effect
      document.querySelector('.omnisearch .results-item button[aria-label="Buy"]').click();
    } else {
      throw new Error(`Symbol ${symbol} not found in ${exchange} exchange`);
    }
    await smartSleep(); // Wait for order window to open
  }

  async function selectRegularTab() {
    document.querySelector(".order-window input[type=radio][title='Regular order']+label").click();
    await smartSleep(); // Wait for results to load
  }

  async function setOrderDetails(quantity, orderType, orderSide = 'BUY', price = null, triggerPrice = null, limitPrice = null) {
    // Select the appropriate quantity input based on order side
    const quantityInput = document.querySelector(`.order-window input[type=number][aria-label^='${orderSide} ']`);
    quantityInput.value = quantity;
    quantityInput.dispatchEvent(new Event('input', {
      bubbles: true,
      cancelable: true
    }));
    
    // Handle price input if provided (for regular limit orders)
    const priceInput = document.querySelector(".order-window input[type=number][label='Price']");
    if (priceInput && price) {
      priceInput.value = price;
      priceInput.dispatchEvent(new Event('input', {
        bubbles: true,
        cancelable: true
      }));
    }
    
    // Handle limit price input if provided (for stop-loss limit orders)
    if (priceInput && limitPrice) {
      priceInput.value = limitPrice;
      priceInput.dispatchEvent(new Event('input', {
        bubbles: true,
        cancelable: true
      }));
    }
    
    // Handle trigger price input if provided (for stop-loss orders)
    const triggerPriceInput = document.querySelector(".order-window input[type=number][label='Trigger price']");
    if (triggerPriceInput && triggerPrice) {
      triggerPriceInput.value = triggerPrice;
      triggerPriceInput.dispatchEvent(new Event('input', {
        bubbles: true,
        cancelable: true
      }));
    }
    
    // Handle order type (CNC vs MIS)
    if (orderType === 'CNC') {
      document.querySelector(".order-window div[data-tooltip-content^='CashNCarry'] label").click();
    } else {
      document.querySelector(".order-window div[data-tooltip-content^='Intraday'] label").click();
    }
    await smartSleep(); // Wait for form to stabilize
  }

  async function clickBuyButton() {
    document.querySelector(".order-window button[type=submit]").click();
    await smartSleep(); // Wait for action to happen
  }

  async function openSellModal(symbol, exchange) {
    const searchResults = Array.from(document.querySelectorAll('.omnisearch .results-item'));
    if (searchResults.length === 0) {
      throw new Error('Search was triggered, but no result elements were found.');
    }
    
    let targetResult = null;
    
    if (isIndex(symbol)) {
      // Use enhanced index finding function
      targetResult = findIndexInSearchResults(symbol, searchResults);
    } else {
      // For regular stocks, use the existing logic
      for (let i = 0; i < searchResults.length; i++) {
        // Try multiple selectors to find the name element
        let nameElement = searchResults[i].querySelector(".name span");
        if (!nameElement) {
          nameElement = searchResults[i].querySelector(".name");
        }
        
        if (nameElement && nameElement.textContent.trim().toLowerCase() === symbol.toLowerCase()) {
          const tags = Array.from(searchResults[i].querySelectorAll(".tag"));
          for (let j = 0; j < tags.length; j++) {
            if (tags[j].textContent.toLowerCase() === exchange.toLowerCase()) {
              targetResult = searchResults[i];
              break;
            }
          }
          if (targetResult) break;
        }
      }
    }
    
    if (targetResult) {
      targetResult.dispatchEvent(new MouseEvent('mouseenter', {
        bubbles: true,
        cancelable: true,
        view: window
      }));
      await smartSleep(); // Wait for hover to take effect
      document.querySelector('.omnisearch .results-item button[aria-label="Sell"]').click();
    } else {
      throw new Error(`Symbol ${symbol} not found in ${exchange} exchange`);
    }
    await smartSleep(); // Wait for order window to open
  }

  async function selectOrderType(orderType) {
    if (orderType === "market") {
      document.querySelector(`.order-window input[type=radio][label='Market']`).click();
    } else if (orderType === "limit") {
      document.querySelector(`.order-window input[type=radio][label='Limit']`).click();
    } else if (orderType === "stoplossmarket") {
      document.querySelector(`.order-window input[type=radio][label='SL-M']`).click();
    } else if (orderType === "stoplosslimit") {
      document.querySelector(`.order-window input[type=radio][label='SL']`).click();
    }
    await smartSleep(); // Wait for results to load
  }

  async function clickSellButton() {
    document.querySelector(".order-window button[type=submit]").click();
    await smartSleep(); // Wait for action to happen
  }

  async function exitAllPositions(args = {}) {
    try {
      if (needsLogin()) {
        alert("Please login first.");
        return { success: false, message: "ExitAllPositions failed: " + "Login required" };
      }

      // Parse arguments with defaults
      const includeHoldings = args.include_holdings !== undefined ? args.include_holdings : true;
      const includeIntraday = args.include_intraday !== undefined ? args.include_intraday : true;

      console.log(`ExitAllPositions called with: include_holdings=${includeHoldings}, include_intraday=${includeIntraday}`);

      const allResults = [];
      let totalSuccessCount = 0;
      let totalFailureCount = 0;
      let totalSkippedCount = 0;

      // Process positions if include_intraday is true OR include_holdings is true
      if (includeIntraday || includeHoldings) {
        console.log("Processing positions...");
        
        // Navigate to positions page
        await navigate("positions");
        await smartSleep(); // Wait for positions table to load
        
        // Find all position rows
        const positionRows = Array.from(document.querySelectorAll('tbody tr'));
        
        if (positionRows.length > 0) {
          for (let i = 0; i < positionRows.length; i++) {
            const row = positionRows[i];
            
            try {
              // Get symbol and product type for this row
              const symbolElement = row.querySelector('.tradingsymbol');
              const productElement = row.querySelector('.product span');
              
              if (!symbolElement || !productElement) {
                console.warn(`Row ${i + 1}: Missing symbol or product type elements`);
                continue;
              }
              
              const symbol = symbolElement.textContent.trim();
              const productType = productElement.textContent.trim();
              
              console.log(`Processing position ${i + 1}/${positionRows.length}: ${symbol} (${productType})`);
              
              // Determine which positions to process based on arguments
              let shouldProcess = false;
              
              if (productType === 'MIS' && includeIntraday) {
                shouldProcess = true;
                console.log(`Including MIS position: ${symbol}`);
              } else if (productType === 'CNC' && includeHoldings) {
                shouldProcess = true;
                console.log(`Including CNC position: ${symbol}`);
              } else {
                console.log(`Skipping ${productType} position: ${symbol} (not included in filter)`);
                totalSkippedCount++;
                continue;
              }
              
              // Find the menu column for this row
              const menuColumn = row.querySelector('.col-menu');
              if (!menuColumn) {
                console.warn(`Row ${i + 1}: Menu column not found`);
                totalFailureCount++;
                continue;
              }
              
              // Trigger hover event on the menu column
              menuColumn.dispatchEvent(new MouseEvent('mouseenter', {
                bubbles: true,
                cancelable: true,
                view: window
              }));
              
              await smartSleep(); // Wait for hover menu to appear
              
              // Click on the menu button (three dots)
              const menuButton = document.querySelector('.table-menu-button.icon.icon-more-vertical');
              if (menuButton) {
                menuButton.click();
                await smartSleep(); // Wait for dropdown to fully open
              }
              
              // Click on the Exit button from the dropdown menu
              const exitButton = document.querySelector('.table-menu-toolbar a[data-label="Exit"]');
              if (exitButton) {
                exitButton.click();
                await smartSleep(); // Wait for exit modal to open
                
                // Handle the exit modal - click the submit button
                const submitButton = document.querySelector('.order-window button[type=submit]');
                if (submitButton) {
                  submitButton.click();
                  await smartSleep(); // Wait for order to be placed
                  
                  // Check for toast notification
                  try {
                    const toastResult = await checkToast();
                    allResults.push({ 
                      symbol, 
                      productType, 
                      source: 'positions',
                      success: true, 
                      message: `Successfully exited position for ${symbol} (${productType})`,
                      orderId: toastResult.orderId 
                    });
                    totalSuccessCount++;
                  } catch (toastError) {
                    allResults.push({ 
                      symbol, 
                      productType, 
                      source: 'positions',
                      success: false, 
                      message: `Failed to exit position for ${symbol} (${productType}): ${toastError.message}` 
                    });
                    totalFailureCount++;
                  }
                } else {
                  throw new Error("Submit button not found in exit modal");
                }
              } else {
                throw new Error("Exit button not found in position menu");
              }
              
              // Clear any toast notifications before processing next position
              await clearToast();
              
            } catch (rowError) {
              console.error(`Error processing position row ${i + 1}:`, rowError);
              const symbolElement = row.querySelector('.tradingsymbol');
              const productElement = row.querySelector('.product span');
              const symbol = symbolElement ? symbolElement.textContent.trim() : 'Unknown';
              const productType = productElement ? productElement.textContent.trim() : 'Unknown';
              
              allResults.push({ 
                symbol, 
                productType, 
                source: 'positions',
                success: false, 
                message: `Failed to exit position for ${symbol} (${productType}): ${rowError.message}` 
              });
              totalFailureCount++;
              
              // Clear any toast notifications before continuing
              await clearToast();
            }
          }
        } else {
          console.log("No positions found to exit");
        }
      }

      // Process holdings if include_holdings is true
      if (includeHoldings) {
        console.log("Processing holdings...");
        
        // Navigate to holdings page
        await navigate("holdings");
        await smartSleep(); // Wait for holdings table to load
        
        // Find all holding rows
        const holdingRows = Array.from(document.querySelectorAll('tbody tr'));
        
        if (holdingRows.length > 0) {
          for (let i = 0; i < holdingRows.length; i++) {
            const row = holdingRows[i];
            
            try {
              // Get symbol for this row
              const symbolElement = row.querySelector('.instrument a span');
              
              if (!symbolElement) {
                console.warn(`Holding row ${i + 1}: Missing symbol element`);
                continue;
              }
              
              const symbol = symbolElement.textContent.trim();
              
              console.log(`Processing holding ${i + 1}/${holdingRows.length}: ${symbol}`);
              
              // Find the menu column for this row
              const menuColumn = row.querySelector('.col-menu');
              if (!menuColumn) {
                console.warn(`Holding row ${i + 1}: Menu column not found`);
                totalFailureCount++;
                continue;
              }
              
              // Trigger hover event on the menu column
              menuColumn.dispatchEvent(new MouseEvent('mouseenter', {
                bubbles: true,
                cancelable: true,
                view: window
              }));
              
              await smartSleep(); // Wait for hover menu to appear
              
              // Click on the menu button (three dots)
              const menuButton = document.querySelector('.table-menu-button.icon.icon-more-vertical');
              if (menuButton) {
                menuButton.click();
                await smartSleep(); // Wait for dropdown to fully open
              }
              
              // Click on the Exit button from the dropdown menu
              const exitButton = document.querySelector('.table-menu-toolbar a[data-label="Exit"]');
              if (exitButton) {
                exitButton.click();
                await smartSleep(); // Wait for exit modal to open
                
                // Handle the exit modal - click the submit button
                const submitButton = document.querySelector('.order-window button[type=submit]');
                if (submitButton) {
                  submitButton.click();
                  await smartSleep(); // Wait for order to be placed
                  
                  // Check for toast notification
                  try {
                    const toastResult = await checkToast();
                    allResults.push({ 
                      symbol, 
                      productType: 'HOLDING', 
                      source: 'holdings',
                      success: true, 
                      message: `Successfully exited holding for ${symbol}`,
                      orderId: toastResult.orderId 
                    });
                    totalSuccessCount++;
                  } catch (toastError) {
                    allResults.push({ 
                      symbol, 
                      productType: 'HOLDING', 
                      source: 'holdings',
                      success: false, 
                      message: `Failed to exit holding for ${symbol}: ${toastError.message}` 
                    });
                    totalFailureCount++;
                  }
                } else {
                  throw new Error("Submit button not found in exit modal");
                }
              } else {
                throw new Error("Exit button not found in holdings menu");
              }
              
              // Clear any toast notifications before processing next holding
              await clearToast();
              
            } catch (rowError) {
              console.error(`Error processing holding row ${i + 1}:`, rowError);
              const symbolElement = row.querySelector('.instrument a span');
              const symbol = symbolElement ? symbolElement.textContent.trim() : 'Unknown';
              
              allResults.push({ 
                symbol, 
                productType: 'HOLDING', 
                source: 'holdings',
                success: false, 
                message: `Failed to exit holding for ${symbol}: ${rowError.message}` 
              });
              totalFailureCount++;
              
              // Clear any toast notifications before continuing
              await clearToast();
            }
          }
        } else {
          console.log("No holdings found to exit");
        }
      }

      const summary = `ExitAllPositions completed: ${totalSuccessCount} successful, ${totalFailureCount} failed, ${totalSkippedCount} skipped`;
      console.log(summary);
      
      return { 
        success: totalFailureCount === 0, 
        message: summary,
        details: allResults,
        stats: {
          successful: totalSuccessCount,
          failed: totalFailureCount,
          skipped: totalSkippedCount
        }
      };
      
    } catch (e) {
      return { success: false, message: "ExitAllPositions failed: " + e.message };
    }
  }

  /**
  * Get Total P&L from the positions page
  * @returns {Promise<number>} The total P&L value from positions
  */
  async function getTotalPnLFromPositions() {
    try {
      // Navigate to positions page
      await navigate("positions");
      await smartSleep(); // Wait for positions table to load
      
      // Look for the Total P&L in the table footer
      const totalPnLElement = document.querySelector('tfoot tr td:nth-child(6) div');
      
      if (!totalPnLElement) {
        // Try alternative selector if the first one doesn't work
        const alternativeElement = document.querySelector('tfoot .text-red, tfoot .text-green');
        if (!alternativeElement) {
          throw new Error('Could not find Total P&L element in positions table footer');
        }
        
        const totalPnLText = alternativeElement.textContent.trim();
        const totalPnLValue = parseFloat(totalPnLText.replace(/[₹,]/g, '').replace(/[^\d.-]/g, ''));
        
        if (isNaN(totalPnLValue)) {
          throw new Error(`Could not parse Total P&L value: ${totalPnLText}`);
        }
        
        return totalPnLValue;
      }
      
      const totalPnLText = totalPnLElement.textContent.trim();
      const totalPnLValue = parseFloat(totalPnLText.replace(/[₹,]/g, '').replace(/[^\d.-]/g, ''));
      
      if (isNaN(totalPnLValue)) {
        throw new Error(`Could not parse Total P&L value: ${totalPnLText}`);
      }
      
      return totalPnLValue;
    } catch (e) {
      console.error('Error getting Total P&L from positions:', e);
      throw new Error(`Failed to get Total P&L from positions: ${e.message}`);
    }
  }

  /**
  * Get open position P&L directly from positions page (optimized for monitoring)
  * @returns {Promise<number>} The open position P&L value
  */
  async function getOpenPositionPnL() {
    try {
      return await getTotalPnLFromPositions();
    } catch (e) {
      console.error('Error getting open position PnL:', e);
      throw new Error(`Failed to get open position PnL: ${e.message}`);
    }
  }

  /**
  * Get portfolio statistics from the holdings page
  * @returns {Promise<Object>} Portfolio statistics including current value, PnL values, and percentages
  */
  async function getPortfolioPnL() {
    try {
      // Navigate to holdings page to get current portfolio statistics
      await navigate("holdings");
      await smartSleep(); // Wait for holdings table to load
      
      // Extract portfolio statistics from the stats section
      const stats = {};
      
      // Get current value (holding_current_value)
      const currentValueElement = document.querySelector('.stats .three.columns:nth-child(2) .value');
      if (currentValueElement) {
        const currentValueText = currentValueElement.textContent.trim();
        stats.holding_current_value = parseFloat(currentValueText.replace(/[₹,]/g, ''));
      }
      
      // Get day's P&L value (holding_day's_PnL_value)
      const dayPnLElement = document.querySelector('.stats .three.columns.pnl:nth-child(3) .value');
      if (dayPnLElement) {
        const dayPnLText = dayPnLElement.textContent.trim();
        // Remove any percentage text and extract just the number
        const dayPnLValue = parseFloat(dayPnLText.replace(/[₹,]/g, '').replace(/[^\d.-]/g, ''));
        stats.holding_days_PnL_value = dayPnLValue;
      }
      
      // Get total P&L value (holding_total_PnL_value)
      const totalPnLElement = document.querySelector('.stats .three.columns.pnl:nth-child(4) .value');
      if (totalPnLElement) {
        const totalPnLText = totalPnLElement.textContent.trim();
        const totalPnLValue = parseFloat(totalPnLText.replace(/[₹,]/g, ''));
        stats.holding_total_PnL_value = totalPnLValue;
      }
      
      // Get total P&L percentage (holding_total_PnL_%)
      const totalPnLPercentElement = document.querySelector('.stats .three.columns.pnl:nth-child(4) .pnl-percentage');
      if (totalPnLPercentElement) {
        const totalPnLPercentText = totalPnLPercentElement.textContent.trim();
        // Extract percentage value (remove + or - sign and % symbol)
        const totalPnLPercent = parseFloat(totalPnLPercentText.replace(/[+%]/g, ''));
        stats.holding_total_PnL_percent = totalPnLPercent;
      }
      
      // Get open position PnL from positions page
      try {
        stats.open_position_PnL = await getTotalPnLFromPositions();
      } catch (positionsError) {
        console.warn('Failed to get open position PnL from positions page, using holdings total P&L as fallback:', positionsError.message);
        stats.open_position_PnL = stats.holding_total_PnL_value || 0;
      }
      
      // Validate that we got at least some data
      if (Object.keys(stats).length === 0) {
        throw new Error('Could not extract any portfolio statistics from the holdings page');
      }
      
      return stats;
    } catch (e) {
      console.error('Error getting portfolio PnL:', e);
      throw new Error(`Failed to get portfolio PnL: ${e.message}`);
    }
  }

  /**
  * Get the current stock price for a given symbol
  * @param {string} symbol - The stock symbol to monitor
  * @param {string} exchange - The exchange (default: NSE)
  * @returns {Promise<number>} The current stock price
  */
  async function getStockPrice(symbol, exchange = 'NSE') {
    try {
      // Navigate to dashboard to access search functionality
      await navigate("dashboard");
      await smartSleep(); // Wait for dashboard to load
      
      // For indices, determine the appropriate exchange
      const effectiveExchange = getExchangeForSymbol(symbol, exchange);
      
      // Search for the symbol
      await searchSymbol(symbol);
      
      // Wait for search results to appear
      await smartSleep();
      
      // Look for the price in search results
      const searchResults = Array.from(document.querySelectorAll('.omnisearch .results-item'));
      if (searchResults.length === 0) {
        throw new Error('No search results found for symbol: ' + symbol);
      }
      
      // Find the matching symbol with correct exchange
      let targetResult = null;
      
      if (isIndex(symbol)) {
        // Use enhanced index finding function
        targetResult = findIndexInSearchResults(symbol, searchResults);
      } else {
        // For regular stocks, use the existing logic
        for (const result of searchResults) {
          // Try multiple selectors to find the name element
          let nameElement = result.querySelector(".name span");
          if (!nameElement) {
            nameElement = result.querySelector(".name");
          }
          const tags = Array.from(result.querySelectorAll(".tag"));
          
          if (nameElement && nameElement.textContent.trim().toLowerCase() === symbol.toLowerCase()) {
            // For regular stocks, check if exchange matches
            const hasMatchingExchange = tags.some(tag => 
              tag.textContent.toLowerCase() === effectiveExchange.toLowerCase()
            );
            
            if (hasMatchingExchange) {
              targetResult = result;
              break;
            }
          }
        }
      }
      
      if (!targetResult) {
        if (isIndex(symbol)) {
          throw new Error(`Index ${symbol} not found in search results`);
        } else {
          throw new Error(`Symbol ${symbol} not found in ${effectiveExchange} exchange`);
        }
      }
      
      // Look for price information in the search result
      const priceSelectors = [
        '.price',
        '.last-price',
        '.ltp',
        '[data-label="LTP"]',
        '.value'
      ];
      
      let priceElement = null;
      for (const selector of priceSelectors) {
        priceElement = targetResult.querySelector(selector);
        if (priceElement) break;
      }
      
      if (!priceElement) {
        // If no price found in search result, try to get it from the main dashboard
        // Look for pinned instruments or watchlist
        const dashboardPriceSelectors = [
          '.pinned-instruments .instrument-widget .last-price',
          '.watchlist .instrument .price',
          '.market-watch .instrument .ltp',
          '[data-symbol="' + symbol + '"] .price'
        ];
        
        for (const selector of dashboardPriceSelectors) {
          priceElement = document.querySelector(selector);
          if (priceElement) break;
        }
      }
      
      if (!priceElement) {
        throw new Error(`Could not find price element for ${symbol}`);
      }
      
      const priceText = priceElement.textContent.trim();
      const priceValue = parseFloat(priceText.replace(/[₹,]/g, ''));
      
      if (isNaN(priceValue)) {
        throw new Error(`Could not parse price value: ${priceText}`);
      }
      
      return priceValue;
    } catch (e) {
      console.error('Error getting stock price:', e);
      throw new Error(`Failed to get stock price for ${symbol}: ${e.message}`);
    }
  }

  /**
  * Find a symbol in the watchlist/market watch section
  * @param {string} symbol - The stock symbol to find
  * @param {string} exchange - The exchange (default: NSE)
  * @returns {Promise<Element|null>} The watchlist item element or null if not found
  */
  async function findSymbolInWatchlist(symbol, exchange = 'NSE') {
    try {
      // Navigate to dashboard to access watchlist
      await navigate("dashboard");
      await smartSleep(); // Wait for dashboard to load
      
      // For indices, determine the appropriate exchange
      const effectiveExchange = getExchangeForSymbol(symbol, exchange);
      
      // Look for the symbol in the watchlist/market watch section
      // Based on the HTML structure provided, symbols are in .item-wrapper elements
      const watchlistItems = Array.from(document.querySelectorAll('.item-wrapper.draggable-item'));
      
      for (const item of watchlistItems) {
        const symbolElement = item.querySelector('.symbol .name');
        console.log("symbolElement =>",symbolElement)
        const tags = Array.from(item.querySelectorAll('.tag'));
        console.log("tags =>",tags)
        if (symbolElement) {
          const watchlistSymbol = symbolElement.textContent.trim();
          
          // For indices, handle different naming patterns
          if (isIndex(symbol)) {
            const normalizedSearchSymbol = symbol.toUpperCase().replace(/\s+/g, '');
            const normalizedWatchlistSymbol = watchlistSymbol.toUpperCase().replace(/\s+/g, '');
            
            if (normalizedWatchlistSymbol.includes(normalizedSearchSymbol) || 
                normalizedSearchSymbol.includes(normalizedWatchlistSymbol)) {
              console.log(`Found index in watchlist: ${watchlistSymbol} matches ${symbol}`);
              return item;
            }
          } else {
            // For regular stocks, exact match
            if (watchlistSymbol.toLowerCase() === symbol.toLowerCase()) {
              // For regular stocks, check if exchange matches (if specified)
              console.log("effectiveExchange.toLowerCase() =>",effectiveExchange.toLowerCase())
              if (exchange) {
                
                const hasMatchingExchange = tags.some(tag => 
                  tag.textContent.toLowerCase() === effectiveExchange.toLowerCase()
                );
                
                // If no exchange tag is found, assume it's the default exchange (NSE)
                if ((tags.length === 0 || (tags.length === 1 && tags[0].textContent.toLowerCase() === 'event')) && effectiveExchange.toLowerCase() === 'nse' ) {
                  return item;
                }
                
                if (hasMatchingExchange) {
                  return item;
                }
              } else {
                // If no exchange specified, return the first match
                return item;
              }
            }
          }
        }
      }
      
      return null; // Symbol not found in watchlist
    } catch (e) {
      console.error('Error finding symbol in watchlist:', e);
      throw new Error(`Failed to find symbol ${symbol} in watchlist: ${e.message}`);
    }
  }

  /**
  * Get the current stock price for a given symbol from watchlist
  * @param {string} symbol - The stock symbol to monitor
  * @param {string} exchange - The exchange (default: NSE)
  * @returns {Promise<number>} The current stock price
  */
  async function getStockPriceFromWatchlist(symbol, exchange = 'NSE') {
    try {
      // For indices, determine the appropriate exchange
      const effectiveExchange = getExchangeForSymbol(symbol, exchange);
      const watchlistItem = await findSymbolInWatchlist(symbol, effectiveExchange);
      
      if (!watchlistItem) {
        // If symbol not found in watchlist, fall back to search method
        console.log(`Symbol ${symbol} not found in watchlist, falling back to search method`);
        return await getStockPrice(symbol, effectiveExchange);
      }
      
      // Find the price element within the watchlist item
      const priceElement = watchlistItem.querySelector('.price .last-price');
      
      if (!priceElement) {
        throw new Error(`Could not find price element for ${symbol} in watchlist`);
      }
      
      const priceText = priceElement.textContent.trim();
      const priceValue = parseFloat(priceText.replace(/[₹,]/g, ''));
      
      if (isNaN(priceValue)) {
        throw new Error(`Could not parse price value: ${priceText}`);
      }
      
      return priceValue;
    } catch (e) {
      console.error('Error getting stock price from watchlist:', e);
      throw new Error(`Failed to get stock price for ${symbol} from watchlist: ${e.message}`);
    }
  }

  /**
  * Monitor a condition and execute an action when triggered
  * @param {Object} condition - The condition to monitor
  * @param {Object} onTrigger - The action to execute when condition is met
  * @returns {Promise<Object>} Result of the monitoring and action execution
  */
  async function monitorConditionThenAct(condition, onTrigger) {
    try {
      if (needsLogin()) {
        alert("Please login first.");
        return { success: false, message: "MonitorConditionThenAct failed: " + "Login required" };
      }

      console.log("Starting condition monitoring:", condition);
      console.log("Action to execute on trigger:", onTrigger);

      // Validate condition structure
      if (!condition.observe || !condition.operator || condition.value === undefined) {
        throw new Error("Invalid condition structure. Required: observe, operator, value");
      }

      // Validate onTrigger structure
      if (!onTrigger.action) {
        throw new Error("Invalid onTrigger structure. Required: action");
      }

          // Check if the action exists (case-insensitive)
    const actionFunction = findActionCaseInsensitive(onTrigger.action);
    if (!actionFunction) {
      throw new Error(`Action '${onTrigger.action}' not found in available actions`);
    }

    // Load timeout settings from shared config
    const timeoutSettings = await getTimeoutSettings();
    
    // Extract timeout settings from condition arguments or use config defaults
    const maxDurationMinutes = condition.timeout_minutes || timeoutSettings.monitor_max_duration_minutes;
    const pollingIntervalSeconds = condition.polling_interval_seconds || timeoutSettings.monitor_polling_interval_seconds;
    const maxRetryAttempts = condition.max_retry_attempts || timeoutSettings.monitor_max_retry_attempts;
    const actionTimeoutSeconds = condition.action_timeout_seconds || timeoutSettings.action_execution_timeout_seconds;
    const observerTimeoutMinutes = condition.observer_timeout_minutes || timeoutSettings.price_monitor_observer_timeout_minutes;

    console.log(`🔍 Starting monitoring with config: max=${maxDurationMinutes}min, polling=${pollingIntervalSeconds}s, retries=${maxRetryAttempts}, action_timeout=${actionTimeoutSeconds}s`);

        // For price monitoring, we can use DOM observation for real-time updates
    if (condition.observe === 'price' && condition.symbol) {
      return new Promise(async (resolve) => {
        let monitoringStartTime = Date.now();
        let observerTimeoutId = null;
        let currentObserver = null;

        const cleanupAndResolve = (result) => {
          if (observerTimeoutId) clearTimeout(observerTimeoutId);
          if (currentObserver) currentObserver.disconnect();
          const duration = (Date.now() - monitoringStartTime) / 1000;
          console.log(`🏁 Price monitoring ended after ${duration}s:`, result.message);
          resolve(result);
        };

        // Set up overall monitoring timeout from config
        observerTimeoutId = setTimeout(() => {
          console.log(`⏰ Price monitoring timeout after ${observerTimeoutMinutes} minutes`);
          cleanupAndResolve({
            success: false,
            message: `Price monitoring timed out after ${observerTimeoutMinutes} minutes without condition being met`,
            error: 'MONITORING_TIMEOUT',
            monitoringDuration: Date.now() - monitoringStartTime
          });
        }, observerTimeoutMinutes * 60 * 1000);

        try {
            // For indices, determine the appropriate exchange
            const effectiveExchange = getExchangeForSymbol(condition.symbol, condition.exchange || 'NSE');
            
            // First try to find the symbol in the watchlist
            const watchlistItem = await findSymbolInWatchlist(condition.symbol, effectiveExchange);
            
            let priceElement = null;
            let monitoringSource = '';
            
            if (watchlistItem) {
              // Use watchlist for monitoring
              priceElement = watchlistItem.querySelector('.price .last-price');
              monitoringSource = 'watchlist';
              console.log(`Found ${condition.symbol} in watchlist, using for monitoring`);
            } else {
              // Fall back to search method
              console.log(`Symbol ${condition.symbol} not found in watchlist, using search method`);
              await navigate("dashboard");
              await smartSleep();
              await searchSymbol(condition.symbol);
              await smartSleep();
              
              // Find the search result to click on
              const searchResults = Array.from(document.querySelectorAll('.omnisearch .results-item'));
              console.log(`Found ${searchResults.length} search results`);
              
              let targetResult = null;
              
              if (isIndex(condition.symbol)) {
                // Use enhanced index finding function
                targetResult = findIndexInSearchResults(condition.symbol, searchResults);
              } else {
                // For regular stocks, use the existing logic
                for (const result of searchResults) {
                  // Try multiple selectors to find the name element
                  let nameElement = result.querySelector(".name span");
                  if (!nameElement) {
                    nameElement = result.querySelector(".name");
                  }
                  
                  if (nameElement) {
                    const symbolName = nameElement.textContent.trim();
                    const tags = Array.from(result.querySelectorAll(".tag"));
                    console.log(`Checking result: ${symbolName}, tags: ${tags.map(t => t.textContent).join(', ')}`);
                    
                    if (symbolName.toLowerCase() === condition.symbol.toLowerCase()) {
                      const hasMatchingExchange = tags.some(tag => 
                        tag.textContent.toLowerCase() === effectiveExchange.toLowerCase()
                      );
                      
                      if (hasMatchingExchange) {
                        targetResult = result;
                        console.log(`Found matching result: ${symbolName} with tags: ${tags.map(t => t.textContent).join(', ')}`);
                        break;
                      }
                    }
                  }
                }
              }
              
              if (!targetResult) {
                // Provide more detailed error information
                const availableResults = searchResults.map(result => {
                  // Try multiple selectors to find the name element
                  let nameElement = result.querySelector(".name span");
                  if (!nameElement) {
                    nameElement = result.querySelector(".name");
                  }
                  const tags = Array.from(result.querySelectorAll(".tag"));
                  return {
                    name: nameElement ? nameElement.textContent.trim() : 'Unknown',
                    tags: tags.map(t => t.textContent).join(', ')
                  };
                });
                
                console.error('Available search results:', availableResults);
                console.error('Searching for symbol:', condition.symbol);
                console.error('Effective exchange:', effectiveExchange);
                console.error('Is index:', isIndex(condition.symbol));
                
                throw new Error(`Symbol ${condition.symbol} not found in ${effectiveExchange} exchange. Available results: ${availableResults.map(r => `${r.name}(${r.tags})`).join(', ')}`);
              }
              
              // Click on the search result to add it to watchlist
              console.log(`Clicking on search result for ${condition.symbol}`);
              targetResult.click();
              await smartSleep(2000); // Wait for symbol to be added to watchlist

              // Now try to find the symbol in watchlist
              let watchlistItem = await findSymbolInWatchlist(condition.symbol, effectiveExchange);
              
              // If not found immediately, wait a bit more and try again
              if (!watchlistItem) {
                console.log('Symbol not found in watchlist immediately, waiting and retrying...');
                await smartSleep(1000);
                watchlistItem = await findSymbolInWatchlist(condition.symbol, effectiveExchange);
              }
              
              if (watchlistItem) {
                // Find price element in watchlist item
                const priceSelectors = ['.price .last-price', '.price', '.last-price', '.ltp', '[data-label="LTP"]', '.value'];
                
                for (const selector of priceSelectors) {
                  priceElement = watchlistItem.querySelector(selector);
                  if (priceElement) {
                    console.log(`Found price element using selector: ${selector}`);
                    break;
                  }
                }
                
                monitoringSource = 'watchlist';
              } else {
                throw new Error(`Symbol ${condition.symbol} was not added to watchlist after clicking search result`);
              }
            }
            
            if (!priceElement) {
              throw new Error(`Could not find price element for ${condition.symbol}`);
            }
            
            console.log(`Starting real-time price monitoring for ${condition.symbol} at target: ${condition.value} using ${monitoringSource}`);

            // Initial price check: if already meets the condition, execute immediately without waiting for changes
            try {
              const priceTextInit = (priceElement?.textContent || '').trim();
              const initialPrice = parseFloat(priceTextInit.replace(/[₹,]/g, ''));
              if (Number.isFinite(initialPrice)) {
                console.log(`Initial price: ${initialPrice}, Target: ${condition.value}, Operator: ${condition.operator}`);
                let conditionMetInitial = false;
                switch (condition.operator) {
                  case 'gte': conditionMetInitial = initialPrice >= condition.value; break;
                  case 'gt':  conditionMetInitial = initialPrice >  condition.value; break;
                  case 'lte': conditionMetInitial = initialPrice <= condition.value; break;
                  case 'lt':  conditionMetInitial = initialPrice <  condition.value; break;
                  case 'eq':  conditionMetInitial = initialPrice === condition.value; break;
                  case 'ne':  conditionMetInitial = initialPrice !== condition.value; break;
                  default: throw new Error(`Unknown operator: ${condition.operator}`);
                }
                if (conditionMetInitial) {
                  console.log(`Initial condition satisfied. Executing action: ${onTrigger.action}`);
                  const actionTimeout = setTimeout(() => {
                    cleanupAndResolve({
                      success: false,
                      message: `Action '${onTrigger.action}' timed out after ${actionTimeoutSeconds} seconds`,
                      conditionValue: initialPrice,
                      error: 'ACTION_TIMEOUT'
                    });
                  }, actionTimeoutSeconds * 1000);

                  const runInNewTab = () => new Promise((resolveRun, rejectRun) => {
                    const payload = {
                      type: 'EXECUTE_ACTIONS',
                      actions: [{ action: onTrigger.action, arguments: onTrigger.arguments || {} }],
                      automationMode: 'background',
                      siteId: 'kiteByZerodha',
                      graphId: `monitor_${Date.now()}`
                    };
                    try {
                      chrome.runtime.sendMessage(payload, (resp) => {
                        if (chrome.runtime.lastError) {
                          rejectRun(new Error(chrome.runtime.lastError.message));
                          return;
                        }
                        resolveRun(resp);
                      });
                    } catch (err) {
                      rejectRun(err);
                    }
                  });

                  runInNewTab()
                    .then(actionResult => {
                      clearTimeout(actionTimeout);
                      cleanupAndResolve({
                        success: actionResult?.success !== false,
                        message: `Price condition initially satisfied. Action '${onTrigger.action}' executed.`,
                        conditionValue: initialPrice,
                        actionResult: actionResult
                      });
                    })
                    .catch(err => {
                      clearTimeout(actionTimeout);
                      cleanupAndResolve({
                        success: false,
                        message: `Action '${onTrigger.action}' failed: ${err.message}`,
                        conditionValue: initialPrice,
                        error: 'ACTION_FAILED'
                      });
                    });
                  return; // Skip setting up observer since we already executed
                }
              }
            } catch (e) {
              console.warn('Initial price check failed:', e?.message || e);
            }

            const observer = new MutationObserver((mutations) => {
              for (const mutation of mutations) {
                if (mutation.type === 'characterData' || mutation.type === 'childList') {
                  try {
                    const priceText = priceElement.textContent.trim();
                    const currentPrice = parseFloat(priceText.replace(/[₹,]/g, ''));
                    
                    if (isNaN(currentPrice)) {
                      console.warn(`Could not parse price: ${priceText}`);
                      return;
                    }
                    
                    console.log(`Current price: ${currentPrice}, Target: ${condition.value}, Operator: ${condition.operator}`);
                    
                    // Check condition
                    let conditionMet = false;
                    switch (condition.operator) {
                      case 'gte': conditionMet = currentPrice >= condition.value; break;
                      case 'gt': conditionMet = currentPrice > condition.value; break;
                      case 'lte': conditionMet = currentPrice <= condition.value; break;
                      case 'lt': conditionMet = currentPrice < condition.value; break;
                      case 'eq': conditionMet = currentPrice === condition.value; break;
                      case 'ne': conditionMet = currentPrice !== condition.value; break;
                      default: throw new Error(`Unknown operator: ${condition.operator}`);
                    }
                    
                    if (conditionMet) {
                      console.log(`Price condition met! Executing action: ${onTrigger.action}`);
                      
                      // Set timeout for action execution using config
                      const actionTimeout = setTimeout(() => {
                        cleanupAndResolve({
                          success: false,
                          message: `Action '${onTrigger.action}' timed out after ${actionTimeoutSeconds} seconds`,
                          conditionValue: currentPrice,
                          error: 'ACTION_TIMEOUT'
                        });
                      }, actionTimeoutSeconds * 1000);
                      
                      // Execute the action in a fresh background tab via background service
                      const runInNewTab = () => new Promise((resolveRun, rejectRun) => {
                        const payload = {
                          type: 'EXECUTE_ACTIONS',
                          actions: [{ action: onTrigger.action, arguments: onTrigger.arguments || {} }],
                          automationMode: 'background',
                          siteId: 'kiteByZerodha',
                          graphId: `monitor_${Date.now()}`
                        };
                        try {
                          chrome.runtime.sendMessage(payload, (resp) => {
                            if (chrome.runtime.lastError) {
                              rejectRun(new Error(chrome.runtime.lastError.message));
                              return;
                            }
                            resolveRun(resp);
                          });
                        } catch (err) {
                          rejectRun(err);
                        }
                      });

                      runInNewTab()
                        .then(actionResult => {
                          clearTimeout(actionTimeout);
                          cleanupAndResolve({
                            success: actionResult?.success !== false,
                            message: `Price condition monitoring completed. Action '${onTrigger.action}' executed.`,
                            conditionValue: currentPrice,
                            actionResult: actionResult
                          });
                        })
                        .catch(err => {
                          clearTimeout(actionTimeout);
                          cleanupAndResolve({
                            success: false,
                            message: `Action '${onTrigger.action}' failed: ${err.message}`,
                            conditionValue: currentPrice,
                            error: 'ACTION_FAILED'
                          });
                        });
                    }
                  } catch (error) {
                    console.error('Error processing price change:', error);
                  }
                }
              }
                      });
          
          currentObserver = observer;
          observer.observe(priceElement, {
            characterData: true,
            childList: true,
            subtree: true
          });
          
        } catch (error) {
          cleanupAndResolve({ success: false, message: `Price monitoring setup failed: ${error.message}` });
        }
        });
      } else {
        // For other observation types (like PnL), use polling approach with config-based timeouts
        return new Promise(async (resolve) => {
          let monitoringStartTime = Date.now();
          let attemptCount = 0;
          let consecutiveErrors = 0;

          const checkCondition = async () => {
            attemptCount++;
            
            // Check if we've exceeded the maximum monitoring duration
            const elapsedMinutes = (Date.now() - monitoringStartTime) / (1000 * 60);
            if (elapsedMinutes > maxDurationMinutes) {
              resolve({
                success: false,
                message: `Monitoring timed out after ${maxDurationMinutes} minutes (${attemptCount} attempts)`,
                error: 'MONITORING_TIMEOUT',
                attemptCount: attemptCount,
                monitoringDuration: Date.now() - monitoringStartTime
              });
              return;
            }
            
            // Check if we've exceeded the maximum retry attempts
            if (attemptCount > maxRetryAttempts) {
              resolve({
                success: false,
                message: `Monitoring stopped after ${maxRetryAttempts} attempts (${elapsedMinutes.toFixed(1)} minutes)`,
                error: 'MAX_ATTEMPTS_EXCEEDED',
                attemptCount: attemptCount,
                monitoringDuration: Date.now() - monitoringStartTime
              });
              return;
            }

            try {
              let currentValue = null;

              // Handle different observation types
              switch (condition.observe) {
                case 'open_position_PnL':
                  // Use optimized function to get open position PnL directly from positions page
                  currentValue = await getOpenPositionPnL();
                  break;
                case 'holding_current_value':
                  const currentValueStats = await getPortfolioPnL();
                  currentValue = currentValueStats.holding_current_value;
                  break;
                case 'holding_days_PnL_value':
                  const daysPnLStats = await getPortfolioPnL();
                  currentValue = daysPnLStats.holding_days_PnL_value;
                  break;
                case 'holding_total_PnL_value':
                  const totalPnLStats = await getPortfolioPnL();
                  currentValue = totalPnLStats.holding_total_PnL_value;
                  break;
                case 'holding_total_PnL_percent':
                  const totalPnLPercentStats = await getPortfolioPnL();
                  currentValue = totalPnLPercentStats.holding_total_PnL_percent;
                  break;
                case 'price':
                  if (!condition.symbol) {
                    throw new Error("Symbol required for price monitoring");
                  }
                  // Try watchlist first, then fall back to search method
                  try {
                    currentValue = await getStockPriceFromWatchlist(condition.symbol, condition.exchange);
                  } catch (watchlistError) {
                    console.log(`Watchlist monitoring failed, falling back to search: ${watchlistError.message}`);
                    currentValue = await getStockPrice(condition.symbol, condition.exchange);
                  }
                  break;
                default:
                  throw new Error(`Unknown observation type: ${condition.observe}`);
              }

              console.log(`📊 Attempt ${attemptCount}/${maxRetryAttempts}: Current value: ${currentValue}, Target: ${condition.value}, Operator: ${condition.operator} (${elapsedMinutes.toFixed(1)}min elapsed)`);

              // Reset consecutive error count on successful check
              consecutiveErrors = 0;

              // Check condition based on operator
              let conditionMet = false;
              switch (condition.operator) {
                case 'gte': // greater than or equal
                  conditionMet = currentValue >= condition.value;
                  break;
                case 'gt': // greater than
                  conditionMet = currentValue > condition.value;
                  break;
                case 'lte': // less than or equal
                  conditionMet = currentValue <= condition.value;
                  break;
                case 'lt': // less than
                  conditionMet = currentValue < condition.value;
                  break;
                case 'eq': // equal
                  conditionMet = currentValue === condition.value;
                  break;
                case 'ne': // not equal
                  conditionMet = currentValue !== condition.value;
                  break;
                default:
                  throw new Error(`Unknown operator: ${condition.operator}`);
              }

              if (conditionMet) {
                console.log(`Condition met after ${attemptCount} attempts! Executing action: ${onTrigger.action}`);
                
                // Execute the action (case-insensitive) with timeout
                const actionTimeout = setTimeout(() => {
                  resolve({
                    success: false,
                    message: `Action '${onTrigger.action}' timed out after ${actionTimeoutSeconds} seconds`,
                    conditionValue: currentValue,
                    attemptCount: attemptCount,
                    monitoringDuration: Date.now() - monitoringStartTime,
                    error: 'ACTION_TIMEOUT'
                  });
                }, actionTimeoutSeconds * 1000);

                try {
                  const actionResult = await actionFunction(onTrigger.arguments || {});
                  clearTimeout(actionTimeout);
                  
                  resolve({
                    success: true,
                    message: `Condition monitoring completed after ${attemptCount} attempts. Action '${onTrigger.action}' executed successfully.`,
                    conditionValue: currentValue,
                    actionResult: actionResult,
                    attemptCount: attemptCount,
                    monitoringDuration: Date.now() - monitoringStartTime
                  });
                } catch (actionError) {
                  clearTimeout(actionTimeout);
                  resolve({
                    success: false,
                    message: `Condition met but action execution failed: ${actionError.message}`,
                    conditionValue: currentValue,
                    actionError: actionError.message,
                    attemptCount: attemptCount,
                    monitoringDuration: Date.now() - monitoringStartTime
                  });
                }
              } else {
                // Condition not met, check again after a delay
                setTimeout(checkCondition, pollingIntervalSeconds * 1000);
              }
            } catch (error) {
              consecutiveErrors++;
              console.error(`❌ Error during condition check (attempt ${attemptCount}, consecutive errors: ${consecutiveErrors}):`, error);
              
              if (consecutiveErrors >= timeoutSettings.monitor_max_consecutive_errors) {
                resolve({
                  success: false,
                  message: `Monitoring stopped after ${timeoutSettings.monitor_max_consecutive_errors} consecutive errors. Last error: ${error.message}`,
                  error: 'TOO_MANY_CONSECUTIVE_ERRORS',
                  attemptCount: attemptCount,
                  monitoringDuration: Date.now() - monitoringStartTime,
                  lastError: error.message
                });
                return;
              }
              
              // Continue monitoring with longer delay after error using config
              setTimeout(checkCondition, timeoutSettings.monitor_error_interval_seconds * 1000);
            }
          };

          // Start the monitoring
          console.log(`🚀 Starting polling-based monitoring: max=${maxDurationMinutes}min, polling=${pollingIntervalSeconds}s, max_attempts=${maxRetryAttempts}`);
          checkCondition();
        });
      }

    } catch (e) {
      return { success: false, message: "MonitorConditionThenAct failed: " + e.message };
    }
  }

  /**
   * Check if a symbol is an index (like NIFTY 50, SENSEX, etc.)
   * @param {string} symbol - The symbol to check
   * @returns {boolean} True if the symbol is an index
   */
  function isIndex(symbol) {
    if (!symbol) return false;
    
    const symbolUpper = symbol.toUpperCase();
    const indexKeywords = [
      'NIFTY', 'SENSEX', 'BANKEX', 'ALLCAP', 'INDEX', 'INDICES'
    ];
    
    // Check if symbol contains index keywords
    return indexKeywords.some(keyword => symbolUpper.includes(keyword));
  }

  /**
   * Get the appropriate exchange for a symbol
   * @param {string} symbol - The symbol
   * @param {string} defaultExchange - Default exchange to use
   * @returns {string} The appropriate exchange for the symbol
   */
  function getExchangeForSymbol(symbol, defaultExchange = 'NSE') {
    if (isIndex(symbol)) {
      // For indices, determine exchange based on symbol
      const symbolUpper = symbol.toUpperCase();
      if (symbolUpper.includes('SENSEX') || symbolUpper.includes('BANKEX') || symbolUpper.includes('ALLCAP')) {
        return 'BSE';
      } else {
        return 'NSE'; // NIFTY indices are on NSE
      }
    }
    return defaultExchange;
  }

  const siteActions = {
    async BUY(args) {
      try {
        if (needsLogin()) {
          alert("Please login first.");
          // TODO: Add the logic to fetch credentials from the user and attemp login flow
          return { success: false, message: "BUY failed: " + "Login required" };
        }
    
        // Normalize arguments with required fields
        const normalizedArgs = normalizeArguments(args, ['SYMBOL', 'QUANTITY', 'PRODUCT_TYPE']);
        
        // Validate required arguments
        if (!normalizedArgs.SYMBOL) {
          return { success: false, message: "BUY failed: Symbol is required" };
        }
        if (!normalizedArgs.QUANTITY) {
          return { success: false, message: "BUY failed: Quantity is required" };
        }
        if (!normalizedArgs.PRODUCT_TYPE) {
          return { success: false, message: "BUY failed: Product type is required" };
        }
        
        await clearToast();
        console.log("BUY details set", normalizedArgs.PRODUCT_TYPE);
        
        // For indices, determine the appropriate exchange
        const effectiveExchange = getExchangeForSymbol(normalizedArgs.SYMBOL, normalizedArgs.EXCHANGE ?? 'NSE');
        
        await searchSymbol(normalizedArgs.SYMBOL);
        await openBuyModal(normalizedArgs.SYMBOL, effectiveExchange);
        await selectRegularTab();
        await selectOrderType("market");
        await setOrderDetails(normalizedArgs.QUANTITY, normalizedArgs.PRODUCT_TYPE, 'BUY');
        await clickBuyButton();
        const result = await checkToast();
        console.log(`BUY executed for ${normalizedArgs.QUANTITY} of ${normalizedArgs.SYMBOL} in ${effectiveExchange} at ${normalizedArgs.PRICE} for ${normalizedArgs.PRODUCT_TYPE}`);
        return { success: true, message: `BUY executed for ${normalizedArgs.QUANTITY} of ${normalizedArgs.SYMBOL} in ${effectiveExchange} at ${normalizedArgs.PRICE} for ${normalizedArgs.PRODUCT_TYPE}`, orderId: result.orderId ,status: result.status,message: result.message};
      } catch (e) {
        return { success: false, message: "BUY failed: " + e.message };
      }
    },
    async SELL(args) {
      try {
        if (needsLogin()) {
          alert("Please login first.");
          return { success: false, message: "SELL failed: " + "Login required" };
        }
    
        // Normalize arguments with required fields
        const normalizedArgs = normalizeArguments(args, ['SYMBOL', 'QUANTITY', 'PRODUCT_TYPE']);
        
        // Validate required arguments
        if (!normalizedArgs.SYMBOL) {
          return { success: false, message: "SELL failed: Symbol is required" };
        }
        if (!normalizedArgs.QUANTITY) {
          return { success: false, message: "SELL failed: Quantity is required" };
        }
        if (!normalizedArgs.PRODUCT_TYPE) {
          return { success: false, message: "SELL failed: Product type is required" };
        }
        
        await clearToast();
        
        // For indices, determine the appropriate exchange
        const effectiveExchange = getExchangeForSymbol(normalizedArgs.SYMBOL, normalizedArgs.EXCHANGE ?? 'NSE');
        
        await searchSymbol(normalizedArgs.SYMBOL);
        await openSellModal(normalizedArgs.SYMBOL, effectiveExchange);
        await selectRegularTab();
        await selectOrderType("market");
        await setOrderDetails(normalizedArgs.QUANTITY, normalizedArgs.PRODUCT_TYPE, 'SELL');
        console.log("SELL details set", normalizedArgs.PRODUCT_TYPE);
        await clickSellButton();
        const result = await checkToast();
        console.log(`SELL executed for ${normalizedArgs.QUANTITY} of ${normalizedArgs.SYMBOL} in ${effectiveExchange} at ${normalizedArgs.PRICE} for ${normalizedArgs.PRODUCT_TYPE}`);
        return { success: true, message: `SELL executed for ${normalizedArgs.QUANTITY} of ${normalizedArgs.SYMBOL} in ${effectiveExchange} at ${normalizedArgs.PRICE} for ${normalizedArgs.PRODUCT_TYPE}`, orderId: result.orderId,status: result.status,message: result.message};
      } catch (e) {
        return { success: false, message: "SELL failed: " + e.message };
      }
    },
    async PlaceBuyLimitOrder(args) {
      try {
        if (needsLogin()) {
          alert("Please login first.");
          return { success: false, message: "PlaceBuyLimitOrder failed: " + "Login required" };
        }
    
        // Normalize arguments with required fields
        const normalizedArgs = normalizeArguments(args, ['SYMBOL', 'QUANTITY', 'PRODUCT_TYPE', 'PRICE']);
        
        // Validate required arguments
        if (!normalizedArgs.SYMBOL) {
          return { success: false, message: "PlaceBuyLimitOrder failed: Symbol is required" };
        }
        if (!normalizedArgs.QUANTITY) {
          return { success: false, message: "PlaceBuyLimitOrder failed: Quantity is required" };
        }
        if (!normalizedArgs.PRODUCT_TYPE) {
          return { success: false, message: "PlaceBuyLimitOrder failed: Product type is required" };
        }
        if (!normalizedArgs.PRICE) {
          return { success: false, message: "PlaceBuyLimitOrder failed: Price is required" };
        }
        
        await clearToast();
        console.log("PlaceBuyLimitOrder details set", normalizedArgs.PRODUCT_TYPE);
        
        // For indices, determine the appropriate exchange
        const effectiveExchange = getExchangeForSymbol(normalizedArgs.SYMBOL, normalizedArgs.EXCHANGE ?? 'NSE');
        
        await searchSymbol(normalizedArgs.SYMBOL);
        await openBuyModal(normalizedArgs.SYMBOL, effectiveExchange);
        await selectRegularTab();
        await selectOrderType("limit");
        await setOrderDetails(normalizedArgs.QUANTITY, normalizedArgs.PRODUCT_TYPE, 'BUY', normalizedArgs.PRICE);
        await clickBuyButton();
        const result = await checkToast();
        console.log(`PlaceBuyLimitOrder executed for ${normalizedArgs.QUANTITY} of ${normalizedArgs.SYMBOL} in ${effectiveExchange} at ${normalizedArgs.PRICE} for ${normalizedArgs.PRODUCT_TYPE}`);
        return { success: true, message: `PlaceBuyLimitOrder executed for ${normalizedArgs.QUANTITY} of ${normalizedArgs.SYMBOL} in ${effectiveExchange} at ${normalizedArgs.PRICE} for ${normalizedArgs.PRODUCT_TYPE}`, orderId: result.orderId ,status: result.status,message: result.message};
      } catch (e) {
        return { success: false, message: "PlaceBuyLimitOrder failed: " + e.message };
      }
    },
    async PlaceSellLimitOrder(args) {
      try {
        if (needsLogin()) {
          alert("Please login first.");
          return { success: false, message: "PlaceSellLimitOrder failed: " + "Login required" };
        }
    
        // Normalize arguments with required fields
        const normalizedArgs = normalizeArguments(args, ['SYMBOL', 'QUANTITY', 'PRODUCT_TYPE', 'PRICE']);
        
        // Validate required arguments
        if (!normalizedArgs.SYMBOL) {
          return { success: false, message: "PlaceSellLimitOrder failed: Symbol is required" };
        }
        if (!normalizedArgs.QUANTITY) {
          return { success: false, message: "PlaceSellLimitOrder failed: Quantity is required" };
        }
        if (!normalizedArgs.PRODUCT_TYPE) {
          return { success: false, message: "PlaceSellLimitOrder failed: Product type is required" };
        }
        if (!normalizedArgs.PRICE) {
          return { success: false, message: "PlaceSellLimitOrder failed: Price is required" };
        }
        
        await clearToast();
        console.log("PlaceSellLimitOrder details set", normalizedArgs.PRODUCT_TYPE);
        
        // For indices, determine the appropriate exchange
        const effectiveExchange = getExchangeForSymbol(normalizedArgs.SYMBOL, normalizedArgs.EXCHANGE ?? 'NSE');
        
        await searchSymbol(normalizedArgs.SYMBOL);
        await openSellModal(normalizedArgs.SYMBOL, effectiveExchange);
        await selectRegularTab();
        await selectOrderType("limit");
        await setOrderDetails(normalizedArgs.QUANTITY, normalizedArgs.PRODUCT_TYPE, 'SELL', normalizedArgs.PRICE);
        console.log("PlaceSellLimitOrder details set", normalizedArgs.PRODUCT_TYPE);
        await clickSellButton();
        const result = await checkToast();
        console.log(`PlaceSellLimitOrder executed for ${normalizedArgs.QUANTITY} of ${normalizedArgs.SYMBOL} in ${effectiveExchange} at ${normalizedArgs.PRICE} for ${normalizedArgs.PRODUCT_TYPE}`);
        return { success: true, message: `PlaceSellLimitOrder executed for ${normalizedArgs.QUANTITY} of ${normalizedArgs.SYMBOL} in ${effectiveExchange} at ${normalizedArgs.PRICE} for ${normalizedArgs.PRODUCT_TYPE}`, orderId: result.orderId ,status: result.status,message: result.message};
      } catch (e) {
        return { success: false, message: "PlaceSellLimitOrder failed: " + e.message };
      }
    },
    async PlaceBuyStopLossMarketOrder(args) {
      try {
        if (needsLogin()) {
          alert("Please login first.");
          return { success: false, message: "PlaceBuyStopLossMarketOrder failed: " + "Login required" };
        }
    
        // Normalize arguments with required fields
        const normalizedArgs = normalizeArguments(args, ['SYMBOL', 'QUANTITY', 'PRODUCT_TYPE', 'TRIGGER_PRICE']);
        
        // Validate required arguments
        if (!normalizedArgs.SYMBOL) {
          return { success: false, message: "PlaceBuyStopLossMarketOrder failed: Symbol is required" };
        }
        if (!normalizedArgs.QUANTITY) {
          return { success: false, message: "PlaceBuyStopLossMarketOrder failed: Quantity is required" };
        }
        if (!normalizedArgs.PRODUCT_TYPE) {
          return { success: false, message: "PlaceBuyStopLossMarketOrder failed: Product type is required" };
        }
        if (!normalizedArgs.TRIGGER_PRICE) {
          return { success: false, message: "PlaceBuyStopLossMarketOrder failed: Trigger price is required" };
        }
        
        await clearToast();
        console.log("PlaceBuyStopLossMarketOrder details set", normalizedArgs.PRODUCT_TYPE);
        
        // For indices, determine the appropriate exchange
        const effectiveExchange = getExchangeForSymbol(normalizedArgs.SYMBOL, normalizedArgs.EXCHANGE ?? 'NSE');
        
        await searchSymbol(normalizedArgs.SYMBOL);
        await openBuyModal(normalizedArgs.SYMBOL, effectiveExchange);
        await selectRegularTab();
        await selectOrderType("stoplossmarket");
        await setOrderDetails(normalizedArgs.QUANTITY, normalizedArgs.PRODUCT_TYPE, 'BUY', null, normalizedArgs.TRIGGER_PRICE);
        await clickBuyButton();
        const result = await checkToast();
        console.log(`PlaceBuyStopLossMarketOrder executed for ${normalizedArgs.QUANTITY} of ${normalizedArgs.SYMBOL} in ${effectiveExchange} at trigger price ${normalizedArgs.TRIGGER_PRICE} for ${normalizedArgs.PRODUCT_TYPE}`);
        return { success: true, message: `PlaceBuyStopLossMarketOrder executed for ${normalizedArgs.QUANTITY} of ${normalizedArgs.SYMBOL} in ${effectiveExchange} at trigger price ${normalizedArgs.TRIGGER_PRICE} for ${normalizedArgs.PRODUCT_TYPE}`, orderId: result.orderId ,status: result.status,message: result.message};
      } catch (e) {
        return { success: false, message: "PlaceBuyStopLossMarketOrder failed: " + e.message };
      }
    },
    async PlaceSellStopLossMarketOrder(args) {
      try {
        if (needsLogin()) {
          alert("Please login first.");
          return { success: false, message: "PlaceSellStopLossMarketOrder failed: " + "Login required" };
        }
    
        // Normalize arguments with required fields
        const normalizedArgs = normalizeArguments(args, ['SYMBOL', 'QUANTITY', 'PRODUCT_TYPE', 'TRIGGER_PRICE']);
        
        // Validate required arguments
        if (!normalizedArgs.SYMBOL) {
          return { success: false, message: "PlaceSellStopLossMarketOrder failed: Symbol is required" };
        }
        if (!normalizedArgs.QUANTITY) {
          return { success: false, message: "PlaceSellStopLossMarketOrder failed: Quantity is required" };
        }
        if (!normalizedArgs.PRODUCT_TYPE) {
          return { success: false, message: "PlaceSellStopLossMarketOrder failed: Product type is required" };
        }
        if (!normalizedArgs.TRIGGER_PRICE) {
          return { success: false, message: "PlaceSellStopLossMarketOrder failed: Trigger price is required" };
        }
        
        await clearToast();
        console.log("PlaceSellStopLossMarketOrder details set", normalizedArgs.PRODUCT_TYPE);
        
        // For indices, determine the appropriate exchange
        const effectiveExchange = getExchangeForSymbol(normalizedArgs.SYMBOL, normalizedArgs.EXCHANGE ?? 'NSE');
        
        await searchSymbol(normalizedArgs.SYMBOL);
        await openSellModal(normalizedArgs.SYMBOL, effectiveExchange);
        await selectRegularTab();
        await selectOrderType("stoplossmarket");
        await setOrderDetails(normalizedArgs.QUANTITY, normalizedArgs.PRODUCT_TYPE, 'SELL', null, normalizedArgs.TRIGGER_PRICE);
        console.log("PlaceSellStopLossMarketOrder details set", normalizedArgs.PRODUCT_TYPE);
        await clickSellButton();
        const result = await checkToast();
        console.log(`PlaceSellStopLossMarketOrder executed for ${normalizedArgs.QUANTITY} of ${normalizedArgs.SYMBOL} in ${effectiveExchange} at trigger price ${normalizedArgs.TRIGGER_PRICE} for ${normalizedArgs.PRODUCT_TYPE}`);
        return { success: true, message: `PlaceSellStopLossMarketOrder executed for ${normalizedArgs.QUANTITY} of ${normalizedArgs.SYMBOL} in ${effectiveExchange} at trigger price ${normalizedArgs.TRIGGER_PRICE} for ${normalizedArgs.PRODUCT_TYPE}`, orderId: result.orderId,status: result.status,message: result.message};
      } catch (e) {
        return { success: false, message: "PlaceSellStopLossMarketOrder failed: " + e.message };
      }
    },
    async PlaceBuyStopLossLimitOrder(args) {
      try {
        if (needsLogin()) {
          alert("Please login first.");
          return { success: false, message: "PlaceBuyStopLossLimitOrder failed: " + "Login required" };
        }
        
        // Normalize arguments with required fields
        const normalizedArgs = normalizeArguments(args, ['SYMBOL', 'QUANTITY', 'PRODUCT_TYPE', 'TRIGGER_PRICE', 'limitPrice']);
        
        // Validate required arguments
        if (!normalizedArgs.SYMBOL) {
          return { success: false, message: "PlaceBuyStopLossLimitOrder failed: Symbol is required" };
        }
        if (!normalizedArgs.QUANTITY) {
          return { success: false, message: "PlaceBuyStopLossLimitOrder failed: Quantity is required" };
        }
        if (!normalizedArgs.PRODUCT_TYPE) {
          return { success: false, message: "PlaceBuyStopLossLimitOrder failed: Product type is required" };
        }
        if (!normalizedArgs.TRIGGER_PRICE) {
          return { success: false, message: "PlaceBuyStopLossLimitOrder failed: Trigger price is required" };
        }
        if (!normalizedArgs.limitPrice) {
          return { success: false, message: "PlaceBuyStopLossLimitOrder failed: Limit price is required" };
        }
        
        console.log("TRIGGER_PRICE", normalizedArgs.TRIGGER_PRICE);
        console.log("limitPrice", normalizedArgs.limitPrice);
        await clearToast();
        console.log("PlaceBuyStopLossLimitOrder details set", normalizedArgs.PRODUCT_TYPE);
        
        // For indices, determine the appropriate exchange
        const effectiveExchange = getExchangeForSymbol(normalizedArgs.SYMBOL, normalizedArgs.EXCHANGE ?? 'NSE');
        
        await searchSymbol(normalizedArgs.SYMBOL);
        await openBuyModal(normalizedArgs.SYMBOL, effectiveExchange);
        await selectRegularTab();
        await selectOrderType("stoplosslimit");
        await setOrderDetails(normalizedArgs.QUANTITY, normalizedArgs.PRODUCT_TYPE, 'BUY', null, normalizedArgs.TRIGGER_PRICE, normalizedArgs.limitPrice);
        console.log("PlaceBuyStopLossLimitOrder details set", normalizedArgs.PRODUCT_TYPE);
        await clickBuyButton();
        const result = await checkToast();
        console.log(`PlaceBuyStopLossLimitOrder executed for ${normalizedArgs.QUANTITY} of ${normalizedArgs.SYMBOL} in ${effectiveExchange} at limit price ${normalizedArgs.limitPrice} with trigger price ${normalizedArgs.TRIGGER_PRICE} for ${normalizedArgs.PRODUCT_TYPE}`);
        return { success: true, message: `PlaceBuyStopLossLimitOrder executed for ${normalizedArgs.QUANTITY} of ${normalizedArgs.SYMBOL} in ${effectiveExchange} at limit price ${normalizedArgs.limitPrice} with trigger price ${normalizedArgs.TRIGGER_PRICE} for ${normalizedArgs.PRODUCT_TYPE}`, orderId: result.orderId ,status: result.status,message: result.message};
      } catch (e) {
        return { success: false, message: "PlaceBuyStopLossLimitOrder failed: " + e.message };
      }
    },
    
    async PlaceSellStopLossLimitOrder(args) {
      try {
        if (needsLogin()) {
          alert("Please login first.");
          return { success: false, message: "PlaceSellStopLossLimitOrder failed: " + "Login required" };
        }
        
        // Normalize arguments with required fields
        const normalizedArgs = normalizeArguments(args, ['SYMBOL', 'QUANTITY', 'PRODUCT_TYPE', 'TRIGGER_PRICE', 'limitPrice']);
        
        // Validate required arguments
        if (!normalizedArgs.SYMBOL) {
          return { success: false, message: "PlaceSellStopLossLimitOrder failed: Symbol is required" };
        }
        if (!normalizedArgs.QUANTITY) {
          return { success: false, message: "PlaceSellStopLossLimitOrder failed: Quantity is required" };
        }
        if (!normalizedArgs.PRODUCT_TYPE) {
          return { success: false, message: "PlaceSellStopLossLimitOrder failed: Product type is required" };
        }
        if (!normalizedArgs.TRIGGER_PRICE) {
          return { success: false, message: "PlaceSellStopLossLimitOrder failed: Trigger price is required" };
        }
        if (!normalizedArgs.limitPrice) {
          return { success: false, message: "PlaceSellStopLossLimitOrder failed: Limit price is required" };
        }
        
        console.log("TRIGGER_PRICE", normalizedArgs.TRIGGER_PRICE);
        console.log("limitPrice", normalizedArgs.limitPrice);
        await clearToast();
        console.log("PlaceSellStopLossLimitOrder details set", normalizedArgs.PRODUCT_TYPE);
        
        // For indices, determine the appropriate exchange
        const effectiveExchange = getExchangeForSymbol(normalizedArgs.SYMBOL, normalizedArgs.EXCHANGE ?? 'NSE');
        
        await searchSymbol(normalizedArgs.SYMBOL);
        await openSellModal(normalizedArgs.SYMBOL, effectiveExchange);
        await selectRegularTab();
        await selectOrderType("stoplosslimit");
        await setOrderDetails(normalizedArgs.QUANTITY, normalizedArgs.PRODUCT_TYPE, 'SELL', null, normalizedArgs.TRIGGER_PRICE, normalizedArgs.limitPrice);
        console.log("PlaceSellStopLossLimitOrder details set", normalizedArgs.PRODUCT_TYPE);
        await clickSellButton();
        const result = await checkToast();
        console.log(`PlaceSellStopLossLimitOrder executed for ${normalizedArgs.QUANTITY} of ${normalizedArgs.SYMBOL} in ${effectiveExchange} at limit price ${normalizedArgs.limitPrice} with trigger price ${normalizedArgs.TRIGGER_PRICE} for ${normalizedArgs.PRODUCT_TYPE}`);
        return { success: true, message: `PlaceSellStopLossLimitOrder executed for ${normalizedArgs.QUANTITY} of ${normalizedArgs.SYMBOL} in ${effectiveExchange} at limit price ${normalizedArgs.limitPrice} with trigger price ${normalizedArgs.TRIGGER_PRICE} for ${normalizedArgs.PRODUCT_TYPE}`, orderId: result.orderId ,status: result.status,message: result.message};
      } catch (e) {
        return { success: false, message: "PlaceSellStopLossLimitOrder failed: " + e.message };
      }
    },
    async MONITORPROFIT(args) {
      try {
        // Arguments are already normalized by the message listener
        const amountToExceed = parseFloat(args.TARGET_PROFIT_AMOUNT);
        if (needsLogin()) {
          alert("Please login first.");
          return { success: false, message: "MONITORPROFIT failed: " + "Login required" };
        }
    
        // TODO: We check the first pinned instrument's last price value for now, till I figure out how to monitor profit value
        /**Run the following IIFE to simulate change in value:
    (() => {
    let value = 1;
    let intervalId = setInterval(() => {
      document.querySelector(".pinned-instruments .instrument-widget .last-price").textContent = "\n" + (value++) + "\n\t\t";
      if (value > 1000) {
        clearInterval(intervalId);
      }
    }, 200);
    })(); */
        const lastPriceElement = document.querySelector(".pinned-instruments .instrument-widget .last-price");
        if (!lastPriceElement) {
          throw new Error("Could not find price element to monitor");
        }
    
        return new Promise((resolve) => {
          const observer = new MutationObserver((mutations) => {
            for (const mutation of mutations) {
              if (mutation.type === 'characterData' || mutation.type === 'childList') {
                const currentPrice = parseFloat(lastPriceElement.textContent.replace(/[₹,]/g, ''));
                if (currentPrice > amountToExceed) {
                  observer.disconnect();
                  resolve({ success: true, message: `MONITORPROFIT: Price ${currentPrice} exceeded ${amountToExceed}` });
                }
              }
            }
          });
    
          observer.observe(lastPriceElement, {
            characterData: true,
            childList: true,
            subtree: true
          });
        });
      } catch (e) {
        return { success: false, message: "MONITORPROFIT failed: " + e.message };
      }
    },
    async ExitAllPositions(args) {
      try {
        console.log("ExitAllPositions called with args:", args);
        
        const result = await exitAllPositions(args);
        return result;
      } catch (e) {
        return { success: false, message: "ExitAllPositions failed: " + e.message };
      }
    },
    async MonitorConditionThenAct(args) {
      try {
        // Validate required arguments
        if (!args.condition) {
          throw new Error("Condition is required");
        }
        if (!args.on_trigger) {
          throw new Error("on_trigger is required");
        }

        console.log("MonitorConditionThenAct called with args:", args);
        
        const result = await monitorConditionThenAct(args.condition, args.on_trigger);
        return result;
      } catch (e) {
        return { success: false, message: "MonitorConditionThenAct failed: " + e.message };
      }
    },
    async NavigateToProfile(args) {
      try {
        console.log("NavigateToProfile called with args:", args);
        
        const result = await navigateToProfile();
        return result;
      } catch (e) {
        return { success: false, message: "NavigateToProfile failed: " + e.message };
      }
    },
    async GetProfileInfo(args) {
      try {
        console.log("GetProfileInfo called with args:", args);
        
        const result = await getProfileInfo();
        return result;
      } catch (e) {
        return { success: false, message: "GetProfileInfo failed: " + e.message };
      }
    },
    async GetPortfolioStats(args) {
      try {
        console.log("GetPortfolioStats called with args:", args);
        
        const result = await getPortfolioPnL();
        return { 
          success: true, 
          message: "Portfolio statistics retrieved successfully",
          data: result
        };
      } catch (e) {
        return { success: false, message: "GetPortfolioStats failed: " + e.message };
      }
    },
    async GetOpenPositionPnL(args) {
      try {
        console.log("GetOpenPositionPnL called with args:", args);
        
        const result = await getOpenPositionPnL();
        return { 
          success: true, 
          message: "Open position P&L retrieved successfully",
          data: { open_position_PnL: result }
        };
      } catch (e) {
        return { success: false, message: "GetOpenPositionPnL failed: " + e.message };
      }
    },
    async MonitorSymbolFromWatchlist(args) {
      try {
        // Validate required arguments
        if (!args.symbol) {
          throw new Error("Symbol is required");
        }
        if (!args.condition) {
          throw new Error("Condition is required");
        }
        if (!args.on_trigger) {
          throw new Error("on_trigger is required");
        }

        console.log("MonitorSymbolFromWatchlist called with args:", args);
        
        // For indices, determine the appropriate exchange
        const effectiveExchange = getExchangeForSymbol(args.symbol, args.exchange || 'NSE');
        
        // Create a condition object for the monitorConditionThenAct function
        const condition = {
          observe: 'price',
          symbol: args.symbol,
          exchange: effectiveExchange,
          operator: args.condition.operator,
          value: args.condition.value
        };
        
        const result = await monitorConditionThenAct(condition, args.on_trigger);
        return result;
      } catch (e) {
        return { success: false, message: "MonitorSymbolFromWatchlist failed: " + e.message };
      }
    },
    async SelectOrderByCriteria(args) {
      try {
        console.log("SelectOrderByCriteria called with args:", args);
        
        // Normalize arguments with required fields
        const normalizedArgs = normalizeArguments(args, ['TYPE', 'SYMBOL', 'EXCHANGE', 'PRODUCT', 'QUANTITY']);
        
        // Validate required arguments
        if (!normalizedArgs.TYPE) {
          throw new Error("Type is required");
        }
        if (!normalizedArgs.SYMBOL) {
          throw new Error("Symbol is required");
        }
        if (!normalizedArgs.EXCHANGE) {
          throw new Error("Exchange is required");
        }
        if (!normalizedArgs.PRODUCT) {
          throw new Error("Product is required");
        }
        if (!normalizedArgs.QUANTITY) {
          throw new Error("Quantity is required");
        }

        // Navigate to orders page if not already there
        await navigate("orders");
        await smartSleep(1000);

        // Wait for the pending orders table to load
        const pendingOrdersTable = await waitForElement('.pending-orders table tbody', 10000);
        if (!pendingOrdersTable) {
          throw new Error("Pending orders table not found");
        }

        // Find all order rows
        const orderRows = pendingOrdersTable.querySelectorAll('tr');
        let targetOrderRow = null;

        // Iterate through each row to find the matching order
        for (const row of orderRows) {
          try {
            // Extract order details from the row
            const typeElement = row.querySelector('.transaction-type .text-label');
            const symbolElement = row.querySelector('.instrument .tradingsymbol');
            const exchangeElement = row.querySelector('.instrument .exchange');
            const productElement = row.querySelector('.product');
            const quantityElement = row.querySelector('.quantity');

            if (!typeElement || !symbolElement || !exchangeElement || !productElement || !quantityElement) {
              continue; // Skip rows that don't have all required elements
            }

            const orderType = typeElement.textContent.trim();
            const symbol = symbolElement.textContent.trim();
            const exchange = exchangeElement.textContent.trim();
            const product = productElement.textContent.trim();
            const quantity = quantityElement.textContent.trim();

            // Extract the denominator (y) from quantity format "x/y"
            const extractDenominator = (quantityStr) => {
              if (quantityStr && quantityStr.includes('/')) {
                const parts = quantityStr.split('/');
                return parts[parts.length - 1].trim(); // Get the last part after '/'
              }
              return quantityStr; // Return as-is if no '/' found
            };

            const quantityDenominator = extractDenominator(quantity);
            const argsQuantityDenominator = extractDenominator(normalizedArgs.QUANTITY);

            console.log(`Checking order: Type=${orderType}, Symbol=${symbol}, Exchange=${exchange}, Product=${product}, Quantity=${quantity} (denominator: ${quantityDenominator})`);

            // Check if this row matches all criteria
            if (normalizeString(orderType) === normalizeString(normalizedArgs.TYPE) &&
                normalizeString(symbol) === normalizeString(normalizedArgs.SYMBOL) &&
                normalizeString(exchange) === normalizeString(normalizedArgs.EXCHANGE) &&
                normalizeString(product) === normalizeString(normalizedArgs.PRODUCT) &&
                normalizeString(quantityDenominator) === normalizeString(argsQuantityDenominator)) {
              
              targetOrderRow = row;
              console.log("Found matching order row:", row);
              break;
            }
          } catch (rowError) {
            console.warn("Error processing order row:", rowError);
            continue;
          }
        }

        if (!targetOrderRow) {
          throw new Error(`No order found matching criteria: Type=${normalizedArgs.TYPE}, Symbol=${normalizedArgs.SYMBOL}, Exchange=${normalizedArgs.EXCHANGE}, Product=${normalizedArgs.PRODUCT}, Quantity=${normalizedArgs.QUANTITY}`);
        }

        // Find the checkbox in the target row
        const checkbox = targetOrderRow.querySelector('input[type="checkbox"]');
        if (!checkbox) {
          throw new Error("Checkbox not found in the target order row");
        }

        // Check if the checkbox is already selected
        if (checkbox.checked) {
          console.log("Order is already selected");
          return { success: true, message: "Order is already selected" };
        }

        // Click the checkbox to select the order
        checkbox.click();
        await smartSleep(500);

        // Verify the checkbox is now checked
        if (!checkbox.checked) {
          throw new Error("Failed to select the order checkbox");
        }

        console.log("Order selected successfully");
        return { success: true, message: "Order selected successfully" };

      } catch (error) {
        console.error("SelectOrderByCriteria failed:", error);
        return { success: false, message: `SelectOrderByCriteria failed: ${error.message}` };
      }
    },
    async CancelOrder(args) {
      try {
        console.log("CancelOrder called with args:", args);
        
        // Normalize arguments with required fields
        const normalizedArgs = normalizeArguments(args, ['SYMBOL', 'QUANTITY', 'EXCHANGE', 'PRODUCT', 'TYPE']);
        
        // Validate required arguments
        if (!normalizedArgs.SYMBOL) {
          throw new Error("Symbol is required");
        }
        if (!normalizedArgs.QUANTITY) {
          throw new Error("Quantity is required");
        }
        if (!normalizedArgs.EXCHANGE) {
          throw new Error("Exchange is required");
        }
        if (!normalizedArgs.PRODUCT) {
          throw new Error("Product is required");
        }
        if (!normalizedArgs.TYPE) {
          throw new Error("Type is required");
        }

        // First, select the order using SelectOrderByCriteria
        console.log("Selecting order before cancellation...");
        const selectResult = await siteActions.SelectOrderByCriteria(normalizedArgs);
        
        if (!selectResult.success) {
          throw new Error(`Failed to select order: ${selectResult.message}`);
        }

        console.log("Order selected successfully, proceeding with cancellation...");

        // Wait a moment for the selection to register
        await smartSleep(500);

        // Look for the cancel button in the toolbar
        const cancelButton = document.querySelector('.toolbar .delete-selected, .toolbar button.button-small.button-blue');
        
        if (!cancelButton) {
          throw new Error("Cancel button not found in toolbar");
        }

        console.log("Found cancel button, clicking...");
        cancelButton.click();
        await smartSleep(1000);

        // Wait for confirmation dialog if it appears
        const confirmDialog = document.querySelector('.modal-content, .dialog-content, [role="dialog"]');
        if (confirmDialog) {
          console.log("Confirmation dialog found, confirming cancellation...");
          
          // Look for confirm/yes button in the dialog
          const confirmButton = confirmDialog.querySelector('button[type="submit"], .button-blue, .button-red, .confirm, .yes');
          if (confirmButton) {
            confirmButton.click();
            await smartSleep(1000);
          }
        }

        // Check if the order is no longer in the pending orders table
        const pendingOrdersTable = document.querySelector('.pending-orders table tbody');
        if (pendingOrdersTable) {
          const orderRows = pendingOrdersTable.querySelectorAll('tr');
          let orderStillExists = false;
          
          for (const row of orderRows) {
            const symbolElement = row.querySelector('.instrument .tradingsymbol');
            const typeElement = row.querySelector('.transaction-type .text-label');
            
            if (symbolElement && typeElement) {
              const symbol = symbolElement.textContent.trim();
              const orderType = typeElement.textContent.trim();
              
              if (normalizeString(symbol) === normalizeString(normalizedArgs.SYMBOL) && 
                  normalizeString(orderType) === normalizeString(normalizedArgs.TYPE)) {
                orderStillExists = true;
                break;
              }
            }
          }
          
          if (orderStillExists) {
            console.warn("Order still appears in pending orders table");
          } else {
            console.log("Order successfully removed from pending orders");
          }
        }

        console.log("Cancel order operation completed");
        return { success: true, message: "Order cancellation initiated successfully" };

      } catch (error) {
        console.error("CancelOrder failed:", error);
        return { success: false, message: `CancelOrder failed: ${error.message}` };
      }
    },
    async GetOpenOrders(args) {
      try {
        console.log("GetOpenOrders called with args:", args);
        
        // Navigate to orders page if not already there
        await navigate("orders");
        await smartSleep(1000);

        // Wait for the pending orders table to load, or use direct query if not found
        let pendingOrdersTable = await waitForElement('.pending-orders table tbody', 5000);
        if (!pendingOrdersTable) {
          console.warn("[GetOpenOrders] Pending orders table not found via waitForElement, trying direct query");
          pendingOrdersTable = document.querySelector('.pending-orders table tbody');
        }

        // Find all order rows - handle empty case gracefully
        const orderRows = pendingOrdersTable ? pendingOrdersTable.querySelectorAll('tr') : [];
        console.log(`[GetOpenOrders] Found ${orderRows.length} order rows`);
        const openOrders = [];
        // Boundary-aware scanning: allow early stop from top using previous firstKey
        const scanDirection = (args && args.scanDirection) || null; // currently supports 'top' for open orders delta
        const stopAtKeyTop = args && args.stopAtKeyTop ? String(args.stopAtKeyTop) : null;
        const buildKey = (row) => {
          const time = row.querySelector('.order-timestamp')?.textContent?.trim() || '';
          const type = row.querySelector('.transaction-type .text-label')?.textContent?.trim() || '';
          const symbol = row.querySelector('.instrument .tradingsymbol')?.textContent?.trim() || '';
          const exchange = row.querySelector('.instrument .exchange')?.textContent?.trim() || '';
          const product = row.querySelector('.product')?.textContent?.trim() || '';
          const quantity = row.querySelector('.quantity')?.textContent?.trim() || '';
          const status = row.querySelector('.order-status .order-status-label')?.textContent?.trim() || '';
          const price = row.querySelector('.average-price')?.textContent?.trim() || row.querySelector('.last-price')?.textContent?.trim() || '';
          return [symbol, type, exchange, product, quantity, status, time, price].join('|');
        };

        // If scanning from bottom, iterate upward from the end until previous last key
        if (scanDirection === 'bottom' && orderRows.length > 0) {
          const collected = [];
          for (let i = orderRows.length - 1; i >= 0; i--) {
            const row = orderRows[i];
            const rowKey = buildKey(row);
            const stopAtKeyBottom = args && args.stopAtKeyBottom ? String(args.stopAtKeyBottom) : null;
            if (stopAtKeyBottom && rowKey === stopAtKeyBottom) {
              console.log('[GetOpenOrders] Reached stopAtKeyBottom; stopping scan early at index', i);
              break;
            }

            try {
              const timeElement = row.querySelector('.order-timestamp');
              const typeElement = row.querySelector('.transaction-type .text-label');
              const symbolElement = row.querySelector('.instrument .tradingsymbol');
              const exchangeElement = row.querySelector('.instrument .exchange');
              const productElement = row.querySelector('.product');
              const quantityElement = row.querySelector('.quantity');
              const ltpElement = row.querySelector('.last-price');
              const priceElement = row.querySelector('.average-price');
              const statusElement = row.querySelector('.order-status .order-status-label');

              if (!timeElement || !typeElement || !symbolElement || !exchangeElement || !productElement || !quantityElement || !statusElement) {
                continue;
              }

              const time = timeElement.textContent.trim();
              const type = typeElement.textContent.trim();
              const symbol = symbolElement.textContent.trim();
              const exchange = exchangeElement.textContent.trim();
              const product = productElement.textContent.trim();
              const quantity = quantityElement.textContent.trim();
              const ltp = ltpElement ? ltpElement.textContent.trim() : '';
              const price = priceElement ? priceElement.textContent.trim() : '';
              const status = statusElement.textContent.trim();

              let filledQty = 0;
              let totalQty = 0;
              if (quantity.includes('/')) {
                const parts = quantity.split('/');
                filledQty = parseInt(parts[0].trim()) || 0;
                totalQty = parseInt(parts[1].trim()) || 0;
              } else {
                totalQty = parseInt(quantity.trim()) || 0;
              }

              const normalizedStatus = status.toUpperCase();
              const openLikeStatuses = ['OPEN','AMO REQ RECEIVED','VALIDATION PENDING','TRIGGER PENDING','PENDING','OPEN PENDING'];
              if (!openLikeStatuses.includes(normalizedStatus)) {
                continue;
              }

              const orderData = {
                time,
                type,
                symbol,
                exchange,
                product,
                quantity: { filled: filledQty, total: totalQty, remaining: totalQty - filledQty },
                ltp: parseFloat(ltp.replace(/,/g, '')) || 0,
                price: parseFloat(price.replace(/,/g, '')) || 0,
                status,
                detailedInfo: null
              };

              try {
                const menuColumn = row.querySelector('.col-menu');
                if (menuColumn) {
                  menuColumn.dispatchEvent(new MouseEvent('mouseenter', { bubbles: true, cancelable: true, view: window }));
                  await smartSleep(500);
                  const menuButton = menuColumn.querySelector('.table-menu-button.icon.icon-more-vertical');
                  if (menuButton) {
                    menuButton.click();
                    await smartSleep();
                  }
                  let infoButton = null;
                  const allMenuLinks = document.querySelectorAll('.addon-menu a');
                  for (const link of allMenuLinks) {
                    const iconElement = link.querySelector('.icon-info');
                    if (iconElement || link.textContent.trim().includes('Info')) { infoButton = link; break; }
                  }
                  if (infoButton) {
                    infoButton.click();
                    await smartSleep(1000);
                    const infoModal = document.querySelector('.modal-container.layer-2');
                    if (infoModal) {
                      const detailedInfo = {};
                      const transactionTypeElement = infoModal.querySelector('.modal-header .transaction-type.text-label');
                      if (transactionTypeElement) detailedInfo.transactionType = transactionTypeElement.textContent.trim();
                      const tradingSymbolElement = infoModal.querySelector('.modal-header .tradingsymbol');
                      if (tradingSymbolElement) detailedInfo.tradingSymbol = tradingSymbolElement.textContent.trim();
                      const statusElement2 = infoModal.querySelector('.modal-header .order-status-label');
                      if (statusElement2) detailedInfo.orderStatus = statusElement2.textContent.trim();
                      const orderIdElement = infoModal.querySelector('.order-id, .orderid, [data-testid="order-id"]');
                      if (orderIdElement) detailedInfo.orderId = orderIdElement.textContent.trim();
                      const exchangeOrderIdElement = infoModal.querySelector('.exchange-order-id, .exchangeorderid, [data-testid="exchange-order-id"]');
                      if (exchangeOrderIdElement) detailedInfo.exchangeOrderId = exchangeOrderIdElement.textContent.trim();
                      const orderTimestampElement = infoModal.querySelector('.order-timestamp, .ordertimestamp, [data-testid="order-timestamp"]');
                      if (orderTimestampElement) detailedInfo.orderTimestamp = orderTimestampElement.textContent.trim();
                      Object.keys(detailedInfo).forEach(k => { if (detailedInfo[k] === null || detailedInfo[k] === '') delete detailedInfo[k]; });
                      orderData.detailedInfo = detailedInfo;
                      let closeButton = null;
                      const allButtons = infoModal.querySelectorAll('.modal-footer .button, .modal-footer button');
                      for (const button of allButtons) { if (button.textContent.trim() === 'Close') { closeButton = button; break; } }
                      if (closeButton) { closeButton.click(); await smartSleep(500); } else {
                        const allModalButtons = infoModal.querySelectorAll('button, .modal-header .close, .modal-header .icon-close');
                        for (const button of allModalButtons) { if ((button.textContent && button.textContent.trim() === 'Close') || button.classList.contains('close') || button.classList.contains('icon-close')) { closeButton = button; break; } }
                        if (closeButton) { closeButton.click(); await smartSleep(500); } else { document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape' })); await smartSleep(500); }
                      }
                    }
                  }
                  menuColumn.dispatchEvent(new MouseEvent('mouseleave', { bubbles: true, cancelable: true, view: window }));
                }
              } catch (detailError) {
                orderData.detailedInfo = { error: detailError.message };
              }

              collected.push(orderData);
            } catch (err) {
              console.warn('[GetOpenOrders] bottom-scan row error:', err);
              continue;
            }
          }
          collected.reverse();
          return {
            success: true,
            message: `Retrieved ${collected.length} open orders (bottom boundary scan)`,
            data: { count: collected.length, orders: collected }
          };
        }

        // Iterate through each row to extract order details
        for (let i = 0; i < orderRows.length; i++) {
          const row = orderRows[i];
          if (scanDirection === 'top' && stopAtKeyTop) {
            const rowKey = buildKey(row);
            if (rowKey === stopAtKeyTop) {
              console.log('[GetOpenOrders] Reached stopAtKeyTop; stopping scan early at index', i);
              break;
            }
          }
          
          try {
            // Extract basic order details from the row
            const timeElement = row.querySelector('.order-timestamp');
            const typeElement = row.querySelector('.transaction-type .text-label');
            const symbolElement = row.querySelector('.instrument .tradingsymbol');
            const exchangeElement = row.querySelector('.instrument .exchange');
            const productElement = row.querySelector('.product');
            const quantityElement = row.querySelector('.quantity');
            const ltpElement = row.querySelector('.last-price');
            const priceElement = row.querySelector('.average-price');
            const statusElement = row.querySelector('.order-status .order-status-label');

            if (!timeElement || !typeElement || !symbolElement || !exchangeElement || 
                !productElement || !quantityElement || !statusElement) {
              continue; // Skip rows that don't have all required elements
            }

            const time = timeElement.textContent.trim();
            const type = typeElement.textContent.trim();
            const symbol = symbolElement.textContent.trim();
            const exchange = exchangeElement.textContent.trim();
            const product = productElement.textContent.trim();
            const quantity = quantityElement.textContent.trim();
            const ltp = ltpElement ? ltpElement.textContent.trim() : '';
            const price = priceElement ? priceElement.textContent.trim() : '';
            const status = statusElement.textContent.trim();

            // Parse quantity to extract filled and total quantities
            let filledQty = 0;
            let totalQty = 0;
            if (quantity.includes('/')) {
              const parts = quantity.split('/');
              filledQty = parseInt(parts[0].trim()) || 0;
              totalQty = parseInt(parts[1].trim()) || 0;
            } else {
              totalQty = parseInt(quantity.trim()) || 0;
            }

            // Only process orders with OPEN-like status
            const normalizedStatus = status.toUpperCase();
            const openLikeStatuses = [
              'OPEN',
              'AMO REQ RECEIVED',
              'VALIDATION PENDING',
              'TRIGGER PENDING',
              'PENDING',
              'OPEN PENDING'
            ];

            if (openLikeStatuses.includes(normalizedStatus)) {
              console.log(`Processing open order ${i + 1}: ${symbol} ${type}`);
              
              // Initialize order data with basic information
              const orderData = {
                time: time,
                type: type,
                symbol: symbol,
                exchange: exchange,
                product: product,
                quantity: {
                  filled: filledQty,
                  total: totalQty,
                  remaining: totalQty - filledQty
                },
                ltp: parseFloat(ltp.replace(/,/g, '')) || 0,
                price: parseFloat(price.replace(/,/g, '')) || 0,
                status: status,
                detailedInfo: null
              };

              try {
                // Find the menu column for this row (last column)
                const menuColumn = row.querySelector('.col-menu');
                if (menuColumn) {
                  console.log(`Hovering over menu column for ${symbol}...`);
                  
                  // Trigger hover event on the menu column
                  menuColumn.dispatchEvent(new MouseEvent('mouseenter', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                  }));
                  
                  await smartSleep(500); // Wait for hover menu to appear
                  
                  // Click on the menu button (three dots)
                  const menuButton = menuColumn.querySelector('.table-menu-button.icon.icon-more-vertical');
                  if (menuButton) {
                    menuButton.click();
                    await smartSleep(); // Wait for dropdown to fully open
                  }
                  
                  // Click on the Info button from the dropdown menu
                  let infoButton = null;
                  
                  // Try to find the Info button by looking for the icon-info class
                  const allMenuLinks = document.querySelectorAll('.addon-menu a');
                  for (const link of allMenuLinks) {
                    const iconElement = link.querySelector('.icon-info');
                    if (iconElement || link.textContent.trim().includes('Info')) {
                      infoButton = link;
                      break;
                    }
                  }
                  
                  if (infoButton) {
                    console.log(`Clicking Info button for ${symbol}...`);
                    infoButton.click();
                    await smartSleep(1000); // Wait for info modal to open
                    
                    // Extract detailed information from the info modal/popup
                    const infoModal = document.querySelector('.modal-container.layer-2');
                    
                    if (infoModal) {
                      console.log(`Extracting detailed info for ${symbol}...`);
                      
                      // Extract various details from the info modal using specific selectors
                      const detailedInfo = {};
                      
                      // Extract from modal header
                      const transactionTypeElement = infoModal.querySelector('.modal-header .transaction-type.text-label');
                      if (transactionTypeElement) {
                        detailedInfo.transactionType = transactionTypeElement.textContent.trim();
                      }
                      
                      const tradingSymbolElement = infoModal.querySelector('.modal-header .tradingsymbol');
                      if (tradingSymbolElement) {
                        detailedInfo.tradingSymbol = tradingSymbolElement.textContent.trim();
                      }
                      
                      const statusElement = infoModal.querySelector('.modal-header .order-status-label');
                      if (statusElement) {
                        detailedInfo.orderStatus = statusElement.textContent.trim();
                      }
                      
                      // Extract from left column (order details)
                      const extractFieldValue = (labelText) => {
                        const rows = infoModal.querySelectorAll('.order-info-body .row');
                        for (const row of rows) {
                          const label = row.querySelector('label');
                          if (label && label.textContent.trim() === labelText) {
                            const valueElement = row.querySelector('.seven.columns .price, .seven.columns .order-type, .seven.columns .product, .seven.columns span, .seven.columns .trigger-price, .seven.columns');
                            if (valueElement) {
                              return valueElement.textContent.trim();
                            }
                          }
                        }
                        return null;
                      };
                      
                      // Left column fields
                      detailedInfo.quantity = extractFieldValue('Quantity');
                      detailedInfo.price = extractFieldValue('Price');
                      detailedInfo.avgPrice = extractFieldValue('Avg. price');
                      detailedInfo.triggerPrice = extractFieldValue('Trigger price');
                      detailedInfo.orderType = extractFieldValue('Order type');
                      detailedInfo.product = extractFieldValue('Product');
                      detailedInfo.validity = extractFieldValue('Validity');
                      detailedInfo.terminalId = extractFieldValue('Terminal ID');
                      
                      // Right column fields - using specific selectors
                      const orderIdElement = infoModal.querySelector('.order-id, .orderid, [data-testid="order-id"]');
                      if (orderIdElement) {
                        detailedInfo.orderId = orderIdElement.textContent.trim();
                      }
                      
                      const exchangeOrderIdElement = infoModal.querySelector('.exchange-order-id, .exchangeorderid, [data-testid="exchange-order-id"]');
                      if (exchangeOrderIdElement) {
                        detailedInfo.exchangeOrderId = exchangeOrderIdElement.textContent.trim();
                      }
                      
                      const orderTimestampElement = infoModal.querySelector('.order-timestamp, .ordertimestamp, [data-testid="order-timestamp"]');
                      if (orderTimestampElement) {
                        detailedInfo.orderTimestamp = orderTimestampElement.textContent.trim();
                      }
                      
                      const exchangeTimestampElement = infoModal.querySelector('.exchange-timestamp, .exchangetimestamp, [data-testid="exchange-timestamp"]');
                      if (exchangeTimestampElement) {
                        detailedInfo.exchangeTimestamp = exchangeTimestampElement.textContent.trim();
                      }
                      
                      const placedByElement = infoModal.querySelector('.placed-by, .placedby, [data-testid="placed-by"]');
                      if (placedByElement) {
                        detailedInfo.placedBy = placedByElement.textContent.trim();
                      }
                      
                      // Clean up null values
                      Object.keys(detailedInfo).forEach(key => {
                        if (detailedInfo[key] === null || detailedInfo[key] === '') {
                          delete detailedInfo[key];
                        }
                      });
                      
                      orderData.detailedInfo = detailedInfo;
                      console.log(`Detailed info extracted for ${symbol}:`, detailedInfo);
                      
                      // Close the info modal
                      let closeButton = null;
                      
                      // First try to find the Close button by text content
                      const allButtons = infoModal.querySelectorAll('.modal-footer .button, .modal-footer button');
                      for (const button of allButtons) {
                        if (button.textContent.trim() === 'Close') {
                          closeButton = button;
                          break;
                        }
                      }
                      
                      if (closeButton) {
                        closeButton.click();
                        await smartSleep(500);
                      } else {
                        // Fallback: Try to find any button with "Close" or an icon-only close within the header
                        const allModalButtons = infoModal.querySelectorAll('button, .modal-header .close, .modal-header .icon-close');
                        for (const button of allModalButtons) {
                          if ((button.textContent && button.textContent.trim() === 'Close') || button.classList.contains('close') || button.classList.contains('icon-close')) {
                            closeButton = button;
                            break;
                          }
                        }
                        
                        if (closeButton) {
                          closeButton.click();
                          await smartSleep(500);
                        } else {
                          // Try pressing Escape key to close modal
                          document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape' }));
                          await smartSleep(500);
                        }
                      }
                    } else {
                      console.warn(`Info modal not found for ${symbol}`);
                    }
                  } else {
                    console.warn(`Info button not found for ${symbol}`);
                  }
                  
                  // Clear any hover states
                  menuColumn.dispatchEvent(new MouseEvent('mouseleave', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                  }));
                  
                } else {
                  console.warn(`Menu column not found for ${symbol}`);
                }
              } catch (detailError) {
                console.warn(`Error extracting detailed info for ${symbol}:`, detailError);
                orderData.detailedInfo = { error: detailError.message };
              }

              openOrders.push(orderData);
              console.log("Added open order:", orderData);
            }
          } catch (rowError) {
            console.warn("Error processing order row:", rowError);
            continue;
          }
        }

        console.log(`Found ${openOrders.length} open orders with detailed information`);
        return { 
          success: true, 
          message: `Retrieved ${openOrders.length} open orders with detailed information successfully`,
          data: {
            count: openOrders.length,
            orders: openOrders
          }
        };

      } catch (error) {
        console.error("GetOpenOrders failed:", error);
        return { success: false, message: `GetOpenOrders failed: ${error.message}` };
      }
    },
    async GetCompletedOrders(args) {
      try {
        console.log("GetCompletedOrders called with args:", args);
        
        // Navigate to orders page if not already there
        await navigate("orders");
        await smartSleep(1000);

        // Wait for the completed/executed orders table to load, or use direct query if not found
        let completedOrdersTable = await waitForElement('.completed-orders table tbody', 5000);
        if (!completedOrdersTable) {
          console.warn("[GetCompletedOrders] Completed orders table not found via waitForElement, trying direct query");
          completedOrdersTable = document.querySelector('.completed-orders table tbody');
        }

        // Find all order rows - handle empty case gracefully
        const orderRows = completedOrdersTable ? completedOrdersTable.querySelectorAll('tr') : [];
        console.log(`[GetCompletedOrders] Found ${orderRows.length} order rows`);
        const completedOrders = [];
        // Boundary-aware scanning for completed (executed) orders
        const scanDirection = (args && args.scanDirection) || null; // 'top' | 'bottom'
        const stopAtKeyTop = args && args.stopAtKeyTop ? String(args.stopAtKeyTop) : null;
        const stopAtKeyBottom = args && args.stopAtKeyBottom ? String(args.stopAtKeyBottom) : null;
        const buildKey = (row) => {
          const time = row.querySelector('.order-timestamp')?.textContent?.trim() || '';
          const type = row.querySelector('.transaction-type .text-label')?.textContent?.trim() || '';
          const symbol = row.querySelector('.instrument .tradingsymbol')?.textContent?.trim() || '';
          const exchange = row.querySelector('.instrument .exchange')?.textContent?.trim() || '';
          const product = row.querySelector('.product')?.textContent?.trim() || '';
          const quantity = row.querySelector('.quantity')?.textContent?.trim() || '';
          const status = row.querySelector('.order-status .order-status-label')?.textContent?.trim() || '';
          const price = row.querySelector('.average-price')?.textContent?.trim() || '';
          return [symbol, type, exchange, product, quantity, status, time, price].join('|');
        };

        if (scanDirection === 'bottom' && orderRows.length > 0) {
          const collected = [];
          for (let i = orderRows.length - 1; i >= 0; i--) {
            const row = orderRows[i];
            const rowKey = buildKey(row);
            if (stopAtKeyBottom && rowKey === stopAtKeyBottom) {
              console.log('[GetCompletedOrders] Reached stopAtKeyBottom; stopping scan early at index', i);
              break;
            }
            try {
              // Extract basic order details from the row
              const timeElement = row.querySelector('.order-timestamp');
              const typeElement = row.querySelector('.transaction-type .text-label');
              const symbolElement = row.querySelector('.instrument .tradingsymbol');
              const exchangeElement = row.querySelector('.instrument .exchange');
              const productElement = row.querySelector('.product');
              const quantityElement = row.querySelector('.quantity');
              const avgPriceElement = row.querySelector('.average-price');
              const statusElement = row.querySelector('.order-status .order-status-label');

              if (!timeElement || !typeElement || !symbolElement || !exchangeElement || !productElement || !quantityElement || !statusElement) {
                continue;
              }

              const time = timeElement.textContent.trim();
              const type = typeElement.textContent.trim();
              const symbol = symbolElement.textContent.trim();
              const exchange = exchangeElement.textContent.trim();
              const product = productElement.textContent.trim();
              const quantity = quantityElement.textContent.trim();
              const avgPrice = avgPriceElement ? avgPriceElement.textContent.trim() : '';
              const status = statusElement.textContent.trim();

              let filledQty = 0;
              let totalQty = 0;
              if (quantity.includes('/')) {
                const parts = quantity.split('/');
                filledQty = parseInt(parts[0].trim()) || 0;
                totalQty = parseInt(parts[1].trim()) || 0;
              } else {
                filledQty = parseInt(quantity.trim()) || 0;
                totalQty = filledQty;
              }

              const orderData = {
                time,
                type,
                symbol,
                exchange,
                product,
                quantity: { filled: filledQty, total: totalQty, remaining: Math.max(0, totalQty - filledQty) },
                avgPrice: parseFloat(avgPrice) || 0,
                status,
                detailedInfo: null
              };

              try {
                const menuColumn = row.querySelector('.col-menu');
                if (menuColumn) {
                  menuColumn.dispatchEvent(new MouseEvent('mouseenter', { bubbles: true, cancelable: true, view: window }));
                  await smartSleep(500);
                  const menuButton = document.querySelector('.table-menu-button.icon.icon-more-vertical');
                  if (menuButton) { menuButton.click(); await smartSleep(); }
                  let infoButton = null;
                  const allMenuLinks = document.querySelectorAll('.addon-menu a');
                  for (const link of allMenuLinks) {
                    const iconElement = link.querySelector('.icon-info');
                    if (iconElement || link.textContent.trim().includes('Info')) { infoButton = link; break; }
                  }
                  if (infoButton) {
                    infoButton.click();
                    await smartSleep(1000);
                    const infoModal = document.querySelector('.modal-container.layer-2');
                    if (infoModal) {
                      const detailedInfo = {};
                      const transactionTypeElement = infoModal.querySelector('.modal-header .transaction-type.text-label');
                      if (transactionTypeElement) detailedInfo.transactionType = transactionTypeElement.textContent.trim();
                      const tradingSymbolElement = infoModal.querySelector('.modal-header .tradingsymbol');
                      if (tradingSymbolElement) detailedInfo.tradingSymbol = tradingSymbolElement.textContent.trim();
                      const headerStatusElement = infoModal.querySelector('.modal-header .order-status-label');
                      if (headerStatusElement) detailedInfo.orderStatus = headerStatusElement.textContent.trim();
                      const orderIdElement = infoModal.querySelector('.order-id');
                      if (orderIdElement) detailedInfo.orderId = orderIdElement.textContent.trim();
                      const exchangeOrderIdElement = infoModal.querySelector('.exchange-order-id');
                      if (exchangeOrderIdElement) detailedInfo.exchangeOrderId = exchangeOrderIdElement.textContent.trim();
                      const orderTimestampElement = infoModal.querySelector('.order-timestamp');
                      if (orderTimestampElement) detailedInfo.orderTimestamp = orderTimestampElement.textContent.trim();
                      Object.keys(detailedInfo).forEach(k => { if (detailedInfo[k] === null || detailedInfo[k] === '') delete detailedInfo[k]; });
                      orderData.detailedInfo = detailedInfo;
                      let closeButton = null;
                      const allButtons = infoModal.querySelectorAll('.modal-footer .button');
                      for (const button of allButtons) { if (button.textContent.trim() === 'Close') { closeButton = button; break; } }
                      if (closeButton) { closeButton.click(); await smartSleep(500); } else {
                        const allModalButtons = infoModal.querySelectorAll('button');
                        for (const button of allModalButtons) { if (button.textContent.trim() === 'Close') { closeButton = button; break; } }
                        if (closeButton) { closeButton.click(); await smartSleep(500); } else { document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape' })); await smartSleep(500); }
                      }
                    }
                  }
                  menuColumn.dispatchEvent(new MouseEvent('mouseleave', { bubbles: true, cancelable: true, view: window }));
                }
              } catch (detailError) {
                orderData.detailedInfo = { error: detailError.message };
              }

              collected.push(orderData);
            } catch (err) {
              console.warn('[GetCompletedOrders] bottom-scan row error:', err);
              continue;
            }
          }
          collected.reverse();
          return {
            success: true,
            message: `Retrieved ${collected.length} completed orders (bottom boundary scan)`,
            data: { count: collected.length, orders: collected }
          };
        }

        // Iterate through each row to extract order details
        for (let i = 0; i < orderRows.length; i++) {
          const row = orderRows[i];
          if (scanDirection === 'top' && stopAtKeyTop) {
            const rowKey = buildKey(row);
            if (rowKey === stopAtKeyTop) {
              console.log('[GetCompletedOrders] Reached stopAtKeyTop; stopping scan early at index', i);
              break;
            }
          }
          
          try {
            // Extract basic order details from the row
            const timeElement = row.querySelector('.order-timestamp');
            const typeElement = row.querySelector('.transaction-type .text-label');
            const symbolElement = row.querySelector('.instrument .tradingsymbol');
            const exchangeElement = row.querySelector('.instrument .exchange');
            const productElement = row.querySelector('.product');
            const quantityElement = row.querySelector('.quantity');
            const avgPriceElement = row.querySelector('.average-price');
            const statusElement = row.querySelector('.order-status .order-status-label');

            if (!timeElement || !typeElement || !symbolElement || !exchangeElement || 
                !productElement || !quantityElement || !statusElement) {
              continue; // Skip rows that don't have all required elements
            }

            const time = timeElement.textContent.trim();
            const type = typeElement.textContent.trim();
            const symbol = symbolElement.textContent.trim();
            const exchange = exchangeElement.textContent.trim();
            const product = productElement.textContent.trim();
            const quantity = quantityElement.textContent.trim();
            const avgPrice = avgPriceElement ? avgPriceElement.textContent.trim() : '';
            const status = statusElement.textContent.trim();

            // Parse quantity to extract filled and total quantities
            let filledQty = 0;
            let totalQty = 0;
            if (quantity.includes('/')) {
              const parts = quantity.split('/');
              filledQty = parseInt(parts[0].trim()) || 0;
              totalQty = parseInt(parts[1].trim()) || 0;
            } else {
              // For fully executed orders it might be a single number
              filledQty = parseInt(quantity.trim()) || 0;
              totalQty = filledQty;
            }

            // Initialize order data with basic information
            const orderData = {
              time: time,
              type: type,
              symbol: symbol,
              exchange: exchange,
              product: product,
              quantity: {
                filled: filledQty,
                total: totalQty,
                remaining: Math.max(0, totalQty - filledQty)
              },
              avgPrice: parseFloat(avgPrice) || 0,
              status: status,
              detailedInfo: null
            };

            try {
              // Find the menu column for this row (last column)
              const menuColumn = row.querySelector('.col-menu');
              if (menuColumn) {
                console.log(`Hovering over menu column for ${symbol}...`);
                
                // Trigger hover event on the menu column
                menuColumn.dispatchEvent(new MouseEvent('mouseenter', {
                  bubbles: true,
                  cancelable: true,
                  view: window
                }));
                
                await smartSleep(500); // Wait for hover menu to appear
                
                // Click on the menu button (three dots)
                const menuButton = document.querySelector('.table-menu-button.icon.icon-more-vertical');
                if (menuButton) {
                  menuButton.click();
                  await smartSleep(); // Wait for dropdown to fully open
                }
                
                // Click on the Info button from the dropdown menu
                let infoButton = null;
                
                // Try to find the Info button by looking for the icon-info class
                const allMenuLinks = document.querySelectorAll('.addon-menu a');
                for (const link of allMenuLinks) {
                  const iconElement = link.querySelector('.icon-info');
                  if (iconElement || link.textContent.trim().includes('Info')) {
                    infoButton = link;
                    break;
                  }
                }
                
                if (infoButton) {
                  console.log(`Clicking Info button for ${symbol}...`);
                  infoButton.click();
                  await smartSleep(1000); // Wait for info modal to open
                  
                  // Extract detailed information from the info modal/popup
                  const infoModal = document.querySelector('.modal-container.layer-2');
                  
                  if (infoModal) {
                    console.log(`Extracting detailed info for ${symbol}...`);
                    
                    // Extract various details from the info modal using specific selectors
                    const detailedInfo = {};
                    
                    // Extract from modal header
                    const transactionTypeElement = infoModal.querySelector('.modal-header .transaction-type.text-label');
                    if (transactionTypeElement) {
                      detailedInfo.transactionType = transactionTypeElement.textContent.trim();
                    }
                    
                    const tradingSymbolElement = infoModal.querySelector('.modal-header .tradingsymbol');
                    if (tradingSymbolElement) {
                      detailedInfo.tradingSymbol = tradingSymbolElement.textContent.trim();
                    }
                    
                    const headerStatusElement = infoModal.querySelector('.modal-header .order-status-label');
                    if (headerStatusElement) {
                      detailedInfo.orderStatus = headerStatusElement.textContent.trim();
                    }
                    
                    // Extract from left column (order details)
                    const extractFieldValue = (labelText) => {
                      const rows = infoModal.querySelectorAll('.order-info-body .row');
                      for (const infoRow of rows) {
                        const label = infoRow.querySelector('label');
                        if (label && label.textContent.trim() === labelText) {
                          const valueElement = infoRow.querySelector('.seven.columns .price, .seven.columns .order-type, .seven.columns .product, .seven.columns span, .seven.columns .trigger-price, .seven.columns');
                          if (valueElement) {
                            return valueElement.textContent.trim();
                          }
                        }
                      }
                      return null;
                    };
                    
                    // Left column fields
                    detailedInfo.quantity = extractFieldValue('Quantity');
                    detailedInfo.price = extractFieldValue('Price');
                    detailedInfo.avgPrice = extractFieldValue('Avg. price');
                    detailedInfo.triggerPrice = extractFieldValue('Trigger price');
                    detailedInfo.orderType = extractFieldValue('Order type');
                    detailedInfo.product = extractFieldValue('Product');
                    detailedInfo.validity = extractFieldValue('Validity');
                    detailedInfo.terminalId = extractFieldValue('Terminal ID');
                    
                    // Right column fields - using specific selectors
                    const orderIdElement = infoModal.querySelector('.order-id');
                    if (orderIdElement) {
                      detailedInfo.orderId = orderIdElement.textContent.trim();
                    }
                    
                    const exchangeOrderIdElement = infoModal.querySelector('.exchange-order-id');
                    if (exchangeOrderIdElement) {
                      detailedInfo.exchangeOrderId = exchangeOrderIdElement.textContent.trim();
                    }
                    
                    const orderTimestampElement = infoModal.querySelector('.order-timestamp');
                    if (orderTimestampElement) {
                      detailedInfo.orderTimestamp = orderTimestampElement.textContent.trim();
                    }
                    
                    const exchangeTimestampElement = infoModal.querySelector('.exchange-timestamp');
                    if (exchangeTimestampElement) {
                      detailedInfo.exchangeTimestamp = exchangeTimestampElement.textContent.trim();
                    }
                    
                    const placedByElement = infoModal.querySelector('.placed-by');
                    if (placedByElement) {
                      detailedInfo.placedBy = placedByElement.textContent.trim();
                    }
                    
                    // Clean up null values
                    Object.keys(detailedInfo).forEach(key => {
                      if (detailedInfo[key] === null || detailedInfo[key] === '') {
                        delete detailedInfo[key];
                      }
                    });
                    
                    orderData.detailedInfo = detailedInfo;
                    console.log(`Detailed info extracted for ${symbol}:`, detailedInfo);
                    
                    // Close the info modal
                    let closeButton = null;
                    
                    // First try to find the Close button by text content
                    const allButtons = infoModal.querySelectorAll('.modal-footer .button');
                    for (const button of allButtons) {
                      if (button.textContent.trim() === 'Close') {
                        closeButton = button;
                        break;
                      }
                    }
                    
                    if (closeButton) {
                      closeButton.click();
                      await smartSleep(500);
                    } else {
                      // Fallback: Try to find any button with "Close" text anywhere in the modal
                      const allModalButtons = infoModal.querySelectorAll('button');
                      for (const button of allModalButtons) {
                        if (button.textContent.trim() === 'Close') {
                          closeButton = button;
                          break;
                        }
                      }
                      
                      if (closeButton) {
                        closeButton.click();
                        await smartSleep(500);
                      } else {
                        // Try pressing Escape key to close modal
                        document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape' }));
                        await smartSleep(500);
                      }
                    }
                  } else {
                    console.warn(`Info modal not found for ${symbol}`);
                  }
                } else {
                  console.warn(`Info button not found for ${symbol}`);
                }
                
                // Clear any hover states
                menuColumn.dispatchEvent(new MouseEvent('mouseleave', {
                  bubbles: true,
                  cancelable: true,
                  view: window
                }));
                
              } else {
                console.warn(`Menu column not found for ${symbol}`);
              }
            } catch (detailError) {
              console.warn(`Error extracting detailed info for ${symbol}:`, detailError);
              orderData.detailedInfo = { error: detailError.message };
            }

            completedOrders.push(orderData);
            console.log("Added completed/executed order:", orderData);
          } catch (rowError) {
            console.warn("Error processing completed order row:", rowError);
            continue;
          }
        }

        console.log(`Found ${completedOrders.length} completed/executed orders with detailed information`);
        return { 
          success: true, 
          message: `Retrieved ${completedOrders.length} completed/executed orders with detailed information successfully`,
          data: {
            count: completedOrders.length,
            orders: completedOrders
          }
        };

      } catch (error) {
        console.error("GetCompletedOrders failed:", error);
        return { success: false, message: `GetCompletedOrders failed: ${error.message}` };
      }
    },
    async NavigateToOrders() {
      try {
        await navigate("orders");
        await smartSleep(200);
        return { success: true, message: "Navigated to orders page" };
      } catch (e) {
        return { success: false, message: "NavigateToOrders failed: " + e.message };
      }
    },
    async ShowMonitoringBanner(args) {
      try {
        const text = (args && args.text) || 'SmartAgent: Login Monitor';
        const bg = (args && args.bg) || '#0b3d91';
        const fg = (args && args.fg) || '#ffffff';

        let el = document.getElementById('smartagent-login-monitor-banner');
        if (!el) {
          el = document.createElement('div');
          el.id = 'smartagent-login-monitor-banner';
          el.style.position = 'fixed';
          el.style.top = '0';
          el.style.left = '0';
          el.style.right = '0';
          el.style.zIndex = '2147483647';
          el.style.pointerEvents = 'none';
          el.style.padding = '6px 10px';
          el.style.fontFamily = 'Inter, system-ui, -apple-system, Segoe UI, Roboto, sans-serif';
          el.style.fontSize = '12px';
          el.style.fontWeight = '600';
          el.style.textAlign = 'center';
          el.style.letterSpacing = '0.2px';
          el.style.boxShadow = '0 1px 6px rgba(0,0,0,0.12)';
          document.documentElement.appendChild(el);
        }
        el.style.background = bg;
        el.style.color = fg;
        el.textContent = text;

        return { success: true, message: 'Monitoring banner shown' };
      } catch (e) {
        return { success: false, message: 'ShowMonitoringBanner failed: ' + e.message };
      }
    },
    async HideMonitoringBanner() {
      try {
        const el = document.getElementById('smartagent-login-monitor-banner');
        if (el && el.parentNode) el.parentNode.removeChild(el);
        return { success: true, message: 'Monitoring banner hidden' };
      } catch (e) {
        return { success: false, message: 'HideMonitoringBanner failed: ' + e.message };
      }
    },
    async GetOrdersSummary() {
      try {
        console.log('[GetOrdersSummary] Navigating to orders page');
        await navigate("orders");
        await smartSleep(200);

        const buildKey = (row) => {
          const time = row.querySelector('.order-timestamp')?.textContent?.trim() || '';
          const type = row.querySelector('.transaction-type .text-label')?.textContent?.trim() || '';
          const symbol = row.querySelector('.instrument .tradingsymbol')?.textContent?.trim() || '';
          const exchange = row.querySelector('.instrument .exchange')?.textContent?.trim() || '';
          const product = row.querySelector('.product')?.textContent?.trim() || '';
          const quantity = row.querySelector('.quantity')?.textContent?.trim() || '';
          const status = row.querySelector('.order-status .order-status-label')?.textContent?.trim() || '';
          const price = row.querySelector('.average-price')?.textContent?.trim() || row.querySelector('.last-price')?.textContent?.trim() || '';
          return [symbol, type, exchange, product, quantity, status, time, price].join('|');
        };

        const summarize = (selector) => {
          const tbody = document.querySelector(selector);
          if (!tbody) {
            console.warn(`[GetOrdersSummary] Selector not found: ${selector}. Treating as empty.`);
          }
          const rows = tbody ? Array.from(tbody.querySelectorAll('tr')) : [];
          const keys = rows.map(buildKey);
          const count = keys.length;
          const firstKey = count > 0 ? keys[0] : null;
          const lastKey = count > 0 ? keys[count - 1] : null;
          const contentHash = JSON.stringify([...keys].sort());
          const t = (s) => (s ? (s.length > 120 ? s.slice(0, 120) + '…' : s) : s);
          console.log(`[GetOrdersSummary] ${selector} → rows=${count}, first=${t(firstKey)}, last=${t(lastKey)}, hashLen=${contentHash.length}`);
          return { count, firstKey, lastKey, contentHash };
        };

        const open = summarize('.pending-orders table tbody');
        const completed = summarize('.completed-orders table tbody');

        console.log('[GetOrdersSummary] Final summary:', {
          openCount: open.count,
          completedCount: completed.count
        });

        return { success: true, data: { open, completed } };
      } catch (error) {
        console.error("[GetOrdersSummary] Failed:", error);
        return { success: false, message: `GetOrdersSummary failed: ${error.message}` };
      }
    },
    async IsLoginRequired() {
      try {
        const loginRequired = needsLogin();
        return { success: true, data: { loginRequired }, message: `Login required: ${loginRequired}` };
      } catch (e) {
        return { success: false, message: "IsLoginRequired failed: " + e.message };
      }
    },
    
  };

  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('📨 Content script received message:', request);
    
    if (request.type === "PERFORM_SITE_ACTIONS") {
      console.log('🎯 Processing PERFORM_SITE_ACTIONS:', request.actions);
      
      (async () => {
        try {
          // Load timeout settings from shared config
          const timeoutSettings = await getTimeoutSettings();
          
          // Check if this is a monitoring operation that needs extended timeout
          const hasMonitoringAction = request.actions.some(action => 
            ['MonitorConditionThenAct', 'MONITORPROFIT', 'MonitorSymbolFromWatchlist'].includes(action.action)
          );
          
          // Remove general content-script timeout; monitoring actions manage their own timeouts
          let timeout = null;
          if (hasMonitoringAction) {
            const timeoutDuration = timeoutSettings.content_script_monitoring_timeout_seconds * 1000;
            console.log(`Using timeout: ${timeoutDuration/1000}s for monitoring action`);
            // Add timeout only for monitoring to avoid indefinite hangs
            timeout = setTimeout(() => {
              console.error(`⏰ Content script monitoring execution timeout after ${timeoutDuration/1000} seconds`);
              try {
                sendResponse({
                  success: false,
                  message: `Content script monitoring execution timed out after ${timeoutDuration/1000} seconds`,
                  error: 'TIMEOUT'
                });
              } catch (e) {
                console.error('Failed to send timeout response:', e);
              }
            }, timeoutDuration);
          } else {
            console.log('No general content-script timeout applied for regular actions');
          }
          
          try {
            const results = [];
            let overallSuccess = true;
            let abortMessage = "";
            
            for (const actionData of request.actions) {
              console.log(`🔧 Executing action: ${actionData.action} with args:`, actionData.arguments);
              
              const fn = findActionCaseInsensitive(actionData.action);
              if (!fn) {
                console.error(`❌ Unsupported action: ${actionData.action}`);
                results.push({ success: false, message: `Unsupported action: ${actionData.action}` });
                overallSuccess = false;
                abortMessage = `Unsupported action: ${actionData.action}`;
                break;
              }
              
              // For monitoring actions, clear the general timeout and let the action manage its own timeout
              if (['MonitorConditionThenAct', 'MONITORPROFIT', 'MonitorSymbolFromWatchlist'].includes(actionData.action)) {
                clearTimeout(timeout);
                console.log(`🔍 Monitoring action detected - disabling general timeout, action will manage its own timeout`);
              }
              
              const result = await fn(actionData.arguments);
              console.log(`✅ Action ${actionData.action} result:`, result);
              results.push(result);
              
              if (!result.success) {
                overallSuccess = false;
                abortMessage = result.message;
                break;
              }
            }
            
            clearTimeout(timeout);
            
            const response = {
              success: overallSuccess,
              results: results,
              abortMessage: abortMessage
            };
            
            console.log('🎉 Sending final response:', response);
            sendResponse(response);
          } catch (error) {
            clearTimeout(timeout);
            console.error('❌ Error during action execution:', error);
            sendResponse({
              success: false,
              message: `Action execution failed: ${error.message}`,
              error: error.toString()
            });
          }
      } catch (configError) {
        console.error('❌ Error loading timeout config:', configError);
        sendResponse({
          success: false,
          message: `Failed to load timeout configuration: ${configError.message}`,
          error: 'CONFIG_ERROR'
        });
      }
      })();
      
      return true; // Keep message channel open for async response
    } else {
      console.log('⚠️ Unknown message type:', request.type);
      sendResponse({ success: false, message: `Unknown message type: ${request.type}` });
    }
  });
})();

