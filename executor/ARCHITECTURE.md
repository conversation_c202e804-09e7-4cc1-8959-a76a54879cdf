# Aagman SmartAgent - Architecture Documentation

## 🏗️ System Overview

The Aagman SmartAgent executor is a sophisticated Chrome extension built with a modular, event-driven architecture designed for high-performance trading automation. The system employs a multi-layered approach with clear separation of concerns, enabling scalable and maintainable code.

## 📐 Architecture Layers

```
┌─────────────────────────────────────────────────────────────┐
│                    User Interface Layer                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐         │
│  │   Side Panel│ │   Popup UI  │ │  Content UI │         │
│  └─────────────┘ └─────────────┘ └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                  Communication Layer                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐         │
│  │ Message Bus │ │ WebSocket   │ │ Event Emitter│         │
│  └─────────────┘ └─────────────┘ └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   Execution Layer                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐         │
│  │Rule Engine  │ │Execution Svc│ │Validation   │         │
│  └─────────────┘ └─────────────┘ └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   Platform Layer                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐         │
│  │Background   │ │Content Script│ │Chrome APIs  │         │
│  └─────────────┘ └─────────────┘ └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Core Components

### 1. Background Script (`background.js`)
**Purpose**: Service worker that manages extension lifecycle and coordinates all operations

**Key Responsibilities**:
- Extension initialization and lifecycle management
- Message routing between components
- Tab management and monitoring
- Login state detection and management
- Rule engine coordination

**Architecture Pattern**: Service Worker with Event-Driven Architecture

```javascript
// Key architectural elements
class BackgroundManager {
  constructor() {
    this.engineController = null;
    this.activeTabs = new Map();
    this.loginMonitor = new LoginMonitor();
  }
  
  async initialize() {
    // Initialize rule engine
    // Start login monitoring
    // Setup message listeners
  }
}
```

### 2. Execution Service (`execution-service.js`)
**Purpose**: High-performance action execution engine with parallel processing capabilities

**Key Features**:
- Parallel tab execution (up to 5 concurrent tabs)
- Graph-based dependency resolution
- Automatic tab pool management
- Login state monitoring
- Error recovery and retry mechanisms

**Architecture Pattern**: Command Pattern with Observer Pattern

```javascript
class ExecutionService {
  constructor() {
    this.activeTabs = new Map();
    this.activeGraphs = new Map();
    this.nodeExecutions = new Map();
    this.tabPool = new Set();
    this.maxConcurrentTabs = 5;
  }
  
  async executeGraphNodeByNode(graphId, actions, options) {
    // Graph-based execution with dependency resolution
  }
  
  async executeInTab(tabId, site, actions) {
    // Single tab execution with error handling
  }
}
```

### 3. Primitive Engine Controller (`primitive-engine-controller.js`)
**Purpose**: Unified rule engine and controller that combines JSON rule engine with execution orchestration

---

## 🔐 Login Status Monitoring & Messaging System

The executor implements a sophisticated login status monitoring system that provides real-time feedback to the frontend about broker authentication status.

### Login Monitor Architecture

```javascript
// execution-service.js - Login monitoring system
class ExecutionService {
  constructor() {
    this.loginMonitor = {
      tabId: null,
      intervalId: null,
      checking: false,
      required: null,
      lastChecked: null,
      pausedGraphsSet: new Set(),
      started: false,
      isDedicated: false,
      creationPromise: null,
      onRemovedHandler: null,
      fetchedThisSession: false,
      lastFetchAt: null
    };
  }
}
```

### Dedicated Monitor Tab

The system maintains a dedicated, pinned, inactive tab on the broker's Orders page to continuously monitor login status:

```javascript
async ensureLoginMonitorTab() {
  const ordersUrl = await this.getOrdersPageUrl();
  
  // Create dedicated tab if not exists
  if (!this.loginMonitor.tabId) {
    this.loginMonitor.creationPromise = chrome.tabs.create({ 
      url: ordersUrl, 
      pinned: true, 
      active: false 
    });
  }
  
  // Ensure content script is injected
  await this.ensureContentScript(this.loginMonitor.tabId, site.contentScript);
}
```

### Status Detection & Transition Management

The monitor checks login status every second and emits messages only on state transitions:

```javascript
async checkLoginAndTogglePause() {
  // Check login status via content script
  const result = await this.sendMessageToTab(this.loginMonitor.tabId, {
    type: 'PERFORM_SITE_ACTIONS',
    actions: [{ action: 'IsLoginRequired', arguments: {} }]
  });
  
  const required = result?.results?.[0]?.data?.loginRequired;
  const previousRequired = this.loginMonitor.required;
  
  // Emit LOGIN_REQUIRED only on transition (false → true)
  if (required === true && previousRequired !== true) {
    chrome.runtime.sendMessage({
      type: 'LOGIN_REQUIRED',
      brokerName,
      targetTab: 'chat'
    });
  }
  
  // Emit LOGIN_RESOLVED only on transition (true → false)
  if (required === false && previousRequired === true) {
    chrome.runtime.sendMessage({
      type: 'LOGIN_RESOLVED',
      brokerName,
      targetTab: 'chat'
    });
  }
}
```

### Background Status Caching

The background script maintains cached login status for frontend queries:

```javascript
// background.js - Status cache management
let cachedLoginStatus = {
  required: null,
  brokerName: null,
  updatedAt: null
};

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  switch (request.type) {
    case 'GET_LOGIN_STATUS':
      sendResponse({
        success: true,
        data: cachedLoginStatus
      });
      break;
      
    case 'LOGIN_REQUIRED':
      cachedLoginStatus = {
        required: true,
        brokerName: request.brokerName,
        updatedAt: Date.now()
      };
      break;
      
    case 'LOGIN_RESOLVED':
      cachedLoginStatus = {
        required: false,
        brokerName: request.brokerName,
        updatedAt: Date.now()
      };
      break;
  }
});
```

### Graph Execution Pause/Resume

When login is required, running automation graphs are automatically paused and resumed when login is restored:

```javascript
// Pause graphs when login required
if (required === true && previousRequired !== true) {
  for (const [graphId, graph] of this.activeGraphs.entries()) {
    if (graph.status === 'running') {
      const res = this.pauseGraph(graphId);
      if (res?.success) {
        this.loginMonitor.pausedGraphsSet.add(graphId);
      }
    }
  }
}

// Resume graphs when login restored
if (required === false && previousRequired === true) {
  for (const graphId of Array.from(this.loginMonitor.pausedGraphsSet)) {
    const res = this.resumeGraph(graphId);
    if (res?.success) {
      this.loginMonitor.pausedGraphsSet.delete(graphId);
    }
  }
}
```

### Content Script Integration

The broker content script provides the actual login detection:

```javascript
// content-scripts/zerodha.js
function needsLogin() {
  return document.querySelector(".login-form form") !== null;
}

async function IsLoginRequired() {
  try {
    const loginRequired = needsLogin();
    return { 
      success: true, 
      data: { loginRequired }, 
      message: `Login required: ${loginRequired}` 
    };
  } catch (e) {
    return { success: false, message: "IsLoginRequired failed: " + e.message };
  }
}
```

### Key Benefits

- **Real-time Detection**: Continuous monitoring via dedicated tab
- **Transition-based Messaging**: Prevents spam by emitting only on state changes
- **Automatic Graph Management**: Pauses/resumes automation based on login status
- **Robust Fallback**: Frontend polling ensures messages are delivered even if broadcasts are missed
- **Non-intrusive**: Dedicated tab doesn't interfere with user's current browsing

This system ensures users receive immediate feedback about their broker authentication status and prevents automation from running when broker actions are unavailable.

## 🔄 Data Flow Architecture

### 1. Message Flow
```
UI Layer → Message Bus → Background Script → Execution Service → Content Scripts
     ↑                                                              ↓
     └─────────────── Status Updates ←──────────────────────────────┘
```

### 2. Execution Flow
```
Rule Engine → Graph Builder → Dependency Resolver → Tab Manager → Action Executor
     ↑                                                                    ↓
     └─────────────── Status Tracker ←────────────────────────────────────┘
```

### 3. State Management Flow
```
User Action → State Change → Event Emission → UI Update → Persistence
     ↑                                                      ↓
     └─────────────── State Recovery ←──────────────────────┘
```

## 🗂️ Module Organization

### Core Modules (`lib/`)

#### Configuration (`config.js`)
- Site-specific configurations
- Action argument definitions
- Environment detection
- Supported sites registry

#### Constants (`constants.js`)
- Message types and protocols
- Action types and categories
- Error codes and messages
- System constants

#### Validation (`validation.js`)
- Input sanitization
- Action validation
- Parameter verification
- Security checks

#### UI Service (`ui-service.js`)
- UI state management
- Component communication
- Event handling
- Theme management

### Content Scripts (`content-scripts/`)

#### Zerodha Integration (`zerodha.js`)
- **Size**: 139KB, 3269 lines
- **Complexity**: High (comprehensive DOM manipulation)
- **Dependencies**: Chrome extension APIs, DOM APIs

#### Login Bridge (`login-bridge.js`)
- **Size**: 3.2KB, 101 lines
- **Complexity**: Low (simple message passing)
- **Dependencies**: Chrome messaging APIs

## 🔐 Security Architecture

### 1. Permission Model
```json
{
  "permissions": [
    "sidePanel",    // UI access
    "storage",      // Data persistence
    "tabs",         // Tab management
    "activeTab",    // Current tab access
    "scripting"     // Content script injection
  ],
  "host_permissions": [
    "https://kite.zerodha.com/*",  // Primary target
    "http://localhost:*/*",         // Development
    "https://*/*"                   // General web access
  ]
}
```

### 2. Data Validation
- Input sanitization for all user inputs
- Action parameter validation
- Cross-site scripting (XSS) prevention
- Content Security Policy (CSP) compliance

### 3. Communication Security
- Message origin verification
- Cross-origin request validation
- Secure WebSocket connections
- Encrypted data transmission

## ⚡ Performance Architecture

### 1. Parallel Processing
- **Concurrent Tab Execution**: Up to 5 tabs simultaneously
- **Tab Pool Management**: Efficient tab reuse
- **Memory Management**: Automatic cleanup of unused tabs
- **Resource Optimization**: Minimal memory footprint

### 2. Caching Strategy
- **Configuration Caching**: Shared config loading
- **Tab State Caching**: Tab status persistence
- **Rule Engine Caching**: Compiled rule caching
- **UI State Caching**: Component state persistence

### 3. Error Handling
- **Graceful Degradation**: Continue operation on partial failures
- **Retry Mechanisms**: Automatic retry with exponential backoff
- **Error Recovery**: State restoration after failures
- **Logging and Monitoring**: Comprehensive error tracking

## 🔄 State Management

### 1. Global State
```javascript
class GlobalState {
  constructor() {
    this.activeGraphs = new Map();
    this.nodeStatuses = new Map();
    this.activeTabs = new Map();
    this.loginState = null;
    this.configuration = null;
  }
}
```

### 2. Component State
- **UI State**: Component-specific state management
- **Execution State**: Action execution status tracking
- **Tab State**: Tab-specific data and status
- **Graph State**: Graph execution progress

### 3. Persistence Strategy
- **Chrome Storage**: Extension data persistence
- **Session Storage**: Temporary state during session
- **Local Storage**: User preferences and settings
- **Memory Storage**: Runtime state management

## 🧪 Testing Architecture

### 1. Unit Testing
- **Module Testing**: Individual component testing
- **Mock Services**: Isolated testing environments
- **Test Coverage**: Comprehensive coverage requirements
- **Automated Testing**: CI/CD integration

### 2. Integration Testing
- **End-to-End Testing**: Full workflow testing
- **Cross-Component Testing**: Component interaction testing
- **Performance Testing**: Load and stress testing
- **Security Testing**: Vulnerability assessment

### 3. Manual Testing
- **User Acceptance Testing**: Real-world scenario testing
- **Browser Compatibility**: Cross-browser testing
- **Platform Testing**: Different Chrome versions
- **Extension Testing**: Chrome Web Store compliance

## 🔧 Configuration Management

### 1. Shared Configuration (`shared-config.json`)
```json
{
  "TIMEOUT_SETTINGS": {
    "execution_service_general_timeout_seconds": 30,
    "execution_service_monitoring_timeout_seconds": 600,
    "background_test_timeout_seconds": 10
  },
  "EXECUTION_SETTINGS": {
    "max_concurrent_tabs": 5,
    "default_action_timeout": 300000,
    "retry_attempts": 3
  }
}
```

### 2. Environment-Specific Config
- **Development**: Local development settings
- **Testing**: Test environment configuration
- **Production**: Production environment settings
- **Staging**: Pre-production validation

## 📊 Monitoring and Logging

### 1. Logging Strategy
- **Structured Logging**: JSON-formatted logs
- **Log Levels**: Debug, Info, Warn, Error
- **Log Rotation**: Automatic log management
- **Performance Metrics**: Execution time tracking

### 2. Monitoring Points
- **Execution Performance**: Action execution times
- **Memory Usage**: Extension memory consumption
- **Error Rates**: Failure frequency tracking
- **User Interactions**: Usage pattern analysis

## 🔄 Deployment Architecture

### 1. Build Process
```bash
# Development build
npm run dev

# Production build
npm run build

# Testing build
npm run test:build
```

### 2. Distribution
- **Chrome Web Store**: Public distribution
- **Manual Installation**: Developer mode installation
- **Enterprise Deployment**: Corporate distribution
- **Beta Testing**: Limited release testing

## 🚀 Scalability Considerations

### 1. Horizontal Scaling
- **Multi-Tab Support**: Parallel execution across tabs
- **Graph Execution**: Complex workflow support
- **Modular Architecture**: Component-based scaling
- **Plugin System**: Extensible functionality

### 2. Performance Optimization
- **Lazy Loading**: On-demand component loading
- **Code Splitting**: Modular code organization
- **Tree Shaking**: Unused code elimination
- **Bundle Optimization**: Minimized bundle sizes

## 🔮 Future Architecture Considerations

### 1. Microservices Integration
- **API Gateway**: Centralized API management
- **Service Discovery**: Dynamic service registration
- **Load Balancing**: Distributed load management
- **Circuit Breakers**: Fault tolerance patterns

### 2. Advanced Features
- **Machine Learning Integration**: AI-powered decision making
- **Real-Time Analytics**: Live performance monitoring
- **Advanced Security**: Enhanced security measures
- **Multi-Platform Support**: Cross-browser compatibility

---

This architecture provides a robust foundation for the Aagman SmartAgent executor, enabling scalable, maintainable, and high-performance trading automation while maintaining security and reliability standards.
