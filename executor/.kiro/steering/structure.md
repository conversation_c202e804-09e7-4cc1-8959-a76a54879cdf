# Project Structure

## Root Level Files

- `manifest.json` - Chrome extension configuration and permissions
- `background.js` - Service worker handling automation coordination and validation
- `popup.html/js/css` - Extension popup UI and logic
- `offscreen.html/js` - Background automation document (future use)
- `README.md` - Project documentation

## Directory Organization

### `/content-scripts/`
Platform-specific automation scripts that interact with trading site DOMs:
- `zerodha.js` - Kite by Zerodha automation logic
- Future platform scripts follow naming: `{platform}.js`

### `/lib/`
Shared utilities and configuration:
- `shared-config.json` - Central configuration (sites, actions, message types)
- `config.js` - Configuration loading utilities
- `validation.js` - Input validation logic
- `message-types.js` - Message type definitions (fallback)

### `/icons/`
Extension assets:
- `icon128.png` - Extension icon (128x128)

### `/.kiro/`
Kiro AI assistant configuration and steering documents

## Architecture Flow

1. **User Input**: Popup collects JSON actions and automation mode
2. **Background Coordination**: Service worker validates and routes execution
3. **Content Script Injection**: Platform-specific scripts injected based on site detection
4. **DOM Automation**: Content scripts perform actual trading actions
5. **Response Chain**: Results flow back through message passing

## Naming Conventions

- **Files**: kebab-case for multi-word files (`shared-config.json`)
- **Directories**: kebab-case (`content-scripts/`)
- **Variables**: camelCase in JavaScript
- **Constants**: UPPER_SNAKE_CASE for configuration keys
- **Platform Scripts**: Use platform name as filename (`zerodha.js`)

## Configuration Pattern

All site support and action definitions centralized in `lib/shared-config.json`:
- `SUPPORTED_SITES` - Platform configurations with URL patterns
- `ACTION_ARGUMENTS` - Required parameters for each action type
- `MESSAGE_TYPES` - Inter-script communication message types