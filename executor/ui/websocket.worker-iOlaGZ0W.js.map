{"version": 3, "file": "websocket.worker-iOlaGZ0W.js", "sources": ["src/workers/websocket.worker.ts"], "sourcesContent": ["type WorkerMessage = {\n  type: \"CONNECT\" | \"DISCONNECT\" | \"SEND_MESSAGE\" | \"NETWORK_STATUS_CHANGE\";\n  payload: any;\n};\n\ntype MainThreadMessage = {\n  type: \"CONNECTION_STATUS\" | \"WEBSOCKET_MESSAGE\" | \"ERROR\";\n  payload: any;\n};\n\n// Declare Vite-defined variables\ndeclare const __WS_URL__: string;\ndeclare const __WS_CHAT_ENDPOINT__: string;\n\n// Read from Vite-defined variables\nconst wsUrl = __WS_URL__;\nconst wsEndpoint = __WS_CHAT_ENDPOINT__;\n\n// Check if we're in mock mode - FORCE TO FALSE FOR DEBUGGING\nconst isMockMode = false; // Always use real WebSocket, never mock\n\nclass WebSocketWorker {\n  private ws: WebSocket | null = null;\n  private reconnectAttempts = 0;\n  private maxReconnectAttempts = 5;\n  private reconnectTimeout = 1000;\n  private isReconnecting = false;\n  private userId: string = \"\";\n  private firebaseToken: string | null = null;\n  private isConnected = false;\n  private messageQueue: any[] = [];\n  private lastMessageTime: any | null = null;\n  private lastNetworkStatus: boolean | null = null;\n  private currentTab: \"chat\" | \"orders\" | \"monitoring\" = \"chat\";\n\n  constructor() {\n    this.lastNetworkStatus = navigator.onLine;\n    self.onmessage = this.handleMessage.bind(this);\n    console.log(\"🚀 [Worker] WebSocket Worker STARTED successfully!\");\n    console.log(\"[Worker] Initialized with:\", { wsUrl, wsEndpoint });\n    console.log(\"[Worker] Ready to receive messages from main thread\");\n  }\n\n  private handleMessage(event: MessageEvent<WorkerMessage>) {\n    const { type, payload } = event.data;\n    console.log(\"[Worker] Received message:\", { type, payload });\n\n    switch (type) {\n      case \"CONNECT\":\n        this.userId = payload.userId;\n        this.firebaseToken = payload.token || null;\n        console.log(\"[Worker] Connecting with userId and token:\", {\n          userId: this.userId,\n          hasToken: !!this.firebaseToken,\n        });\n        this.connect();\n        break;\n\n      case \"DISCONNECT\":\n        console.log(\"[Worker] Disconnecting\");\n        this.disconnect();\n        break;\n\n      case \"SEND_MESSAGE\":\n        console.log(\"[Worker] Sending message:\", payload);\n        // Update current tab for error tracking\n        if (payload.typeOfMessage) {\n          this.currentTab = payload.typeOfMessage;\n        }\n        if (!this.isConnected) {\n          console.log(\"[Worker] Not connected, queueing message\");\n          this.messageQueue.push(payload);\n          this.connect(); // Try to reconnect\n        } else {\n          this.sendMessage(payload);\n        }\n        break;\n\n      case \"NETWORK_STATUS_CHANGE\":\n        console.log(\"[Worker] Network status changed:\", payload.isOnline);\n\n        // Only process if the status actually changed\n        if (this.lastNetworkStatus === payload.isOnline) {\n          console.log(\"[Worker] Network status unchanged, skipping\");\n          return;\n        }\n\n        this.lastNetworkStatus = payload.isOnline;\n\n        if (\n          payload.isOnline &&\n          !this.isConnected &&\n          !this.isReconnecting &&\n          this.ws?.readyState !== WebSocket.CONNECTING\n        ) {\n          console.log(\n            \"[Worker] Network is back online, attempting to reconnect\"\n          );\n          this.connect();\n        } else if (!payload.isOnline) {\n          console.log(\"[Worker] Network is offline\");\n          this.isConnected = false;\n        }\n        break;\n    }\n  }\n\n  private connect() {\n    // If in mock mode, don't attempt WebSocket connection\n    if (isMockMode) {\n      console.log(\"[Worker] Mock mode enabled, skipping WebSocket connection\");\n      this.postMessage({\n        type: \"CONNECTION_STATUS\",\n        payload: { isConnected: false, mockMode: true },\n      });\n      return;\n    }\n\n    try {\n      if (this.ws) {\n        console.log(\"[Worker] WebSocket already exists, checking state...\");\n        if (this.ws.readyState === WebSocket.OPEN && this.isConnected) {\n          console.log(\"[Worker] WebSocket already connected\");\n          return;\n        }\n        if (this.ws.readyState === WebSocket.CONNECTING) {\n          console.log(\"[Worker] WebSocket is already connecting, skipping\");\n          return;\n        }\n        // If not open, close it so we can create a new one\n        this.ws.close();\n        this.ws = null;\n      }\n\n      // Build WebSocket URL with Firebase token if available\n      let fullUrl = `${wsUrl}${wsEndpoint}?user_id=${this.userId}`;\n      if (this.firebaseToken) {\n        fullUrl += `&token=${encodeURIComponent(this.firebaseToken)}`;\n      }\n      console.log(\n        \"[Worker] Connecting to:\",\n        fullUrl.replace(/token=[^&]+/, \"token=***\")\n      );\n\n      this.ws = new WebSocket(fullUrl);\n\n      this.ws.onopen = () => {\n        console.log(\"[Worker] WebSocket connected\");\n        this.isConnected = true;\n        this.reconnectAttempts = 0;\n        this.reconnectTimeout = 1000;\n        this.isReconnecting = false;\n\n        this.postMessage({\n          type: \"CONNECTION_STATUS\",\n          payload: { isConnected: true },\n        });\n\n        // Process any queued messages\n        while (this.messageQueue.length > 0) {\n          const message = this.messageQueue.shift();\n          if (message) {\n            this.sendMessage(message);\n          }\n        }\n      };\n\n      this.ws.onmessage = (event) => {\n        try {\n          const data = JSON.parse(event.data);\n          console.log(\"[Worker] Received WebSocket message:\", data);\n\n          // Check if this is an error response from the backend\n          if (data.error || data.status === \"error\") {\n            console.log(\"[Worker] Backend error detected:\", data);\n            this.postMessage({\n              type: \"ERROR\",\n              payload: {\n                message: data.error || \"Backend error occurred\",\n                details: data.details || data,\n                tab: this.currentTab, // Include current tab for error routing\n              },\n            });\n          } else {\n            // Include the current tab information in the response\n            this.postMessage({\n              type: \"WEBSOCKET_MESSAGE\",\n              payload: {\n                ...data,\n                typeOfMessage: this.currentTab,\n              },\n            });\n          }\n        } catch (error) {\n          console.error(\"[Worker] Failed to parse message:\", error);\n          this.postMessage({\n            type: \"ERROR\",\n            payload: {\n              message: \"Failed to parse message\",\n              tab: this.currentTab,\n            },\n          });\n        }\n      };\n\n      this.ws.onclose = (event) => {\n        console.log(\"[Worker] WebSocket closed:\", event);\n        this.isConnected = false;\n\n        this.postMessage({\n          type: \"CONNECTION_STATUS\",\n          payload: { isConnected: false, code: event.code },\n        });\n\n        // Only attempt to reconnect if it wasn't a clean close\n        if (!event.wasClean && !this.isReconnecting) {\n          this.attemptReconnect();\n        }\n      };\n\n      this.ws.onerror = (error) => {\n        console.error(\"[Worker] WebSocket error:\", error);\n\n        // Only send error if we're not already connected or connecting\n        if (!this.isConnected && this.ws?.readyState !== WebSocket.OPEN) {\n          this.isConnected = false;\n          this.postMessage({\n            type: \"ERROR\",\n            payload: {\n              message: \"WebSocket connection failed\",\n              url: `${wsUrl}${wsEndpoint}`,\n              timestamp: Date.now(),\n              connectionAttempt: this.reconnectAttempts + 1,\n              tab: this.currentTab,\n              details: {\n                readyState: this.ws?.readyState,\n                readyStateText: this.ws\n                  ? this.ws.readyState === WebSocket.CONNECTING\n                    ? \"CONNECTING\"\n                    : this.ws.readyState === WebSocket.OPEN\n                      ? \"OPEN\"\n                      : this.ws.readyState === WebSocket.CLOSING\n                        ? \"CLOSING\"\n                        : this.ws.readyState === WebSocket.CLOSED\n                          ? \"CLOSED\"\n                          : \"UNKNOWN\"\n                  : \"UNKNOWN\",\n                isOnline: navigator.onLine,\n                userId: this.userId || \"\",\n                lastMessageTime: this.lastMessageTime || null,\n              },\n            },\n          });\n        }\n      };\n    } catch (error) {\n      console.error(\"[Worker] Connection error:\", error);\n      this.isConnected = false;\n      this.postMessage({\n        type: \"ERROR\",\n        payload: {\n          message:\n            error instanceof Error\n              ? error.message\n              : \"Connection error occurred\",\n          timestamp: Date.now(),\n          tab: this.currentTab,\n        },\n      });\n    }\n  }\n\n  private attemptReconnect() {\n    if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n      console.log(\"[Worker] Max reconnection attempts reached\");\n      this.postMessage({\n        type: \"CONNECTION_STATUS\",\n        payload: {\n          isConnected: false,\n          message: \"Max reconnection attempts reached\",\n        },\n      });\n      return;\n    }\n\n    this.isReconnecting = true;\n    this.reconnectAttempts++;\n\n    console.log(\n      `[Worker] Attempting reconnect ${this.reconnectAttempts}/${this.maxReconnectAttempts}`\n    );\n    this.postMessage({\n      type: \"CONNECTION_STATUS\",\n      payload: {\n        isConnected: false,\n        attempt: this.reconnectAttempts,\n      },\n    });\n\n    setTimeout(() => {\n      this.connect();\n      this.reconnectTimeout *= 2; // Exponential backoff\n    }, this.reconnectTimeout);\n  }\n\n  private sendMessage(message: any) {\n    if (!this.ws) {\n      console.log(\"[Worker] No WebSocket instance\");\n      this.messageQueue.push(message);\n      this.connect();\n      return;\n    }\n\n    if (!this.isConnected || this.ws.readyState !== WebSocket.OPEN) {\n      console.log(\n        \"[Worker] WebSocket not connected, readyState:\",\n        this.ws.readyState,\n        \"isConnected:\",\n        this.isConnected\n      );\n\n      // Queue the message\n      console.log(\"[Worker] Queueing message\");\n      this.messageQueue.push(message);\n\n      // If we're CONNECTING, just wait for onopen\n      if (this.ws.readyState === WebSocket.CONNECTING) {\n        console.log(\"[Worker] WebSocket is connecting, message queued\");\n        return;\n      }\n\n      // If CLOSED or CLOSING, try to reconnect\n      if (this.ws.readyState >= WebSocket.CLOSING) {\n        console.log(\n          \"[Worker] WebSocket is closed/closing, attempting to reconnect...\"\n        );\n        this.connect();\n      }\n      return;\n    }\n\n    try {\n      console.log(\"[Worker] Sending WebSocket message:\", message);\n      this.ws.send(JSON.stringify(message));\n    } catch (error) {\n      console.error(\"[Worker] Send error:\", error);\n      this.messageQueue.push(message);\n      this.postMessage({\n        type: \"ERROR\",\n        payload: {\n          message:\n            error instanceof Error ? error.message : \"Send error occurred\",\n          timestamp: Date.now(),\n          tab: this.currentTab,\n        },\n      });\n    }\n  }\n\n  private disconnect() {\n    if (this.ws) {\n      console.log(\"[Worker] Closing WebSocket connection\");\n      this.ws.close(1000, \"Normal closure\");\n      this.ws = null;\n    }\n    this.isConnected = false;\n    this.isReconnecting = false;\n    this.userId = \"\";\n    this.messageQueue = [];\n  }\n\n  private postMessage(message: MainThreadMessage) {\n    console.log(\"[Worker] Posting message to main thread:\", message);\n    (self as any).postMessage(message);\n  }\n}\n\n// Initialize the worker\nnew WebSocketWorker();\n\n// Export empty object to make it a module\nexport {};\n"], "names": ["wsUrl", "wsEndpoint", "constructor", "__publicField", "this", "lastNetworkStatus", "navigator", "onLine", "self", "onmessage", "handleMessage", "bind", "console", "log", "event", "_a", "type", "payload", "data", "userId", "firebaseToken", "token", "hasToken", "connect", "disconnect", "typeOfMessage", "currentTab", "isConnected", "sendMessage", "messageQueue", "push", "isOnline", "isReconnecting", "ws", "readyState", "WebSocket", "CONNECTING", "OPEN", "close", "fullUrl", "encodeURIComponent", "replace", "onopen", "reconnectAttempts", "reconnectTimeout", "postMessage", "length", "message", "shift", "JSON", "parse", "error", "status", "details", "tab", "onclose", "code", "<PERSON><PERSON><PERSON>", "attemptReconnect", "onerror", "_b", "url", "timestamp", "Date", "now", "connectionAttempt", "readyStateText", "CLOSING", "CLOSED", "lastMessageTime", "Error", "maxReconnectAttempts", "attempt", "setTimeout", "send", "stringify"], "mappings": "0JAeA,MAAMA,EAAQ,sBACRC,EAAa,kBA0WnB,IArWA,MAcE,WAAAC,GAbQC,EAAAC,KAAA,KAAuB,MACvBD,EAAAC,KAAA,oBAAoB,GACpBD,EAAAC,KAAA,uBAAuB,GACvBD,EAAAC,KAAA,mBAAmB,KACnBD,EAAAC,KAAA,kBAAiB,GACjBD,EAAAC,KAAA,SAAiB,IACjBD,EAAAC,KAAA,gBAA+B,MAC/BD,EAAAC,KAAA,eAAc,GACdD,EAAAC,KAAA,eAAsB,IACtBD,EAAAC,KAAA,kBAA8B,MAC9BD,EAAAC,KAAA,oBAAoC,MACpCD,EAAAC,KAAA,aAA+C,QAGrDA,KAAKC,kBAAoBC,UAAUC,OACnCC,KAAKC,UAAYL,KAAKM,cAAcC,KAAKP,MACzCQ,QAAQC,IAAI,sDACZD,QAAQC,IAAI,6BAA8B,CAAEb,QAAOC,eACnDW,QAAQC,IAAI,sDACd,CAEQ,aAAAH,CAAcI,GA5BxB,IAAAC,EA6BI,MAAMC,KAAEA,EAAAC,QAAMA,GAAYH,EAAMI,KAGhC,OAFAN,QAAQC,IAAI,6BAA8B,CAAEG,OAAMC,YAE1CD,GACN,IAAK,UACHZ,KAAKe,OAASF,EAAQE,OACtBf,KAAKgB,cAAgBH,EAAQI,OAAS,KACtCT,QAAQC,IAAI,6CAA8C,CACxDM,OAAQf,KAAKe,OACbG,WAAYlB,KAAKgB,gBAEnBhB,KAAKmB,UACL,MAEF,IAAK,aACHX,QAAQC,IAAI,0BACZT,KAAKoB,aACL,MAEF,IAAK,eACHZ,QAAQC,IAAI,4BAA6BI,GAErCA,EAAQQ,gBACVrB,KAAKsB,WAAaT,EAAQQ,eAEvBrB,KAAKuB,YAKRvB,KAAKwB,YAAYX,IAJjBL,QAAQC,IAAI,4CACZT,KAAKyB,aAAaC,KAAKb,GACvBb,KAAKmB,WAIP,MAEF,IAAK,wBAIH,GAHAX,QAAQC,IAAI,mCAAoCI,EAAQc,UAGpD3B,KAAKC,oBAAsBY,EAAQc,SAErC,YADAnB,QAAQC,IAAI,+CAIdT,KAAKC,kBAAoBY,EAAQc,UAG/Bd,EAAQc,UACP3B,KAAKuB,aACLvB,KAAK4B,iBACN,OAAAjB,EAAAX,KAAK6B,SAAL,EAAAlB,EAASmB,cAAeC,UAAUC,WAMxBnB,EAAQc,WAClBnB,QAAQC,IAAI,+BACZT,KAAKuB,aAAc,IANnBf,QAAQC,IACN,4DAEFT,KAAKmB,WAOb,CAEQ,OAAAA,GAWN,IACE,GAAInB,KAAK6B,GAAI,CAEX,GADArB,QAAQC,IAAI,wDACRT,KAAK6B,GAAGC,aAAeC,UAAUE,MAAQjC,KAAKuB,YAEhD,YADAf,QAAQC,IAAI,wCAGd,GAAIT,KAAK6B,GAAGC,aAAeC,UAAUC,WAEnC,YADAxB,QAAQC,IAAI,sDAIdT,KAAK6B,GAAGK,QACRlC,KAAK6B,GAAK,IACZ,CAGA,IAAIM,EAAU,GAAGvC,IAAQC,aAAsBG,KAAKe,SAChDf,KAAKgB,gBACPmB,GAAW,UAAUC,mBAAmBpC,KAAKgB,kBAE/CR,QAAQC,IACN,0BACA0B,EAAQE,QAAQ,cAAe,cAGjCrC,KAAK6B,GAAK,IAAIE,UAAUI,GAExBnC,KAAK6B,GAAGS,OAAS,KAaf,IAZA9B,QAAQC,IAAI,gCACZT,KAAKuB,aAAc,EACnBvB,KAAKuC,kBAAoB,EACzBvC,KAAKwC,iBAAmB,IACxBxC,KAAK4B,gBAAiB,EAEtB5B,KAAKyC,YAAY,CACf7B,KAAM,oBACNC,QAAS,CAAEU,aAAa,KAInBvB,KAAKyB,aAAaiB,OAAS,GAAG,CACnC,MAAMC,EAAU3C,KAAKyB,aAAamB,QAC9BD,GACF3C,KAAKwB,YAAYmB,EAErB,GAGF3C,KAAK6B,GAAGxB,UAAaK,IACnB,IACE,MAAMI,EAAO+B,KAAKC,MAAMpC,EAAMI,MAC9BN,QAAQC,IAAI,uCAAwCK,GAGhDA,EAAKiC,OAAyB,UAAhBjC,EAAKkC,QACrBxC,QAAQC,IAAI,mCAAoCK,GAChDd,KAAKyC,YAAY,CACf7B,KAAM,QACNC,QAAS,CACP8B,QAAS7B,EAAKiC,OAAS,yBACvBE,QAASnC,EAAKmC,SAAWnC,EACzBoC,IAAKlD,KAAKsB,eAKdtB,KAAKyC,YAAY,CACf7B,KAAM,oBACNC,QAAS,IACJC,EACHO,cAAerB,KAAKsB,aAI5B,OAASyB,GACPvC,QAAQuC,MAAM,oCAAqCA,GACnD/C,KAAKyC,YAAY,CACf7B,KAAM,QACNC,QAAS,CACP8B,QAAS,0BACTO,IAAKlD,KAAKsB,aAGhB,GAGFtB,KAAK6B,GAAGsB,QAAWzC,IACjBF,QAAQC,IAAI,6BAA8BC,GAC1CV,KAAKuB,aAAc,EAEnBvB,KAAKyC,YAAY,CACf7B,KAAM,oBACNC,QAAS,CAAEU,aAAa,EAAO6B,KAAM1C,EAAM0C,QAIxC1C,EAAM2C,UAAarD,KAAK4B,gBAC3B5B,KAAKsD,oBAITtD,KAAK6B,GAAG0B,QAAWR,IA7MzB,IAAApC,EAAA6C,EA8MQhD,QAAQuC,MAAM,4BAA6BA,GAGtC/C,KAAKuB,cAAe,OAAAZ,EAAAX,KAAK6B,SAAL,EAAAlB,EAASmB,cAAeC,UAAUE,OACzDjC,KAAKuB,aAAc,EACnBvB,KAAKyC,YAAY,CACf7B,KAAM,QACNC,QAAS,CACP8B,QAAS,8BACTc,IAAK,GAAG7D,IAAQC,IAChB6D,UAAWC,KAAKC,MAChBC,kBAAmB7D,KAAKuC,kBAAoB,EAC5CW,IAAKlD,KAAKsB,WACV2B,QAAS,CACPnB,WAAY,OAAA0B,EAAAxD,KAAK6B,SAAL,EAAA2B,EAAS1B,WACrBgC,eAAgB9D,KAAK6B,GACjB7B,KAAK6B,GAAGC,aAAeC,UAAUC,WAC/B,aACAhC,KAAK6B,GAAGC,aAAeC,UAAUE,KAC/B,OACAjC,KAAK6B,GAAGC,aAAeC,UAAUgC,QAC/B,UACA/D,KAAK6B,GAAGC,aAAeC,UAAUiC,OAC/B,SACA,UACR,UACJrC,SAAUzB,UAAUC,OACpBY,OAAQf,KAAKe,QAAU,GACvBkD,gBAAiBjE,KAAKiE,iBAAmB,UAMrD,OAASlB,GACPvC,QAAQuC,MAAM,6BAA8BA,GAC5C/C,KAAKuB,aAAc,EACnBvB,KAAKyC,YAAY,CACf7B,KAAM,QACNC,QAAS,CACP8B,QACEI,aAAiBmB,MACbnB,EAAMJ,QACN,4BACNe,UAAWC,KAAKC,MAChBV,IAAKlD,KAAKsB,aAGhB,CACF,CAEQ,gBAAAgC,GACN,GAAItD,KAAKuC,mBAAqBvC,KAAKmE,qBASjC,OARA3D,QAAQC,IAAI,mDACZT,KAAKyC,YAAY,CACf7B,KAAM,oBACNC,QAAS,CACPU,aAAa,EACboB,QAAS,uCAMf3C,KAAK4B,gBAAiB,EACtB5B,KAAKuC,oBAEL/B,QAAQC,IACN,iCAAiCT,KAAKuC,qBAAqBvC,KAAKmE,wBAElEnE,KAAKyC,YAAY,CACf7B,KAAM,oBACNC,QAAS,CACPU,aAAa,EACb6C,QAASpE,KAAKuC,qBAIlB8B,WAAW,KACTrE,KAAKmB,UACLnB,KAAKwC,kBAAoB,GACxBxC,KAAKwC,iBACV,CAEQ,WAAAhB,CAAYmB,GAClB,IAAK3C,KAAK6B,GAIR,OAHArB,QAAQC,IAAI,kCACZT,KAAKyB,aAAaC,KAAKiB,QACvB3C,KAAKmB,UAIP,IAAKnB,KAAKuB,aAAevB,KAAK6B,GAAGC,aAAeC,UAAUE,KAaxD,OAZAzB,QAAQC,IACN,gDACAT,KAAK6B,GAAGC,WACR,eACA9B,KAAKuB,aAIPf,QAAQC,IAAI,6BACZT,KAAKyB,aAAaC,KAAKiB,GAGnB3C,KAAK6B,GAAGC,aAAeC,UAAUC,gBACnCxB,QAAQC,IAAI,yDAKVT,KAAK6B,GAAGC,YAAcC,UAAUgC,UAClCvD,QAAQC,IACN,oEAEFT,KAAKmB,YAKT,IACEX,QAAQC,IAAI,sCAAuCkC,GACnD3C,KAAK6B,GAAGyC,KAAKzB,KAAK0B,UAAU5B,GAC9B,OAASI,GACPvC,QAAQuC,MAAM,uBAAwBA,GACtC/C,KAAKyB,aAAaC,KAAKiB,GACvB3C,KAAKyC,YAAY,CACf7B,KAAM,QACNC,QAAS,CACP8B,QACEI,aAAiBmB,MAAQnB,EAAMJ,QAAU,sBAC3Ce,UAAWC,KAAKC,MAChBV,IAAKlD,KAAKsB,aAGhB,CACF,CAEQ,UAAAF,GACFpB,KAAK6B,KACPrB,QAAQC,IAAI,yCACZT,KAAK6B,GAAGK,MAAM,IAAM,kBACpBlC,KAAK6B,GAAK,MAEZ7B,KAAKuB,aAAc,EACnBvB,KAAK4B,gBAAiB,EACtB5B,KAAKe,OAAS,GACdf,KAAKyB,aAAe,EACtB,CAEQ,WAAAgB,CAAYE,GAClBnC,QAAQC,IAAI,2CAA4CkC,GACvDvC,KAAaqC,YAAYE,EAC5B"}