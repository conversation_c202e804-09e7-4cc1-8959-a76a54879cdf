// Bundle entry point that properly exports PrimitiveEngineController
import { PrimitiveEngineController } from './primitive-engine-controller.js';

// Export as both named export and default export for UMD compatibility
export { PrimitiveEngineController };
export default PrimitiveEngineController;

// Also attach to global scope for direct access
// Use a more robust approach for different environments
try {
  // Try globalThis first (modern standard)
  if (typeof globalThis !== 'undefined') {
    globalThis.PrimitiveEngineController = PrimitiveEngineController;
  }
  // Fallback to window (browser)
  else if (typeof window !== 'undefined') {
    window.PrimitiveEngineController = PrimitiveEngineController;
  }
  // Fallback to global (Node.js)
  else if (typeof global !== 'undefined') {
    global.PrimitiveEngineController = PrimitiveEngineController;
  }
  // Fallback to self (Web Workers/Service Workers)
  else if (typeof self !== 'undefined') {
    self.PrimitiveEngineController = PrimitiveEngineController;
  }
} catch (error) {
  console.warn('Could not attach PrimitiveEngineController to global scope:', error);
}
