{"SUPPORTED_SITES": {"kiteByZerodha": {"name": "Kite by <PERSON><PERSON>", "url": "https://kite.zerodha.com/", "urlPrefix": "https://kite.zerodha.com/", "contentScript": "content-scripts/zerodha.js"}}, "ACTION_ARGUMENTS": {"BUY": ["SYMBOL", "QUANTITY", "PRODUCT_TYPE"], "SELL": ["SYMBOL", "QUANTITY", "PRODUCT_TYPE"], "PlaceBuyLimitOrder": ["SYMBOL", "QUANTITY", "PRODUCT_TYPE", "PRICE"], "PlaceSellLimitOrder": ["SYMBOL", "QUANTITY", "PRODUCT_TYPE", "PRICE"], "PlaceBuyStopLossMarketOrder": ["SYMBOL", "QUANTITY", "PRODUCT_TYPE", "TRIGGER_PRICE"], "PlaceSellStopLossMarketOrder": ["SYMBOL", "QUANTITY", "PRODUCT_TYPE", "TRIGGER_PRICE"], "PlaceBuyStopLossLimitOrder": ["SYMBOL", "QUANTITY", "PRODUCT_TYPE", "TRIGGER_PRICE", "limitPrice"], "PlaceSellStopLossLimitOrder": ["SYMBOL", "QUANTITY", "PRODUCT_TYPE", "TRIGGER_PRICE", "limitPrice"], "MONITORPROFIT": ["TARGET_PROFIT_AMOUNT"], "ExitAllPositions": [], "MonitorConditionThenAct": ["condition", "on_trigger"], "NavigateToProfile": [], "GetProfileInfo": [], "GetPortfolioStats": [], "GetOpenPositionPnL": [], "MonitorSymbolFromWatchlist": ["symbol", "condition", "on_trigger"], "SelectOrderByCriteria": ["TYPE", "SYMBOL", "EXCHANGE", "PRODUCT", "QUANTITY"], "CancelOrder": ["SYMBOL", "QUANTITY", "EXCHANGE", "PRODUCT", "TYPE"], "GetOpenOrders": []}, "MESSAGE_TYPES": {"EXECUTE_ACTIONS": "EXECUTE_ACTIONS", "PERFORM_SITE_ACTIONS": "PERFORM_SITE_ACTIONS", "ACTION_STATUS_UPDATE": "ACTION_STATUS_UPDATE", "EXECUTION_COMPLETE": "EXECUTION_COMPLETE"}, "ENVIRONMENT": {"slow_execute": false, "close_tabs_after_execution": true}}