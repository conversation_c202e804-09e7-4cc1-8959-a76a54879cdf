// Action validation utilities for the executor extension

import { ARGUMENT_CONFIG, ARGUMENT_TYPES, ERROR_MESSAGES, SUCCESS_MESSAGES } from './constants.js';

/**
 * Normalizes a string to uppercase for case-insensitive comparison
 * @param {string} str - String to normalize
 * @returns {string} Uppercase string
 */
function normalizeString(str) {
  return typeof str === 'string' ? str.toUpperCase() : str;
}

/**
 * Normalizes arguments to be case-insensitive while preserving original case for required args
 * @param {Object} args - Arguments object
 * @param {Array} requiredArgs - Array of required argument names
 * @returns {Object} Normalized arguments
 */
function normalizeArguments(args, requiredArgs = []) {
  const normalized = {};
  
  // Handle special camelCase mappings first
  if (args.productType && !normalized.PRODUCT_TYPE) {
    normalized.PRODUCT_TYPE = args.productType;
  }
  if (args.triggerPrice && !normalized.TRIGGER_PRICE) {
    normalized.TRIGGER_PRICE = args.triggerPrice;
  }
  
  // Then handle required arguments with proper case
  for (const requiredArg of requiredArgs) {
    const foundArgKey = Object.keys(args).find(
      key => normalizeString(key) === normalizeString(requiredArg)
    );
    if (foundArgKey) {
      normalized[requiredArg] = args[foundArgKey];
    }
  }
  
  // Then add any additional arguments with their original case
  for (const [key, value] of Object.entries(args)) {
    const isRequiredArg = requiredArgs.some(reqArg => normalizeString(reqArg) === normalizeString(key));
    const isSpecialMapping = key === 'productType' || key === 'triggerPrice'; // Skip special mappings as they're already handled
    if (!isRequiredArg && !isSpecialMapping) {
      normalized[key] = value;
    }
  }
  
  // Store original args for validation purposes
  normalized._originalArgs = args;
  
  return normalized;
}

/**
 * Validates the structure and content of action objects
 * @param {Array} actions - Array of action objects to validate
 * @param {Object} actionArgumentsConfig - Configuration defining required arguments for each action
 * @returns {Object} Validation result with isValid boolean and message string
 */
export async function validateActions(actions, actionArgumentsConfig) {
  // Validate input is an array
  if (!Array.isArray(actions)) {
    return { 
      isValid: false, 
      message: "Input must be a JSON array of action objects." 
    };
  }

  // Validate each action object
  for (let i = 0; i < actions.length; i++) {
    const validationResult = validateActionObject(actions[i], i, actionArgumentsConfig);
    if (!validationResult.isValid) {
      return validationResult;
    }
  }

  return { isValid: true, message: SUCCESS_MESSAGES.VALIDATION_SUCCESS };
}

/**
 * Validates a single action object
 * @param {Object} actionObj - Action object to validate
 * @param {number} index - Index of the action in the array
 * @param {Object} actionArgumentsConfig - Configuration for required arguments
 * @returns {Object} Validation result
 */
function validateActionObject(actionObj, index, actionArgumentsConfig) {
  // Check if action is a valid object
  if (!isValidObject(actionObj)) {
    return { 
      isValid: false, 
      message: `Action at index ${index} is not a valid object.` 
    };
  }

  const { action, arguments: args } = actionObj;

  // Validate action string
  if (!isValidString(action)) {
    return { 
      isValid: false, 
      message: `Action at index ${index} is missing a valid 'action' string.` 
    };
  }

  // Normalize action to uppercase for case-insensitive comparison
  const normalizedAction = normalizeString(action);
  
  // Check if action is supported (case-insensitive)
  const supportedAction = Object.keys(actionArgumentsConfig).find(
    key => normalizeString(key) === normalizedAction
  );
  
  if (!supportedAction) {
    return { 
      isValid: false, 
      message: `Action at index ${index} has unrecognized action type '${action}'.` 
    };
  }

  // Validate arguments object
  if (!isValidObject(args)) {
    return { 
      isValid: false, 
      message: `Action at index ${index} is missing a valid 'arguments' object.` 
    };
  }

  // Normalize arguments to be case-insensitive while preserving original case for required args
  const normalizedArgs = normalizeArguments(args, Object.keys(actionArgumentsConfig[supportedAction]));

  // Validate required arguments using the original supported action key
  return validateActionArguments(supportedAction, normalizedArgs, index, actionArgumentsConfig);
}

/**
 * Validates the arguments for a specific action
 * @param {string} action - Action type (original case)
 * @param {Object} args - Arguments object (already normalized)
 * @param {number} index - Action index
 * @param {Object} actionArgumentsConfig - Configuration for required arguments
 * @returns {Object} Validation result
 */
function validateActionArguments(action, args, index, actionArgumentsConfig) {
  const requiredArgs = actionArgumentsConfig[action];
  const originalArgs = args._originalArgs || args;
  
  for (const argKey of requiredArgs) {
    // Check if required argument exists using comprehensive case-insensitive comparison
    let foundArgKey = null;
    
    // First, check normalized args
    foundArgKey = Object.keys(args).find(key => normalizeString(key) === normalizeString(argKey));
    
    // If not found, check original args
    if (!foundArgKey) {
      foundArgKey = Object.keys(originalArgs).find(key => normalizeString(key) === normalizeString(argKey));
    }
    
    // If still not found, check for common variations
    if (!foundArgKey) {
      const argVariations = generateArgumentVariations(argKey);
      for (const variation of argVariations) {
        foundArgKey = Object.keys(originalArgs).find(key => normalizeString(key) === normalizeString(variation));
        if (foundArgKey) break;
      }
    }
    
    if (!foundArgKey) {
      const argVariations = generateArgumentVariations(argKey);
      return { 
        isValid: false, 
        message: `Action '${action}' at index ${index} is missing required argument '${argKey}'. Accepted variations: ${argVariations.join(', ')}.` 
      };
    }

    // Use the found key for type validation, but use the normalized value
    const valueToValidate = args[foundArgKey] || originalArgs[foundArgKey];
    const validationResult = validateArgumentType(argKey, valueToValidate, action, index);
    if (!validationResult.isValid) {
      return validationResult;
    }
  }

  return { isValid: true, message: "Arguments validation successful." };
}

/**
 * Generate common variations of an argument name for case-insensitive matching
 * @param {string} argName - Original argument name
 * @returns {Array} Array of possible variations
 */
function generateArgumentVariations(argName) {
  const variations = [argName];
  
  // Add common variations
  variations.push(argName.toLowerCase());
  variations.push(argName.toUpperCase());
  
  // Convert snake_case to camelCase
  if (argName.includes('_')) {
    const camelCase = argName.toLowerCase().split('_').map((word, index) => 
      index === 0 ? word : word.charAt(0).toUpperCase() + word.slice(1)
    ).join('');
    variations.push(camelCase);
    
    // Also add version without underscores
    variations.push(argName.replace(/_/g, ''));
    variations.push(argName.replace(/_/g, '').toLowerCase());
    variations.push(argName.replace(/_/g, '').toUpperCase());
  }
  
  // Convert camelCase to snake_case
  if (/[A-Z]/.test(argName) && !argName.includes('_')) {
    const snakeCase = argName.replace(/([A-Z])/g, '_$1').toLowerCase().replace(/^_/, '');
    variations.push(snakeCase.toUpperCase());
    variations.push(snakeCase);
  }
  
  return [...new Set(variations)]; // Remove duplicates
}

/**
 * Validates the type of a specific argument
 * @param {string} argKey - Argument key (original case)
 * @param {*} value - Argument value
 * @param {string} action - Action type
 * @param {number} index - Action index
 * @returns {Object} Validation result
 */
function validateArgumentType(argKey, value, action, index) {
  console.log(`🔍 validateArgumentType called with:`);
  console.log(`  - argKey: "${argKey}"`);
  console.log(`  - value:`, value);
  console.log(`  - action: "${action}"`);
  console.log(`  - index: ${index}`);
  
  const expectedType = getExpectedType(argKey);
  const actualType = typeof value;
  
  console.log(`🔍 Type comparison: expected="${expectedType}", actual="${actualType}"`);

  if (actualType !== expectedType) {
    console.log(`❌ Type validation failed!`);
    return { 
      isValid: false, 
      message: `Argument '${argKey}' for action '${action}' at index ${index} must be a ${expectedType}.` 
    };
  }

  console.log(`✅ Type validation passed!`);
  return { isValid: true, message: "Type validation successful." };
}

/**
 * Gets the expected type for a given argument key
 * @param {string} argKey - Argument key
 * @returns {string} Expected type
 */
function getExpectedType(argKey) {
  console.log(`🔍 getExpectedType called with argKey: "${argKey}"`);
  
  // First try exact match
  let config = ARGUMENT_CONFIG[argKey];
  console.log(`🔍 Exact match result:`, config);
  
  // If not found, try case-insensitive match
  if (!config) {
    const normalizedArgKey = normalizeString(argKey);
    console.log(`🔍 Normalized argKey: "${normalizedArgKey}"`);
    const foundKey = Object.keys(ARGUMENT_CONFIG).find(
      key => normalizeString(key) === normalizedArgKey
    );
    console.log(`🔍 Found key: "${foundKey}"`);
    if (foundKey) {
      config = ARGUMENT_CONFIG[foundKey];
      console.log(`🔍 Case-insensitive match result:`, config);
    }
  }
  
  console.log(`🔍 Final config:`, config);
  const result = config ? config.type : ARGUMENT_TYPES.STRING; // Default to string for unknown arguments
  console.log(`🔍 Returning type: "${result}"`);
  return result;
}

/**
 * Checks if a value is a valid object
 * @param {*} value - Value to check
 * @returns {boolean} True if valid object
 */
function isValidObject(value) {
  return typeof value === "object" && value !== null;
}

/**
 * Checks if a value is a valid string
 * @param {*} value - Value to check
 * @returns {boolean} True if valid string
 */
function isValidString(value) {
  return typeof value === "string";
} 