// Enhanced execution service for handling automation with parallel execution and graph support
import { validateActions } from './validation.js';
import { AUTOMATION_MODES, CONTENT_SCRIPT_IDENTIFIER, DELAYS, ERROR_MESSAGES, MESSAGE_TYPES } from './constants.js';
import { getActionArguments, getSupportedSites, getEnvironment } from './config.js';

export class ExecutionService {
  constructor() {
    this.messageTypes = null;
    this.timeoutSettings = {
      execution_service_general_timeout_seconds: 30,
      execution_service_monitoring_timeout_seconds: 600
    };
    
    // Enhanced properties for parallel execution and graph support
    this.activeTabs = new Map(); // tabId -> {tab, site, status, actions}
    this.activeGraphs = new Map(); // graphId -> {status, nodes, progress}
    this.nodeExecutions = new Map(); // nodeId -> {status, result, tabId, startTime, endTime}
    this.maxConcurrentTabs = 5; // Configurable limit
    this.tabPool = new Set(); // Pool of available tabs for reuse
    
    // Fix for race conditions and memory leaks
    this.cancelledActions = new Set(); // Track cancelled actions
    this.tabAcquisitionLocks = new Map(); // Prevent race conditions in tab acquisition
    this.defaultActionTimeout = 300000; // 5 minutes default timeout for actions
    
    // Pause/Resume functionality
    this.pausedGraphs = new Set(); // Track paused graphs
    this.pauseResolvers = new Map(); // graphId -> resolve function to resume execution
    
    // Login monitoring system
    this.loginMonitor = {
      tabId: null,
      intervalId: null,
      checking: false,
      required: null,
      lastChecked: null,
      pausedGraphsSet: new Set(),
      started: false,
      isDedicated: false,
        creationPromise: null,
        onRemovedHandler: null,
        // Track data fetches on successful login to avoid repeating every tick
        fetchedThisSession: false,
        lastFetchAt: null
    };
  }

  async loadTimeoutSettings() {
    try {
      const response = await fetch(chrome.runtime.getURL('lib/shared-config.json'));
      const config = await response.json();
      if (config.TIMEOUT_SETTINGS) {
        this.timeoutSettings = { ...this.timeoutSettings, ...config.TIMEOUT_SETTINGS };
        console.log('[ExecutionService] Loaded timeout settings:', this.timeoutSettings);
      }
    } catch (error) {
      console.warn('[ExecutionService] Failed to load timeout config, using defaults:', error);
    }
  }

  initialize(messageTypes) {
    this.messageTypes = messageTypes;
  }

  /**
   * Generate common variations of an argument name for case-insensitive matching
   * @param {string} argName - Original argument name
   * @returns {Array} Array of possible variations
   */
  generateArgumentVariations(argName) {
    const variations = [argName];
    
    // Add common variations
    variations.push(argName.toLowerCase());
    variations.push(argName.toUpperCase());
    
    // Convert snake_case to camelCase
    if (argName.includes('_')) {
      const camelCase = argName.toLowerCase().split('_').map((word, index) => 
        index === 0 ? word : word.charAt(0).toUpperCase() + word.slice(1)
      ).join('');
      variations.push(camelCase);
      
      // Also add version without underscores
      variations.push(argName.replace(/_/g, ''));
      variations.push(argName.replace(/_/g, '').toLowerCase());
      variations.push(argName.replace(/_/g, '').toUpperCase());
    }
    
    // Convert camelCase to snake_case
    if (/[A-Z]/.test(argName) && !argName.includes('_')) {
      const snakeCase = argName.replace(/([A-Z])/g, '_$1').toLowerCase().replace(/^_/, '');
      variations.push(snakeCase.toUpperCase());
      variations.push(snakeCase);
    }
    
    return [...new Set(variations)]; // Remove duplicates
  }

  /**
   * Normalizes actions and arguments to be completely case-insensitive
   * @param {Array} actions - Actions to normalize
   * @param {Object} actionArgumentsConfig - Configuration for action arguments
   * @returns {Array} Normalized actions
   */
  normalizeActions(actions, actionArgumentsConfig) {
    return actions.map(actionObj => {
      const { action, arguments: args } = actionObj;
      
      // Find the correct action key (case-insensitive)
      const correctActionKey = Object.keys(actionArgumentsConfig).find(
        key => key.toUpperCase() === action.toUpperCase()
      );
      
      if (!correctActionKey) {
        return actionObj; // Return original if not found
      }
      
      // Normalize arguments using comprehensive case-insensitive matching
      const normalizedArgs = {};
      const requiredArgs = actionArgumentsConfig[correctActionKey];
      
      // Map each required argument to its provided value using case-insensitive matching
      for (const requiredArg of requiredArgs) {
        let foundValue = null;
        
        // First try direct case-insensitive match
        const directMatch = Object.keys(args).find(
          key => key.toUpperCase() === requiredArg.toUpperCase()
        );
        
        if (directMatch) {
          foundValue = args[directMatch];
        } else {
          // Try variations of the argument name
          const variations = this.generateArgumentVariations(requiredArg);
          for (const variation of variations) {
            const match = Object.keys(args).find(
              key => key.toUpperCase() === variation.toUpperCase()
            );
            if (match) {
              foundValue = args[match];
              break;
            }
          }
        }
        
        if (foundValue !== null) {
          normalizedArgs[requiredArg] = foundValue;
        }
      }
      
      // Add any additional arguments that weren't matched
      for (const [key, value] of Object.entries(args)) {
        const isAlreadyMapped = requiredArgs.some(reqArg => {
          const variations = this.generateArgumentVariations(reqArg);
          return variations.some(variation => variation.toUpperCase() === key.toUpperCase());
        });
        
        if (!isAlreadyMapped) {
          normalizedArgs[key] = value;
        }
      }
      
      return {
        action: correctActionKey,
        arguments: normalizedArgs
      };
    });
  }

  async executeActions(params) {
    const { actions, site, automationMode, tabId, actionArgumentsConfig } = params;

    const validation = await validateActions(actions, actionArgumentsConfig);
    if (!validation.isValid) {
      return { success: false, message: validation.message };
    }

    const normalizedActions = this.normalizeActions(actions, actionArgumentsConfig);

    if (automationMode === AUTOMATION_MODES.CURRENT_TAB) {
      return this.executeInCurrentTab(tabId, site, normalizedActions);
    } else if (automationMode === AUTOMATION_MODES.BACKGROUND) {
      return this.executeInBackground(site, normalizedActions);
    } else {
      return { success: false, message: `Unknown automation mode: ${automationMode}` };
    }
  }



  /**
   * Execute graph with node-by-node execution (no level blocking)
   * Actions start as soon as their dependencies are satisfied
   * Missing dependencies are treated as independent actions
   * @param {string} graphId - Graph identifier  
   * @param {Array} actions - Actions to execute
   * @param {Object} options - Execution options
   */
  async executeGraphNodeByNode(graphId, actions, options = {}) {
    try {
      console.log(`[ExecutionService] Starting node-by-node graph execution: ${graphId} with ${actions.length} actions`);
      
      // Perform a synchronous login check before starting
      console.log(`[ExecutionService] Performing pre-start login check for graph: ${graphId}`);
      try {
        await this.ensureLoginMonitorTab();
        await this.checkLoginAndTogglePause();
      } catch (e) {
        console.warn('[ExecutionService] Pre-start login check failed:', e);
      }

      // If login is required (from last check), start graph in paused state and auto-resume after login
      if (this.loginMonitor.required === true) {
        console.warn(`[ExecutionService] Login required - starting graph ${graphId} in paused state and waiting`);
        this.activeGraphs.set(graphId, {
          status: 'running',
          nodes: new Map(),
          progress: { completed: 0, failed: 0, total: actions.length },
          startTime: new Date(),
          options
        });
        this.pauseGraph(graphId);
        this.loginMonitor.pausedGraphsSet.add(graphId);
        // Wait until login clears, then resume
        while (this.loginMonitor.required !== false) {
          await this.checkLoginAndTogglePause().catch(() => {});
          await this.delay(500);
        }
        this.resumeGraph(graphId);
      }
      
      // Initialize graph tracking
      this.activeGraphs.set(graphId, {
        status: 'running',
        nodes: new Map(),
        progress: { completed: 0, failed: 0, total: actions.length },
        startTime: new Date(),
        options
      });

      // Build dependency graph (missing dependencies are filtered out automatically)
      const dependencyMap = this.buildDependencyMap(actions);
      const { dependencies, dependents } = dependencyMap;
      
      // Track completion status and results
      const completedActions = new Set();
      const failedActions = new Set();
      const results = [];
      const activePromises = new Map(); // actionId -> Promise
      const actionMap = new Map(actions.map(a => [a.id || a.action, a]));
      
      console.log(`[ExecutionService] Node-by-node execution initialized`);
      console.log(`[ExecutionService] Dependency analysis complete - actions with dependencies:`, 
        Array.from(dependencies.entries()).filter(([id, deps]) => deps.length > 0));
      
      // Function to check if an action can be executed
      const canExecuteAction = (actionId) => {
        if (completedActions.has(actionId) || failedActions.has(actionId) || activePromises.has(actionId)) {
          return false;
        }
        
        // Only check dependencies that actually exist in the action list
        const deps = dependencies.get(actionId) || [];
        return deps.every(dep => completedActions.has(dep));
      };
      
      // Function to start an action with timeout protection
      const startAction = async (actionId) => {
        const action = actionMap.get(actionId);
        if (!action) return;
        
        console.log(`[ExecutionService] Starting action: ${actionId}`);
        
        // Create timeout promise
        const actionTimeout = options.actionTimeout || this.defaultActionTimeout;
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => {
            reject(new Error(`Action timeout: ${actionId} exceeded ${actionTimeout/1000}s`));
          }, actionTimeout);
        });
        
        const promise = Promise.race([
          this.executeNodeWithTab(graphId, action, options),
          timeoutPromise
        ]).then(result => {
          // Remove from active promises first
          activePromises.delete(actionId);
          
          if (result.success) {
            completedActions.add(actionId);
            results.push(result);
            this.updateNodeStatus(graphId, actionId, 'completed', result);
            console.log(`[ExecutionService] Action completed: ${actionId}`);
          } else {
            failedActions.add(actionId);
            results.push({ success: false, action: action.action, error: result.error || result.message });
            this.updateNodeStatus(graphId, actionId, 'failed', result.error || result);
            console.log(`[ExecutionService] Action failed: ${actionId}`);
            
            // If this action failed, fail its dependents too (if configured)
            if (options.failDependentsOnError !== false) {
              this.failDependentActions(graphId, actionId, dependents);
              const deps = dependents.get(actionId) || [];
              deps.forEach(dep => {
                failedActions.add(dep);
                // Cancel if currently running
                if (activePromises.has(dep)) {
                  this.cancelledActions.add(dep);
                }
              });
            }
          }
          
          return result;
        }).catch(error => {
          // Always remove from active promises
          activePromises.delete(actionId);
          failedActions.add(actionId);
          const errorResult = { success: false, action: action.action, error: error.message };
          results.push(errorResult);
          this.updateNodeStatus(graphId, actionId, 'failed', error);
          console.error(`[ExecutionService] Action errored: ${actionId}`, error);
          
          // Fail dependents on error
          if (options.failDependentsOnError !== false) {
            this.failDependentActions(graphId, actionId, dependents);
            const deps = dependents.get(actionId) || [];
            deps.forEach(dep => {
              failedActions.add(dep);
              // Cancel if currently running
              if (activePromises.has(dep)) {
                this.cancelledActions.add(dep);
              }
            });
          }
          
          return errorResult;
        });
        
        activePromises.set(actionId, promise);
        return promise;
      };
      
      // Function to check and start ready actions
      const checkAndStartReadyActions = () => {
        const readyActions = [];
        for (const action of actions) {
          const actionId = action.id || action.action;
          if (canExecuteAction(actionId)) {
            readyActions.push(actionId);
          }
        }
        
        if (readyActions.length > 0) {
          console.log(`[ExecutionService] Starting ${readyActions.length} ready actions:`, readyActions);
        }
        
        // Start all ready actions
        readyActions.forEach(actionId => startAction(actionId));
        
        return readyActions.length;
      };
      
      // Main execution loop with deadlock detection and proper cleanup
      let iteration = 0;
      const maxIterations = actions.length * 3; // Increased safety limit
      let consecutiveNoProgressIterations = 0;

      while (completedActions.size + failedActions.size < actions.length && iteration < maxIterations) {
        iteration++;
        
        // Check for pause before proceeding
        await this.checkForPause(graphId);
        
        // Check and start ready actions
        const startedCount = checkAndStartReadyActions();
        
        if (startedCount === 0 && activePromises.size === 0) {
          // No actions started and none running - check for deadlock
          const remaining = actions.filter(a => {
            const actionId = a.id || a.action;
            return !completedActions.has(actionId) && !failedActions.has(actionId);
          });
          
          if (remaining.length > 0) {
            console.error(`[ExecutionService] Deadlock detected at iteration ${iteration}. Remaining actions:`, 
              remaining.map(a => {
                const actionId = a.id || a.action;
                const deps = dependencies.get(actionId) || [];
                const uncompletedDeps = deps.filter(dep => !completedActions.has(dep));
                return `${actionId} (waiting for: ${uncompletedDeps.join(', ')})`;
              }));
            
            // Fail remaining actions to prevent infinite loop
            remaining.forEach(action => {
              const actionId = action.id || action.action;
              failedActions.add(actionId);
              this.updateNodeStatus(graphId, actionId, 'failed', {
                reason: 'deadlock_detected',
                iteration,
                remainingDependencies: (dependencies.get(actionId) || []).filter(dep => !completedActions.has(dep))
              });
            });
          }
          break;
        }
        
        // Track progress to detect infinite loops
        if (startedCount === 0) {
          consecutiveNoProgressIterations++;
          if (consecutiveNoProgressIterations > 10) {
            console.warn(`[ExecutionService] No progress for ${consecutiveNoProgressIterations} iterations. Active promises: ${activePromises.size}`);
          }
        } else {
          consecutiveNoProgressIterations = 0;
        }
        
        // Wait for at least one active promise to complete before checking again
        if (activePromises.size > 0) {
          try {
            // Create a stable snapshot of promises to avoid race conditions
            const promiseArray = Array.from(activePromises.values());
            await Promise.race(promiseArray);
          } catch (error) {
            console.error(`[ExecutionService] Error in Promise.race:`, error);
            // Continue execution even if one promise fails
          }
        }
        
        // Stop on error if configured and we have failures
        if (options.stopOnError && failedActions.size > 0) {
          console.log(`[ExecutionService] Stopping execution due to error at iteration ${iteration}`);
          break;
        }
      }

      // Handle max iterations exceeded
      if (iteration >= maxIterations) {
        console.error(`[ExecutionService] Max iterations exceeded (${maxIterations}). Forcing completion.`);
        
        // Force fail remaining actions
        const remaining = actions.filter(a => {
          const actionId = a.id || a.action;
          return !completedActions.has(actionId) && !failedActions.has(actionId);
        });
        
        remaining.forEach(action => {
          const actionId = action.id || action.action;
          failedActions.add(actionId);
          this.updateNodeStatus(graphId, actionId, 'failed', {
            reason: 'max_iterations_exceeded',
            iteration: maxIterations
          });
        });
        
        // Cancel any remaining active promises
        for (const actionId of activePromises.keys()) {
          this.cancelledActions.add(actionId);
        }
      }
      
      // Wait for any remaining active promises to complete
      if (activePromises.size > 0) {
        console.log(`[ExecutionService] Waiting for ${activePromises.size} remaining actions to complete`);
        await Promise.allSettled(Array.from(activePromises.values()));
      }
      
      // Update graph status
      const graphData = this.activeGraphs.get(graphId);
      const overallSuccess = failedActions.size === 0;
      graphData.status = overallSuccess ? 'completed' : 'failed';
      graphData.endTime = new Date();
      graphData.duration = graphData.endTime - graphData.startTime;
      
      // Cleanup tabs if configured
      await this.cleanupGraphTabs(graphId);

      // If nothing else is executing, stop and close monitor tab
      if (this.activeGraphs.size === 0 && this.nodeExecutions.size === 0) {
        console.log('[ExecutionService] All graphs completed. Stopping login monitor and closing monitor tab.');
        this.stopLoginMonitor(true);
      }
      
      console.log(`[ExecutionService] Node-by-node graph execution completed: ${graphId}, success: ${overallSuccess}`);
      console.log(`[ExecutionService] Final stats - Completed: ${completedActions.size}, Failed: ${failedActions.size}, Total: ${actions.length}`);
      
      return {
        success: overallSuccess,
        graphId,
        results,
        summary: {
          total: actions.length,
          completed: completedActions.size,
          failed: failedActions.size,
          duration: graphData.duration
        }
      };
      
    } catch (error) {
      console.error(`[ExecutionService] Node-by-node graph execution failed: ${graphId}`, error);
      
      // Update graph status
      if (this.activeGraphs.has(graphId)) {
        const graphData = this.activeGraphs.get(graphId);
        graphData.status = 'failed';
        graphData.error = error.message;
        graphData.endTime = new Date();
      }
      
      return {
        success: false,
        graphId,
        error: error.message,
        message: `Node-by-node graph execution failed: ${error.message}`
      };
    }
  }

  async getSupportedActions() {
    try {
      const actionArgumentsConfig = await getActionArguments();
      return Object.keys(actionArgumentsConfig);
    } catch (error) {
      return ['BUY', 'SELL', 'PlaceBuyLimitOrder', 'PlaceSellLimitOrder', 
              'PlaceBuyStopLossMarketOrder', 'PlaceSellStopLossMarketOrder',
              'MONITORPROFIT', 'ExitAllPositions', 'NavigateToProfile', 'GetProfileInfo'];
    }
  }

  async executeAction(actionType, arguments_, tabId) {
    try {
      const action = { action: actionType, arguments: arguments_ };
      const actionArgumentsConfig = await getActionArguments();
      
      const validation = await validateActions([action], actionArgumentsConfig);
      if (!validation.isValid) {
        return { success: false, message: validation.message };
      }

      const normalizedAction = this.normalizeActions([action], actionArgumentsConfig)[0];
      const site = await this.findSiteForTab(tabId);
      
      return this.executeInTab(tabId, site, [normalizedAction]);
    } catch (error) {
      return { success: false, message: `Action execution failed: ${error.message}` };
    }
  }

  async findSiteForTab(tabId) {
    const sites = await getSupportedSites();
    
    if (tabId) {
      try {
        const tab = await chrome.tabs.get(tabId);
        for (const [siteId, siteConfig] of Object.entries(sites)) {
          const siteUrl = siteConfig.url || siteConfig.urlPrefix;
          if (siteUrl && tab.url && tab.url.includes(siteUrl)) {
            return { ...siteConfig, siteId, url: siteUrl };
          }
        }
      } catch (error) {
        // Tab might not exist, use default
      }
    }
    
    // Use default site
    const defaultSite = Object.values(sites)[0];
    const siteUrl = defaultSite.url || defaultSite.urlPrefix;
    return { ...defaultSite, url: siteUrl };
  }

  async executeInCurrentTab(tabId, site, actions) {
    try {
      await this.ensureContentScript(tabId, site.contentScript);
      return this.sendMessageToTab(tabId, {
        type: MESSAGE_TYPES.PERFORM_SITE_ACTIONS,
        actions
      });
    } catch (error) {
      return { success: false, message: `Execution error: ${error.message}` };
    }
  }

  async executeInBackground(site, actions) {
    let newTab = null;
    
    try {
      newTab = await chrome.tabs.create({
        url: site.urlPrefix || site.url,
        active: false
      });

      await this.waitForTabLoad(newTab.id);
      await this.delay(DELAYS.PAGE_INITIALIZATION);
      await this.ensureContentScript(newTab.id, site.contentScript);

      // Send message and wait for response BEFORE closing tab
      const result = await this.sendMessageToTab(newTab.id, {
        type: MESSAGE_TYPES.PERFORM_SITE_ACTIONS,
        actions
      });

      // Close tab only AFTER getting the response
      const environment = await getEnvironment();
      const shouldCloseTabs = environment.close_tabs_after_execution !== false;
      
      if (shouldCloseTabs && newTab) {
        try {
          console.log(`[ExecutionService] Closing background tab ${newTab.id} after successful execution`);
          await chrome.tabs.remove(newTab.id);
          newTab = null; // Prevent double-closing in finally block
        } catch (error) {
          console.warn(`[ExecutionService] Failed to close tab ${newTab.id}:`, error.message);
        }
      }

      return result;

    } catch (error) {
      return { success: false, message: `Background execution error: ${error.message}` };
    } finally {
      // Only close tab if it wasn't already closed and there was an error
      if (newTab) {
        const environment = await getEnvironment();
        const shouldCloseTabs = environment.close_tabs_after_execution !== false;
        
        if (shouldCloseTabs) {
          try {
            console.log(`[ExecutionService] Cleaning up tab ${newTab.id} after error`);
            await chrome.tabs.remove(newTab.id);
          } catch (error) {
            // Ignore close errors during cleanup
          }
        }
      }
    }
  }

  async executeInTab(tabId, site, actions) {
    const maxRetries = 2;
    let lastError = null;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        await this.waitForTabLoad(tabId);
        
        if (site.contentScript) {
          await this.ensureContentScript(tabId, site.contentScript);
        }

        return this.sendMessageToTab(tabId, {
          type: MESSAGE_TYPES.PERFORM_SITE_ACTIONS,
          actions
        });
      } catch (error) {
        lastError = error;
        
        // Check if tab still exists
        try {
          await chrome.tabs.get(tabId);
        } catch (tabError) {
          return { 
            success: false, 
            message: `Tab execution failed: Tab no longer exists (${error.message})`,
            error: 'TAB_CLOSED'
          };
        }
        
        // If this is the last attempt, return the error
        if (attempt === maxRetries) {
          // Check if it's a message channel error
          if (error.message.includes('message channel closed') || 
              error.message.includes('asynchronous response') ||
              error.message.includes('Could not establish connection')) {
            return { 
              success: false, 
              message: `Tab execution failed after ${maxRetries + 1} attempts: ${error.message}. The content script may not be ready or the tab was navigated.`,
              error: 'MESSAGE_CHANNEL_ERROR'
            };
          }
          
          return { success: false, message: `Tab execution failed after ${maxRetries + 1} attempts: ${error.message}` };
        }
        
        // Wait before retrying
        console.log(`[ExecutionService] Execution attempt ${attempt + 1} failed for tab ${tabId}, retrying in 1 second...`, error.message);
        await this.delay(1000);
      }
    }
  }

  // Consolidated content script injection
  async ensureContentScript(tabId, contentScript) {
    try {
      // First check if tab is accessible
      await chrome.tabs.get(tabId);
      
      const [checkResult] = await chrome.scripting.executeScript({
        target: { tabId },
        func: (identifier) => window[identifier] === true,
        args: [CONTENT_SCRIPT_IDENTIFIER],
        world: "ISOLATED"
      });

      if (!checkResult.result) {
        await chrome.scripting.executeScript({
          target: { tabId },
          files: [contentScript],
          world: "ISOLATED"
        });
        
        // Wait a bit for the content script to initialize
        await this.delay(500);
      }
    } catch (error) {
      if (error.message.includes('Cannot access contents of url')) {
        throw new Error(ERROR_MESSAGES.TAB_NOT_ACCESSIBLE);
      }
      throw new Error(`Failed to inject content script: ${error.message}`);
    }
  }



  waitForTabLoad(tabId) {
    return new Promise(async (resolve) => {
      try {
        const tab = await chrome.tabs.get(tabId);
        if (tab.status === 'complete') {
          resolve();
          return;
        }
        
        const listener = (updatedTabId, changeInfo) => {
          if (updatedTabId === tabId && changeInfo.status === 'complete') {
            chrome.tabs.onUpdated.removeListener(listener);
            resolve();
          }
        };
        
        chrome.tabs.onUpdated.addListener(listener);
      } catch (error) {
        resolve(); // Continue even if tab check fails
      }
    });
  }

  sendMessageToTab(tabId, message) {
    return new Promise(async (resolve, reject) => {
      // Ensure timeout settings are loaded
      if (!this.timeoutSettings.execution_service_monitoring_timeout_seconds) {
        await this.loadTimeoutSettings();
    
    // Load configuration for parallel execution
    try {
      const response = await fetch(chrome.runtime.getURL('lib/shared-config.json'));
      const config = await response.json();
      if (config.EXECUTION_SERVICE) {
        this.maxConcurrentTabs = config.EXECUTION_SERVICE.max_concurrent_tabs || 5;
      }
    } catch (error) {
      console.warn('[ExecutionService] Failed to load execution service config, using defaults:', error);
    }
      }

      console.log(`[ExecutionService] Sending message to tab ${tabId}:`, message);
      
      // Check if this is a monitoring operation that needs extended timeout
      const hasMonitoringAction = message.actions && message.actions.some(action => 
        ['MonitorConditionThenAct', 'MONITORPROFIT', 'MonitorSymbolFromWatchlist'].includes(action.action)
      );
      
      // Use different timeout for monitoring vs regular actions from config
      const timeoutDuration = hasMonitoringAction 
        ? this.timeoutSettings.execution_service_monitoring_timeout_seconds * 1000
        : this.timeoutSettings.execution_service_general_timeout_seconds * 1000;
      
      console.log(`[ExecutionService] Using ${timeoutDuration/1000}s timeout for ${hasMonitoringAction ? 'monitoring' : 'regular'} action`);
      
      // Add timeout to prevent hanging
      const timeout = setTimeout(() => {
        console.error(`[ExecutionService] Message timeout for tab ${tabId} after ${timeoutDuration/1000}s`);
        reject(new Error(`Message timeout: No response received within ${timeoutDuration/1000} seconds`));
      }, timeoutDuration);

      chrome.tabs.sendMessage(tabId, message, (response) => {
        clearTimeout(timeout);
        
        if (chrome.runtime.lastError) {
          const errorMessage = chrome.runtime.lastError.message;
          console.error(`[ExecutionService] Chrome runtime error for tab ${tabId}:`, errorMessage);
          
          // Handle specific message channel closed error
          if (errorMessage.includes('message channel closed') || 
              errorMessage.includes('asynchronous response') ||
              errorMessage.includes('Could not establish connection')) {
            console.error(`[ExecutionService] Message channel closed error detected`);
            reject(new Error(ERROR_MESSAGES.MESSAGE_CHANNEL_CLOSED));
          } else {
            reject(new Error(`Failed to send message: ${errorMessage}`));
          }
          return;
        }
        
        if (!response) {
          console.error(`[ExecutionService] No response received from tab ${tabId}`);
          reject(new Error(ERROR_MESSAGES.NO_RESPONSE));
          return;
        }
        
        console.log(`[ExecutionService] Received response from tab ${tabId}:`, response);
        resolve(response);
      });
    });
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Build dependency map from actions, filtering out missing dependencies
   * @param {Array} actions - Actions with dependency information
   */
  buildDependencyMap(actions) {
    const dependencyMap = new Map(); // actionId -> [dependencies]
    const dependents = new Map(); // actionId -> [dependents]
    const actionIds = new Set(actions.map(a => a.id || a.action)); // Available action IDs
    
    actions.forEach(action => {
      const actionId = action.id || action.action;
      dependencyMap.set(actionId, []);
      dependents.set(actionId, []);
      
      if (action.depends_on) {
        let dependencies = [];
        
        if (Array.isArray(action.depends_on)) {
          // Filter out missing dependencies - treat as independent if dependency doesn't exist
          dependencies = action.depends_on.filter(dep => {
            if (actionIds.has(dep)) {
              return true;
            } else {
              console.log(`[ExecutionService] Missing dependency '${dep}' for action '${actionId}' - treating as independent`);
              return false;
            }
          });
        } else {
          // Single dependency
          if (actionIds.has(action.depends_on)) {
            dependencies = [action.depends_on];
          } else {
            console.log(`[ExecutionService] Missing dependency '${action.depends_on}' for action '${actionId}' - treating as independent`);
            dependencies = [];
          }
        }
        
        dependencyMap.set(actionId, dependencies);
        
        // Build reverse mapping (dependents)
        dependencies.forEach(dep => {
          if (!dependents.has(dep)) dependents.set(dep, []);
          dependents.get(dep).push(actionId);
        });
      }
    });
    
    return { dependencies: dependencyMap, dependents };
  }
  

  
  /**
   * Execute a single node with tab management and proper tab release
   * @param {string} graphId - Graph identifier
   * @param {Object} action - Action to execute
   * @param {Object} options - Execution options
   */
  async executeNodeWithTab(graphId, action, options = {}) {
    const nodeId = action.id || action.action;
    let tabId = null;
    let tabInfo = null;
    
    console.log(`[ExecutionService] Starting node execution: ${nodeId}`);
    
    // Initialize node tracking
    this.nodeExecutions.set(nodeId, {
      status: 'running',
      graphId,
      action,
      startTime: new Date(),
      tabId: null
    });
    
    try {
      // Check if action was cancelled before starting
      if (this.cancelledActions.has(nodeId)) {
        throw new Error(`Action ${nodeId} was cancelled before execution`);
      }
      
      // Get or create tab for this action
      const tabResult = await this.getOrCreateTab(action, options);
      tabId = tabResult.tabId;
      tabInfo = this.activeTabs.get(tabId);
      
      // Update node tracking with tab info
      const nodeExecution = this.nodeExecutions.get(nodeId);
      nodeExecution.tabId = tabId;
      
      // Ensure content script is injected
      await this.ensureContentScript(tabId, tabResult.site.contentScript);
      
      // Check again if action was cancelled during setup
      if (this.cancelledActions.has(nodeId)) {
        throw new Error(`Action ${nodeId} was cancelled during setup`);
      }
      
      // CRITICAL: Check login status right before execution
      // This prevents actions from executing when login is required
      await this.checkLoginBeforeExecution(tabId, nodeId, graphId);
      
      // Execute the action
      const result = await this.sendMessageToTab(tabId, {
        type: MESSAGE_TYPES.PERFORM_SITE_ACTIONS,
        actions: [action]
      });
      
      // Update node tracking with result
      nodeExecution.status = result.success ? 'completed' : 'failed';
      nodeExecution.result = result;
      nodeExecution.endTime = new Date();
      nodeExecution.duration = nodeExecution.endTime - nodeExecution.startTime;
      
      console.log(`[ExecutionService] Node execution completed: ${nodeId}, success: ${result.success}`);
      
      return {
        success: result.success,
        nodeId,
        action: action.action,
        result: result.results ? result.results[0] : result,
        tabId,
        duration: nodeExecution.duration
      };
      
    } catch (error) {
      console.error(`[ExecutionService] Node execution failed: ${nodeId}`, error);
      
      // Update node tracking with error
      const nodeExecution = this.nodeExecutions.get(nodeId);
      if (nodeExecution) {
        nodeExecution.status = 'failed';
        nodeExecution.error = error.message;
        nodeExecution.endTime = new Date();
      }
      
      return {
        success: false,
        nodeId,
        action: action.action,
        error: error.message,
        tabId: tabId
      };
    } finally {
      // CRITICAL: Release tab for reuse
      if (tabId && tabInfo && options.reuseTab !== false) {
        if (tabInfo.status === 'busy' && tabInfo.acquiredBy === nodeId) {
          tabInfo.status = 'available';
          tabInfo.lastUsed = new Date();
          tabInfo.acquiredBy = null;
          console.log(`[ExecutionService] Released tab ${tabId} for reuse`);
        }
      }
      
      // Cleanup cancelled action tracking
      this.cancelledActions.delete(nodeId);
    }
  }
  
  /**
   * Get or create tab for action execution with atomic reuse protection
   * @param {Object} action - Action to execute
   * @param {Object} options - Execution options
   */
  async getOrCreateTab(action, options = {}) {
    const sites = await getSupportedSites();
    let site = null;
    
    // Determine site for this action
    if (options.siteId) {
      site = sites[options.siteId];
    } else {
      // Use default site (Zerodha Kite)
      site = Object.values(sites)[0];
    }
    
    if (!site) {
      throw new Error('No supported site found for action execution');
    }
    
    const siteKey = site.url;
    
    // Atomic tab acquisition with locks
    const reuseTab = options.reuseTab !== false;
    if (reuseTab) {
      // Check if there's already a lock for this site
      if (!this.tabAcquisitionLocks.has(siteKey)) {
        this.tabAcquisitionLocks.set(siteKey, Promise.resolve());
      }
      
      // Wait for any existing acquisition to complete, then try to acquire
      const result = await this.tabAcquisitionLocks.get(siteKey).then(async () => {
        // Atomic check-and-acquire
        for (const [tabId, tabInfo] of this.activeTabs) {
          if (tabInfo.site.url === site.url && tabInfo.status === 'available') {
            // Double-check and atomically set to busy
            if (tabInfo.status === 'available') {
              tabInfo.status = 'busy';
              tabInfo.acquiredBy = action.id || action.action;
              tabInfo.lastAcquired = new Date();
              console.log(`[ExecutionService] Reusing existing tab: ${tabId} for action: ${action.id || action.action}`);
              return { tabId, site, reused: true };
            }
          }
        }
        return null; // No available tab found
      });
      
      if (result) {
        return result;
      }
    }
    
    // Check concurrent tab limit
    if (this.activeTabs.size >= this.maxConcurrentTabs) {
      throw new Error(`Maximum concurrent tabs (${this.maxConcurrentTabs}) reached. Consider increasing limit or enabling tab reuse.`);
    }
    
    // Create new tab with lock protection
    const createTabPromise = this.tabAcquisitionLocks.get(siteKey).then(async () => {
      console.log(`[ExecutionService] Creating new tab for site: ${site.url}`);
      const newTab = await chrome.tabs.create({
        url: site.url,
        active: false
      });
      
      // Track the new tab
      this.activeTabs.set(newTab.id, {
        tab: newTab,
        site,
        status: 'busy',
        created: new Date(),
        acquiredBy: action.id || action.action,
        actions: []
      });
      
      // Wait for tab to load
      await this.waitForTabLoad(newTab.id);
      await this.delay(DELAYS.PAGE_INITIALIZATION);
      
      console.log(`[ExecutionService] New tab created and loaded: ${newTab.id}`);
      
      return { tabId: newTab.id, site, reused: false };
    });
    
    // Update the lock for this site
    this.tabAcquisitionLocks.set(siteKey, createTabPromise);
    
    return await createTabPromise;
  }
  
  /**
   * Update node status in graph
   * @param {string} graphId - Graph identifier
   * @param {string} nodeId - Node identifier
   * @param {string} status - New status
   * @param {Object} data - Additional data
   */
  updateNodeStatus(graphId, nodeId, status, data = {}) {
    if (this.activeGraphs.has(graphId)) {
      const graph = this.activeGraphs.get(graphId);
      graph.nodes.set(nodeId, {
        status,
        data,
        timestamp: new Date()
      });
      
      // Update progress counters
      if (status === 'completed') {
        graph.progress.completed++;
      } else if (status === 'failed') {
        graph.progress.failed++;
      }
    }
  }
  
  /**
   * Fail dependent actions when a dependency fails
   * @param {string} graphId - Graph identifier
   * @param {string} failedNodeId - Node that failed
   * @param {Map} dependents - Dependents map
   */
  failDependentActions(graphId, failedNodeId, dependents) {
    const toFail = dependents.get(failedNodeId) || [];
    const processed = new Set();
    
    const failRecursively = (nodeId) => {
      if (processed.has(nodeId)) return;
      processed.add(nodeId);
      
      // Cancel if currently running
      this.cancelledActions.add(nodeId);
      console.log(`[ExecutionService] Cancelling running action due to dependency failure: ${nodeId}`);
      
      this.updateNodeStatus(graphId, nodeId, 'failed', {
        reason: 'dependency_failed',
        failedDependency: failedNodeId,
        timestamp: new Date()
      });
      
      // Fail this node's dependents too
      const nodeDependents = dependents.get(nodeId) || [];
      nodeDependents.forEach(failRecursively);
    };
    
    console.log(`[ExecutionService] Failing ${toFail.length} dependent actions for failed node: ${failedNodeId}`);
    toFail.forEach(failRecursively);
  }
  
  /**
   * Cleanup tabs used by a graph
   * @param {string} graphId - Graph identifier
   */
  async cleanupGraphTabs(graphId) {
    const environment = await getEnvironment();
    const shouldCloseTabs = environment.close_tabs_after_execution !== false;
    
    if (!shouldCloseTabs) {
      console.log(`[ExecutionService] Tab cleanup disabled for graph: ${graphId}`);
      return;
    }
    
    console.log(`[ExecutionService] Cleaning up tabs for graph: ${graphId}`);
    
    // Find tabs used by this graph's nodes
    const tabsToClose = new Set();
    for (const [nodeId, execution] of this.nodeExecutions) {
      if (execution.graphId === graphId && execution.tabId) {
        tabsToClose.add(execution.tabId);
      }
    }
    
    // Close tabs
    for (const tabId of tabsToClose) {
      try {
        await chrome.tabs.remove(tabId);
        this.activeTabs.delete(tabId);
        console.log(`[ExecutionService] Closed tab: ${tabId}`);
      } catch (error) {
        console.warn(`[ExecutionService] Failed to close tab ${tabId}:`, error.message);
      }
    }
    
    // Cleanup node executions for this graph
    for (const [nodeId, execution] of this.nodeExecutions) {
      if (execution.graphId === graphId) {
        this.nodeExecutions.delete(nodeId);
      }
    }
    
    // Remove graph tracking
    this.activeGraphs.delete(graphId);

    // If no graphs remain and no nodes are running, stop and optionally close monitor tab
    if (this.activeGraphs.size === 0 && this.nodeExecutions.size === 0) {
      console.log('[ExecutionService] No active graphs or nodes remaining. Stopping login monitor and closing monitor tab.');
      this.stopLoginMonitor(true);
    }
  }
  
  /**
   * Get execution status for a graph
   * @param {string} graphId - Graph identifier
   */
  getGraphStatus(graphId) {
    const graph = this.activeGraphs.get(graphId);
    if (!graph) {
      return { found: false };
    }
    
    const nodeStatuses = [];
    for (const [nodeId, execution] of this.nodeExecutions) {
      if (execution.graphId === graphId) {
        nodeStatuses.push({
          nodeId,
          status: execution.status,
          action: execution.action.action,
          tabId: execution.tabId,
          duration: execution.duration,
          startTime: execution.startTime,
          endTime: execution.endTime
        });
      }
    }
    
    return {
      found: true,
      graphId,
      status: graph.status,
      progress: graph.progress,
      nodeStatuses,
      startTime: graph.startTime,
      endTime: graph.endTime,
      duration: graph.duration
    };
  }
  
  /**
   * Get status of all active executions
   */
  getAllExecutionStatus() {
    const graphStatuses = [];
    for (const graphId of this.activeGraphs.keys()) {
      graphStatuses.push(this.getGraphStatus(graphId));
    }
    
    return {
      activeGraphs: graphStatuses.length,
      activeTabs: this.activeTabs.size,
      activeNodes: this.nodeExecutions.size,
      pausedGraphs: this.pausedGraphs.size,
      graphs: graphStatuses,
      tabs: Array.from(this.activeTabs.entries()).map(([tabId, info]) => ({
        tabId,
        url: info.site.url,
        status: info.status,
        created: info.created
      })),
      login: {
        required: this.loginMonitor.required,
        lastChecked: this.loginMonitor.lastChecked,
        monitorTabId: this.loginMonitor.tabId,
        pausedByLogin: Array.from(this.loginMonitor.pausedGraphsSet)
      }
    };
  }
  
  /**
   * Pause a graph execution
   * @param {string} graphId - Graph identifier
   */
  pauseGraph(graphId) {
    try {
      const graph = this.activeGraphs.get(graphId);
      if (!graph) {
        return { success: false, message: `Graph '${graphId}' not found` };
      }
      
      if (graph.status !== 'running') {
        return { success: false, message: `Graph '${graphId}' is not running (status: ${graph.status})` };
      }
      
      if (this.pausedGraphs.has(graphId)) {
        return { success: false, message: `Graph '${graphId}' is already paused` };
      }
      
      // Mark graph as paused
      this.pausedGraphs.add(graphId);
      graph.status = 'paused';
      graph.pausedAt = new Date();
      
      console.log(`[ExecutionService] Graph paused: ${graphId}`);
      
      return { 
        success: true, 
        message: `Graph '${graphId}' paused successfully`,
        pausedAt: graph.pausedAt
      };
    } catch (error) {
      console.error(`[ExecutionService] Error pausing graph ${graphId}:`, error);
      return { success: false, message: `Failed to pause graph: ${error.message}` };
    }
  }
  
  /**
   * Resume a paused graph execution
   * @param {string} graphId - Graph identifier
   */
  resumeGraph(graphId) {
    try {
      const graph = this.activeGraphs.get(graphId);
      if (!graph) {
        return { success: false, message: `Graph '${graphId}' not found` };
      }
      
      if (graph.status !== 'paused') {
        return { success: false, message: `Graph '${graphId}' is not paused (status: ${graph.status})` };
      }
      
      if (!this.pausedGraphs.has(graphId)) {
        return { success: false, message: `Graph '${graphId}' is not in paused state` };
      }
      
      // Resume graph
      this.pausedGraphs.delete(graphId);
      graph.status = 'running';
      graph.resumedAt = new Date();
      
      // Calculate pause duration
      if (graph.pausedAt) {
        graph.pauseDuration = (graph.pauseDuration || 0) + (graph.resumedAt - graph.pausedAt);
      }
      
      // Resume execution by resolving the pause promise
      const resolver = this.pauseResolvers.get(graphId);
      if (resolver) {
        resolver();
        this.pauseResolvers.delete(graphId);
      }
      
      console.log(`[ExecutionService] Graph resumed: ${graphId}`);
      
      return { 
        success: true, 
        message: `Graph '${graphId}' resumed successfully`,
        resumedAt: graph.resumedAt,
        totalPauseDuration: graph.pauseDuration || 0
      };
    } catch (error) {
      console.error(`[ExecutionService] Error resuming graph ${graphId}:`, error);
      return { success: false, message: `Failed to resume graph: ${error.message}` };
    }
  }
  
  /**
   * Check if execution should pause and wait if needed
   * @param {string} graphId - Graph identifier
   */
  async checkForPause(graphId) {
    if (this.pausedGraphs.has(graphId)) {
      console.log(`[ExecutionService] Graph ${graphId} is paused, waiting for resume...`);
      
      // Create a promise that resolves when the graph is resumed
      return new Promise((resolve) => {
        // If already resumed, resolve immediately
        if (!this.pausedGraphs.has(graphId)) {
          resolve();
          return;
        }
        
        // Store the resolver to be called when resuming
        this.pauseResolvers.set(graphId, resolve);
      });
    }
  }

  // ==================== LOGIN MONITORING SYSTEM ====================

  /**
   * Get Kite base URL from config
   * @returns {Promise<string>} Base URL for Kite
   */
  async getKiteBaseUrl() {
    try {
      const res = await fetch(chrome.runtime.getURL('lib/shared-config.json'));
      const cfg = await res.json();
      const site = cfg?.SUPPORTED_SITES && Object.values(cfg.SUPPORTED_SITES)[0];
      return site?.urlPrefix || site?.url || 'https://kite.zerodha.com/';
    } catch { 
      return 'https://kite.zerodha.com/'; 
    }
  }

  /**
   * Get orders page URL
   * @returns {Promise<string>} Orders page URL
   */
  async getOrdersPageUrl() {
    const base = await this.getKiteBaseUrl();
    return new URL('orders', base).toString();
  }

  /**
   * Ensure login monitor tab exists and is ready
   */
  async ensureLoginMonitorTab() {
    const ordersUrl = await this.getOrdersPageUrl();
    
    // Reuse if exists
    if (this.loginMonitor.tabId) {
      try { 
        await chrome.tabs.get(this.loginMonitor.tabId); 
      } catch { 
        this.loginMonitor.tabId = null; 
      }
    }
    
    // IMPORTANT: Always create a dedicated monitor tab to avoid touching user's current tab
    // Do not adopt existing tabs; keep this tab pinned and inactive
    // Guard against concurrent calls with a creation promise
    if (!this.loginMonitor.tabId) {
      if (!this.loginMonitor.creationPromise) {
        this.loginMonitor.creationPromise = chrome.tabs.create({ url: ordersUrl, pinned: true, active: false })
          .then(tab => {
            this.loginMonitor.tabId = tab.id;
            this.loginMonitor.isDedicated = true;
          })
          .catch(() => {})
          .finally(() => {
            this.loginMonitor.creationPromise = null;
          });
      }
      await this.loginMonitor.creationPromise;
    }

    // Ensure content script is ready
    const sites = await getSupportedSites();
    const site = Object.values(sites)[0];
    await this.waitForTabLoad(this.loginMonitor.tabId);
    await this.ensureContentScript(this.loginMonitor.tabId, site.contentScript);
  }

  /**
   * Check login status and toggle pause/resume for all graphs
   */
  async checkLoginAndTogglePause() {
    if (this.loginMonitor.checking) return;
    if (!this.loginMonitor.tabId) return;
    
    this.loginMonitor.checking = true;

    try {
      const ordersUrl = await this.getOrdersPageUrl();
      // Refresh and keep on Orders ONLY on the dedicated monitor tab
      if (this.loginMonitor.isDedicated) {
        await chrome.tabs.update(this.loginMonitor.tabId, { url: ordersUrl, pinned: true, active: false });
      }
      await this.waitForTabLoad(this.loginMonitor.tabId);

      const result = await this.sendMessageToTab(this.loginMonitor.tabId, {
        type: 'PERFORM_SITE_ACTIONS',
        actions: [{ action: 'IsLoginRequired', arguments: {} }]
      });

      let required = null;
      if (result && Array.isArray(result.results)) {
        const r0 = result.results[0];
        if (r0?.success) required = !!r0?.data?.loginRequired;
      }

      const previousRequired = this.loginMonitor.required;
      this.loginMonitor.required = required;
      this.loginMonitor.lastChecked = new Date();

      console.log(`[ExecutionService] Login check: required=${required}, previous=${previousRequired}`);

      // If login state just switched to required=false, allow a fresh fetch
      if (required === false && previousRequired !== false) {
        this.loginMonitor.fetchedThisSession = false;
      }

      if (required === true && previousRequired !== true) {
        // Login now required - pause all running graphs
        console.log(`[ExecutionService] Login required - pausing all running graphs`);
        for (const [graphId, graph] of this.activeGraphs.entries()) {
          if (graph.status === 'running') {
            console.log(`[ExecutionService] Pausing graph ${graphId} due to login requirement`);
            const res = this.pauseGraph(graphId);
            if (res?.success) {
              this.loginMonitor.pausedGraphsSet.add(graphId);
            }
          }
        }
      } else if (required === false && previousRequired === true && this.loginMonitor.pausedGraphsSet.size > 0) {
        // Login no longer required - resume graphs paused due to login
        console.log(`[ExecutionService] Login no longer required - resuming paused graphs`);
        for (const graphId of Array.from(this.loginMonitor.pausedGraphsSet)) {
          console.log(`[ExecutionService] Resuming graph ${graphId} - login resolved`);
          const res = this.resumeGraph(graphId);
          if (res?.success) {
            this.loginMonitor.pausedGraphsSet.delete(graphId);
          }
        }
      }

      // When logged in on the monitor tab, repeatedly fetch orders and reload each interval
      if (required === false) {
        try {
          console.log('[ExecutionService] Logged in on monitor tab. Fetching orders (recurring)...');
          const fetchResult = await this.sendMessageToTab(this.loginMonitor.tabId, {
            type: MESSAGE_TYPES.PERFORM_SITE_ACTIONS,
            actions: [
              { action: 'GetOpenOrders', arguments: {} },
              { action: 'GetCompletedOrders', arguments: {} }
            ]
          });
          console.log('[ExecutionService] Orders fetch result:', fetchResult);
          this.loginMonitor.lastFetchAt = new Date();
        } catch (fetchErr) {
          console.warn('[ExecutionService] Failed to fetch orders on monitor tab:', fetchErr);
        }

        // After each fetch cycle, reload to keep page fresh for next interval
        try { await chrome.tabs.reload(this.loginMonitor.tabId); } catch (_) {}
      }
    } catch (e) {
      console.warn(`[ExecutionService] Login check failed:`, e);
      // If tab vanished, recreate next tick
      try { 
        await chrome.tabs.get(this.loginMonitor.tabId); 
      } catch { 
        this.loginMonitor.tabId = null; 
      }
    } finally {
      this.loginMonitor.checking = false;
    }
  }

  /**
   * Start the login monitoring system
   */
  startLoginMonitor() {
    if (this.loginMonitor.started) return;
    this.loginMonitor.started = true;

    console.log(`[ExecutionService] Starting login monitor`);

    // Ensure tab, then start ticker
    this.ensureLoginMonitorTab().catch((e) => {
      console.warn(`[ExecutionService] Failed to ensure login monitor tab:`, e);
    });
    
    // Recreate if closed. Keep a reference so we can remove this listener on stop.
    this.loginMonitor.onRemovedHandler = (tabId) => {
      if (!this.loginMonitor.started) return; // Do not recreate if monitor is stopped
      if (tabId === this.loginMonitor.tabId && this.loginMonitor.isDedicated) {
        console.log(`[ExecutionService] Login monitor tab closed, recreating`);
        this.loginMonitor.tabId = null;
        // Debounce recreation to avoid duplicate creations from race
        setTimeout(() => {
          if (!this.loginMonitor.started) return; // Respect stopped state
          if (!this.loginMonitor.tabId && !this.loginMonitor.creationPromise) {
            this.ensureLoginMonitorTab().catch((e) => {
              console.warn(`[ExecutionService] Failed to recreate login monitor tab:`, e);
            });
          }
        }, 200);
      }
    };
    chrome.tabs.onRemoved.addListener(this.loginMonitor.onRemovedHandler);

    // Start 1-second interval check
    this.loginMonitor.intervalId = setInterval(async () => {
      if (!this.loginMonitor.tabId && !this.loginMonitor.creationPromise) {
        await this.ensureLoginMonitorTab().catch((e) => {
          console.warn(`[ExecutionService] Failed to ensure login monitor tab in interval:`, e);
        });
        return;
      }
      if (this.loginMonitor.tabId) {
        await this.checkLoginAndTogglePause();
      }
    }, 1000);

    // Also check immediately when graphs are added or start executing
    this.loginMonitor.checkImmediately = () => {
      if (!this.loginMonitor.checking && this.loginMonitor.tabId) {
        this.checkLoginAndTogglePause().catch((e) => {
          console.warn(`[ExecutionService] Immediate login check failed:`, e);
        });
      }
    };

    // First immediate check - delay to avoid race with tab creation
    setTimeout(() => {
      if (this.loginMonitor.tabId) {
        this.checkLoginAndTogglePause().catch((e) => {
          console.warn(`[ExecutionService] Initial login check failed:`, e);
        });
      }
    }, 500);
  }

  /**
   * Stop the login monitoring system
   */
  stopLoginMonitor(closeTab = false) {
    if (!this.loginMonitor.started && !this.loginMonitor.tabId) return;
    
    console.log(`[ExecutionService] Stopping login monitor`);
    
    if (this.loginMonitor.intervalId) {
      clearInterval(this.loginMonitor.intervalId);
      this.loginMonitor.intervalId = null;
    }
    
    // Remove onRemoved listener if present to avoid recreating after stop
    if (this.loginMonitor.onRemovedHandler) {
      try { chrome.tabs.onRemoved.removeListener(this.loginMonitor.onRemovedHandler); } catch (_) {}
      this.loginMonitor.onRemovedHandler = null;
    }
    
    // Optionally close the monitoring tab
    if (closeTab && this.loginMonitor.tabId && this.loginMonitor.isDedicated) {
      const tabIdToClose = this.loginMonitor.tabId;
      try { chrome.tabs.remove(tabIdToClose); } catch (_) {}
      this.loginMonitor.tabId = null;
      this.loginMonitor.isDedicated = false;
    }
    
    this.loginMonitor.started = false;
  }

  /**
   * Check login status right before action execution
   * This is the critical final check to prevent actions from executing when login is required
   * @param {number} tabId - Tab ID to check login status in
   * @param {string} nodeId - Node ID for logging
   */
  async checkLoginBeforeExecution(tabId, nodeId, graphId) {
    try {
      console.log(`[ExecutionService] Pre-execution login check for node: ${nodeId}`);
      
      // Send login check to the execution tab (not the monitor tab)
      const result = await this.sendMessageToTab(tabId, {
        type: 'PERFORM_SITE_ACTIONS',
        actions: [{ action: 'IsLoginRequired', arguments: {} }]
      });

      let loginRequired = false;
      if (result && Array.isArray(result.results)) {
        const r0 = result.results[0];
        if (r0?.success) {
          loginRequired = !!r0?.data?.loginRequired;
        }
      }

      console.log(`[ExecutionService] Pre-execution login check result: loginRequired=${loginRequired}`);

      if (loginRequired) {
        // Ensure monitor reflects required
        this.loginMonitor.required = true;
        this.loginMonitor.lastChecked = new Date();

        // Pause current graph to prevent progress
        if (graphId) {
          this.pauseGraph(graphId);
          this.loginMonitor.pausedGraphsSet.add(graphId);
        }

        console.log(`[ExecutionService] Waiting for login before executing node ${nodeId}`);
        // Wait until login is no longer required
        while (this.loginMonitor.required !== false) {
          await this.checkLoginAndTogglePause().catch(() => {});
          await this.delay(500);
        }

        // Resume graph if it was paused here
        if (graphId && this.pausedGraphs.has(graphId)) {
          this.resumeGraph(graphId);
        }
      }
    } catch (error) {
      console.warn(`[ExecutionService] Pre-execution login check failed for node ${nodeId}:`, error.message);
      // Fall back to monitor state: if login currently required, wait; otherwise proceed
      if (this.loginMonitor.required === true) {
        if (graphId) {
          this.pauseGraph(graphId);
          this.loginMonitor.pausedGraphsSet.add(graphId);
        }
        while (this.loginMonitor.required !== false) {
          await this.checkLoginAndTogglePause().catch(() => {});
          await this.delay(500);
        }
        if (graphId && this.pausedGraphs.has(graphId)) {
          this.resumeGraph(graphId);
        }
      }
    }
  }
} 