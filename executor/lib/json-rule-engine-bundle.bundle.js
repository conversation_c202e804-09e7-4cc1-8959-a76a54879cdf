/*! For license information please see json-rule-engine-bundle.bundle.js.LICENSE.txt */
var JsonRuleEngineBundle;!function(){var e={11:function(e,t,n){var r;!function(o){var i=Object.hasOwnProperty,a=Array.isArray?Array.isArray:function(e){return"[object Array]"===Object.prototype.toString.call(e)},s="object"==typeof process&&"function"==typeof process.nextTick,c="function"==typeof Symbol,u="object"==typeof Reflect,l="function"==typeof setImmediate?setImmediate:setTimeout,f=c?u&&"function"==typeof Reflect.ownKeys?Reflect.ownKeys:function(e){var t=Object.getOwnPropertyNames(e);return t.push.apply(t,Object.getOwnPropertySymbols(e)),t}:Object.keys;function h(){this._events={},this._conf&&p.call(this,this._conf)}function p(e){e&&(this._conf=e,e.delimiter&&(this.delimiter=e.delimiter),e.maxListeners!==o&&(this._maxListeners=e.maxListeners),e.wildcard&&(this.wildcard=e.wildcard),e.newListener&&(this._newListener=e.newListener),e.removeListener&&(this._removeListener=e.removeListener),e.verboseMemoryLeak&&(this.verboseMemoryLeak=e.verboseMemoryLeak),e.ignoreErrors&&(this.ignoreErrors=e.ignoreErrors),this.wildcard&&(this.listenerTree={}))}function d(e,t){var n="(node) warning: possible EventEmitter memory leak detected. "+e+" listeners added. Use emitter.setMaxListeners() to increase limit.";if(this.verboseMemoryLeak&&(n+=" Event name: "+t+"."),"undefined"!=typeof process&&process.emitWarning){var r=new Error(n);r.name="MaxListenersExceededWarning",r.emitter=this,r.count=e,process.emitWarning(r)}else console.error(n),console.trace&&console.trace()}var v=function(e,t,n){var r=arguments.length;switch(r){case 0:return[];case 1:return[e];case 2:return[e,t];case 3:return[e,t,n];default:for(var o=new Array(r);r--;)o[r]=arguments[r];return o}};function y(e,t){for(var n={},r=e.length,i=t?t.length:0,a=0;a<r;a++)n[e[a]]=a<i?t[a]:o;return n}function g(e,t,n){var r,o;if(this._emitter=e,this._target=t,this._listeners={},this._listenersCount=0,(n.on||n.off)&&(r=n.on,o=n.off),t.addEventListener?(r=t.addEventListener,o=t.removeEventListener):t.addListener?(r=t.addListener,o=t.removeListener):t.on&&(r=t.on,o=t.off),!r&&!o)throw Error("target does not implement any known event API");if("function"!=typeof r)throw TypeError("on method must be a function");if("function"!=typeof o)throw TypeError("off method must be a function");this._on=r,this._off=o;var i=e._observers;i?i.push(this):e._observers=[this]}function m(e,t,n,r){var a=Object.assign({},t);if(!e)return a;if("object"!=typeof e)throw TypeError("options must be an object");var s,c,u,l=Object.keys(e),f=l.length;function h(e){throw Error('Invalid "'+s+'" option value'+(e?". Reason: "+e:""))}for(var p=0;p<f;p++){if(s=l[p],!r&&!i.call(t,s))throw Error('Unknown "'+s+'" option');(c=e[s])!==o&&(u=n[s],a[s]=u?u(c,h):c)}return a}function b(e,t){return"function"==typeof e&&e.hasOwnProperty("prototype")||t("value must be a constructor"),e}function E(e){var t="value must be type of "+e.join("|"),n=e.length,r=e[0],o=e[1];return 1===n?function(e,n){if(typeof e===r)return e;n(t)}:2===n?function(e,n){var i=typeof e;if(i===r||i===o)return e;n(t)}:function(r,o){for(var i=typeof r,a=n;a-- >0;)if(i===e[a])return r;o(t)}}Object.assign(g.prototype,{subscribe:function(e,t,n){var r=this,o=this._target,i=this._emitter,a=this._listeners,s=function(){var r=v.apply(null,arguments),a={data:r,name:t,original:e};n?!1!==n.call(o,a)&&i.emit.apply(i,[a.name].concat(r)):i.emit.apply(i,[t].concat(r))};if(a[e])throw Error("Event '"+e+"' is already listening");this._listenersCount++,i._newListener&&i._removeListener&&!r._onNewListener?(this._onNewListener=function(n){n===t&&null===a[e]&&(a[e]=s,r._on.call(o,e,s))},i.on("newListener",this._onNewListener),this._onRemoveListener=function(n){n===t&&!i.hasListeners(n)&&a[e]&&(a[e]=null,r._off.call(o,e,s))},a[e]=null,i.on("removeListener",this._onRemoveListener)):(a[e]=s,r._on.call(o,e,s))},unsubscribe:function(e){var t,n,r,o=this,i=this._listeners,a=this._emitter,s=this._off,c=this._target;if(e&&"string"!=typeof e)throw TypeError("event must be a string");function u(){o._onNewListener&&(a.off("newListener",o._onNewListener),a.off("removeListener",o._onRemoveListener),o._onNewListener=null,o._onRemoveListener=null);var e=T.call(a,o);a._observers.splice(e,1)}if(e){if(!(t=i[e]))return;s.call(c,e,t),delete i[e],--this._listenersCount||u()}else{for(r=(n=f(i)).length;r-- >0;)e=n[r],s.call(c,e,i[e]);this._listeners={},this._listenersCount=0,u()}}});var S=E(["function"]),w=E(["object","function"]);function _(e,t,n){var r,o,i,a=0,s=new e(function(c,u,l){function f(){o&&(o=null),a&&(clearTimeout(a),a=0)}n=m(n,{timeout:0,overload:!1},{timeout:function(e,t){return("number"!=typeof(e*=1)||e<0||!Number.isFinite(e))&&t("timeout must be a positive number"),e}}),r=!n.overload&&"function"==typeof e.prototype.cancel&&"function"==typeof l;var h=function(e){f(),c(e)},p=function(e){f(),u(e)};r?t(h,p,l):(o=[function(e){p(e||Error("canceled"))}],t(h,p,function(e){if(i)throw Error("Unable to subscribe on cancel event asynchronously");if("function"!=typeof e)throw TypeError("onCancel callback must be a function");o.push(e)}),i=!0),n.timeout>0&&(a=setTimeout(function(){var e=Error("timeout");e.code="ETIMEDOUT",a=0,s.cancel(e),u(e)},n.timeout))});return r||(s.cancel=function(e){if(o){for(var t=o.length,n=1;n<t;n++)o[n](e);o[0](e),o=null}}),s}function T(e){var t=this._observers;if(!t)return-1;for(var n=t.length,r=0;r<n;r++)if(t[r]._target===e)return r;return-1}function O(e,t,n,r,o){if(!n)return null;if(0===r){var i=typeof t;if("string"===i){var a,s,c=0,u=0,l=this.delimiter,h=l.length;if(-1!==(s=t.indexOf(l))){a=new Array(5);do{a[c++]=t.slice(u,s),u=s+h}while(-1!==(s=t.indexOf(l,u)));a[c++]=t.slice(u),t=a,o=c}else t=[t],o=1}else"object"===i?o=t.length:(t=[t],o=1)}var p,d,v,y,g,m,b,E=null,S=t[r],w=t[r+1];if(r===o)n._listeners&&("function"==typeof n._listeners?(e&&e.push(n._listeners),E=[n]):(e&&e.push.apply(e,n._listeners),E=[n]));else{if("*"===S){for(s=(m=f(n)).length;s-- >0;)"_listeners"!==(p=m[s])&&(b=O(e,t,n[p],r+1,o))&&(E?E.push.apply(E,b):E=b);return E}if("**"===S){for((g=r+1===o||r+2===o&&"*"===w)&&n._listeners&&(E=O(e,t,n,o,o)),s=(m=f(n)).length;s-- >0;)"_listeners"!==(p=m[s])&&("*"===p||"**"===p?(n[p]._listeners&&!g&&(b=O(e,t,n[p],o,o))&&(E?E.push.apply(E,b):E=b),b=O(e,t,n[p],r,o)):b=O(e,t,n[p],p===w?r+2:r,o),b&&(E?E.push.apply(E,b):E=b));return E}n[S]&&(E=O(e,t,n[S],r+1,o))}if((d=n["*"])&&O(e,t,d,r+1,o),v=n["**"])if(r<o)for(v._listeners&&O(e,t,v,o,o),s=(m=f(v)).length;s-- >0;)"_listeners"!==(p=m[s])&&(p===w?O(e,t,v[p],r+2,o):p===S?O(e,t,v[p],r+1,o):((y={})[p]=v[p],O(e,t,{"**":y},r+1,o)));else v._listeners?O(e,t,v,o,o):v["*"]&&v["*"]._listeners&&O(e,t,v["*"],o,o);return E}function k(e,t,n){var r,o,i=0,a=0,s=this.delimiter,c=s.length;if("string"==typeof e)if(-1!==(r=e.indexOf(s))){o=new Array(5);do{o[i++]=e.slice(a,r),a=r+c}while(-1!==(r=e.indexOf(s,a)));o[i++]=e.slice(a)}else o=[e],i=1;else o=e,i=e.length;if(i>1)for(r=0;r+1<i;r++)if("**"===o[r]&&"**"===o[r+1])return;var u,l=this.listenerTree;for(r=0;r<i;r++)if(l=l[u=o[r]]||(l[u]={}),r===i-1)return l._listeners?("function"==typeof l._listeners&&(l._listeners=[l._listeners]),n?l._listeners.unshift(t):l._listeners.push(t),!l._listeners.warned&&this._maxListeners>0&&l._listeners.length>this._maxListeners&&(l._listeners.warned=!0,d.call(this,l._listeners.length,u))):l._listeners=t,!0;return!0}function P(e,t,n,r){for(var o,i,a,s,c=f(e),u=c.length,l=e._listeners;u-- >0;)o=e[i=c[u]],a="_listeners"===i?n:n?n.concat(i):[i],s=r||"symbol"==typeof i,l&&t.push(s?a:a.join(this.delimiter)),"object"==typeof o&&P.call(this,o,t,a,s);return t}function A(e){for(var t,n,r,o=f(e),i=o.length;i-- >0;)(t=e[n=o[i]])&&(r=!0,"_listeners"===n||A(t)||delete e[n]);return r}function x(e,t,n){this.emitter=e,this.event=t,this.listener=n}function I(e,t,n){if(!0===n)i=!0;else if(!1===n)r=!0;else{if(!n||"object"!=typeof n)throw TypeError("options should be an object or true");var r=n.async,i=n.promisify,a=n.nextTick,c=n.objectify}if(r||a||i){var u=t,f=t._origin||t;if(a&&!s)throw Error("process.nextTick is not supported");i===o&&(i="AsyncFunction"===t.constructor.name),t=function(){var e=arguments,t=this,n=this.event;return i?a?Promise.resolve():new Promise(function(e){l(e)}).then(function(){return t.event=n,u.apply(t,e)}):(a?process.nextTick:l)(function(){t.event=n,u.apply(t,e)})},t._async=!0,t._origin=f}return[t,c?new x(this,e,t):this]}function M(e){this._events={},this._newListener=!1,this._removeListener=!1,this.verboseMemoryLeak=!1,p.call(this,e)}x.prototype.off=function(){return this.emitter.off(this.event,this.listener),this},M.EventEmitter2=M,M.prototype.listenTo=function(e,t,n){if("object"!=typeof e)throw TypeError("target musts be an object");var r=this;function i(t){if("object"!=typeof t)throw TypeError("events must be an object");var o,i=n.reducers,a=T.call(r,e);o=-1===a?new g(r,e,n):r._observers[a];for(var s,c=f(t),u=c.length,l="function"==typeof i,h=0;h<u;h++)s=c[h],o.subscribe(s,t[s]||s,l?i:i&&i[s])}return n=m(n,{on:o,off:o,reducers:o},{on:S,off:S,reducers:w}),a(t)?i(y(t)):i("string"==typeof t?y(t.split(/\s+/)):t),this},M.prototype.stopListeningTo=function(e,t){var n=this._observers;if(!n)return!1;var r,o=n.length,i=!1;if(e&&"object"!=typeof e)throw TypeError("target should be an object");for(;o-- >0;)r=n[o],e&&r._target!==e||(r.unsubscribe(t),i=!0);return i},M.prototype.delimiter=".",M.prototype.setMaxListeners=function(e){e!==o&&(this._maxListeners=e,this._conf||(this._conf={}),this._conf.maxListeners=e)},M.prototype.getMaxListeners=function(){return this._maxListeners},M.prototype.event="",M.prototype.once=function(e,t,n){return this._once(e,t,!1,n)},M.prototype.prependOnceListener=function(e,t,n){return this._once(e,t,!0,n)},M.prototype._once=function(e,t,n,r){return this._many(e,1,t,n,r)},M.prototype.many=function(e,t,n,r){return this._many(e,t,n,!1,r)},M.prototype.prependMany=function(e,t,n,r){return this._many(e,t,n,!0,r)},M.prototype._many=function(e,t,n,r,o){var i=this;if("function"!=typeof n)throw new Error("many only accepts instances of Function");function a(){return 0===--t&&i.off(e,a),n.apply(this,arguments)}return a._origin=n,this._on(e,a,r,o)},M.prototype.emit=function(){if(!this._events&&!this._all)return!1;this._events||h.call(this);var e,t,n,r,o,i,a=arguments[0],s=this.wildcard;if("newListener"===a&&!this._newListener&&!this._events.newListener)return!1;if(s&&(e=a,"newListener"!==a&&"removeListener"!==a&&"object"==typeof a)){if(n=a.length,c)for(r=0;r<n;r++)if("symbol"==typeof a[r]){i=!0;break}i||(a=a.join(this.delimiter))}var u,l=arguments.length;if(this._all&&this._all.length)for(r=0,n=(u=this._all.slice()).length;r<n;r++)switch(this.event=a,l){case 1:u[r].call(this,a);break;case 2:u[r].call(this,a,arguments[1]);break;case 3:u[r].call(this,a,arguments[1],arguments[2]);break;default:u[r].apply(this,arguments)}if(s)u=[],O.call(this,u,e,this.listenerTree,0,n);else{if("function"==typeof(u=this._events[a])){switch(this.event=a,l){case 1:u.call(this);break;case 2:u.call(this,arguments[1]);break;case 3:u.call(this,arguments[1],arguments[2]);break;default:for(t=new Array(l-1),o=1;o<l;o++)t[o-1]=arguments[o];u.apply(this,t)}return!0}u&&(u=u.slice())}if(u&&u.length){if(l>3)for(t=new Array(l-1),o=1;o<l;o++)t[o-1]=arguments[o];for(r=0,n=u.length;r<n;r++)switch(this.event=a,l){case 1:u[r].call(this);break;case 2:u[r].call(this,arguments[1]);break;case 3:u[r].call(this,arguments[1],arguments[2]);break;default:u[r].apply(this,t)}return!0}if(!this.ignoreErrors&&!this._all&&"error"===a)throw arguments[1]instanceof Error?arguments[1]:new Error("Uncaught, unspecified 'error' event.");return!!this._all},M.prototype.emitAsync=function(){if(!this._events&&!this._all)return!1;this._events||h.call(this);var e,t,n,r,o,i,a=arguments[0],s=this.wildcard;if("newListener"===a&&!this._newListener&&!this._events.newListener)return Promise.resolve([!1]);if(s&&(e=a,"newListener"!==a&&"removeListener"!==a&&"object"==typeof a)){if(r=a.length,c)for(o=0;o<r;o++)if("symbol"==typeof a[o]){t=!0;break}t||(a=a.join(this.delimiter))}var u,l=[],f=arguments.length;if(this._all)for(o=0,r=this._all.length;o<r;o++)switch(this.event=a,f){case 1:l.push(this._all[o].call(this,a));break;case 2:l.push(this._all[o].call(this,a,arguments[1]));break;case 3:l.push(this._all[o].call(this,a,arguments[1],arguments[2]));break;default:l.push(this._all[o].apply(this,arguments))}if(s?(u=[],O.call(this,u,e,this.listenerTree,0)):u=this._events[a],"function"==typeof u)switch(this.event=a,f){case 1:l.push(u.call(this));break;case 2:l.push(u.call(this,arguments[1]));break;case 3:l.push(u.call(this,arguments[1],arguments[2]));break;default:for(n=new Array(f-1),i=1;i<f;i++)n[i-1]=arguments[i];l.push(u.apply(this,n))}else if(u&&u.length){if(u=u.slice(),f>3)for(n=new Array(f-1),i=1;i<f;i++)n[i-1]=arguments[i];for(o=0,r=u.length;o<r;o++)switch(this.event=a,f){case 1:l.push(u[o].call(this));break;case 2:l.push(u[o].call(this,arguments[1]));break;case 3:l.push(u[o].call(this,arguments[1],arguments[2]));break;default:l.push(u[o].apply(this,n))}}else if(!this.ignoreErrors&&!this._all&&"error"===a)return arguments[1]instanceof Error?Promise.reject(arguments[1]):Promise.reject("Uncaught, unspecified 'error' event.");return Promise.all(l)},M.prototype.on=function(e,t,n){return this._on(e,t,!1,n)},M.prototype.prependListener=function(e,t,n){return this._on(e,t,!0,n)},M.prototype.onAny=function(e){return this._onAny(e,!1)},M.prototype.prependAny=function(e){return this._onAny(e,!0)},M.prototype.addListener=M.prototype.on,M.prototype._onAny=function(e,t){if("function"!=typeof e)throw new Error("onAny only accepts instances of Function");return this._all||(this._all=[]),t?this._all.unshift(e):this._all.push(e),this},M.prototype._on=function(e,t,n,r){if("function"==typeof e)return this._onAny(e,t),this;if("function"!=typeof t)throw new Error("on only accepts instances of Function");this._events||h.call(this);var i,a=this;return r!==o&&(t=(i=I.call(this,e,t,r))[0],a=i[1]),this._newListener&&this.emit("newListener",e,t),this.wildcard?(k.call(this,e,t,n),a):(this._events[e]?("function"==typeof this._events[e]&&(this._events[e]=[this._events[e]]),n?this._events[e].unshift(t):this._events[e].push(t),!this._events[e].warned&&this._maxListeners>0&&this._events[e].length>this._maxListeners&&(this._events[e].warned=!0,d.call(this,this._events[e].length,e))):this._events[e]=t,a)},M.prototype.off=function(e,t){if("function"!=typeof t)throw new Error("removeListener only takes instances of Function");var n,r=[];if(this.wildcard){var o="string"==typeof e?e.split(this.delimiter):e.slice();if(!(r=O.call(this,null,o,this.listenerTree,0)))return this}else{if(!this._events[e])return this;n=this._events[e],r.push({_listeners:n})}for(var i=0;i<r.length;i++){var s=r[i];if(n=s._listeners,a(n)){for(var c=-1,u=0,l=n.length;u<l;u++)if(n[u]===t||n[u].listener&&n[u].listener===t||n[u]._origin&&n[u]._origin===t){c=u;break}if(c<0)continue;return this.wildcard?s._listeners.splice(c,1):this._events[e].splice(c,1),0===n.length&&(this.wildcard?delete s._listeners:delete this._events[e]),this._removeListener&&this.emit("removeListener",e,t),this}(n===t||n.listener&&n.listener===t||n._origin&&n._origin===t)&&(this.wildcard?delete s._listeners:delete this._events[e],this._removeListener&&this.emit("removeListener",e,t))}return this.listenerTree&&A(this.listenerTree),this},M.prototype.offAny=function(e){var t,n=0,r=0;if(e&&this._all&&this._all.length>0){for(n=0,r=(t=this._all).length;n<r;n++)if(e===t[n])return t.splice(n,1),this._removeListener&&this.emit("removeListenerAny",e),this}else{if(t=this._all,this._removeListener)for(n=0,r=t.length;n<r;n++)this.emit("removeListenerAny",t[n]);this._all=[]}return this},M.prototype.removeListener=M.prototype.off,M.prototype.removeAllListeners=function(e){if(e===o)return!this._events||h.call(this),this;if(this.wildcard){var t,n=O.call(this,null,e,this.listenerTree,0);if(!n)return this;for(t=0;t<n.length;t++)n[t]._listeners=null;this.listenerTree&&A(this.listenerTree)}else this._events&&(this._events[e]=null);return this},M.prototype.listeners=function(e){var t,n,r,i,a,s=this._events;if(e===o){if(this.wildcard)throw Error("event name required for wildcard emitter");if(!s)return[];for(i=(t=f(s)).length,r=[];i-- >0;)"function"==typeof(n=s[t[i]])?r.push(n):r.push.apply(r,n);return r}if(this.wildcard){if(!(a=this.listenerTree))return[];var c=[],u="string"==typeof e?e.split(this.delimiter):e.slice();return O.call(this,c,u,a,0),c}return s&&(n=s[e])?"function"==typeof n?[n]:n:[]},M.prototype.eventNames=function(e){var t=this._events;return this.wildcard?P.call(this,this.listenerTree,[],null,e):t?f(t):[]},M.prototype.listenerCount=function(e){return this.listeners(e).length},M.prototype.hasListeners=function(e){if(this.wildcard){var t=[],n="string"==typeof e?e.split(this.delimiter):e.slice();return O.call(this,t,n,this.listenerTree,0),t.length>0}var r=this._events,i=this._all;return!!(i&&i.length||r&&(e===o?f(r).length:r[e]))},M.prototype.listenersAny=function(){return this._all?this._all:[]},M.prototype.waitFor=function(e,t){var n=this,r=typeof t;return"number"===r?t={timeout:t}:"function"===r&&(t={filter:t}),_((t=m(t,{timeout:0,filter:o,handleError:!1,Promise:Promise,overload:!1},{filter:S,Promise:b})).Promise,function(r,o,i){function a(){var i=t.filter;if(!i||i.apply(n,arguments))if(n.off(e,a),t.handleError){var s=arguments[0];s?o(s):r(v.apply(null,arguments).slice(1))}else r(v.apply(null,arguments))}i(function(){n.off(e,a)}),n._on(e,a,!1)},{timeout:t.timeout,overload:t.overload})};var C=M.prototype;Object.defineProperties(M,{defaultMaxListeners:{get:function(){return C._maxListeners},set:function(e){if("number"!=typeof e||e<0||Number.isNaN(e))throw TypeError("n must be a non-negative number");C._maxListeners=e},enumerable:!0},once:{value:function(e,t,n){return _((n=m(n,{Promise:Promise,timeout:0,overload:!1},{Promise:b})).Promise,function(n,r,o){var i;if("function"==typeof e.addEventListener)return i=function(){n(v.apply(null,arguments))},o(function(){e.removeEventListener(t,i)}),void e.addEventListener(t,i,{once:!0});var a,s=function(){a&&e.removeListener("error",a),n(v.apply(null,arguments))};"error"!==t&&(a=function(n){e.removeListener(t,s),r(n)},e.once("error",a)),o(function(){a&&e.removeListener("error",a),e.removeListener(t,s)}),e.once(t,s)},{timeout:n.timeout,overload:n.overload})},writable:!0,configurable:!0}}),Object.defineProperties(C,{_maxListeners:{value:10,writable:!0,configurable:!0},_observers:{value:null,writable:!0,configurable:!0}}),(r=function(){return M}.call(t,n,t,e))===o||(e.exports=r)}()},26:function(e,t,n){"use strict";t.Engine=void 0;var r=s(n(589)),o=s(n(153)),i=s(n(273)),a=s(n(759));function s(e){return e&&e.__esModule?e:{default:e}}o.default,i.default,a.default,t.Engine=r.default},89:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,o=(r=n(759))&&r.__esModule?r:{default:r},i=[];function a(e){return"NaN"!==Number.parseFloat(e).toString()}i.push(new o.default("equal",function(e,t){return e===t})),i.push(new o.default("notEqual",function(e,t){return e!==t})),i.push(new o.default("in",function(e,t){return t.indexOf(e)>-1})),i.push(new o.default("notIn",function(e,t){return-1===t.indexOf(e)})),i.push(new o.default("contains",function(e,t){return e.indexOf(t)>-1},Array.isArray)),i.push(new o.default("doesNotContain",function(e,t){return-1===e.indexOf(t)},Array.isArray)),i.push(new o.default("lessThan",function(e,t){return e<t},a)),i.push(new o.default("lessThanInclusive",function(e,t){return e<=t},a)),i.push(new o.default("greaterThan",function(e,t){return e>t},a)),i.push(new o.default("greaterThanInclusive",function(e,t){return e>=t},a)),t.default=i},138:function(e){e.exports=function(e){return!!e&&"object"==typeof e}},148:function(e){e.exports=function(){"use strict";var e=function(e,t){return e.reduce(function(e,n){var r="[object "+n+"]";return t?e[r]=n:e[n]=r,e},{})},t=function(e){return e.reduce(function(e,t){return e[t]=!0,e},{})},n=["Array","Arguments","Object","RegExp","Symbol","Map","Set","Date","Error","Event","Generator","Promise","WeakMap","WeakSet","DocumentFragment","Float32Array","Float64Array","Int8Array","Int16Array","Int32Array","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","ArrayBuffer","DataView","DocumentFragment","Window","String","Number","Boolean","Function","Undefined","GeneratorFunction","BigInt","Null"],r=e(n,!1),o=e(n,!0),i=t([r.Generator,r.Promise,r.WeakMap,r.WeakSet]),a=t([r.Map,r.Set]),s=t([r.Date,r.RegExp]),c=t(["bigint","boolean","function","number","string","undefined"]),u=t([r.Arguments,r.Array]),l=t([r.RegExp,r.Symbol]),f=t([r.Float32Array,r.Float64Array,r.Int8Array,r.Int16Array,r.Int32Array,r.Uint8Array,r.Uint8ClampedArray,r.Uint16Array,r.Uint32Array]),h="undefined"!=typeof Buffer&&"function"==typeof Buffer.from,p="function"==typeof Uint16Array;var d=h?function(e){return Buffer.from(e).toString("utf8")}:p?function(e){return String.fromCharCode.apply(null,new Uint16Array(e))}:function(e){return""},v=/\[object ([HTML|SVG](.*)Element)\]/,y=Object.prototype.toString,g=Object.keys;function m(e,t){return e>t}function b(e,t){return e[0]>t[0]}function E(e,t){for(var n,r,o=0;o<e.length;++o){for(r=e[o],n=o-1;~n&&t(e[n],r);--n)e[n+1]=e[n];e[n+1]=r}return e}function S(e){for(var t,n=E(g(e),m),r={},o=0;o<n.length;++o)r[t=n[o]]=e[t];return r}function w(e,t){for(var n=0;n<e.length;++n)if(e[n]===t)return n+1;return 0}function _(e,t,n,s){if(!s){var h=typeof e;if(c[h])return h+"|"+e;if(null===e)return e+"|"+e}var p,g=s||y.call(e);return u[g]?e:g===r.Object?S(e):l[g]?o[g]+"|"+e.toString():a[g]?e instanceof Map?function(e,t,n){var r=[];e.forEach(function(e,o){r.push([T(o,t,n),T(e,t,n)])}),E(r,b);for(var o,i=0;i<r.length;++i)o=r[i],r[i]="["+o[0]+","+o[1]+"]";return"Map|["+r.join(",")+"]"}(e,t,n):function(e,t,n){var r=[];return e.forEach(function(e){r.push(T(e,t,n))}),E(r,m),"Set|["+r.join(",")+"]"}(e,t,n):g===r.Date?o[g]+"|"+e.getTime():g===r.Error?o[g]+"|"+e.stack:g===r.Event?{bubbles:(p=e).bubbles,cancelBubble:p.cancelBubble,cancelable:p.cancelable,composed:p.composed,currentTarget:p.currentTarget,defaultPrevented:p.defaultPrevented,eventPhase:p.eventPhase,isTrusted:p.isTrusted,returnValue:p.returnValue,target:p.target,type:p.type}:i[g]?o[g]+"|NOT_ENUMERABLE":v.test(g)?g.slice(8,-1)+"|"+e.outerHTML:g===r.DocumentFragment?o[g]+"|"+function(e){for(var t=e.children,n=[],r=0;r<t.length;++r)n.push(t[r].outerHTML);return n.join(",")}(e):f[g]?o[g]+"|"+e.join(","):g===r.ArrayBuffer?o[g]+"|"+d(e):g===r.DataView?o[g]+"|"+d(e.buffer):e}function T(e,t,n){if(!e||"object"!=typeof e)return _(e,t,n);var o=y.call(e);return s[o]?_(e,t,n,o):JSON.stringify(e,function(e,t){return void 0===e&&(e=[]),void 0===t&&(t=[]),function(n,o){if("object"==typeof o)if(e.length){var i=w(e,this);0===i?e.push(this):(e.splice(i),t.splice(i)),t.push(n);var a=w(e,o);if(0!==a)return"[~"+(t.slice(0,a).join(".")||".")+"]";e.push(o)}else e[0]=o,t[0]=n;return n&&this[n]instanceof Date?_(this[n],e,t,r.Date):_(o,e,t)}}(t,n))}function O(e){return function(e){for(var t,n=e.length,r=5381,o=52711;n--;)r=33*r^(t=e.charCodeAt(n)),o=33*o^t;return 4096*(r>>>0)+(o>>>0)}(T(e))}function k(e,t){return O(e)===O(t)}return k.all=function(e){for(var t=0;t<(arguments.length<=1?0:arguments.length-1);++t)if(!k(e,t+1<1||arguments.length<=t+1?void 0:arguments[t+1]))return!1;return!0},k.any=function(e){for(var t=0;t<(arguments.length<=1?0:arguments.length-1);++t)if(k(e,t+1<1||arguments.length<=t+1?void 0:arguments[t+1]))return!0;return!1},k.not=function(e,t){return O(e)!==O(t)},O.is=k,O}()},151:function(e,t,n){"use strict";function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function o(e){return o=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},o(e)}function i(e,t){return i=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},i(e,t)}function a(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}function s(e,t,n){return s=a()?Reflect.construct:function(e,t,n){var r=[null];r.push.apply(r,t);var o=new(Function.bind.apply(e,r));return n&&i(o,n.prototype),o},s.apply(null,arguments)}function c(e){var t="function"==typeof Map?new Map:void 0;return c=function(e){if(null===e||(n=e,-1===Function.toString.call(n).indexOf("[native code]")))return e;var n;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return s(e,arguments,o(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),i(r,e)},c(e)}function u(e){return function(e){if(Array.isArray(e))return f(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||l(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(e,t){if(e){if("string"==typeof e)return f(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(e,t):void 0}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}n.r(t),n.d(t,{JSONPath:function(){return y}});var h=Object.prototype.hasOwnProperty;function p(e,t){return(e=e.slice()).push(t),e}function d(e,t){return(t=t.slice()).unshift(e),t}var v=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&i(e,t)}(s,e);var t,n,r=(t=s,n=a(),function(){var e,r=o(t);if(n){var i=o(this).constructor;e=Reflect.construct(r,arguments,i)}else e=r.apply(this,arguments);return function(e,t){return!t||"object"!=typeof t&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}(this,e)});function s(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),(t=r.call(this,'JSONPath should not be called with "new" (it prevents return of (unwrapped) scalar values)')).avoidNew=!0,t.value=e,t.name="NewError",t}return s}(c(Error));function y(e,t,n,o,i){if(!(this instanceof y))try{return new y(e,t,n,o,i)}catch(e){if(!e.avoidNew)throw e;return e.value}"string"==typeof e&&(i=o,o=n,n=t,t=e,e=null);var a=e&&"object"===r(e);if(e=e||{},this.json=e.json||n,this.path=e.path||t,this.resultType=e.resultType||"value",this.flatten=e.flatten||!1,this.wrap=!h.call(e,"wrap")||e.wrap,this.sandbox=e.sandbox||{},this.preventEval=e.preventEval||!1,this.parent=e.parent||null,this.parentProperty=e.parentProperty||null,this.callback=e.callback||o||null,this.otherTypeCallback=e.otherTypeCallback||i||function(){throw new TypeError("You must supply an otherTypeCallback callback option with the @other() operator.")},!1!==e.autostart){var s={path:a?e.path:t};a?"json"in e&&(s.json=e.json):s.json=n;var c=this.evaluate(s);if(!c||"object"!==r(c))throw new v(c);return c}}y.prototype.evaluate=function(e,t,n,o){var i=this,a=this.parent,s=this.parentProperty,c=this.flatten,u=this.wrap;if(this.currResultType=this.resultType,this.currPreventEval=this.preventEval,this.currSandbox=this.sandbox,n=n||this.callback,this.currOtherTypeCallback=o||this.otherTypeCallback,t=t||this.json,(e=e||this.path)&&"object"===r(e)&&!Array.isArray(e)){if(!e.path&&""!==e.path)throw new TypeError('You must supply a "path" property when providing an object argument to JSONPath.evaluate().');if(!h.call(e,"json"))throw new TypeError('You must supply a "json" property when providing an object argument to JSONPath.evaluate().');t=e.json,c=h.call(e,"flatten")?e.flatten:c,this.currResultType=h.call(e,"resultType")?e.resultType:this.currResultType,this.currSandbox=h.call(e,"sandbox")?e.sandbox:this.currSandbox,u=h.call(e,"wrap")?e.wrap:u,this.currPreventEval=h.call(e,"preventEval")?e.preventEval:this.currPreventEval,n=h.call(e,"callback")?e.callback:n,this.currOtherTypeCallback=h.call(e,"otherTypeCallback")?e.otherTypeCallback:this.currOtherTypeCallback,a=h.call(e,"parent")?e.parent:a,s=h.call(e,"parentProperty")?e.parentProperty:s,e=e.path}if(a=a||null,s=s||null,Array.isArray(e)&&(e=y.toPathString(e)),(e||""===e)&&t){var l=y.toPathArray(e);"$"===l[0]&&l.length>1&&l.shift(),this._hasParentSelector=null;var f=this._trace(l,t,["$"],a,s,n).filter(function(e){return e&&!e.isParentSelector});return f.length?u||1!==f.length||f[0].hasArrExpr?f.reduce(function(e,t){var n=i._getPreferredOutput(t);return c&&Array.isArray(n)?e=e.concat(n):e.push(n),e},[]):this._getPreferredOutput(f[0]):u?[]:void 0}},y.prototype._getPreferredOutput=function(e){var t=this.currResultType;switch(t){case"all":var n=Array.isArray(e.path)?e.path:y.toPathArray(e.path);return e.pointer=y.toPointer(n),e.path="string"==typeof e.path?e.path:y.toPathString(e.path),e;case"value":case"parent":case"parentProperty":return e[t];case"path":return y.toPathString(e[t]);case"pointer":return y.toPointer(e.path);default:throw new TypeError("Unknown result type")}},y.prototype._handleCallback=function(e,t,n){if(t){var r=this._getPreferredOutput(e);e.path="string"==typeof e.path?e.path:y.toPathString(e.path),t(r,n,e)}},y.prototype._trace=function(e,t,n,o,i,a,s,c){var u,f=this;if(!e.length)return u={path:n,value:t,parent:o,parentProperty:i,hasArrExpr:s},this._handleCallback(u,a,"value"),u;var v=e[0],y=e.slice(1),g=[];function m(e){Array.isArray(e)?e.forEach(function(e){g.push(e)}):g.push(e)}if(("string"!=typeof v||c)&&t&&h.call(t,v))m(this._trace(y,t[v],p(n,v),t,v,a,s));else if("*"===v)this._walk(v,y,t,n,o,i,a,function(e,t,n,r,o,i,a,s){m(f._trace(d(e,n),r,o,i,a,s,!0,!0))});else if(".."===v)m(this._trace(y,t,n,o,i,a,s)),this._walk(v,y,t,n,o,i,a,function(e,t,n,o,i,a,s,c){"object"===r(o[e])&&m(f._trace(d(t,n),o[e],p(i,e),o,e,c,!0))});else{if("^"===v)return this._hasParentSelector=!0,{path:n.slice(0,-1),expr:y,isParentSelector:!0};if("~"===v)return u={path:p(n,v),value:i,parent:o,parentProperty:null},this._handleCallback(u,a,"property"),u;if("$"===v)m(this._trace(y,t,n,null,null,a,s));else if(/^(\x2D?[0-9]*):(\x2D?[0-9]*):?([0-9]*)$/.test(v))m(this._slice(v,y,t,n,o,i,a));else if(0===v.indexOf("?(")){if(this.currPreventEval)throw new Error("Eval [?(expr)] prevented in JSONPath expression.");this._walk(v,y,t,n,o,i,a,function(e,t,n,r,o,i,a,s){f._eval(t.replace(/^\?\(((?:[\0-\t\x0B\f\x0E-\u2027\u202A-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*?)\)$/,"$1"),r[e],e,o,i,a)&&m(f._trace(d(e,n),r,o,i,a,s,!0))})}else if("("===v[0]){if(this.currPreventEval)throw new Error("Eval [(expr)] prevented in JSONPath expression.");m(this._trace(d(this._eval(v,t,n[n.length-1],n.slice(0,-1),o,i),y),t,n,o,i,a,s))}else if("@"===v[0]){var b=!1,E=v.slice(1,-2);switch(E){case"scalar":t&&["object","function"].includes(r(t))||(b=!0);break;case"boolean":case"string":case"undefined":case"function":r(t)===E&&(b=!0);break;case"integer":!Number.isFinite(t)||t%1||(b=!0);break;case"number":Number.isFinite(t)&&(b=!0);break;case"nonFinite":"number"!=typeof t||Number.isFinite(t)||(b=!0);break;case"object":t&&r(t)===E&&(b=!0);break;case"array":Array.isArray(t)&&(b=!0);break;case"other":b=this.currOtherTypeCallback(t,n,o,i);break;case"null":null===t&&(b=!0);break;default:throw new TypeError("Unknown value type "+E)}if(b)return u={path:n,value:t,parent:o,parentProperty:i},this._handleCallback(u,a,"value"),u}else if("`"===v[0]&&t&&h.call(t,v.slice(1))){var S=v.slice(1);m(this._trace(y,t[S],p(n,S),t,S,a,s,!0))}else if(v.includes(",")){var w,_=function(e){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=l(e))){t&&(e=t);var n=0,r=function(){};return{s:r,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,a=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return i=e.done,e},e:function(e){a=!0,o=e},f:function(){try{i||null==t.return||t.return()}finally{if(a)throw o}}}}(v.split(","));try{for(_.s();!(w=_.n()).done;){var T=w.value;m(this._trace(d(T,y),t,n,o,i,a,!0))}}catch(e){_.e(e)}finally{_.f()}}else!c&&t&&h.call(t,v)&&m(this._trace(y,t[v],p(n,v),t,v,a,s,!0))}if(this._hasParentSelector)for(var O=0;O<g.length;O++){var k=g[O];if(k&&k.isParentSelector){var P=this._trace(k.expr,t,k.path,o,i,a,s);if(Array.isArray(P)){g[O]=P[0];for(var A=P.length,x=1;x<A;x++)O++,g.splice(O,0,P[x])}else g[O]=P}}return g},y.prototype._walk=function(e,t,n,o,i,a,s,c){if(Array.isArray(n))for(var u=n.length,l=0;l<u;l++)c(l,e,t,n,o,i,a,s);else n&&"object"===r(n)&&Object.keys(n).forEach(function(r){c(r,e,t,n,o,i,a,s)})},y.prototype._slice=function(e,t,n,r,o,i,a){if(Array.isArray(n)){var s=n.length,c=e.split(":"),u=c[2]&&Number.parseInt(c[2])||1,l=c[0]&&Number.parseInt(c[0])||0,f=c[1]&&Number.parseInt(c[1])||s;l=l<0?Math.max(0,l+s):Math.min(s,l),f=f<0?Math.max(0,f+s):Math.min(s,f);for(var h=[],p=l;p<f;p+=u)this._trace(d(p,t),n,r,o,i,a,!0).forEach(function(e){h.push(e)});return h}},y.prototype._eval=function(e,t,n,r,o,i){e.includes("@parentProperty")&&(this.currSandbox._$_parentProperty=i,e=e.replace(/@parentProperty/g,"_$_parentProperty")),e.includes("@parent")&&(this.currSandbox._$_parent=o,e=e.replace(/@parent/g,"_$_parent")),e.includes("@property")&&(this.currSandbox._$_property=n,e=e.replace(/@property/g,"_$_property")),e.includes("@path")&&(this.currSandbox._$_path=y.toPathString(r.concat([n])),e=e.replace(/@path/g,"_$_path")),e.includes("@root")&&(this.currSandbox._$_root=this.json,e=e.replace(/@root/g,"_$_root")),/@([\t-\r \)\.\[\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF])/.test(e)&&(this.currSandbox._$_v=t,e=e.replace(/@([\t-\r \)\.\[\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF])/g,"_$_v$1"));try{return this.vm.runInNewContext(e,this.currSandbox)}catch(t){throw console.log(t),new Error("jsonPath: "+t.message+": "+e)}},y.cache={},y.toPathString=function(e){for(var t=e,n=t.length,r="$",o=1;o<n;o++)/^(~|\^|@(?:[\0-\t\x0B\f\x0E-\u2027\u202A-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*?\(\))$/.test(t[o])||(r+=/^[\*0-9]+$/.test(t[o])?"["+t[o]+"]":"['"+t[o]+"']");return r},y.toPointer=function(e){for(var t=e,n=t.length,r="",o=1;o<n;o++)/^(~|\^|@(?:[\0-\t\x0B\f\x0E-\u2027\u202A-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*?\(\))$/.test(t[o])||(r+="/"+t[o].toString().replace(/~/g,"~0").replace(/\//g,"~1"));return r},y.toPathArray=function(e){var t=y.cache;if(t[e])return t[e].concat();var n=[],r=e.replace(/@(?:null|boolean|number|string|integer|undefined|nonFinite|scalar|array|object|function|other)\(\)/g,";$&;").replace(/['\[](\??\((?:[\0-\t\x0B\f\x0E-\u2027\u202A-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*?\))['\]]/g,function(e,t){return"[#"+(n.push(t)-1)+"]"}).replace(/\[["']((?:(?!['\]])[\s\S])*)["']\]/g,function(e,t){return"['"+t.replace(/\./g,"%@%").replace(/~/g,"%%@@%%")+"']"}).replace(/~/g,";~;").replace(/["']?\.["']?(?!(?:(?!\[)[\s\S])*\])|\[["']?/g,";").replace(/%@%/g,".").replace(/%%@@%%/g,"~").replace(/(?:;)?(\^+)(?:;)?/g,function(e,t){return";"+t.split("").join(";")+";"}).replace(/;;;|;;/g,";..;").replace(/;$|'?\]|'$/g,"").split(";").map(function(e){var t=e.match(/#([0-9]+)/);return t&&t[1]?n[t[1]]:e});return t[e]=r,t[e].concat()},y.prototype.vm={runInNewContext:function(e,t){var n=Object.keys(t),r=[];!function(e,t,n){for(var r=e.length,o=0;o<r;o++)n(e[o])&&t.push(e.splice(o--,1)[0])}(n,r,function(e){return"function"==typeof t[e]});var o=n.map(function(e,n){return t[e]}),i=r.reduce(function(e,n){var r=t[n].toString();return/function/.test(r)||(r="function "+r),"var "+n+"="+r+";"+e},"");/(["'])use strict\1/.test(e=i+e)||n.includes("arguments")||(e="var arguments = undefined;"+e);var a=(e=e.replace(/;[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*$/,"")).lastIndexOf(";"),c=a>-1?e.slice(0,a+1)+" return "+e.slice(a+1):" return "+e;return s(Function,u(n).concat([c])).apply(void 0,u(o))}}},153:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=(r=n(148))&&r.__esModule?r:{default:r},a=function(){function e(t,n,r){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.id=t;var o={cache:!0};if(void 0===r&&(r=o),"function"!=typeof n?(this.value=n,this.type=this.constructor.CONSTANT):(this.calculationMethod=n,this.type=this.constructor.DYNAMIC),!this.id)throw new Error("factId required");return this.priority=parseInt(r.priority||1,10),this.options=Object.assign({},o,r),this.cacheKeyMethod=this.defaultCacheKeys,this}return o(e,[{key:"isConstant",value:function(){return this.type===this.constructor.CONSTANT}},{key:"isDynamic",value:function(){return this.type===this.constructor.DYNAMIC}},{key:"calculate",value:function(e,t){return Object.prototype.hasOwnProperty.call(this,"value")?this.value:this.calculationMethod(e,t)}},{key:"defaultCacheKeys",value:function(e,t){return{params:t,id:e}}},{key:"getCacheKey",value:function(t){if(!0===this.options.cache){var n=this.cacheKeyMethod(this.id,t);return e.hashFromObject(n)}}}],[{key:"hashFromObject",value:function(e){return(0,i.default)(e)}}]),e}();a.CONSTANT="CONSTANT",a.DYNAMIC="DYNAMIC",t.default=a},239:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=(r=n(779))&&r.__esModule?r:{default:r},a=function(){function e(t,n,r,o){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.conditions=(0,i.default)(t),this.event=(0,i.default)(n),this.priority=(0,i.default)(r),this.name=(0,i.default)(o),this.result=null}return o(e,[{key:"setResult",value:function(e){this.result=e}},{key:"toJSON",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t={conditions:this.conditions.toJSON(!1),event:this.event,priority:this.priority,name:this.name,result:this.result};return e?JSON.stringify(t):t}}]),e}();t.default=a},273:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),o=s(n(694)),i=s(n(239)),a=s(n(572));function s(e){return e&&e.__esModule?e:{default:e}}var c=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));"string"==typeof e&&(e=JSON.parse(e)),e&&e.conditions&&n.setConditions(e.conditions),e&&e.onSuccess&&n.on("success",e.onSuccess),e&&e.onFailure&&n.on("failure",e.onFailure),e&&(e.name||0===e.name)&&n.setName(e.name);var r=e&&e.priority||1;n.setPriority(r);var o=e&&e.event||{type:"unknown"};return n.setEvent(o),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),r(t,[{key:"setPriority",value:function(e){if((e=parseInt(e,10))<=0)throw new Error("Priority must be greater than zero");return this.priority=e,this}},{key:"setName",value:function(e){if(!e&&0!==e)throw new Error('Rule "name" must be defined');return this.name=e,this}},{key:"setConditions",value:function(e){if(!Object.prototype.hasOwnProperty.call(e,"all")&&!Object.prototype.hasOwnProperty.call(e,"any"))throw new Error('"conditions" root must contain a single instance of "all" or "any"');return this.conditions=new o.default(e),this}},{key:"setEvent",value:function(e){if(!e)throw new Error("Rule: setEvent() requires event object");if(!Object.prototype.hasOwnProperty.call(e,"type"))throw new Error('Rule: setEvent() requires event object with "type" property');return this.ruleEvent={type:e.type},e.params&&(this.ruleEvent.params=e.params),this}},{key:"getEvent",value:function(){return this.ruleEvent}},{key:"getPriority",value:function(){return this.priority}},{key:"getConditions",value:function(){return this.conditions}},{key:"getEngine",value:function(){return this.engine}},{key:"setEngine",value:function(e){return this.engine=e,this}},{key:"toJSON",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t={conditions:this.conditions.toJSON(!1),priority:this.priority,event:this.ruleEvent,name:this.name};return e?JSON.stringify(t):t}},{key:"prioritizeConditions",value:function(e){var t=this,n=e.reduce(function(e,n){var r=n.priority;if(!r){var o=t.engine.getFact(n.fact);r=o&&o.priority||1}return e[r]||(e[r]=[]),e[r].push(n),e},{});return Object.keys(n).sort(function(e,t){return Number(e)>Number(t)?-1:1}).map(function(e){return n[e]})}},{key:"evaluate",value:function(e){var t=this,n=new i.default(this.conditions,this.ruleEvent,this.priority,this.name),r=function(n,r){if(0===n.length)return Promise.resolve(!0);var i=Array.prototype.some;"all"===r&&(i=Array.prototype.every);for(var c=t.prioritizeConditions(n),u=Promise.resolve(),l=function(n){var l=c[n],f=!1;u=u.then(function(n){return"any"===r&&!0===n||f?((0,a.default)("prioritizeAndRun::detected truthy result; skipping remaining conditions"),f=!0,!0):"all"===r&&!1===n||f?((0,a.default)("prioritizeAndRun::detected falsey result; skipping remaining conditions"),f=!0,!1):function(n,r){return Array.isArray(n)||(n=[n]),Promise.all(n.map(function(n){return function(n){if(n.isBooleanOperator()){var r=n[n.operator];return("all"===n.operator?s(r):o(r)).then(function(e){var t=!0===e;return n.result=t,t})}return n.evaluate(e,t.engine.operators).then(function(e){var t=e.result;return n.factResult=e.leftHandSideValue,n.result=t,t})}(n)})).then(function(e){return(0,a.default)("rule::evaluateConditions results",e),r.call(e,function(e){return!0===e})})}(l,i)})},f=0;f<c.length;f++)l(f);return u},o=function(e){return r(e,"any")},s=function(e){return r(e,"all")},c=function(r){n.setResult(r);var o=r?"success":"failure";return t.emitAsync(o,n.event,e,n).then(function(){return n})};return n.conditions.any?o(n.conditions.any).then(function(e){return c(e)}):s(n.conditions.all).then(function(e){return c(e)})}}]),t}(s(n(11)).default);t.default=c},528:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=l(n(153)),a=n(716),s=l(n(572)),c=n(151),u=l(n(138));function l(e){return e&&e.__esModule?e:{default:e}}function f(e,t){return(0,c.JSONPath)({path:t,json:e,wrap:!1})}var h=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};for(var a in function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.factMap=new Map(t),this.factResultsCache=new Map,this.allowUndefinedFacts=Boolean(o.allowUndefinedFacts),this.pathResolver=o.pathResolver||f,this.events={success:[],failure:[]},this.ruleResults=[],n){var c;c=n[a]instanceof i.default?n[a]:new i.default(a,n[a]),this._addConstantFact(c),(0,s.default)("almanac::constructor initialized runtime fact:"+c.id+" with "+c.value+"<"+r(c.value)+">")}}return o(e,[{key:"addEvent",value:function(e,t){if(!t)throw new Error('outcome required: "success" | "failure"]');this.events[t].push(e)}},{key:"getEvents",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e?this.events[e]:this.events.success.concat(this.events.failure)}},{key:"addResult",value:function(e){this.ruleResults.push(e)}},{key:"getResults",value:function(){return this.ruleResults}},{key:"_getFact",value:function(e){return this.factMap.get(e)}},{key:"_addConstantFact",value:function(e){this.factMap.set(e.id,e),this._setFactValue(e,{},e.value)}},{key:"_setFactValue",value:function(e,t,n){var r=e.getCacheKey(t),o=Promise.resolve(n);return r&&this.factResultsCache.set(r,o),o}},{key:"addRuntimeFact",value:function(e,t){(0,s.default)("almanac::addRuntimeFact id:"+e);var n=new i.default(e,t);return this._addConstantFact(n)}},{key:"factValue",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",i=void 0,c=this._getFact(e);if(void 0===c)return this.allowUndefinedFacts?Promise.resolve(void 0):Promise.reject(new a.UndefinedFactError("Undefined fact: "+e));if(c.isConstant())i=Promise.resolve(c.calculate(n,this));else{var l=c.getCacheKey(n),f=l&&this.factResultsCache.get(l);f?(i=Promise.resolve(f),(0,s.default)("almanac::factValue cache hit for fact:"+e)):((0,s.default)("almanac::factValue cache miss for fact:"+e+"; calculating"),i=this._setFactValue(c,n,c.calculate(n,this)))}return o?((0,s.default)("condition::evaluate extracting object property "+o),i.then(function(e){if((0,u.default)(e)){var n=t.pathResolver(e,o);return(0,s.default)("condition::evaluate extracting object property "+o+", received: "+JSON.stringify(n)),n}return(0,s.default)("condition::evaluate could not compute object path("+o+") of non-object: "+e+" <"+(void 0===e?"undefined":r(e))+">; continuing with "+e),e})):i}}]),e}();t.default=h},572:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){try{("undefined"!=typeof process&&process.env&&process.env.DEBUG&&process.env.DEBUG.match(/json-rules-engine/)||"undefined"!=typeof window&&window.localStorage&&window.localStorage.debug&&window.localStorage.debug.match(/json-rules-engine/))&&console.log(e)}catch(e){}}},589:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FINISHED=t.RUNNING=t.READY=void 0;var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),o=f(n(153)),i=f(n(273)),a=f(n(759)),s=f(n(528)),c=f(n(11)),u=f(n(89)),l=f(n(572));function f(e){return e&&e.__esModule?e:{default:e}}var h=t.READY="READY",p=t.RUNNING="RUNNING",d=t.FINISHED="FINISHED",v=function(e){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var r=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return r.rules=[],r.allowUndefinedFacts=n.allowUndefinedFacts||!1,r.pathResolver=n.pathResolver,r.operators=new Map,r.facts=new Map,r.status=h,e.map(function(e){return r.addRule(e)}),u.default.map(function(e){return r.addOperator(e)}),r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),r(t,[{key:"addRule",value:function(e){if(!e)throw new Error("Engine: addRule() requires options");var t=void 0;if(e instanceof i.default)t=e;else{if(!Object.prototype.hasOwnProperty.call(e,"event"))throw new Error('Engine: addRule() argument requires "event" property');if(!Object.prototype.hasOwnProperty.call(e,"conditions"))throw new Error('Engine: addRule() argument requires "conditions" property');t=new i.default(e)}return t.setEngine(this),this.rules.push(t),this.prioritizedRules=null,this}},{key:"updateRule",value:function(e){var t=this.rules.findIndex(function(t){return t.name===e.name});if(!(t>-1))throw new Error("Engine: updateRule() rule not found");this.rules.splice(t,1),this.addRule(e),this.prioritizedRules=null}},{key:"removeRule",value:function(e){var t=!1;if(e instanceof i.default){var n=this.rules.indexOf(e);n>-1&&(t=Boolean(this.rules.splice(n,1).length))}else{var r=this.rules.filter(function(t){return t.name!==e});t=r.length!==this.rules.length,this.rules=r}return t&&(this.prioritizedRules=null),t}},{key:"addOperator",value:function(e,t){var n;n=e instanceof a.default?e:new a.default(e,t),(0,l.default)("engine::addOperator name:"+n.name),this.operators.set(n.name,n)}},{key:"removeOperator",value:function(e){var t;return t=e instanceof a.default?e.name:e,this.operators.delete(t)}},{key:"addFact",value:function(e,t,n){var r=e,i=void 0;return e instanceof o.default?(r=e.id,i=e):i=new o.default(e,t,n),(0,l.default)("engine::addFact id:"+r),this.facts.set(r,i),this}},{key:"removeFact",value:function(e){var t;return t=e instanceof o.default?e.id:e,this.facts.delete(t)}},{key:"prioritizeRules",value:function(){if(!this.prioritizedRules){var e=this.rules.reduce(function(e,t){var n=t.priority;return e[n]||(e[n]=[]),e[n].push(t),e},{});this.prioritizedRules=Object.keys(e).sort(function(e,t){return Number(e)>Number(t)?-1:1}).map(function(t){return e[t]})}return this.prioritizedRules}},{key:"stop",value:function(){return this.status=d,this}},{key:"getFact",value:function(e){return this.facts.get(e)}},{key:"evaluateRules",value:function(e,t){var n=this;return Promise.all(e.map(function(e){return n.status!==p?((0,l.default)("engine::run status:"+n.status+"; skipping remaining rules"),Promise.resolve()):e.evaluate(t).then(function(e){return(0,l.default)("engine::run ruleResult:"+e.result),t.addResult(e),e.result?(t.addEvent(e.event,"success"),n.emitAsync("success",e.event,t,e).then(function(){return n.emitAsync(e.event.type,e.event.params,t,e)})):(t.addEvent(e.event,"failure"),n.emitAsync("failure",e.event,t,e))})}))}},{key:"run",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,l.default)("engine::run started"),this.status=p;var n={allowUndefinedFacts:this.allowUndefinedFacts,pathResolver:this.pathResolver},r=new s.default(this.facts,t,n),o=this.prioritizeRules(),i=Promise.resolve();return new Promise(function(t,n){o.map(function(t){return i=i.then(function(){return e.evaluateRules(t,r)}).catch(n)}),i.then(function(){e.status=d,(0,l.default)("engine::run completed");var n=r.getResults().reduce(function(e,t){return e[t.result?"results":"failureResults"].push(t),e},{results:[],failureResults:[]}),o=n.results,i=n.failureResults;t({almanac:r,results:o,failureResults:i,events:r.getEvents("success"),failureEvents:r.getEvents("failure")})}).catch(n)})}}]),t}(c.default);t.default=v},694:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),o=a(n(572)),i=a(n(138));function a(e){return e&&e.__esModule?e:{default:e}}var s=function(){function e(t){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),!t)throw new Error("Condition: constructor options required");var n=e.booleanOperator(t);if(Object.assign(this,t),n){var r=t[n];if(!Array.isArray(r))throw new Error('"'+n+'" must be an array');this.operator=n,this.priority=parseInt(t.priority,10)||1,this[n]=r.map(function(t){return new e(t)})}else{if(!Object.prototype.hasOwnProperty.call(t,"fact"))throw new Error('Condition: constructor "fact" property required');if(!Object.prototype.hasOwnProperty.call(t,"operator"))throw new Error('Condition: constructor "operator" property required');if(!Object.prototype.hasOwnProperty.call(t,"value"))throw new Error('Condition: constructor "value" property required');Object.prototype.hasOwnProperty.call(t,"priority")&&(t.priority=parseInt(t.priority,10))}}return r(e,[{key:"toJSON",value:function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],n={};this.priority&&(n.priority=this.priority);var r=e.booleanOperator(this);return r?n[r]=this[r].map(function(e){return e.toJSON(t)}):(n.operator=this.operator,n.value=this.value,n.fact=this.fact,void 0!==this.factResult&&(n.factResult=this.factResult),void 0!==this.result&&(n.result=this.result),this.params&&(n.params=this.params),this.path&&(n.path=this.path)),t?JSON.stringify(n):n}},{key:"_getValue",value:function(e){var t=this.value;return(0,i.default)(t)&&Object.prototype.hasOwnProperty.call(t,"fact")?e.factValue(t.fact,t.params,t.path):Promise.resolve(t)}},{key:"evaluate",value:function(e,t){var n=this;if(!e)return Promise.reject(new Error("almanac required"));if(!t)return Promise.reject(new Error("operatorMap required"));if(this.isBooleanOperator())return Promise.reject(new Error("Cannot evaluate() a boolean condition"));var r=t.get(this.operator);return r?this._getValue(e).then(function(t){return e.factValue(n.fact,n.params,n.path).then(function(e){var i=r.evaluate(e,t);return(0,o.default)("condition::evaluate <"+JSON.stringify(e)+" "+n.operator+" "+JSON.stringify(t)+"?> ("+i+")"),{result:i,leftHandSideValue:e,rightHandSideValue:t,operator:n.operator}})}):Promise.reject(new Error("Unknown operator: "+this.operator))}},{key:"booleanOperator",value:function(){return e.booleanOperator(this)}},{key:"isBooleanOperator",value:function(){return void 0!==e.booleanOperator(this)}}],[{key:"booleanOperator",value:function(e){return Object.prototype.hasOwnProperty.call(e,"any")?"any":Object.prototype.hasOwnProperty.call(e,"all")?"all":void 0}}]),e}();t.default=s},716:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.UndefinedFactError=function(){function e(){var t;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);for(var n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];var i=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t=e.__proto__||Object.getPrototypeOf(e)).call.apply(t,[this].concat(r)));return i.code="UNDEFINED_FACT",i}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,Error),e}()},759:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),r=function(){function e(t,n,r){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.name=String(t),!t)throw new Error("Missing operator name");if("function"!=typeof n)throw new Error("Missing operator callback");this.cb=n,this.factValueValidator=r,this.factValueValidator||(this.factValueValidator=function(){return!0})}return n(e,[{key:"evaluate",value:function(e,t){return this.factValueValidator(e)&&this.cb(e,t)}}]),e}();t.default=r},763:function(e,t,n){"use strict";e.exports=n(26)},779:function(e){var t=function(){"use strict";function e(e,t){return null!=t&&e instanceof t}var t,n,r;try{t=Map}catch(e){t=function(){}}try{n=Set}catch(e){n=function(){}}try{r=Promise}catch(e){r=function(){}}function o(i,s,c,u,l){"object"==typeof s&&(c=s.depth,u=s.prototype,l=s.includeNonEnumerable,s=s.circular);var f=[],h=[],p="undefined"!=typeof Buffer;return void 0===s&&(s=!0),void 0===c&&(c=1/0),function i(c,d){if(null===c)return null;if(0===d)return c;var v,y;if("object"!=typeof c)return c;if(e(c,t))v=new t;else if(e(c,n))v=new n;else if(e(c,r))v=new r(function(e,t){c.then(function(t){e(i(t,d-1))},function(e){t(i(e,d-1))})});else if(o.__isArray(c))v=[];else if(o.__isRegExp(c))v=new RegExp(c.source,a(c)),c.lastIndex&&(v.lastIndex=c.lastIndex);else if(o.__isDate(c))v=new Date(c.getTime());else{if(p&&Buffer.isBuffer(c))return v=Buffer.allocUnsafe?Buffer.allocUnsafe(c.length):new Buffer(c.length),c.copy(v),v;e(c,Error)?v=Object.create(c):void 0===u?(y=Object.getPrototypeOf(c),v=Object.create(y)):(v=Object.create(u),y=u)}if(s){var g=f.indexOf(c);if(-1!=g)return h[g];f.push(c),h.push(v)}for(var m in e(c,t)&&c.forEach(function(e,t){var n=i(t,d-1),r=i(e,d-1);v.set(n,r)}),e(c,n)&&c.forEach(function(e){var t=i(e,d-1);v.add(t)}),c){var b;y&&(b=Object.getOwnPropertyDescriptor(y,m)),b&&null==b.set||(v[m]=i(c[m],d-1))}if(Object.getOwnPropertySymbols){var E=Object.getOwnPropertySymbols(c);for(m=0;m<E.length;m++){var S=E[m];(!(_=Object.getOwnPropertyDescriptor(c,S))||_.enumerable||l)&&(v[S]=i(c[S],d-1),_.enumerable||Object.defineProperty(v,S,{enumerable:!1}))}}if(l){var w=Object.getOwnPropertyNames(c);for(m=0;m<w.length;m++){var _,T=w[m];(_=Object.getOwnPropertyDescriptor(c,T))&&_.enumerable||(v[T]=i(c[T],d-1),Object.defineProperty(v,T,{enumerable:!1}))}}return v}(i,c)}function i(e){return Object.prototype.toString.call(e)}function a(e){var t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),t}return o.clonePrototype=function(e){if(null===e)return null;var t=function(){};return t.prototype=e,new t},o.__objToStr=i,o.__isDate=function(e){return"object"==typeof e&&"[object Date]"===i(e)},o.__isArray=function(e){return"object"==typeof e&&"[object Array]"===i(e)},o.__isRegExp=function(e){return"object"==typeof e&&"[object RegExp]"===i(e)},o.__getRegExpFlags=a,o}();e.exports&&(e.exports=t)}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r].call(i.exports,i,i.exports,n),i.exports}n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},function(){"use strict";var e=n(763);function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}function r(){var e,t,n="function"==typeof Symbol?Symbol:{},i=n.iterator||"@@iterator",a=n.toStringTag||"@@toStringTag";function s(n,r,i,a){var s=r&&r.prototype instanceof u?r:u,l=Object.create(s.prototype);return o(l,"_invoke",function(n,r,o){var i,a,s,u=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,a=0,s=e,h.n=n,c}};function p(n,r){for(a=n,s=r,t=0;!f&&u&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(s=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(a=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,a=0))}if(o||n>1)return c;throw f=!0,r}return function(o,l,d){if(u>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),a=l,s=d;(t=a<2?e:s)||!f;){i||(a?a<3?(a>1&&(h.n=-1),p(a,s)):h.n=s:h.v=s);try{if(u=2,i){if(a||(o="next"),t=i[o]){if(!(t=t.call(i,s)))throw TypeError("iterator result is not an object");if(!t.done)return t;s=t.value,a<2&&(a=0)}else 1===a&&(t=i.return)&&t.call(i),a<2&&(s=TypeError("The iterator does not provide a '"+o+"' method"),a=1);i=e}else if((t=(f=h.n<0)?s:n.call(r,h))!==c)break}catch(t){i=e,a=1,s=t}finally{u=1}}return{value:t,done:f}}}(n,i,a),!0),l}var c={};function u(){}function l(){}function f(){}t=Object.getPrototypeOf;var h=[][i]?t(t([][i]())):(o(t={},i,function(){return this}),t),p=f.prototype=u.prototype=Object.create(h);function d(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,o(e,a,"GeneratorFunction")),e.prototype=Object.create(p),e}return l.prototype=f,o(p,"constructor",f),o(f,"constructor",l),l.displayName="GeneratorFunction",o(f,a,"GeneratorFunction"),o(p),o(p,a,"Generator"),o(p,i,function(){return this}),o(p,"toString",function(){return"[object Generator]"}),(r=function(){return{w:s,m:d}})()}function o(e,t,n,r){var i=Object.defineProperty;try{i({},"",{})}catch(e){i=0}o=function(e,t,n,r){function a(t,n){o(e,t,function(e){return this._invoke(t,n,e)})}t?i?i(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(a("next",0),a("throw",1),a("return",2))},o(e,t,n,r)}function i(e,t,n,r,o,i,a){try{var s=e[i](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,o)}function a(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var a=e.apply(t,n);function s(e){i(a,r,o,s,c,"next",e)}function c(e){i(a,r,o,s,c,"throw",e)}s(void 0)})}}function s(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,c(r.key),r)}}function c(e){var n=function(e){if("object"!=t(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=t(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==t(n)?n:n+""}var u=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.sharedConfig=null,this.configUrl="lib/shared-config.json"},t=[{key:"loadSharedConfig",value:(l=a(r().m(function e(){var t,n;return r().w(function(e){for(;;)switch(e.p=e.n){case 0:if(!this.sharedConfig){e.n=1;break}return e.a(2,this.sharedConfig);case 1:return e.p=1,console.log("🔍 Loading shared config from:",this.configUrl),e.n=2,fetch(chrome.runtime.getURL(this.configUrl));case 2:if((t=e.v).ok){e.n=3;break}throw new Error("HTTP ".concat(t.status,": ").concat(t.statusText));case 3:return e.n=4,t.json();case 4:return this.sharedConfig=e.v,console.log("✅ Shared config loaded successfully"),e.a(2,this.sharedConfig);case 5:return e.p=5,n=e.v,console.error("❌ Failed to load shared config:",n),console.log("🔄 Using fallback config"),this.sharedConfig=this.getFallbackConfig(),e.a(2,this.sharedConfig)}},e,this,[[1,5]])})),function(){return l.apply(this,arguments)})},{key:"getFallbackConfig",value:function(){return{SUPPORTED_SITES:{kiteByZerodha:{name:"Kite by Zerodha",url:"https://kite.zerodha.com/",contentScript:"content-scripts/zerodha.js"}},ACTION_ARGUMENTS:{BUY:["SYMBOL","QUANTITY","EXCHANGE","PRODUCT_TYPE","PRODUCTTYPE"],SELL:["SYMBOL","QUANTITY","EXCHANGE","PRODUCT_TYPE","PRODUCTTYPE"],MONITORPROFIT:["TARGET_PROFIT_AMOUNT","TARGET_PROFIT_PERCENTAGE","MONITOR_INTERVAL_SECONDS"]},MESSAGE_TYPES:{EXECUTE_ACTIONS:"EXECUTE_ACTIONS",PERFORM_SITE_ACTIONS:"PERFORM_SITE_ACTIONS",ACTION_STATUS_UPDATE:"ACTION_STATUS_UPDATE",EXECUTION_COMPLETE:"EXECUTION_COMPLETE"},ENVIRONMENT:{slow_execute:!1,close_tabs_after_execution:!0}}}},{key:"getSupportedSites",value:(u=a(r().m(function e(){var t;return r().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,this.loadSharedConfig();case 1:return t=e.v,e.a(2,t.SUPPORTED_SITES)}},e,this)})),function(){return u.apply(this,arguments)})},{key:"getActionArguments",value:(c=a(r().m(function e(){var t;return r().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,this.loadSharedConfig();case 1:return t=e.v,e.a(2,t.ACTION_ARGUMENTS)}},e,this)})),function(){return c.apply(this,arguments)})},{key:"getMessageTypes",value:(i=a(r().m(function e(){var t;return r().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,this.loadSharedConfig();case 1:return t=e.v,e.a(2,t.MESSAGE_TYPES)}},e,this)})),function(){return i.apply(this,arguments)})},{key:"getEnvironment",value:(o=a(r().m(function e(){var t;return r().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,this.loadSharedConfig();case 1:return t=e.v,e.a(2,t.ENVIRONMENT)}},e,this)})),function(){return o.apply(this,arguments)})},{key:"getFullConfig",value:(n=a(r().m(function e(){return r().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,this.loadSharedConfig();case 1:return e.a(2,e.v)}},e,this)})),function(){return n.apply(this,arguments)})}],t&&s(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,o,i,c,u,l}(),l=new u;function f(){return h.apply(this,arguments)}function h(){return(h=a(r().m(function e(){return r().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,l.getSupportedSites();case 1:return e.a(2,e.v)}},e)}))).apply(this,arguments)}function p(){return d.apply(this,arguments)}function d(){return(d=a(r().m(function e(){return r().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,l.getActionArguments();case 1:return e.a(2,e.v)}},e)}))).apply(this,arguments)}function v(){return y.apply(this,arguments)}function y(){return(y=a(r().m(function e(){return r().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,l.getMessageTypes();case 1:return e.a(2,e.v)}},e)}))).apply(this,arguments)}function g(){return m.apply(this,arguments)}function m(){return(m=a(r().m(function e(){return r().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,l.getEnvironment();case 1:return e.a(2,e.v)}},e)}))).apply(this,arguments)}function b(){return E.apply(this,arguments)}function E(){return(E=a(r().m(function e(){return r().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,l.getFullConfig();case 1:return e.a(2,e.v)}},e)}))).apply(this,arguments)}var S={kiteByZerodha:{name:"Kite by Zerodha",url:"https://kite.zerodha.com/",contentScript:"content-scripts/zerodha.js"}},w={BUY:["SYMBOL","QUANTITY","PRICE","EXCHANGE","PRODUCT_TYPE","PRODUCTTYPE"],SELL:["SYMBOL","QUANTITY","PRICE","EXCHANGE","PRODUCT_TYPE","PRODUCTTYPE"],MONITORPROFIT:["TARGET_PROFIT_AMOUNT","TARGET_PROFIT_PERCENTAGE","MONITOR_INTERVAL_SECONDS"]},_={CURRENT_TAB:"currentTab",BACKGROUND:"background"},T={EXECUTE_ACTIONS:"EXECUTE_ACTIONS",PERFORM_SITE_ACTIONS:"PERFORM_SITE_ACTIONS",ACTION_STATUS_UPDATE:"ACTION_STATUS_UPDATE",EXECUTION_COMPLETE:"EXECUTION_COMPLETE"},O={BUY:"BUY",SELL:"SELL",PLACE_BUY_LIMIT_ORDER:"PlaceBuyLimitOrder",PLACE_SELL_LIMIT_ORDER:"PlaceSellLimitOrder",PLACE_BUY_STOP_LOSS_MARKET_ORDER:"PlaceBuyStopLossMarketOrder",PLACE_SELL_STOP_LOSS_MARKET_ORDER:"PlaceSellStopLossMarketOrder",PLACE_BUY_STOP_LOSS_LIMIT_ORDER:"PlaceBuyStopLossLimitOrder",PLACE_SELL_STOP_LOSS_LIMIT_ORDER:"PlaceSellStopLossLimitOrder",MONITOR_PROFIT:"MONITORPROFIT",CANCEL_ORDER:"CancelOrder",GET_PORTFOLIO_STATS:"GetPortfolioStats",GET_OPEN_POSITION_PNL:"GetOpenPositionPnL",SELECT_ORDER_BY_CRITERIA:"SelectOrderByCriteria"},k={STRING:"string",NUMBER:"number",OBJECT:"object"},P={SYMBOL:{type:k.STRING},QUANTITY:{type:k.NUMBER},PRICE:{type:k.NUMBER},TRIGGER_PRICE:{type:k.NUMBER},TARGET_PROFIT_PERCENTAGE:{type:k.NUMBER},MONITOR_INTERVAL_SECONDS:{type:k.NUMBER},TARGET_PROFIT_AMOUNT:{type:k.NUMBER},EXCHANGE:{type:k.STRING},PRODUCT_TYPE:{type:k.STRING},PRODUCT:{type:k.STRING},PRODUCTTYPE:{type:k.STRING},TYPE:{type:k.STRING},type:{type:k.STRING},symbol:{type:k.STRING},quantity:{type:k.NUMBER},exchange:{type:k.STRING},product:{type:k.STRING},condition:{type:k.OBJECT},on_trigger:{type:k.OBJECT}},A=["SYMBOL","QUANTITY","PRICE","TARGET_PROFIT_PERCENTAGE","MONITOR_INTERVAL_SECONDS","TARGET_PROFIT_AMOUNT","EXCHANGE","PRODUCT_TYPE","PRODUCT","TYPE","type","symbol","quantity","exchange","product","PRODUCTTYPE","condition","on_trigger"],x={SUCCESS:"success",ERROR:"error",INFO:"info"},I="SmartFinAgentContentScriptLoaded",M=["NIFTY","SENSEX","BANKEX","ALLCAP","INDEX","INDICES"],C={NSE:["NIFTY"],BSE:["SENSEX","BANKEX","ALLCAP"]},R={TAB_LOAD_WAIT:2e3,PAGE_INITIALIZATION:2e3},N={INVALID_JSON:"Invalid JSON format",NO_RESPONSE:"No response from content script",UNSUPPORTED_SITE:"Unsupported site",INVALID_AUTOMATION_MODE:"Invalid automation mode",CROSS_ORIGIN_LIMITATION:"Cross-origin iframe access is not supported in this implementation. Please use the 'Current Tab' mode for automation.",MESSAGE_CHANNEL_CLOSED:"Message channel closed before response received. This usually happens when the tab was closed or navigated away during execution.",TAB_NOT_ACCESSIBLE:"Tab is not accessible for content script injection."},j={EXECUTION_COMPLETE:"All actions executed successfully!",VALIDATION_SUCCESS:"Validation successful."};function F(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var c=r&&r.prototype instanceof s?r:s,u=Object.create(c.prototype);return L(u,"_invoke",function(n,r,o){var i,s,c,u=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,s=0,c=e,h.n=n,a}};function p(n,r){for(s=n,c=r,t=0;!f&&u&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(c=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(s=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,s=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,d){if(u>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),s=l,c=d;(t=s<2?e:c)||!f;){i||(s?s<3?(s>1&&(h.n=-1),p(s,c)):h.n=c:h.v=c);try{if(u=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(f=h.n<0)?c:n.call(r,h))!==a)break}catch(t){i=e,s=1,c=t}finally{u=1}}return{value:t,done:f}}}(n,o,i),!0),u}var a={};function s(){}function c(){}function u(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(L(t={},r,function(){return this}),t),f=u.prototype=s.prototype=Object.create(l);function h(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,u):(e.__proto__=u,L(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return c.prototype=u,L(f,"constructor",u),L(u,"constructor",c),c.displayName="GeneratorFunction",L(u,o,"GeneratorFunction"),L(f),L(f,o,"Generator"),L(f,r,function(){return this}),L(f,"toString",function(){return"[object Generator]"}),(F=function(){return{w:i,m:h}})()}function L(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}L=function(e,t,n,r){function i(t,n){L(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},L(e,t,n,r)}function D(e){return D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},D(e)}function G(e,t,n,r,o,i,a){try{var s=e[i](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,o)}function U(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=B(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,i=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw i}}}}function B(e,t){if(e){if("string"==typeof e)return z(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?z(e,t):void 0}}function z(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function K(e){return"string"==typeof e?e.toUpperCase():e}function V(e,t){return q.apply(this,arguments)}function q(){var e;return e=F().m(function e(t,n){var r,o;return F().w(function(e){for(;;)switch(e.n){case 0:if(Array.isArray(t)){e.n=1;break}return e.a(2,{isValid:!1,message:"Input must be a JSON array of action objects."});case 1:r=0;case 2:if(!(r<t.length)){e.n=4;break}if((o=Y(t[r],r,n)).isValid){e.n=3;break}return e.a(2,o);case 3:r++,e.n=2;break;case 4:return e.a(2,{isValid:!0,message:j.VALIDATION_SUCCESS})}},e)}),q=function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){G(i,r,o,a,s,"next",e)}function s(e){G(i,r,o,a,s,"throw",e)}a(void 0)})},q.apply(this,arguments)}function Y(e,t,n){if(!$(e))return{isValid:!1,message:"Action at index ".concat(t," is not a valid object.")};var r=e.action,o=e.arguments;if("string"!=typeof r)return{isValid:!1,message:"Action at index ".concat(t," is missing a valid 'action' string.")};var i=K(r),a=Object.keys(n).find(function(e){return K(e)===i});if(!a)return{isValid:!1,message:"Action at index ".concat(t," has unrecognized action type '").concat(r,"'.")};if(!$(o))return{isValid:!1,message:"Action at index ".concat(t," is missing a valid 'arguments' object.")};var s=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n={};e.productType&&!n.PRODUCT_TYPE&&(n.PRODUCT_TYPE=e.productType),e.triggerPrice&&!n.TRIGGER_PRICE&&(n.TRIGGER_PRICE=e.triggerPrice);var r,o=U(t);try{var i=function(){var t=r.value,o=Object.keys(e).find(function(e){return K(e)===K(t)});o&&(n[t]=e[o])};for(o.s();!(r=o.n()).done;)i()}catch(e){o.e(e)}finally{o.f()}for(var a=function(){var e,r,o=(e=c[s],r=2,function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return s}}(e,r)||B(e,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),i=o[0],a=o[1];t.some(function(e){return K(e)===K(i)})||"productType"===i||"triggerPrice"===i||(n[i]=a)},s=0,c=Object.entries(e);s<c.length;s++)a();return n._originalArgs=e,n}(o,Object.keys(n[a]));return function(e,t,n,r){var o,i=r[e],a=t._originalArgs||t,s=U(i);try{var c,u=function(){var r=o.value,i=null;if((i=Object.keys(t).find(function(e){return K(e)===K(r)}))||(i=Object.keys(a).find(function(e){return K(e)===K(r)})),!i){var s,c=U(H(r));try{var u=function(){var e=s.value;if(i=Object.keys(a).find(function(t){return K(t)===K(e)}))return 1};for(c.s();!(s=c.n()).done&&!u(););}catch(e){c.e(e)}finally{c.f()}}if(!i){var l=H(r);return{v:{isValid:!1,message:"Action '".concat(e,"' at index ").concat(n," is missing required argument '").concat(r,"'. Accepted variations: ").concat(l.join(", "),".")}}}var f=t[i]||a[i],h=function(e,t,n,r){console.log("🔍 validateArgumentType called with:"),console.log('  - argKey: "'.concat(e,'"')),console.log("  - value:",t),console.log('  - action: "'.concat(n,'"')),console.log("  - index: ".concat(r));var o=function(e){console.log('🔍 getExpectedType called with argKey: "'.concat(e,'"'));var t=P[e];if(console.log("🔍 Exact match result:",t),!t){var n=K(e);console.log('🔍 Normalized argKey: "'.concat(n,'"'));var r=Object.keys(P).find(function(e){return K(e)===n});console.log('🔍 Found key: "'.concat(r,'"')),r&&(t=P[r],console.log("🔍 Case-insensitive match result:",t))}console.log("🔍 Final config:",t);var o=t?t.type:k.STRING;return console.log('🔍 Returning type: "'.concat(o,'"')),o}(e),i=D(t);return console.log('🔍 Type comparison: expected="'.concat(o,'", actual="').concat(i,'"')),i!==o?(console.log("❌ Type validation failed!"),{isValid:!1,message:"Argument '".concat(e,"' for action '").concat(n,"' at index ").concat(r," must be a ").concat(o,".")}):(console.log("✅ Type validation passed!"),{isValid:!0,message:"Type validation successful."})}(r,f,e,n);if(!h.isValid)return{v:h}};for(s.s();!(o=s.n()).done;)if(c=u())return c.v}catch(e){s.e(e)}finally{s.f()}return{isValid:!0,message:"Arguments validation successful."}}(a,s,t,n)}function H(e){var t,n=[e];if(n.push(e.toLowerCase()),n.push(e.toUpperCase()),e.includes("_")){var r=e.toLowerCase().split("_").map(function(e,t){return 0===t?e:e.charAt(0).toUpperCase()+e.slice(1)}).join("");n.push(r),n.push(e.replace(/_/g,"")),n.push(e.replace(/_/g,"").toLowerCase()),n.push(e.replace(/_/g,"").toUpperCase())}if(/[A-Z]/.test(e)&&!e.includes("_")){var o=e.replace(/([A-Z])/g,"_$1").toLowerCase().replace(/^_/,"");n.push(o.toUpperCase()),n.push(o)}return function(e){if(Array.isArray(e))return z(e)}(t=new Set(n))||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||B(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $(e){return"object"===D(e)&&null!==e}function J(e){return J="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},J(e)}function X(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return s}}(e,t)||Q(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function W(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=Q(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,i=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw i}}}}function Z(e){return function(e){if(Array.isArray(e))return ee(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Q(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Q(e,t){if(e){if("string"==typeof e)return ee(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ee(e,t):void 0}}function ee(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function te(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var c=r&&r.prototype instanceof s?r:s,u=Object.create(c.prototype);return ne(u,"_invoke",function(n,r,o){var i,s,c,u=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,s=0,c=e,h.n=n,a}};function p(n,r){for(s=n,c=r,t=0;!f&&u&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(c=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(s=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,s=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,d){if(u>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),s=l,c=d;(t=s<2?e:c)||!f;){i||(s?s<3?(s>1&&(h.n=-1),p(s,c)):h.n=c:h.v=c);try{if(u=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(f=h.n<0)?c:n.call(r,h))!==a)break}catch(t){i=e,s=1,c=t}finally{u=1}}return{value:t,done:f}}}(n,o,i),!0),u}var a={};function s(){}function c(){}function u(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(ne(t={},r,function(){return this}),t),f=u.prototype=s.prototype=Object.create(l);function h(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,u):(e.__proto__=u,ne(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return c.prototype=u,ne(f,"constructor",u),ne(u,"constructor",c),c.displayName="GeneratorFunction",ne(u,o,"GeneratorFunction"),ne(f),ne(f,o,"Generator"),ne(f,r,function(){return this}),ne(f,"toString",function(){return"[object Generator]"}),(te=function(){return{w:i,m:h}})()}function ne(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}ne=function(e,t,n,r){function i(t,n){ne(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},ne(e,t,n,r)}function re(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function oe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?re(Object(n),!0).forEach(function(t){ie(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):re(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function ie(e,t,n){return(t=ue(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ae(e,t,n,r,o,i,a){try{var s=e[i](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,o)}function se(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){ae(i,r,o,a,s,"next",e)}function s(e){ae(i,r,o,a,s,"throw",e)}a(void 0)})}}function ce(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,ue(r.key),r)}}function ue(e){var t=function(e){if("object"!=J(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=J(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==J(t)?t:t+""}var le=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.messageTypes=null,this.timeoutSettings={execution_service_general_timeout_seconds:30,execution_service_monitoring_timeout_seconds:600},this.activeTabs=new Map,this.activeGraphs=new Map,this.nodeExecutions=new Map,this.maxConcurrentTabs=6,this.tabPool=new Set,this.cancelledActions=new Set,this.tabAcquisitionLocks=new Map,this.defaultActionTimeout=3e5,this.pausedGraphs=new Set,this.pauseResolvers=new Map,this.loginMonitor={tabId:null,intervalId:null,checking:!1,required:null,lastChecked:null,pausedGraphsSet:new Set,started:!1,isDedicated:!1,creationPromise:null,onRemovedHandler:null,fetchedThisSession:!1,lastFetchAt:null,orderSummary:{open:{count:null,firstKey:null,lastKey:null},completed:{count:null,firstKey:null,lastKey:null}},stabilize:{mismatchStreak:1,maxStreak:1}},this.observerMonitor={tabId:null,isDedicated:!1,creationPromise:null,onRemovedHandler:null}},t=[{key:"loadTimeoutSettings",value:(A=se(te().m(function e(){var t,n,r;return te().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,fetch(chrome.runtime.getURL("lib/shared-config.json"));case 1:return t=e.v,e.n=2,t.json();case 2:(n=e.v).TIMEOUT_SETTINGS&&(this.timeoutSettings=oe(oe({},this.timeoutSettings),n.TIMEOUT_SETTINGS),console.log("[ExecutionService] Loaded timeout settings:",this.timeoutSettings)),n.EXECUTION_SERVICE&&Number.isFinite(n.EXECUTION_SERVICE.max_concurrent_tabs)&&(this.maxConcurrentTabs=n.EXECUTION_SERVICE.max_concurrent_tabs,console.log("[ExecutionService] Loaded max concurrent tabs from config:",this.maxConcurrentTabs)),n.TIMEOUT_SETTINGS&&Number.isFinite(n.TIMEOUT_SETTINGS.action_execution_timeout_seconds)&&(this.defaultActionTimeout=1e3*n.TIMEOUT_SETTINGS.action_execution_timeout_seconds,console.log("[ExecutionService] Loaded default action timeout (ms):",this.defaultActionTimeout)),e.n=4;break;case 3:e.p=3,r=e.v,console.warn("[ExecutionService] Failed to load timeout config, using defaults:",r);case 4:return e.a(2)}},e,this,[[0,3]])})),function(){return A.apply(this,arguments)})},{key:"initialize",value:function(e){this.messageTypes=e}},{key:"generateArgumentVariations",value:function(e){var t=[e];if(t.push(e.toLowerCase()),t.push(e.toUpperCase()),e.includes("_")){var n=e.toLowerCase().split("_").map(function(e,t){return 0===t?e:e.charAt(0).toUpperCase()+e.slice(1)}).join("");t.push(n),t.push(e.replace(/_/g,"")),t.push(e.replace(/_/g,"").toLowerCase()),t.push(e.replace(/_/g,"").toUpperCase())}if(/[A-Z]/.test(e)&&!e.includes("_")){var r=e.replace(/([A-Z])/g,"_$1").toLowerCase().replace(/^_/,"");t.push(r.toUpperCase()),t.push(r)}return Z(new Set(t))}},{key:"normalizeActions",value:function(e,t){var n=this;return e.map(function(e){var r=e.action,o=e.arguments,i=Object.keys(t).find(function(e){return e.toUpperCase()===r.toUpperCase()});if(!i)return e;var a,s={},c=t[i],u=W(c);try{var l=function(){var e=a.value,t=null,r=Object.keys(o).find(function(t){return t.toUpperCase()===e.toUpperCase()});if(r)t=o[r];else{var i,c=W(n.generateArgumentVariations(e));try{var u=function(){var e=i.value,n=Object.keys(o).find(function(t){return t.toUpperCase()===e.toUpperCase()});if(n)return t=o[n],1};for(c.s();!(i=c.n()).done&&!u(););}catch(e){c.e(e)}finally{c.f()}}null!==t&&(s[e]=t)};for(u.s();!(a=u.n()).done;)l()}catch(e){u.e(e)}finally{u.f()}for(var f=function(){var e=X(p[h],2),t=e[0],r=e[1];c.some(function(e){return n.generateArgumentVariations(e).some(function(e){return e.toUpperCase()===t.toUpperCase()})})||(s[t]=r)},h=0,p=Object.entries(o);h<p.length;h++)f();return{action:i,arguments:s}})}},{key:"executeActions",value:(P=se(te().m(function e(t){var n,r,o,i,a,s,c;return te().w(function(e){for(;;)switch(e.n){case 0:return n=t.actions,r=t.site,o=t.automationMode,i=t.tabId,a=t.actionArgumentsConfig,e.n=1,V(n,a);case 1:if((s=e.v).isValid){e.n=2;break}return e.a(2,{success:!1,message:s.message});case 2:if(c=this.normalizeActions(n,a),o!==_.CURRENT_TAB){e.n=3;break}return e.a(2,this.executeInCurrentTab(i,r,c));case 3:if(o!==_.BACKGROUND){e.n=4;break}return e.a(2,this.executeInBackground(r,c));case 4:return e.a(2,{success:!1,message:"Unknown automation mode: ".concat(o)});case 5:return e.a(2)}},e,this)})),function(e){return P.apply(this,arguments)})},{key:"executeGraphNodeByNode",value:(k=se(te().m(function e(t,n){var r,o,i,a,s,c,u,l,f,h,p,d,v,y,g,m,b,E,S,w,_,T,O,k,P,A,x,I,M,C,R,N,j,F,L,D,G,U=this,B=arguments;return te().w(function(e){for(;;)switch(e.p=e.n){case 0:r=B.length>2&&void 0!==B[2]?B[2]:{},o=B.length>3&&void 0!==B[3]?B[3]:function(){},e.p=1,console.log("[ExecutionService] Starting node-by-node graph execution: ".concat(t," with ").concat(n.length," actions")),i=new Set,a=new Set,s=W(n);try{for(s.s();!(c=s.n()).done;)(u=c.value)&&u.id&&(i.has(u.id)&&a.add(u.id),i.add(u.id))}catch(e){s.e(e)}finally{s.f()}if(!(a.size>0)){e.n=2;break}throw new Error("Duplicate action id(s) detected: ".concat(Array.from(a).join(", "),". Please provide unique 'id' for each action."));case 2:return l=n.map(function(e,t){return oe(oe({},e),{},{id:null==e?void 0:e.id})}),f=l.length,console.log("[ExecutionService] Performing pre-start login check for graph: ".concat(t)),e.p=3,e.n=4,this.ensureLoginMonitorTab();case 4:return e.n=5,this.checkLoginAndTogglePause();case 5:e.n=7;break;case 6:e.p=6,L=e.v,console.warn("[ExecutionService] Pre-start login check failed:",L);case 7:if(!0!==this.loginMonitor.required){e.n=12;break}console.warn("[ExecutionService] Login required - starting graph ".concat(t," in paused state and waiting")),this.activeGraphs.set(t,{status:"running",nodes:new Map,progress:{completed:0,failed:0,total:f},startTime:new Date,options:r}),this.pauseGraph(t),this.loginMonitor.pausedGraphsSet.add(t);case 8:if(!1===this.loginMonitor.required){e.n=11;break}return e.n=9,this.checkLoginAndTogglePause().catch(function(){});case 9:return e.n=10,this.delay(500);case 10:e.n=8;break;case 11:this.resumeGraph(t);case 12:this.activeGraphs.set(t,{status:"running",nodes:new Map,progress:{completed:0,failed:0,total:f},startTime:new Date,options:r}),h=this.buildDependencyMap(l),p=h.dependencies,d=h.dependents,v=Number.isFinite(r.maxParallel)?r.maxParallel:this.maxConcurrentTabs,y=new Set,g=new Set,m=[],b=new Map,E=new Map(l.map(function(e){return[e.id,e]})),console.log("[ExecutionService] Node-by-node execution initialized"),console.log("[ExecutionService] Dependency analysis complete - actions with dependencies:",Array.from(p.entries()).filter(function(e){var t=X(e,2);return t[0],t[1].length>0})),S=function(e){return!(y.has(e)||g.has(e)||b.has(e))&&(p.get(e)||[]).every(function(e){return y.has(e)})},w=function(){var e=se(te().m(function e(n){var i,a,s,c;return te().w(function(e){for(;;)switch(e.n){case 0:if(!(b.has(n)||y.has(n)||g.has(n))){e.n=1;break}return e.a(2,b.get(n));case 1:if(i=E.get(n)){e.n=2;break}return e.a(2);case 2:return console.log("[ExecutionService] Starting action: ".concat(n)),a=r.actionTimeout||U.defaultActionTimeout,s=new Promise(function(e,t){setTimeout(function(){t(new Error("Action timeout: ".concat(n," exceeded ").concat(a/1e3,"s")))},a)}),c=Promise.all([U.executeNodeWithTab(t,i,r,o),s]).then(function(e){return b.delete(n),e.success?(y.add(n),m.push(e),U.updateNodeStatus(t,n,"completed",e),console.log("[ExecutionService] Action completed: ".concat(n))):(g.add(n),m.push({success:!1,nodeId:n,action:i.action,error:e.error||e.message}),U.updateNodeStatus(t,n,"failed",e.error||e),console.log("[ExecutionService] Action failed: ".concat(n)),!1!==r.failDependentsOnError&&(U.failDependentActions(t,n,d),(d.get(n)||[]).forEach(function(e){g.add(e),b.has(e)&&U.cancelledActions.add(e)}))),e}).catch(function(e){b.delete(n),g.add(n);var o={success:!1,action:i.action,error:e.message};return m.push(o),U.updateNodeStatus(t,n,"failed",e),console.error("[ExecutionService] Action errored: ".concat(n),e),!1!==r.failDependentsOnError&&(U.failDependentActions(t,n,d),(d.get(n)||[]).forEach(function(e){g.add(e),b.has(e)&&U.cancelledActions.add(e)})),o}),b.set(n,c),e.a(2,c)}},e)}));return function(t){return e.apply(this,arguments)}}(),_=function(){var e,t=[],n=new Set,r=W(l);try{for(r.s();!(e=r.n()).done;){var o=e.value.id;n.has(o)||S(o)&&(n.add(o),t.push(o))}}catch(e){r.e(e)}finally{r.f()}var i=Math.max(0,v-b.size),a=t.slice(0,i);return a.length>0&&console.log("[ExecutionService] Starting ".concat(a.length," ready actions (capacity ").concat(i,"/").concat(v,"):"),a),a.forEach(function(e){return w(e)}),a.length},T=0,O=3*f,k=0;case 13:if(!(y.size+g.size<f&&T<O)){e.n=21;break}return T++,e.n=14,this.checkForPause(t);case 14:if(0!==(P=_())||0!==b.size){e.n=15;break}return A=l.filter(function(e){var t=e.id||e.action;return!y.has(t)&&!g.has(t)}),A.length>0&&(console.error("[ExecutionService] Deadlock detected at iteration ".concat(T,". Remaining actions:"),A.map(function(e){var t=e.id||e.action,n=(p.get(t)||[]).filter(function(e){return!y.has(e)});return"".concat(t," (waiting for: ").concat(n.join(", "),")")})),A.forEach(function(e){var n=e.id||e.action;g.add(n),U.updateNodeStatus(t,n,"failed",{reason:"deadlock_detected",iteration:T,remainingDependencies:(p.get(n)||[]).filter(function(e){return!y.has(e)})})})),e.a(3,21);case 15:if(0===P?++k>10&&console.warn("[ExecutionService] No progress for ".concat(k," iterations. Active promises: ").concat(b.size)):k=0,!(b.size>0)){e.n=19;break}return e.p=16,x=Array.from(b.values()),e.n=17,Promise.race(x);case 17:e.n=19;break;case 18:e.p=18,D=e.v,console.error("[ExecutionService] Error in Promise.race:",D);case 19:if(!(r.stopOnError&&g.size>0)){e.n=20;break}return console.log("[ExecutionService] Stopping execution due to error at iteration ".concat(T)),e.a(3,21);case 20:e.n=13;break;case 21:if(T>=O){console.error("[ExecutionService] Max iterations exceeded (".concat(O,"). Forcing completion.")),I=l.filter(function(e){var t=e.id||e.action;return!y.has(t)&&!g.has(t)}),I.forEach(function(e){var n=e.id||e.action;g.add(n),U.updateNodeStatus(t,n,"failed",{reason:"max_iterations_exceeded",iteration:O})}),M=W(b.keys());try{for(M.s();!(C=M.n()).done;)R=C.value,this.cancelledActions.add(R)}catch(e){M.e(e)}finally{M.f()}}if(!(b.size>0)){e.n=22;break}return console.log("[ExecutionService] Waiting for ".concat(b.size," remaining actions to complete")),e.n=22,Promise.allSettled(Array.from(b.values()));case 22:return N=this.activeGraphs.get(t),j=0===g.size,N.status=j?"completed":"failed",N.endTime=new Date,N.duration=N.endTime-N.startTime,e.n=23,this.cleanupGraphTabs(t);case 23:return 0===this.activeGraphs.size&&0===this.nodeExecutions.size&&console.log("[ExecutionService] All graphs completed. Keeping login monitor running."),console.log("[ExecutionService] Node-by-node graph execution completed: ".concat(t,", success: ").concat(j)),console.log("[ExecutionService] Final stats - Completed: ".concat(y.size,", Failed: ").concat(g.size,", Total: ").concat(f)),e.a(2,{success:j,graphId:t,results:m,summary:{total:f,completed:y.size,failed:g.size,duration:N.duration}});case 24:return e.p=24,G=e.v,console.error("[ExecutionService] Node-by-node graph execution failed: ".concat(t),G),this.activeGraphs.has(t)&&((F=this.activeGraphs.get(t)).status="failed",F.error=G.message,F.endTime=new Date),e.a(2,{success:!1,graphId:t,error:G.message,message:"Node-by-node graph execution failed: ".concat(G.message)})}},e,this,[[16,18],[3,6],[1,24]])})),function(e,t){return k.apply(this,arguments)})},{key:"getSupportedActions",value:(O=se(te().m(function e(){var t;return te().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,p();case 1:return t=e.v,e.a(2,Object.keys(t));case 2:return e.p=2,e.v,e.a(2,["BUY","SELL","PlaceBuyLimitOrder","PlaceSellLimitOrder","PlaceBuyStopLossMarketOrder","PlaceSellStopLossMarketOrder","MONITORPROFIT","ExitAllPositions","NavigateToProfile","GetProfileInfo"])}},e,null,[[0,2]])})),function(){return O.apply(this,arguments)})},{key:"executeAction",value:(w=se(te().m(function e(t,n,r){var o,i,a,s,c,u;return te().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,o={action:t,arguments:n},e.n=1,p();case 1:return i=e.v,e.n=2,V([o],i);case 2:if((a=e.v).isValid){e.n=3;break}return e.a(2,{success:!1,message:a.message});case 3:return s=this.normalizeActions([o],i)[0],e.n=4,this.findSiteForTab(r);case 4:return c=e.v,e.a(2,this.executeInTab(r,c,[s]));case 5:return e.p=5,u=e.v,e.a(2,{success:!1,message:"Action execution failed: ".concat(u.message)})}},e,this,[[0,5]])})),function(e,t,n){return w.apply(this,arguments)})},{key:"findSiteForTab",value:(S=se(te().m(function e(t){var n,r,o,i,a,s,c,u,l,h;return te().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.n=1,f();case 1:if(n=e.v,!t){e.n=8;break}return e.p=2,e.n=3,chrome.tabs.get(t);case 3:r=e.v,o=0,i=Object.entries(n);case 4:if(!(o<i.length)){e.n=6;break}if(a=X(i[o],2),s=a[0],c=a[1],!((u=c.url||c.urlPrefix)&&r.url&&r.url.includes(u))){e.n=5;break}return e.a(2,oe(oe({},c),{},{siteId:s,url:u}));case 5:o++,e.n=4;break;case 6:e.n=8;break;case 7:e.p=7,e.v;case 8:return l=Object.values(n)[0],h=l.url||l.urlPrefix,e.a(2,oe(oe({},l),{},{url:h}))}},e,null,[[2,7]])})),function(e){return S.apply(this,arguments)})},{key:"executeInCurrentTab",value:(E=se(te().m(function e(t,n,r){var o;return te().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,this.ensureContentScript(t,n.contentScript);case 1:return e.a(2,this.sendMessageToTab(t,{type:T.PERFORM_SITE_ACTIONS,actions:r}));case 2:return e.p=2,o=e.v,e.a(2,{success:!1,message:"Execution error: ".concat(o.message)})}},e,this,[[0,2]])})),function(e,t,n){return E.apply(this,arguments)})},{key:"executeInBackground",value:(b=se(te().m(function e(t,n){var r,o,i,a;return te().w(function(e){for(;;)switch(e.p=e.n){case 0:return r=null,e.p=1,e.n=2,chrome.tabs.create({url:t.urlPrefix||t.url,active:!1});case 2:return r=e.v,e.n=3,this.waitForTabLoad(r.id);case 3:return e.n=4,this.delay(R.PAGE_INITIALIZATION);case 4:return e.n=5,this.ensureContentScript(r.id,t.contentScript);case 5:return e.n=6,this.sendMessageToTab(r.id,{type:T.PERFORM_SITE_ACTIONS,actions:n});case 6:return o=e.v,e.n=7,g();case 7:if(!1===e.v.close_tabs_after_execution||!r){e.n=11;break}return e.p=8,console.log("[ExecutionService] Closing background tab ".concat(r.id," after successful execution")),e.n=9,chrome.tabs.remove(r.id);case 9:r=null,e.n=11;break;case 10:e.p=10,i=e.v,console.warn("[ExecutionService] Failed to close tab ".concat(r.id,":"),i.message);case 11:return e.a(2,o);case 12:return e.p=12,a=e.v,e.a(2,{success:!1,message:"Background execution error: ".concat(a.message)});case 13:if(e.p=13,!r){e.n=18;break}return e.n=14,g();case 14:if(!1===e.v.close_tabs_after_execution){e.n=18;break}return e.p=15,console.log("[ExecutionService] Cleaning up tab ".concat(r.id," after error")),e.n=16,chrome.tabs.remove(r.id);case 16:e.n=18;break;case 17:e.p=17,e.v;case 18:return e.f(13);case 19:return e.a(2)}},e,this,[[15,17],[8,10],[1,12,13,19]])})),function(e,t){return b.apply(this,arguments)})},{key:"executeInTab",value:(m=se(te().m(function e(t,n,r){var o,i,a;return te().w(function(e){for(;;)switch(e.p=e.n){case 0:o=2,i=0;case 1:if(!(i<=o)){e.n=13;break}return e.p=2,e.n=3,this.waitForTabLoad(t);case 3:if(!n.contentScript){e.n=4;break}return e.n=4,this.ensureContentScript(t,n.contentScript);case 4:return e.a(2,this.sendMessageToTab(t,{type:T.PERFORM_SITE_ACTIONS,actions:r}));case 5:return e.p=5,a=e.v,e.p=6,e.n=7,chrome.tabs.get(t);case 7:e.n=9;break;case 8:return e.p=8,e.v,e.a(2,{success:!1,message:"Tab execution failed: Tab no longer exists (".concat(a.message,")"),error:"TAB_CLOSED"});case 9:if(i!==o){e.n=11;break}if(!(a.message.includes("message channel closed")||a.message.includes("asynchronous response")||a.message.includes("Could not establish connection"))){e.n=10;break}return e.a(2,{success:!1,message:"Tab execution failed after ".concat(o+1," attempts: ").concat(a.message,". The content script may not be ready or the tab was navigated."),error:"MESSAGE_CHANNEL_ERROR"});case 10:return e.a(2,{success:!1,message:"Tab execution failed after ".concat(o+1," attempts: ").concat(a.message)});case 11:return console.log("[ExecutionService] Execution attempt ".concat(i+1," failed for tab ").concat(t,", retrying in 1 second..."),a.message),e.n=12,this.delay(1e3);case 12:i++,e.n=1;break;case 13:return e.a(2)}},e,this,[[6,8],[2,5]])})),function(e,t,n){return m.apply(this,arguments)})},{key:"ensureContentScript",value:(y=se(te().m(function e(t,n){var r;return te().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,chrome.tabs.get(t);case 1:return e.n=2,chrome.scripting.executeScript({target:{tabId:t},func:function(e){return!0===window[e]},args:[I],world:"ISOLATED"});case 2:if(X(e.v,1)[0].result){e.n=4;break}return e.n=3,chrome.scripting.executeScript({target:{tabId:t},files:[n],world:"ISOLATED"});case 3:return e.n=4,this.delay(500);case 4:e.n=7;break;case 5:if(e.p=5,!(r=e.v).message.includes("Cannot access contents of url")){e.n=6;break}throw new Error(N.TAB_NOT_ACCESSIBLE);case 6:throw new Error("Failed to inject content script: ".concat(r.message));case 7:return e.a(2)}},e,this,[[0,5]])})),function(e,t){return y.apply(this,arguments)})},{key:"flushTabAndForget",value:(v=se(te().m(function e(t){return te().w(function(e){for(;;)switch(e.p=e.n){case 0:if(t){e.n=1;break}return e.a(2);case 1:return e.p=1,e.n=2,chrome.tabs.remove(t);case 2:e.n=4;break;case 3:e.p=3,e.v;case 4:this.activeTabs.delete(t),console.warn("[ExecutionService] Flushed tab ".concat(t," from activeTabs"));case 5:return e.a(2)}},e,this,[[1,3]])})),function(e){return v.apply(this,arguments)})},{key:"waitForTabLoad",value:function(e){return new Promise(function(){var t=se(te().m(function t(n){var r;return te().w(function(t){for(;;)switch(t.p=t.n){case 0:return t.p=0,t.n=1,chrome.tabs.get(e);case 1:if("complete"!==t.v.status){t.n=2;break}return n(),t.a(2);case 2:r=function(t,o){t===e&&"complete"===o.status&&(chrome.tabs.onUpdated.removeListener(r),n())},chrome.tabs.onUpdated.addListener(r),t.n=4;break;case 3:t.p=3,t.v,n();case 4:return t.a(2)}},t,null,[[0,3]])}));return function(e){return t.apply(this,arguments)}}())}},{key:"sendMessageToTab",value:function(e,t){var n=this;return new Promise(function(){var r=se(te().m(function r(o,i){var a,s,c,u,l,f;return te().w(function(r){for(;;)switch(r.p=r.n){case 0:if(n.timeoutSettings.execution_service_monitoring_timeout_seconds){r.n=5;break}return r.n=1,n.loadTimeoutSettings();case 1:return r.p=1,r.n=2,fetch(chrome.runtime.getURL("lib/shared-config.json"));case 2:return a=r.v,r.n=3,a.json();case 3:(s=r.v).EXECUTION_SERVICE&&(n.maxConcurrentTabs=s.EXECUTION_SERVICE.max_concurrent_tabs||5),r.n=5;break;case 4:r.p=4,f=r.v,console.warn("[ExecutionService] Failed to load execution service config, using defaults:",f);case 5:console.log("[ExecutionService] Sending message to tab ".concat(e,":"),t),c=t.actions&&t.actions.some(function(e){return["MonitorConditionThenAct","MONITORPROFIT","MonitorSymbolFromWatchlist"].includes(e.action)}),u=c?1e3*n.timeoutSettings.execution_service_monitoring_timeout_seconds:1e3*n.timeoutSettings.execution_service_general_timeout_seconds,console.log("[ExecutionService] Using ".concat(u/1e3,"s timeout for ").concat(c?"monitoring":"regular"," action")),l=setTimeout(function(){console.error("[ExecutionService] Message timeout for tab ".concat(e," after ").concat(u/1e3,"s")),i(new Error("Message timeout: No response received within ".concat(u/1e3," seconds")))},u),chrome.tabs.sendMessage(e,t,function(t){if(clearTimeout(l),chrome.runtime.lastError){var n=chrome.runtime.lastError.message;return console.error("[ExecutionService] Chrome runtime error for tab ".concat(e,":"),n),void(n.includes("message channel closed")||n.includes("asynchronous response")||n.includes("Could not establish connection")?(console.error("[ExecutionService] Message channel closed error detected"),i(new Error(N.MESSAGE_CHANNEL_CLOSED))):i(new Error("Failed to send message: ".concat(n))))}if(!t)return console.error("[ExecutionService] No response received from tab ".concat(e)),void i(new Error(N.NO_RESPONSE));console.log("[ExecutionService] Received response from tab ".concat(e,":"),t),o(t)});case 6:return r.a(2)}},r,null,[[1,4]])}));return function(e,t){return r.apply(this,arguments)}}())}},{key:"delay",value:function(e){return new Promise(function(t){return setTimeout(t,e)})}},{key:"buildDependencyMap",value:function(e){var t=new Map,n=new Map,r=new Set(e.map(function(e){return e.id||e.action}));return e.forEach(function(e){var o=e.id||e.action;if(t.set(o,[]),n.set(o,[]),e.depends_on){var i=[];Array.isArray(e.depends_on)?i=e.depends_on.filter(function(e){return!!r.has(e)||(console.log("[ExecutionService] Missing dependency '".concat(e,"' for action '").concat(o,"' - treating as independent")),!1)}):r.has(e.depends_on)?i=[e.depends_on]:(console.log("[ExecutionService] Missing dependency '".concat(e.depends_on,"' for action '").concat(o,"' - treating as independent")),i=[]),t.set(o,i),i.forEach(function(e){n.has(e)||n.set(e,[]),n.get(e).push(o)})}}),{dependencies:t,dependents:n}}},{key:"executeNodeWithTab",value:(d=se(te().m(function e(t,n){var r,o,i,a,s,c,u,l,h,p,d,v,y,m,b,E,S,w,_,O,k,P,A,x,I=this,M=arguments;return te().w(function(e){for(;;)switch(e.p=e.n){case 0:if(r=M.length>2&&void 0!==M[2]?M[2]:{},o=M.length>3&&void 0!==M[3]?M[3]:function(){},i=n.id,a=null,s=null,console.log("[ExecutionService] Starting node execution: ".concat(i)),this.nodeExecutions.set(i,{status:"running",graphId:t,action:n,startTime:new Date,tabId:null}),o(t,i,"running",{action:n,graphId:t,nodeId:i}),e.p=1,!this.cancelledActions.has(i)){e.n=2;break}throw new Error("Action ".concat(i," was cancelled before execution"));case 2:if(c=new Set(["MonitorConditionThenAct","MONITORPROFIT","MonitorSymbolFromWatchlist"]),u=c.has(((null==n?void 0:n.action)||"").toString()),l=null,!u){e.n=5;break}return e.n=3,this.ensureObserverMonitorTab();case 3:return a=this.observerMonitor.tabId,e.n=4,f();case 4:h=e.v,l=Object.values(h)[0],e.n=7;break;case 5:return e.n=6,this.getOrCreateTab(n,r);case 6:p=e.v,a=p.tabId,s=this.activeTabs.get(a),l=p.site;case 7:return(d=this.nodeExecutions.get(i)).tabId=a,v=function(){var e=se(te().m(function e(r,o){return te().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,I.ensureContentScript(r,o.contentScript);case 1:if(!I.cancelledActions.has(i)){e.n=2;break}throw new Error("Action ".concat(i," was cancelled during setup"));case 2:return e.n=3,I.checkLoginBeforeExecution(r,i,t);case 3:return e.a(2,I.sendMessageToTab(r,{type:T.PERFORM_SITE_ACTIONS,actions:[n]}))}},e)}));return function(t,n){return e.apply(this,arguments)}}(),e.p=8,e.n=9,v(a,l);case 9:y=e.v,console.log("result from tab during execution =>",y),e.n=20;break;case 10:if(e.p=10,A=e.v,(m=(null==A?void 0:A.message)||"")!==N.TAB_NOT_ACCESSIBLE&&m!==N.MESSAGE_CHANNEL_CLOSED&&m!==N.NO_RESPONSE){e.n=19;break}if(console.warn("[ExecutionService] Tab ".concat(a," inaccessible (").concat(m,"). Flushing and retrying with a new tab.")),!u){e.n=14;break}return this.observerMonitor.tabId=null,e.n=11,this.ensureObserverMonitorTab();case 11:return a=this.observerMonitor.tabId,e.n=12,f();case 12:return b=e.v,l=Object.values(b)[0],(E=this.nodeExecutions.get(i))&&(E.tabId=a),e.n=13,v(a,l);case 13:y=e.v,e.n=18;break;case 14:return e.n=15,this.flushTabAndForget(a);case 15:return e.n=16,this.getOrCreateTab(n,oe(oe({},r),{},{reuseTab:!1}));case 16:return S=e.v,a=S.tabId,s=this.activeTabs.get(a),(w=this.nodeExecutions.get(i))&&(w.tabId=a),e.n=17,v(a,S.site);case 17:y=e.v;case 18:e.n=20;break;case 19:throw A;case 20:return d.status=y.success?"completed":"failed",d.result=y,d.endTime=new Date,d.duration=d.endTime-d.startTime,console.log("[ExecutionService] Node execution completed: ".concat(i,", success: ").concat(y.success)),o(t,i,y.success?"completed":"failed",{action:n,tabId:a,duration:d.duration,resultDetail:y.results?y.results[0]:y}),e.a(2,{success:y.success,nodeId:i,action:n.action,result:y.results?y.results[0]:y,tabId:a,duration:d.duration});case 21:return e.p=21,x=e.v,console.error("[ExecutionService] Node execution failed: ".concat(i),x),(_=this.nodeExecutions.get(i))&&(_.status="failed",_.error=x.message,_.endTime=new Date),e.a(2,{success:!1,nodeId:i,action:n.action,error:x.message,tabId:a});case 22:return e.p=22,!isMonitoringAction&&a&&s&&!1!==r.reuseTab&&"busy"===s.status&&s.acquiredBy===i&&(s.status="available",s.lastUsed=new Date,s.acquiredBy=null,console.log("[ExecutionService] Released tab ".concat(a," for reuse"))),e.p=23,e.n=24,g();case 24:if(O=e.v,k=!1!==O.close_tabs_after_execution,P=!0===r.closeAfterNode||!1===r.reuseTab,isMonitoringAction||!a||!k||!P){e.n=29;break}if(this.loginMonitor&&this.loginMonitor.isDedicated&&this.loginMonitor.tabId===a||this.observerMonitor&&this.observerMonitor.isDedicated&&this.observerMonitor.tabId===a){e.n=29;break}return e.p=25,e.n=26,chrome.tabs.remove(a);case 26:e.n=28;break;case 27:e.p=27,e.v;case 28:this.activeTabs.delete(a),console.log("[ExecutionService] Closed tab ".concat(a," after node ").concat(i));case 29:e.n=31;break;case 30:e.p=30,e.v;case 31:return this.cancelledActions.delete(i),e.f(22);case 32:return e.a(2)}},e,this,[[25,27],[23,30],[8,10],[1,21,22,32]])})),function(e,t){return d.apply(this,arguments)})},{key:"getOrCreateTab",value:(h=se(te().m(function e(t){var n,r,o,i,a,s,c=this,u=arguments;return te().w(function(e){for(;;)switch(e.n){case 0:return n=u.length>1&&void 0!==u[1]?u[1]:{},e.n=1,f();case 1:if(r=e.v,o=null,o=n.siteId?r[n.siteId]:Object.values(r)[0]){e.n=2;break}throw new Error("No supported site found for action execution");case 2:if(i=o.url,!1===n.reuseTab){e.n=4;break}return this.tabAcquisitionLocks.has(i)||this.tabAcquisitionLocks.set(i,Promise.resolve()),e.n=3,this.tabAcquisitionLocks.get(i).then(se(te().m(function e(){var n,r,i,a,s,u;return te().w(function(e){for(;;)switch(e.p=e.n){case 0:n=W(c.activeTabs),e.p=1,n.s();case 2:if((r=n.n()).done){e.n=9;break}if(i=X(r.value,2),a=i[0],(s=i[1]).site.url!==o.url||"available"!==s.status){e.n=8;break}return e.p=3,e.n=4,chrome.tabs.get(a);case 4:e.n=7;break;case 5:return e.p=5,e.v,e.n=6,c.flushTabAndForget(a);case 6:return e.a(3,8);case 7:if("available"!==s.status){e.n=8;break}return s.status="busy",s.acquiredBy=t.id||t.action,s.lastAcquired=new Date,console.log("[ExecutionService] Reusing existing tab: ".concat(a," for action: ").concat(t.id||t.action)),e.a(2,{tabId:a,site:o,reused:!0});case 8:e.n=2;break;case 9:e.n=11;break;case 10:e.p=10,u=e.v,n.e(u);case 11:return e.p=11,n.f(),e.f(11);case 12:return e.a(2,null)}},e,null,[[3,5],[1,10,11,12]])})));case 3:if(!(a=e.v)){e.n=4;break}return e.a(2,a);case 4:if(!(this.activeTabs.size>=this.maxConcurrentTabs)){e.n=5;break}throw new Error("Maximum concurrent tabs (".concat(this.maxConcurrentTabs,") reached. Consider increasing limit or enabling tab reuse."));case 5:return s=this.tabAcquisitionLocks.get(i).then(se(te().m(function e(){var n;return te().w(function(e){for(;;)switch(e.n){case 0:return console.log("[ExecutionService] Creating new tab for site: ".concat(o.url)),e.n=1,chrome.tabs.create({url:o.url,active:!1});case 1:return n=e.v,c.activeTabs.set(n.id,{tab:n,site:o,status:"busy",created:new Date,acquiredBy:t.id||t.action,actions:[]}),e.n=2,c.waitForTabLoad(n.id);case 2:return e.n=3,c.delay(R.PAGE_INITIALIZATION);case 3:return console.log("[ExecutionService] New tab created and loaded: ".concat(n.id)),e.a(2,{tabId:n.id,site:o,reused:!1})}},e)}))),this.tabAcquisitionLocks.set(i,s),e.n=6,s;case 6:return e.a(2,e.v)}},e,this)})),function(e){return h.apply(this,arguments)})},{key:"updateNodeStatus",value:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if(this.activeGraphs.has(e)){var o=this.activeGraphs.get(e);o.nodes.set(t,{status:n,data:r,timestamp:new Date}),"completed"===n?o.progress.completed++:"failed"===n&&o.progress.failed++}}},{key:"failDependentActions",value:function(e,t,n){var r=this,o=n.get(t)||[],i=new Set,a=function(o){i.has(o)||(i.add(o),r.cancelledActions.add(o),console.log("[ExecutionService] Cancelling running action due to dependency failure: ".concat(o)),r.updateNodeStatus(e,o,"failed",{reason:"dependency_failed",failedDependency:t,timestamp:new Date}),(n.get(o)||[]).forEach(a))};console.log("[ExecutionService] Failing ".concat(o.length," dependent actions for failed node: ").concat(t)),o.forEach(a)}},{key:"cleanupGraphTabs",value:(l=se(te().m(function e(t){var n,r,o,i,a,s,c,u,l,f,h,p,d,v;return te().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.n=1,g();case 1:if(!1!==e.v.close_tabs_after_execution){e.n=2;break}return console.log("[ExecutionService] Tab cleanup disabled for graph: ".concat(t)),e.a(2);case 2:console.log("[ExecutionService] Cleaning up tabs for graph: ".concat(t)),n=new Set,r=W(this.nodeExecutions);try{for(r.s();!(o=r.n()).done;)(i=X(o.value,2))[0],(a=i[1]).graphId===t&&a.tabId&&n.add(a.tabId)}catch(e){r.e(e)}finally{r.f()}s=W(n),e.p=3,s.s();case 4:if((c=s.n()).done){e.n=9;break}if(!((u=c.value)===this.loginMonitor.tabId&&this.loginMonitor.isDedicated||u===this.observerMonitor.tabId&&this.observerMonitor.isDedicated)){e.n=5;break}return console.log("[ExecutionService] Skipping monitor/observer tab during cleanup:",u),e.a(3,8);case 5:return e.p=5,e.n=6,chrome.tabs.remove(u);case 6:this.activeTabs.delete(u),console.log("[ExecutionService] Closed tab: ".concat(u)),e.n=8;break;case 7:e.p=7,d=e.v,console.warn("[ExecutionService] Failed to close tab ".concat(u,":"),d.message);case 8:e.n=4;break;case 9:e.n=11;break;case 10:e.p=10,v=e.v,s.e(v);case 11:return e.p=11,s.f(),e.f(11);case 12:l=W(this.nodeExecutions);try{for(l.s();!(f=l.n()).done;)h=X(f.value,2),p=h[0],h[1].graphId===t&&this.nodeExecutions.delete(p)}catch(e){l.e(e)}finally{l.f()}this.activeGraphs.delete(t),0===this.activeGraphs.size&&0===this.nodeExecutions.size&&console.log("[ExecutionService] No active graphs or nodes remaining. Keeping login monitor running.");case 13:return e.a(2)}},e,this,[[5,7],[3,10,11,12]])})),function(e){return l.apply(this,arguments)})},{key:"getGraphStatus",value:function(e){var t=this.activeGraphs.get(e);if(!t)return{found:!1};var n,r=[],o=W(this.nodeExecutions);try{for(o.s();!(n=o.n()).done;){var i=X(n.value,2),a=i[0],s=i[1];s.graphId===e&&r.push({nodeId:a,status:s.status,action:s.action.action,tabId:s.tabId,duration:s.duration,startTime:s.startTime,endTime:s.endTime})}}catch(e){o.e(e)}finally{o.f()}return{found:!0,graphId:e,status:t.status,progress:t.progress,nodeStatuses:r,startTime:t.startTime,endTime:t.endTime,duration:t.duration}}},{key:"getAllExecutionStatus",value:function(){var e,t=[],n=W(this.activeGraphs.keys());try{for(n.s();!(e=n.n()).done;){var r=e.value;t.push(this.getGraphStatus(r))}}catch(e){n.e(e)}finally{n.f()}return{activeGraphs:t.length,activeTabs:this.activeTabs.size,activeNodes:this.nodeExecutions.size,pausedGraphs:this.pausedGraphs.size,graphs:t,tabs:Array.from(this.activeTabs.entries()).map(function(e){var t=X(e,2),n=t[0],r=t[1];return{tabId:n,url:r.site.url,status:r.status,created:r.created}}),login:{required:this.loginMonitor.required,lastChecked:this.loginMonitor.lastChecked,monitorTabId:this.loginMonitor.tabId,pausedByLogin:Array.from(this.loginMonitor.pausedGraphsSet)}}}},{key:"pauseGraph",value:function(e){try{var t=this.activeGraphs.get(e);return t?"running"!==t.status?{success:!1,message:"Graph '".concat(e,"' is not running (status: ").concat(t.status,")")}:this.pausedGraphs.has(e)?{success:!1,message:"Graph '".concat(e,"' is already paused")}:(this.pausedGraphs.add(e),t.status="paused",t.pausedAt=new Date,console.log("[ExecutionService] Graph paused: ".concat(e)),{success:!0,message:"Graph '".concat(e,"' paused successfully"),pausedAt:t.pausedAt}):{success:!1,message:"Graph '".concat(e,"' not found")}}catch(t){return console.error("[ExecutionService] Error pausing graph ".concat(e,":"),t),{success:!1,message:"Failed to pause graph: ".concat(t.message)}}}},{key:"resumeGraph",value:function(e){try{var t=this.activeGraphs.get(e);if(!t)return{success:!1,message:"Graph '".concat(e,"' not found")};if("paused"!==t.status)return{success:!1,message:"Graph '".concat(e,"' is not paused (status: ").concat(t.status,")")};if(!this.pausedGraphs.has(e))return{success:!1,message:"Graph '".concat(e,"' is not in paused state")};this.pausedGraphs.delete(e),t.status="running",t.resumedAt=new Date,t.pausedAt&&(t.pauseDuration=(t.pauseDuration||0)+(t.resumedAt-t.pausedAt));var n=this.pauseResolvers.get(e);return n&&(n(),this.pauseResolvers.delete(e)),console.log("[ExecutionService] Graph resumed: ".concat(e)),{success:!0,message:"Graph '".concat(e,"' resumed successfully"),resumedAt:t.resumedAt,totalPauseDuration:t.pauseDuration||0}}catch(t){return console.error("[ExecutionService] Error resuming graph ".concat(e,":"),t),{success:!1,message:"Failed to resume graph: ".concat(t.message)}}}},{key:"checkForPause",value:(u=se(te().m(function e(t){var n=this;return te().w(function(e){for(;;)switch(e.n){case 0:if(!this.pausedGraphs.has(t)){e.n=1;break}return console.log("[ExecutionService] Graph ".concat(t," is paused, waiting for resume...")),e.a(2,new Promise(function(e){n.pausedGraphs.has(t)?n.pauseResolvers.set(t,e):e()}));case 1:return e.a(2)}},e,this)})),function(e){return u.apply(this,arguments)})},{key:"updatePrevSummary",value:function(e){try{var t,n,r,o,i,a;if(!e)return;console.log("[ExecutionService] Updating previous summary:",{open:{count:null===(t=e.open)||void 0===t?void 0:t.count,firstKey:!(null===(n=e.open)||void 0===n||!n.firstKey),lastKey:!(null===(r=e.open)||void 0===r||!r.lastKey)},completed:{count:null===(o=e.completed)||void 0===o?void 0:o.count,firstKey:!(null===(i=e.completed)||void 0===i||!i.firstKey),lastKey:!(null===(a=e.completed)||void 0===a||!a.lastKey)}}),this.loginMonitor.orderSummary.open=e.open||this.loginMonitor.orderSummary.open,this.loginMonitor.orderSummary.completed=e.completed||this.loginMonitor.orderSummary.completed}catch(e){}}},{key:"buildOrderKey",value:function(e,t){try{var n=(null==e?void 0:e.symbol)||"",r=(null==e?void 0:e.type)||"",o=(null==e?void 0:e.exchange)||"",i=(null==e?void 0:e.product)||"",a="";if(null!=e&&e.quantity&&"string"==typeof e.quantity)a=e.quantity;else if(null!=e&&e.quantity&&"object"===J(e.quantity)){var s=Number.isFinite(e.quantity.filled)?e.quantity.filled:0,c=Number.isFinite(e.quantity.total)?e.quantity.total:s;a="".concat(s,"/").concat(c)}var u=(null==e?void 0:e.status)||"",l=(null==e?void 0:e.time)||"",f="";if("completed"===t)f=(null!=(null==e?void 0:e.avgPrice)?String(e.avgPrice):"")||"";else{var h=null!=(null==e?void 0:e.price)?e.price:"",p=null!=(null==e?void 0:e.ltp)?e.ltp:"";f=String(""!==h?h:p)}return[n,r,o,i,a,u,l,f].join("|")}catch(e){return""}}},{key:"trimOrdersByBoundary",value:function(e,t,n,r){var o=this;if(!Array.isArray(e)||0===e.length)return[];var i=e.map(function(e){return o.buildOrderKey(e,r)}),a=0,s=e.length-1;if(t){var c=i.indexOf(t);c>=0&&(a=c)}if(n){var u=i.lastIndexOf(n);u>=0&&(s=u)}return a>s?[]:e.slice(a,s+1)}},{key:"storePreviousOrderData",value:function(e,t){try{var n,r;this.loginMonitor.previousOrderData||(this.loginMonitor.previousOrderData={open:null,completed:null}),e&&Array.isArray(e)&&(this.loginMonitor.previousOrderData.open=e),t&&Array.isArray(t)&&(this.loginMonitor.previousOrderData.completed=t),console.log("[ExecutionService] Stored previous order data for reuse:",{openCount:(null===(n=this.loginMonitor.previousOrderData.open)||void 0===n?void 0:n.length)||0,completedCount:(null===(r=this.loginMonitor.previousOrderData.completed)||void 0===r?void 0:r.length)||0})}catch(e){}}},{key:"getPreviousOrderData",value:function(){try{return this.loginMonitor.previousOrderData||{open:null,completed:null}}catch(e){return{open:null,completed:null}}}},{key:"shouldEarlyExitFromSummary",value:function(e){try{if(!e)return!1;var t=this.loginMonitor.orderSummary,n=function(e,t){if(null===t.count)return{unchanged:!1,mismatch:!1};var n=e.count===t.count,r=e.firstKey===t.firstKey&&e.lastKey===t.lastKey;return n&&r?{unchanged:!0,mismatch:!1}:n&&!r?{unchanged:!1,mismatch:!0}:{unchanged:!1,mismatch:!1}},r=n(e.open||{},t.open),o=n(e.completed||{},t.completed);return console.log("[ExecutionService] Early-exit decision:",{prevOpen:t.open,currOpen:e.open,prevCompleted:t.completed,currCompleted:e.completed,checkOpen:r,checkCompleted:o,mismatchStreak:this.loginMonitor.stabilize.mismatchStreak}),r.unchanged&&o.unchanged?(this.loginMonitor.stabilize.mismatchStreak=0,console.log("[ExecutionService] Early exit: summaries unchanged - orders are the same"),!0):(r.mismatch||o.mismatch)&&(this.loginMonitor.stabilize.mismatchStreak++,console.log("[ExecutionService] Summary mismatch with same counts. Streak =",this.loginMonitor.stabilize.mismatchStreak),this.loginMonitor.stabilize.mismatchStreak<this.loginMonitor.stabilize.maxStreak)?(console.log("[ExecutionService] Skipping heavy fetch this tick due to transient mismatch"),!0):(this.loginMonitor.stabilize.mismatchStreak=0,console.log("[ExecutionService] Proceeding to heavy fetch: change detected or stabilized mismatches"),!1)}catch(e){return!1}}},{key:"getKiteBaseUrl",value:(c=se(te().m(function e(){var t,n,r;return te().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,fetch(chrome.runtime.getURL("lib/shared-config.json"));case 1:return t=e.v,e.n=2,t.json();case 2:return n=e.v,r=(null==n?void 0:n.SUPPORTED_SITES)&&Object.values(n.SUPPORTED_SITES)[0],e.a(2,(null==r?void 0:r.urlPrefix)||(null==r?void 0:r.url)||"https://kite.zerodha.com/");case 3:return e.p=3,e.v,e.a(2,"https://kite.zerodha.com/")}},e,null,[[0,3]])})),function(){return c.apply(this,arguments)})},{key:"getOrdersPageUrl",value:(s=se(te().m(function e(){var t;return te().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,this.getKiteBaseUrl();case 1:return t=e.v,e.a(2,new URL("orders",t).toString())}},e,this)})),function(){return s.apply(this,arguments)})},{key:"getDashboardUrl",value:(a=se(te().m(function e(){return te().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,this.getKiteBaseUrl();case 1:return e.a(2,e.v)}},e,this)})),function(){return a.apply(this,arguments)})},{key:"ensureLoginMonitorTab",value:(i=se(te().m(function e(){var t,n,r,o=this;return te().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.n=1,this.getOrdersPageUrl();case 1:if(t=e.v,!this.loginMonitor.tabId){e.n=5;break}return e.p=2,e.n=3,chrome.tabs.get(this.loginMonitor.tabId);case 3:e.n=5;break;case 4:e.p=4,e.v,this.loginMonitor.tabId=null;case 5:if(this.loginMonitor.tabId){e.n=6;break}return this.loginMonitor.creationPromise||(this.loginMonitor.creationPromise=chrome.tabs.create({url:t,pinned:!0,active:!1}).then(function(e){o.loginMonitor.tabId=e.id,o.loginMonitor.isDedicated=!0;try{var t,n;null===(t=chrome.storage)||void 0===t||null===(t=t.local)||void 0===t||null===(n=t.set)||void 0===n||n.call(t,{loginMonitorTabId:e.id})}catch(e){}}).catch(function(){}).finally(function(){o.loginMonitor.creationPromise=null})),e.n=6,this.loginMonitor.creationPromise;case 6:return e.n=7,f();case 7:return n=e.v,r=Object.values(n)[0],e.n=8,this.waitForTabLoad(this.loginMonitor.tabId);case 8:return e.n=9,this.ensureContentScript(this.loginMonitor.tabId,r.contentScript);case 9:return e.n=10,this.sendMessageToTab(this.loginMonitor.tabId,{type:"PERFORM_SITE_ACTIONS",actions:[{action:"ShowMonitoringBanner",arguments:{text:"SmartAgent: Login Monitor (do not close)",bg:"#0b3d91",fg:"#ffffff"}}]}).catch(function(){});case 10:return e.a(2)}},e,this,[[2,4]])})),function(){return i.apply(this,arguments)})},{key:"ensureObserverMonitorTab",value:(o=se(te().m(function e(){var t,n,r,o=this;return te().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.n=1,this.getDashboardUrl();case 1:if(t=e.v,!this.observerMonitor.tabId){e.n=5;break}return e.p=2,e.n=3,chrome.tabs.get(this.observerMonitor.tabId);case 3:e.n=5;break;case 4:e.p=4,e.v,this.observerMonitor.tabId=null;case 5:if(this.observerMonitor.tabId){e.n=6;break}return this.observerMonitor.creationPromise||(this.observerMonitor.creationPromise=chrome.tabs.create({url:t,pinned:!0,active:!1}).then(function(e){o.observerMonitor.tabId=e.id,o.observerMonitor.isDedicated=!0}).catch(function(){}).finally(function(){o.observerMonitor.creationPromise=null})),e.n=6,this.observerMonitor.creationPromise;case 6:return e.n=7,f();case 7:return n=e.v,r=Object.values(n)[0],e.n=8,this.waitForTabLoad(this.observerMonitor.tabId);case 8:return e.n=9,this.ensureContentScript(this.observerMonitor.tabId,r.contentScript);case 9:return e.n=10,this.sendMessageToTab(this.observerMonitor.tabId,{type:"PERFORM_SITE_ACTIONS",actions:[{action:"ShowMonitoringBanner",arguments:{text:"SmartAgent: Observer Tab (do not close)",bg:"#004d40",fg:"#ffffff"}}]}).catch(function(){});case 10:return e.a(2)}},e,this,[[2,4]])})),function(){return o.apply(this,arguments)})},{key:"checkLoginAndTogglePause",value:(r=se(te().m(function e(){var t,n,r,o,i,a,s,c,u,l,h,p,d,v,y,g,m,b,E,S,w,_,O,k,P,A,x,I,M,C,R,N,j,F,L,D,G,U,B,z,K,V,q,Y,H,$,J,Q,ee,ne,re,ie,ae,se,ce,ue,le,fe,he,pe,de,ve,ye,ge,me,be,Ee,Se,we,_e,Te,Oe,ke;return te().w(function(e){for(;;)switch(e.p=e.n){case 0:if(!this.loginMonitor.checking){e.n=1;break}return e.a(2);case 1:if(this.loginMonitor.tabId){e.n=2;break}return e.a(2);case 2:return this.loginMonitor.checking=!0,e.p=3,e.n=4,this.getOrdersPageUrl();case 4:if(t=e.v,!this.loginMonitor.isDedicated){e.n=5;break}return e.n=5,chrome.tabs.update(this.loginMonitor.tabId,{url:t,pinned:!0,active:!1});case 5:return e.n=6,this.waitForTabLoad(this.loginMonitor.tabId);case 6:return e.n=7,this.sendMessageToTab(this.loginMonitor.tabId,{type:"PERFORM_SITE_ACTIONS",actions:[{action:"ShowMonitoringBanner",arguments:{}}]}).catch(function(){});case 7:return e.n=8,this.sendMessageToTab(this.loginMonitor.tabId,{type:"PERFORM_SITE_ACTIONS",actions:[{action:"IsLoginRequired",arguments:{}}]});case 8:if(n=e.v,r=null,n&&Array.isArray(n.results)&&null!=(i=n.results[0])&&i.success&&(r=!(null==i||null===(o=i.data)||void 0===o||!o.loginRequired)),a=this.loginMonitor.required,this.loginMonitor.required=r,this.loginMonitor.lastChecked=new Date,console.log("[ExecutionService] Login check: required=".concat(r,", previous=").concat(a)),!1===r&&!1!==a&&(this.loginMonitor.fetchedThisSession=!1),!0!==r){e.n=14;break}if(!0===a){e.n=13;break}return e.p=9,e.n=10,f();case 10:c=e.v,u=c&&Object.values(c)[0]?Object.values(c)[0]:null,l=u&&u.name?u.name:"Broker","undefined"!=typeof chrome&&null!==(s=chrome)&&void 0!==s&&null!==(s=s.runtime)&&void 0!==s&&s.sendMessage&&chrome.runtime.sendMessage({type:"LOGIN_REQUIRED",brokerName:l,targetTab:"chat"},function(){}),e.n=12;break;case 11:e.p=11,me=e.v,console.warn("[ExecutionService] Failed to publish LOGIN_REQUIRED message:",me);case 12:console.log("[ExecutionService] Login required - pausing all running graphs"),h=W(this.activeGraphs.entries());try{for(h.s();!(p=h.n()).done;)d=X(p.value,2),v=d[0],"running"===d[1].status&&(console.log("[ExecutionService] Pausing graph ".concat(v," due to login requirement")),null!=(y=this.pauseGraph(v))&&y.success&&this.loginMonitor.pausedGraphsSet.add(v))}catch(e){h.e(e)}finally{h.f()}case 13:e.n=19;break;case 14:if(!1!==r||!0!==a){e.n=19;break}return e.p=15,e.n=16,f();case 16:m=e.v,b=m&&Object.values(m)[0]?Object.values(m)[0]:null,E=b&&b.name?b.name:"Broker","undefined"!=typeof chrome&&null!==(g=chrome)&&void 0!==g&&null!==(g=g.runtime)&&void 0!==g&&g.sendMessage&&chrome.runtime.sendMessage({type:"LOGIN_RESOLVED",brokerName:E,targetTab:"chat"},function(){}),e.n=18;break;case 17:e.p=17,be=e.v,console.warn("[ExecutionService] Failed to publish LOGIN_RESOLVED message:",be);case 18:if(this.loginMonitor.pausedGraphsSet.size>0)for(console.log("[ExecutionService] Login no longer required - resuming paused graphs"),S=0,w=Array.from(this.loginMonitor.pausedGraphsSet);S<w.length;S++)_=w[S],console.log("[ExecutionService] Resuming graph ".concat(_," - login resolved")),null!=(O=this.resumeGraph(_))&&O.success&&this.loginMonitor.pausedGraphsSet.delete(_);case 19:if(!1!==r){e.n=51;break}return I=null,e.p=20,e.n=21,this.sendMessageToTab(this.loginMonitor.tabId,{type:T.PERFORM_SITE_ACTIONS,actions:[{action:"GetOrdersSummary",arguments:{}}]});case 21:null!=(M=e.v)&&M.success&&Array.isArray(M.results)?null!=(C=M.results[0])&&C.success?I=C.data:console.warn("[ExecutionService] GetOrdersSummary reported failure:",null==C?void 0:C.message):console.warn("[ExecutionService] Unexpected summaryRes shape:",M),e.n=23;break;case 22:e.p=22,e.v;case 23:if(I?console.log("[ExecutionService] Summary fetched (open/completed):",{open:I.open,completed:I.completed}):console.warn("[ExecutionService] Summary unavailable; skipping early-exit and running heavy fetch"),!I||!this.shouldEarlyExitFromSummary(I)){e.n=32;break}if(this.updatePrevSummary(I),console.log("[ExecutionService] Early-exit: reusing previous order data instead of fetching"),!(N=this.getPreviousOrderData()).open&&!N.completed){e.n=27;break}if(e.p=24,console.log("[ExecutionService] Forwarding stored previous orders to background:",{open:(null===(j=N.open)||void 0===j?void 0:j.length)||0,completed:(null===(F=N.completed)||void 0===F?void 0:F.length)||0}),"function"!=typeof globalThis.__upsertOrdersFromMonitor){e.n=25;break}return e.n=25,globalThis.__upsertOrdersFromMonitor(N.open||[],N.completed||[]);case 25:e.n=27;break;case 26:e.p=26,Ee=e.v,console.warn("[ExecutionService] Failed to forward stored previous orders for PouchDB upsert:",Ee);case 27:return L=Number(null===(R=this.timeoutSettings)||void 0===R?void 0:R.monitor_early_exit_delay_ms)||1e3,console.log("[ExecutionService] Early-exit wait (ms):",L),e.n=28,this.delay(L);case 28:return e.p=28,e.n=29,chrome.tabs.reload(this.loginMonitor.tabId);case 29:e.n=31;break;case 30:e.p=30,Se=e.v,console.warn("[ExecutionService] Failed to reload after early-exit:",null==Se?void 0:Se.message);case 31:return e.a(2);case 32:return D={open:oe({},(null===(k=this.loginMonitor)||void 0===k||null===(k=k.orderSummary)||void 0===k?void 0:k.open)||{}),completed:oe({},(null===(P=this.loginMonitor)||void 0===P||null===(P=P.orderSummary)||void 0===P?void 0:P.completed)||{})},I&&this.updatePrevSummary(I),G={openOrders:null,completedOrders:null},e.p=33,console.log("[ExecutionService] Logged in on monitor tab. Fetching OPEN orders..."),B=(null===(U=this.loginMonitor.orderSummary)||void 0===U?void 0:U.open)||{},z=D.open||{},K={},z.firstKey&&B.firstKey&&B.firstKey!==z.firstKey?K={scanDirection:"top",stopAtKeyTop:z.firstKey}:z.lastKey&&B.lastKey&&B.lastKey!==z.lastKey?K={scanDirection:"bottom",stopAtKeyBottom:z.lastKey}:z.firstKey&&(K={scanDirection:"top",stopAtKeyTop:z.firstKey}),e.n=34,this.sendMessageToTab(this.loginMonitor.tabId,{type:T.PERFORM_SITE_ACTIONS,actions:[{action:"GetOpenOrders",arguments:K}]});case 34:V=e.v,console.log("[ExecutionService] Open orders fetch result:",V),G.openOrders=V,null!=V&&V.success||console.warn("[ExecutionService] Open orders reported failure:",(null==V?void 0:V.abortMessage)||V),e.n=36;break;case 35:e.p=35,we=e.v,console.warn("[ExecutionService] Failed to fetch OPEN orders on monitor tab:",we);case 36:return e.p=36,console.log("[ExecutionService] Logged in on monitor tab. Fetching COMPLETED orders..."),Y=(null===(q=this.loginMonitor.orderSummary)||void 0===q?void 0:q.completed)||{},H=D.completed||{},$={},H.firstKey&&Y.firstKey&&Y.firstKey!==H.firstKey?$={scanDirection:"top",stopAtKeyTop:H.firstKey}:H.lastKey&&Y.lastKey&&Y.lastKey!==H.lastKey?$={scanDirection:"bottom",stopAtKeyBottom:H.lastKey}:H.firstKey&&($={scanDirection:"top",stopAtKeyTop:H.firstKey}),e.n=37,this.sendMessageToTab(this.loginMonitor.tabId,{type:T.PERFORM_SITE_ACTIONS,actions:[{action:"GetCompletedOrders",arguments:$}]});case 37:J=e.v,console.log("[ExecutionService] Completed orders fetch result:",J),G.completedOrders=J,null!=J&&J.success||console.warn("[ExecutionService] Completed orders reported failure:",(null==J?void 0:J.abortMessage)||J),e.n=39;break;case 38:e.p=38,_e=e.v,console.warn("[ExecutionService] Failed to fetch COMPLETED orders on monitor tab:",_e);case 39:return null!==(A=G.openOrders)&&void 0!==A&&A.success||null!==(x=G.completedOrders)&&void 0!==x&&x.success?this.loginMonitor.lastFetchAt=new Date:console.warn("[ExecutionService] Neither OPEN nor COMPLETED orders fetch succeeded; not updating lastFetchAt"),e.p=40,e.n=41,chrome.tabs.reload(this.loginMonitor.tabId);case 41:e.n=43;break;case 42:e.p=42,Te=e.v,console.warn("[ExecutionService] Failed to reload after fetch:",null==Te?void 0:Te.message);case 43:if(e.p=43,fe=Array.isArray(null===(Q=G.openOrders)||void 0===Q?void 0:Q.results)&&null!==(ee=G.openOrders.results[0])&&void 0!==ee&&null!==(ee=ee.data)&&void 0!==ee&&ee.orders?G.openOrders.results[0].data.orders:[],he=Array.isArray(null===(ne=G.completedOrders)||void 0===ne?void 0:ne.results)&&null!==(re=G.completedOrders.results[0])&&void 0!==re&&null!==(re=re.data)&&void 0!==re&&re.orders?G.completedOrders.results[0].data.orders:[],pe=(null===(ie=this.loginMonitor.previousOrderData)||void 0===ie?void 0:ie.open)||[],de=(null===(ae=this.loginMonitor.previousOrderData)||void 0===ae?void 0:ae.completed)||[],ve=this.loginMonitor.orderSummary||{},ye=fe.length>0?[].concat(Z(fe),Z(pe)):Z(pe),ye=this.trimOrdersByBoundary(ye,(null===(se=ve.open)||void 0===se?void 0:se.firstKey)||null,(null===(ce=ve.open)||void 0===ce?void 0:ce.lastKey)||null,"open"),ge=he.length>0?[].concat(Z(he),Z(de)):Z(de),ge=this.trimOrdersByBoundary(ge,(null===(ue=ve.completed)||void 0===ue?void 0:ue.firstKey)||null,(null===(le=ve.completed)||void 0===le?void 0:le.lastKey)||null,"completed"),!(ye.length+ge.length>0)){e.n=49;break}if(this.storePreviousOrderData(ye,ge),e.p=44,console.log("[ExecutionService] Forwarding monitor orders to background for upsert:",{open:ye.length,completed:ge.length,openWithId:ye.filter(function(e){var t;return!(null==e||null===(t=e.detailedInfo)||void 0===t||!t.orderId)}).length,completedWithId:ge.filter(function(e){var t;return!(null==e||null===(t=e.detailedInfo)||void 0===t||!t.orderId)}).length}),"function"!=typeof globalThis.__upsertOrdersFromMonitor){e.n=46;break}return e.n=45,globalThis.__upsertOrdersFromMonitor(ye,ge);case 45:e.n=47;break;case 46:console.warn("[ExecutionService] globalThis.__upsertOrdersFromMonitor is not a function");case 47:e.n=49;break;case 48:e.p=48,e.v;case 49:e.n=51;break;case 50:e.p=50,Oe=e.v,console.warn("[ExecutionService] Failed to forward monitor orders for PouchDB upsert:",Oe);case 51:e.n=56;break;case 52:return e.p=52,ke=e.v,console.warn("[ExecutionService] Login check failed:",ke),e.p=53,e.n=54,chrome.tabs.get(this.loginMonitor.tabId);case 54:e.n=56;break;case 55:e.p=55,e.v,this.loginMonitor.tabId=null;case 56:return e.p=56,this.loginMonitor.checking=!1,e.f(56);case 57:return e.a(2)}},e,this,[[53,55],[44,48],[43,50],[40,42],[36,38],[33,35],[28,30],[24,26],[20,22],[15,17],[9,11],[3,52,56,57]])})),function(){return r.apply(this,arguments)})},{key:"startLoginMonitor",value:function(){var e=this;this.loginMonitor.started||(this.loginMonitor.started=!0,console.log("[ExecutionService] Starting login monitor"),this.ensureLoginMonitorTab().catch(function(e){console.warn("[ExecutionService] Failed to ensure login monitor tab:",e)}),this.ensureObserverMonitorTab().catch(function(e){console.warn("[ExecutionService] Failed to ensure observer monitor tab:",e)}),this.loginMonitor.onRemovedHandler=function(t){e.loginMonitor.started&&t===e.loginMonitor.tabId&&e.loginMonitor.isDedicated&&(console.log("[ExecutionService] Login monitor tab closed, recreating"),e.loginMonitor.tabId=null,setTimeout(function(){e.loginMonitor.started&&(e.loginMonitor.tabId||e.loginMonitor.creationPromise||e.ensureLoginMonitorTab().catch(function(e){console.warn("[ExecutionService] Failed to recreate login monitor tab:",e)}))},200))},chrome.tabs.onRemoved.addListener(this.loginMonitor.onRemovedHandler),this.observerMonitor.onRemovedHandler=function(t){e.loginMonitor.started&&t===e.observerMonitor.tabId&&e.observerMonitor.isDedicated&&(console.log("[ExecutionService] Observer monitor tab closed, recreating"),e.observerMonitor.tabId=null,setTimeout(function(){e.loginMonitor.started&&(e.observerMonitor.tabId||e.observerMonitor.creationPromise||e.ensureObserverMonitorTab().catch(function(e){console.warn("[ExecutionService] Failed to recreate observer monitor tab:",e)}))},200))},chrome.tabs.onRemoved.addListener(this.observerMonitor.onRemovedHandler),this.loginMonitor.intervalId=setInterval(se(te().m(function t(){return te().w(function(t){for(;;)switch(t.n){case 0:if(e.loginMonitor.tabId||e.loginMonitor.creationPromise){t.n=2;break}return t.n=1,e.ensureLoginMonitorTab().catch(function(e){console.warn("[ExecutionService] Failed to ensure login monitor tab in interval:",e)});case 1:case 3:return t.a(2);case 2:if(!e.loginMonitor.tabId){t.n=3;break}return t.n=3,e.checkLoginAndTogglePause()}},t)})),2e3),this.loginMonitor.checkImmediately=function(){!e.loginMonitor.checking&&e.loginMonitor.tabId&&e.checkLoginAndTogglePause().catch(function(e){console.warn("[ExecutionService] Immediate login check failed:",e)})},setTimeout(function(){e.loginMonitor.tabId&&e.checkLoginAndTogglePause().catch(function(e){console.warn("[ExecutionService] Initial login check failed:",e)})},500))}},{key:"stopLoginMonitor",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.loginMonitor.started||this.loginMonitor.tabId){if(console.log("[ExecutionService] Stopping login monitor"),this.loginMonitor.intervalId&&(clearInterval(this.loginMonitor.intervalId),this.loginMonitor.intervalId=null),this.loginMonitor.onRemovedHandler){try{chrome.tabs.onRemoved.removeListener(this.loginMonitor.onRemovedHandler)}catch(e){}this.loginMonitor.onRemovedHandler=null}if(this.observerMonitor.onRemovedHandler){try{chrome.tabs.onRemoved.removeListener(this.observerMonitor.onRemovedHandler)}catch(e){}this.observerMonitor.onRemovedHandler=null}if(e&&this.loginMonitor.tabId&&this.loginMonitor.isDedicated){var t=this.loginMonitor.tabId;try{chrome.tabs.remove(t)}catch(e){}this.loginMonitor.tabId=null,this.loginMonitor.isDedicated=!1;try{var n,r;null===(n=chrome.storage)||void 0===n||null===(n=n.local)||void 0===n||null===(r=n.remove)||void 0===r||r.call(n,"loginMonitorTabId")}catch(e){}}if(e&&this.observerMonitor.tabId&&this.observerMonitor.isDedicated){var o=this.observerMonitor.tabId;try{chrome.tabs.remove(o)}catch(e){}this.observerMonitor.tabId=null,this.observerMonitor.isDedicated=!1}this.loginMonitor.started=!1}}},{key:"checkLoginBeforeExecution",value:(n=se(te().m(function e(t,n,r){var o,i,a,s,c;return te().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,console.log("[ExecutionService] Pre-execution login check for node: ".concat(n)),e.n=1,this.sendMessageToTab(t,{type:"PERFORM_SITE_ACTIONS",actions:[{action:"IsLoginRequired",arguments:{}}]});case 1:if(o=e.v,i=!1,o&&Array.isArray(o.results)&&null!=(a=o.results[0])&&a.success&&(i=!(null==a||null===(s=a.data)||void 0===s||!s.loginRequired)),console.log("[ExecutionService] Pre-execution login check result: loginRequired=".concat(i)),!i){e.n=6;break}this.loginMonitor.required=!0,this.loginMonitor.lastChecked=new Date,r&&(this.pauseGraph(r),this.loginMonitor.pausedGraphsSet.add(r)),console.log("[ExecutionService] Waiting for login before executing node ".concat(n));case 2:if(!1===this.loginMonitor.required){e.n=5;break}return e.n=3,this.checkLoginAndTogglePause().catch(function(){});case 3:return e.n=4,this.delay(500);case 4:e.n=2;break;case 5:r&&this.pausedGraphs.has(r)&&this.resumeGraph(r);case 6:e.n=12;break;case 7:if(e.p=7,c=e.v,console.warn("[ExecutionService] Pre-execution login check failed for node ".concat(n,":"),c.message),!0!==this.loginMonitor.required){e.n=12;break}r&&(this.pauseGraph(r),this.loginMonitor.pausedGraphsSet.add(r));case 8:if(!1===this.loginMonitor.required){e.n=11;break}return e.n=9,this.checkLoginAndTogglePause().catch(function(){});case 9:return e.n=10,this.delay(500);case 10:e.n=8;break;case 11:r&&this.pausedGraphs.has(r)&&this.resumeGraph(r);case 12:return e.a(2)}},e,this,[[0,7]])})),function(e,t,r){return n.apply(this,arguments)})}],t&&ce(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,r,o,i,a,s,c,u,l,h,d,v,y,m,b,E,S,w,O,k,P,A}();function fe(e){return fe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},fe(e)}function he(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return s}}(e,t)||pe(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pe(e,t){if(e){if("string"==typeof e)return de(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?de(e,t):void 0}}function de(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function ve(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function ye(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ve(Object(n),!0).forEach(function(t){ge(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ve(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function ge(e,t,n){return(t=_e(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function me(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var c=r&&r.prototype instanceof s?r:s,u=Object.create(c.prototype);return be(u,"_invoke",function(n,r,o){var i,s,c,u=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,s=0,c=e,h.n=n,a}};function p(n,r){for(s=n,c=r,t=0;!f&&u&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(c=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(s=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,s=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,d){if(u>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),s=l,c=d;(t=s<2?e:c)||!f;){i||(s?s<3?(s>1&&(h.n=-1),p(s,c)):h.n=c:h.v=c);try{if(u=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(f=h.n<0)?c:n.call(r,h))!==a)break}catch(t){i=e,s=1,c=t}finally{u=1}}return{value:t,done:f}}}(n,o,i),!0),u}var a={};function s(){}function c(){}function u(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(be(t={},r,function(){return this}),t),f=u.prototype=s.prototype=Object.create(l);function h(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,u):(e.__proto__=u,be(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return c.prototype=u,be(f,"constructor",u),be(u,"constructor",c),c.displayName="GeneratorFunction",be(u,o,"GeneratorFunction"),be(f),be(f,o,"Generator"),be(f,r,function(){return this}),be(f,"toString",function(){return"[object Generator]"}),(me=function(){return{w:i,m:h}})()}function be(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}be=function(e,t,n,r){function i(t,n){be(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},be(e,t,n,r)}function Ee(e,t,n,r,o,i,a){try{var s=e[i](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,o)}function Se(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){Ee(i,r,o,a,s,"next",e)}function s(e){Ee(i,r,o,a,s,"throw",e)}a(void 0)})}}function we(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,_e(r.key),r)}}function _e(e){var t=function(e){if("object"!=fe(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=fe(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==fe(t)?t:t+""}var Te=function(){return t=function t(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),this.ruleEngine=new e.Engine,this.facts=new Map,this.initialized=!1,this.graphs=new Map,this.nodeStatuses=new Map,this.parentStatusListeners=new Map,this.activeTabs=0,this.executionService=new le},n=[{key:"initialize",value:(d=Se(me().m(function e(){return me().w(function(e){for(;;)switch(e.n){case 0:if(this.initialized){e.n=2;break}return e.n=1,this.executionService.loadTimeoutSettings();case 1:this.initialized=!0,console.log("[PrimitiveEngineController] Initialized with enhanced execution service");case 2:return e.a(2)}},e,this)})),function(){return d.apply(this,arguments)})},{key:"ensureInitialized",value:(h=Se(me().m(function e(){return me().w(function(e){for(;;)switch(e.n){case 0:if(this.initialized){e.n=1;break}return e.n=1,this.initialize();case 1:return e.a(2)}},e,this)})),function(){return h.apply(this,arguments)})},{key:"addRule",value:function(e){if(!e.conditions||!e.event)throw new Error("Rule must have conditions and event properties");try{this.ruleEngine.addRule(e)}catch(e){throw console.error("Failed to add rule:",e),e}}},{key:"addFact",value:function(e,t){this.facts.set(e,t),"function"==typeof t?this.ruleEngine.addFact(e,t):this.ruleEngine.addFact(e,function(){return t})}},{key:"removeRule",value:function(e){return this.ruleEngine.removeRule(e)}},{key:"removeFact",value:function(e){return this.facts.delete(e),this.ruleEngine.removeFact(e)}},{key:"run",value:(l=Se(me().m(function e(){var t,n,r,o=this,i=arguments;return me().w(function(e){for(;;)switch(e.p=e.n){case 0:return t=i.length>0&&void 0!==i[0]?i[0]:{},e.p=1,Object.keys(t).forEach(function(e){o.addFact(e,t[e])}),e.n=2,this.ruleEngine.run();case 2:return n=e.v,e.a(2,{events:n.events||[],results:n.results||[],failureEvents:n.failureEvents||[],failureResults:n.failureResults||[]});case 3:throw e.p=3,r=e.v,console.error("Rule engine execution failed:",r),r;case 4:return e.a(2)}},e,this,[[1,3]])})),function(){return l.apply(this,arguments)})},{key:"clearRules",value:function(){this.ruleEngine=new e.Engine,this.facts.clear()}},{key:"getFacts",value:function(){return new Map(this.facts)}},{key:"hasFact",value:function(e){return this.facts.has(e)}},{key:"getRuleCount",value:function(){return this.ruleEngine.rules?this.ruleEngine.rules.length:0}},{key:"addGraph",value:function(e,t){try{return this.graphs.set(e,{actions:t||[],nodeStatuses:new Map,created:new Date}),{isValid:!0,message:"Graph '".concat(e,"' added with ").concat(t.length," actions")}}catch(e){return{isValid:!1,message:e.message}}}},{key:"executeActionArray",value:(u=Se(me().m(function e(t,n){var r,o,i,a=arguments;return me().w(function(e){for(;;)switch(e.p=e.n){case 0:return r=a.length>2&&void 0!==a[2]?a[2]:{},o=a.length>3&&void 0!==a[3]?a[3]:function(){},e.n=1,this.ensureInitialized();case 1:return this.executionService.startLoginMonitor(),e.p=2,console.log("[PrimitiveEngineController] Executing action array for graph ".concat(t," using node-by-node execution")),e.n=3,this.executionService.executeGraphNodeByNode(t,n,ye(ye({},r),{},{reuseTab:!1!==r.reuseTab,stopOnError:!1!==r.stopOnError,failDependentsOnError:!1!==r.failDependentsOnError}),o);case 3:return e.a(2,e.v);case 4:return e.p=4,i=e.v,console.error("[PrimitiveEngineController] Failed to execute action array for graph ".concat(t,":"),i),e.a(2,{success:!1,error:i.message,message:"Failed to execute actions for graph ".concat(t,": ").concat(i.message),graphId:t})}},e,this,[[2,4]])})),function(e,t){return u.apply(this,arguments)})},{key:"triggerGraph",value:(c=Se(me().m(function e(t,n){var r,o,i,a;return me().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.n=1,this.ensureInitialized();case 1:if(r=this.graphs.get(t)){e.n=2;break}throw new Error("Graph '".concat(t,"' not found"));case 2:if(e.p=2,o=r.actions.find(function(e){return e.id===n})){e.n=3;break}throw new Error("Start action '".concat(n,"' not found in graph '").concat(t,"'"));case 3:return this.setNodeStatus(t,n,"running"),e.n=4,this.executeActionArray(t,[o]);case 4:return i=e.v,this.setNodeStatus(t,n,"completed"),e.a(2,i);case 5:throw e.p=5,a=e.v,this.setNodeStatus(t,n,"failed"),a;case 6:return e.a(2)}},e,this,[[2,5]])})),function(e,t){return c.apply(this,arguments)})},{key:"triggerGraphWithParentTracking",value:(s=Se(me().m(function e(t,n){var r,o,i,a=arguments;return me().w(function(e){for(;;)switch(e.n){case 0:return r=a.length>2&&void 0!==a[2]?a[2]:{},e.n=1,this.ensureInitialized();case 1:return o=r.parentActionId,(void 0===(i=r.trackingEnabled)||i)&&o&&this.addParentStatusListener(t,o,function(e){console.log("Parent action ".concat(o," status changed to: ").concat(e))}),e.a(2,this.triggerGraph(t,n))}},e,this)})),function(e,t){return s.apply(this,arguments)})},{key:"getParentStatusBeforeSubtree",value:(a=Se(me().m(function e(t,n){return me().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,this.ensureInitialized();case 1:return e.a(2,this.getNodeStatus(t,n))}},e,this)})),function(e,t){return a.apply(this,arguments)})},{key:"setNodeStatus",value:function(e,t,n){var r="".concat(e,":").concat(t);this.nodeStatuses.set(r,{status:n,timestamp:new Date,graphId:e,actionId:t}),this.notifyParentStatusListeners(e,t,n)}},{key:"getNodeStatus",value:function(e,t){var n="".concat(e,":").concat(t),r=this.nodeStatuses.get(n);return r?r.status:null}},{key:"getNodeStatuses",value:function(e){var t,n=[],r=function(e){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=pe(e))){t&&(e=t);var n=0,r=function(){};return{s:r,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,a=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return i=e.done,e},e:function(e){a=!0,o=e},f:function(){try{i||null==t.return||t.return()}finally{if(a)throw o}}}}(this.nodeStatuses.entries());try{for(r.s();!(t=r.n()).done;){var o=he(t.value,2),i=(o[0],o[1]);i.graphId===e&&n.push(i)}}catch(e){r.e(e)}finally{r.f()}return n}},{key:"addParentStatusListener",value:function(e,t,n){var r="".concat(e,":").concat(t);this.parentStatusListeners.has(r)||this.parentStatusListeners.set(r,new Set),this.parentStatusListeners.get(r).add(n)}},{key:"removeParentStatusListener",value:function(e,t,n){var r="".concat(e,":").concat(t),o=this.parentStatusListeners.get(r);o&&(o.delete(n),0===o.size&&this.parentStatusListeners.delete(r))}},{key:"notifyParentStatusListeners",value:function(e,t,n){var r="".concat(e,":").concat(t),o=this.parentStatusListeners.get(r);o&&o.forEach(function(e){try{e(n)}catch(e){console.error("Error in parent status listener:",e)}})}},{key:"getStatus",value:function(){var e=this.executionService?this.executionService.getAllExecutionStatus():{};return{initialized:this.initialized,activeTabs:this.activeTabs,graphs:this.graphs.size,rules:this.getRuleCount(),facts:this.facts.size,nodeStatuses:this.nodeStatuses.size,executionService:{activeGraphs:e.activeGraphs||0,activeTabs:e.activeTabs||0,activeNodes:e.activeNodes||0,login:e.login||{required:null,lastChecked:null,monitorTabId:null,pausedByLogin:[]}}}}},{key:"getGraphExecutionStatus",value:function(e){return this.executionService?this.executionService.getGraphStatus(e):{found:!1,error:"Execution service not initialized"}}},{key:"getAllExecutionsStatus",value:function(){return this.executionService?this.executionService.getAllExecutionStatus():{error:"Execution service not initialized"}}},{key:"cancelGraphExecution",value:(i=Se(me().m(function e(t){var n,r;return me().w(function(e){for(;;)switch(e.p=e.n){case 0:if(this.executionService){e.n=1;break}return e.a(2,{success:!1,message:"Execution service not initialized"});case 1:return e.p=1,e.n=2,this.executionService.cleanupGraphTabs(t);case 2:return this.executionService.activeGraphs.has(t)&&((n=this.executionService.activeGraphs.get(t)).status="cancelled",n.endTime=new Date),console.log("[PrimitiveEngineController] Cancelled graph execution: ".concat(t)),e.a(2,{success:!0,message:"Graph execution cancelled: ".concat(t)});case 3:return e.p=3,r=e.v,console.error("[PrimitiveEngineController] Error cancelling graph execution: ".concat(t),r),e.a(2,{success:!1,message:"Failed to cancel graph execution: ".concat(r.message)})}},e,this,[[1,3]])})),function(e){return i.apply(this,arguments)})},{key:"pauseGraphExecution",value:function(e){if(!this.executionService)return{success:!1,message:"Execution service not initialized"};try{console.log("[PrimitiveEngineController] Pausing graph execution: ".concat(e));var t=this.executionService.pauseGraph(e);return t.success&&console.log("[PrimitiveEngineController] Successfully paused graph: ".concat(e)),t}catch(t){return console.error("[PrimitiveEngineController] Error pausing graph execution: ".concat(e),t),{success:!1,message:"Failed to pause graph execution: ".concat(t.message)}}}},{key:"resumeGraphExecution",value:function(e){if(!this.executionService)return{success:!1,message:"Execution service not initialized"};try{console.log("[PrimitiveEngineController] Resuming graph execution: ".concat(e));var t=this.executionService.resumeGraph(e);return t.success&&console.log("[PrimitiveEngineController] Successfully resumed graph: ".concat(e)),t}catch(t){return console.error("[PrimitiveEngineController] Error resuming graph execution: ".concat(e),t),{success:!1,message:"Failed to resume graph execution: ".concat(t.message)}}}},{key:"getConfiguration",value:(o=Se(me().m(function e(){var t,n,r;return me().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,f();case 1:return t=e.v,e.n=2,p();case 2:return n=e.v,r=O,e.a(2,{supportedSites:t,actionArguments:n,constants:r});case 3:return e.p=3,e.v,e.a(2,{supportedSites:{},actionArguments:{},constants:{}})}},e,null,[[0,3]])})),function(){return o.apply(this,arguments)})},{key:"cleanup",value:(r=Se(me().m(function e(){return me().w(function(e){for(;;)switch(e.n){case 0:this.clearRules(),this.graphs.clear(),this.nodeStatuses.clear(),this.parentStatusListeners.clear(),this.initialized=!1,this.activeTabs=0;case 1:return e.a(2)}},e,this)})),function(){return r.apply(this,arguments)})}],n&&we(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r,o,i,a,s,c,u,l,h,d}();!function(){var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{};t.ExecutionService=le,t.EngineController=Te,t.PrimitiveEngineController=Te,t.Engine=e.Engine,t.AUTOMATION_MODES=_,t.MESSAGE_TYPES=T,t.ACTION_TYPES=O,t.ARGUMENT_TYPES=k,t.ARGUMENT_CONFIG=P,t.ALL_ARGUMENT_NAMES=A,t.UI_MESSAGE_TYPES=x,t.CONTENT_SCRIPT_IDENTIFIER=I,t.INDEX_KEYWORDS=M,t.INDEX_EXCHANGE_MAPPING=C,t.DELAYS=R,t.ERROR_MESSAGES=N,t.SUCCESS_MESSAGES=j,t.getSupportedSites=f,t.getActionArguments=p,t.getMessageTypes=v,t.getEnvironment=g,t.getFullConfig=b,t.configManager=l,t.SUPPORTED_SITES=S,t.ACTION_ARGUMENTS=w;try{console.log("✅ [BUNDLE] Exposed EngineController, ExecutionService, constants, and config globally")}catch(e){}}()}(),JsonRuleEngineBundle={}}();