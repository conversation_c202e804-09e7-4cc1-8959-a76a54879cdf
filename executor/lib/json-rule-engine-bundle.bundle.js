/*! For license information please see json-rule-engine-bundle.bundle.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.PrimitiveEngineController=t():e.PrimitiveEngineController=t()}("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:this,function(){return function(){var e={11:function(e,t,n){var r;!function(o){var i=Object.hasOwnProperty,a=Array.isArray?Array.isArray:function(e){return"[object Array]"===Object.prototype.toString.call(e)},s="object"==typeof process&&"function"==typeof process.nextTick,c="function"==typeof Symbol,u="object"==typeof Reflect,l="function"==typeof setImmediate?setImmediate:setTimeout,f=c?u&&"function"==typeof Reflect.ownKeys?Reflect.ownKeys:function(e){var t=Object.getOwnPropertyNames(e);return t.push.apply(t,Object.getOwnPropertySymbols(e)),t}:Object.keys;function h(){this._events={},this._conf&&p.call(this,this._conf)}function p(e){e&&(this._conf=e,e.delimiter&&(this.delimiter=e.delimiter),e.maxListeners!==o&&(this._maxListeners=e.maxListeners),e.wildcard&&(this.wildcard=e.wildcard),e.newListener&&(this._newListener=e.newListener),e.removeListener&&(this._removeListener=e.removeListener),e.verboseMemoryLeak&&(this.verboseMemoryLeak=e.verboseMemoryLeak),e.ignoreErrors&&(this.ignoreErrors=e.ignoreErrors),this.wildcard&&(this.listenerTree={}))}function d(e,t){var n="(node) warning: possible EventEmitter memory leak detected. "+e+" listeners added. Use emitter.setMaxListeners() to increase limit.";if(this.verboseMemoryLeak&&(n+=" Event name: "+t+"."),"undefined"!=typeof process&&process.emitWarning){var r=new Error(n);r.name="MaxListenersExceededWarning",r.emitter=this,r.count=e,process.emitWarning(r)}else console.error(n),console.trace&&console.trace()}var v=function(e,t,n){var r=arguments.length;switch(r){case 0:return[];case 1:return[e];case 2:return[e,t];case 3:return[e,t,n];default:for(var o=new Array(r);r--;)o[r]=arguments[r];return o}};function y(e,t){for(var n={},r=e.length,i=t?t.length:0,a=0;a<r;a++)n[e[a]]=a<i?t[a]:o;return n}function g(e,t,n){var r,o;if(this._emitter=e,this._target=t,this._listeners={},this._listenersCount=0,(n.on||n.off)&&(r=n.on,o=n.off),t.addEventListener?(r=t.addEventListener,o=t.removeEventListener):t.addListener?(r=t.addListener,o=t.removeListener):t.on&&(r=t.on,o=t.off),!r&&!o)throw Error("target does not implement any known event API");if("function"!=typeof r)throw TypeError("on method must be a function");if("function"!=typeof o)throw TypeError("off method must be a function");this._on=r,this._off=o;var i=e._observers;i?i.push(this):e._observers=[this]}function b(e,t,n,r){var a=Object.assign({},t);if(!e)return a;if("object"!=typeof e)throw TypeError("options must be an object");var s,c,u,l=Object.keys(e),f=l.length;function h(e){throw Error('Invalid "'+s+'" option value'+(e?". Reason: "+e:""))}for(var p=0;p<f;p++){if(s=l[p],!r&&!i.call(t,s))throw Error('Unknown "'+s+'" option');(c=e[s])!==o&&(u=n[s],a[s]=u?u(c,h):c)}return a}function m(e,t){return"function"==typeof e&&e.hasOwnProperty("prototype")||t("value must be a constructor"),e}function w(e){var t="value must be type of "+e.join("|"),n=e.length,r=e[0],o=e[1];return 1===n?function(e,n){if(typeof e===r)return e;n(t)}:2===n?function(e,n){var i=typeof e;if(i===r||i===o)return e;n(t)}:function(r,o){for(var i=typeof r,a=n;a-- >0;)if(i===e[a])return r;o(t)}}Object.assign(g.prototype,{subscribe:function(e,t,n){var r=this,o=this._target,i=this._emitter,a=this._listeners,s=function(){var r=v.apply(null,arguments),a={data:r,name:t,original:e};n?!1!==n.call(o,a)&&i.emit.apply(i,[a.name].concat(r)):i.emit.apply(i,[t].concat(r))};if(a[e])throw Error("Event '"+e+"' is already listening");this._listenersCount++,i._newListener&&i._removeListener&&!r._onNewListener?(this._onNewListener=function(n){n===t&&null===a[e]&&(a[e]=s,r._on.call(o,e,s))},i.on("newListener",this._onNewListener),this._onRemoveListener=function(n){n===t&&!i.hasListeners(n)&&a[e]&&(a[e]=null,r._off.call(o,e,s))},a[e]=null,i.on("removeListener",this._onRemoveListener)):(a[e]=s,r._on.call(o,e,s))},unsubscribe:function(e){var t,n,r,o=this,i=this._listeners,a=this._emitter,s=this._off,c=this._target;if(e&&"string"!=typeof e)throw TypeError("event must be a string");function u(){o._onNewListener&&(a.off("newListener",o._onNewListener),a.off("removeListener",o._onRemoveListener),o._onNewListener=null,o._onRemoveListener=null);var e=O.call(a,o);a._observers.splice(e,1)}if(e){if(!(t=i[e]))return;s.call(c,e,t),delete i[e],--this._listenersCount||u()}else{for(r=(n=f(i)).length;r-- >0;)e=n[r],s.call(c,e,i[e]);this._listeners={},this._listenersCount=0,u()}}});var E=w(["function"]),_=w(["object","function"]);function S(e,t,n){var r,o,i,a=0,s=new e(function(c,u,l){function f(){o&&(o=null),a&&(clearTimeout(a),a=0)}n=b(n,{timeout:0,overload:!1},{timeout:function(e,t){return("number"!=typeof(e*=1)||e<0||!Number.isFinite(e))&&t("timeout must be a positive number"),e}}),r=!n.overload&&"function"==typeof e.prototype.cancel&&"function"==typeof l;var h=function(e){f(),c(e)},p=function(e){f(),u(e)};r?t(h,p,l):(o=[function(e){p(e||Error("canceled"))}],t(h,p,function(e){if(i)throw Error("Unable to subscribe on cancel event asynchronously");if("function"!=typeof e)throw TypeError("onCancel callback must be a function");o.push(e)}),i=!0),n.timeout>0&&(a=setTimeout(function(){var e=Error("timeout");e.code="ETIMEDOUT",a=0,s.cancel(e),u(e)},n.timeout))});return r||(s.cancel=function(e){if(o){for(var t=o.length,n=1;n<t;n++)o[n](e);o[0](e),o=null}}),s}function O(e){var t=this._observers;if(!t)return-1;for(var n=t.length,r=0;r<n;r++)if(t[r]._target===e)return r;return-1}function T(e,t,n,r,o){if(!n)return null;if(0===r){var i=typeof t;if("string"===i){var a,s,c=0,u=0,l=this.delimiter,h=l.length;if(-1!==(s=t.indexOf(l))){a=new Array(5);do{a[c++]=t.slice(u,s),u=s+h}while(-1!==(s=t.indexOf(l,u)));a[c++]=t.slice(u),t=a,o=c}else t=[t],o=1}else"object"===i?o=t.length:(t=[t],o=1)}var p,d,v,y,g,b,m,w=null,E=t[r],_=t[r+1];if(r===o)n._listeners&&("function"==typeof n._listeners?(e&&e.push(n._listeners),w=[n]):(e&&e.push.apply(e,n._listeners),w=[n]));else{if("*"===E){for(s=(b=f(n)).length;s-- >0;)"_listeners"!==(p=b[s])&&(m=T(e,t,n[p],r+1,o))&&(w?w.push.apply(w,m):w=m);return w}if("**"===E){for((g=r+1===o||r+2===o&&"*"===_)&&n._listeners&&(w=T(e,t,n,o,o)),s=(b=f(n)).length;s-- >0;)"_listeners"!==(p=b[s])&&("*"===p||"**"===p?(n[p]._listeners&&!g&&(m=T(e,t,n[p],o,o))&&(w?w.push.apply(w,m):w=m),m=T(e,t,n[p],r,o)):m=T(e,t,n[p],p===_?r+2:r,o),m&&(w?w.push.apply(w,m):w=m));return w}n[E]&&(w=T(e,t,n[E],r+1,o))}if((d=n["*"])&&T(e,t,d,r+1,o),v=n["**"])if(r<o)for(v._listeners&&T(e,t,v,o,o),s=(b=f(v)).length;s-- >0;)"_listeners"!==(p=b[s])&&(p===_?T(e,t,v[p],r+2,o):p===E?T(e,t,v[p],r+1,o):((y={})[p]=v[p],T(e,t,{"**":y},r+1,o)));else v._listeners?T(e,t,v,o,o):v["*"]&&v["*"]._listeners&&T(e,t,v["*"],o,o);return w}function k(e,t,n){var r,o,i=0,a=0,s=this.delimiter,c=s.length;if("string"==typeof e)if(-1!==(r=e.indexOf(s))){o=new Array(5);do{o[i++]=e.slice(a,r),a=r+c}while(-1!==(r=e.indexOf(s,a)));o[i++]=e.slice(a)}else o=[e],i=1;else o=e,i=e.length;if(i>1)for(r=0;r+1<i;r++)if("**"===o[r]&&"**"===o[r+1])return;var u,l=this.listenerTree;for(r=0;r<i;r++)if(l=l[u=o[r]]||(l[u]={}),r===i-1)return l._listeners?("function"==typeof l._listeners&&(l._listeners=[l._listeners]),n?l._listeners.unshift(t):l._listeners.push(t),!l._listeners.warned&&this._maxListeners>0&&l._listeners.length>this._maxListeners&&(l._listeners.warned=!0,d.call(this,l._listeners.length,u))):l._listeners=t,!0;return!0}function P(e,t,n,r){for(var o,i,a,s,c=f(e),u=c.length,l=e._listeners;u-- >0;)o=e[i=c[u]],a="_listeners"===i?n:n?n.concat(i):[i],s=r||"symbol"==typeof i,l&&t.push(s?a:a.join(this.delimiter)),"object"==typeof o&&P.call(this,o,t,a,s);return t}function x(e){for(var t,n,r,o=f(e),i=o.length;i-- >0;)(t=e[n=o[i]])&&(r=!0,"_listeners"===n||x(t)||delete e[n]);return r}function A(e,t,n){this.emitter=e,this.event=t,this.listener=n}function j(e,t,n){if(!0===n)i=!0;else if(!1===n)r=!0;else{if(!n||"object"!=typeof n)throw TypeError("options should be an object or true");var r=n.async,i=n.promisify,a=n.nextTick,c=n.objectify}if(r||a||i){var u=t,f=t._origin||t;if(a&&!s)throw Error("process.nextTick is not supported");i===o&&(i="AsyncFunction"===t.constructor.name),t=function(){var e=arguments,t=this,n=this.event;return i?a?Promise.resolve():new Promise(function(e){l(e)}).then(function(){return t.event=n,u.apply(t,e)}):(a?process.nextTick:l)(function(){t.event=n,u.apply(t,e)})},t._async=!0,t._origin=f}return[t,c?new A(this,e,t):this]}function C(e){this._events={},this._newListener=!1,this._removeListener=!1,this.verboseMemoryLeak=!1,p.call(this,e)}A.prototype.off=function(){return this.emitter.off(this.event,this.listener),this},C.EventEmitter2=C,C.prototype.listenTo=function(e,t,n){if("object"!=typeof e)throw TypeError("target musts be an object");var r=this;function i(t){if("object"!=typeof t)throw TypeError("events must be an object");var o,i=n.reducers,a=O.call(r,e);o=-1===a?new g(r,e,n):r._observers[a];for(var s,c=f(t),u=c.length,l="function"==typeof i,h=0;h<u;h++)s=c[h],o.subscribe(s,t[s]||s,l?i:i&&i[s])}return n=b(n,{on:o,off:o,reducers:o},{on:E,off:E,reducers:_}),a(t)?i(y(t)):i("string"==typeof t?y(t.split(/\s+/)):t),this},C.prototype.stopListeningTo=function(e,t){var n=this._observers;if(!n)return!1;var r,o=n.length,i=!1;if(e&&"object"!=typeof e)throw TypeError("target should be an object");for(;o-- >0;)r=n[o],e&&r._target!==e||(r.unsubscribe(t),i=!0);return i},C.prototype.delimiter=".",C.prototype.setMaxListeners=function(e){e!==o&&(this._maxListeners=e,this._conf||(this._conf={}),this._conf.maxListeners=e)},C.prototype.getMaxListeners=function(){return this._maxListeners},C.prototype.event="",C.prototype.once=function(e,t,n){return this._once(e,t,!1,n)},C.prototype.prependOnceListener=function(e,t,n){return this._once(e,t,!0,n)},C.prototype._once=function(e,t,n,r){return this._many(e,1,t,n,r)},C.prototype.many=function(e,t,n,r){return this._many(e,t,n,!1,r)},C.prototype.prependMany=function(e,t,n,r){return this._many(e,t,n,!0,r)},C.prototype._many=function(e,t,n,r,o){var i=this;if("function"!=typeof n)throw new Error("many only accepts instances of Function");function a(){return 0===--t&&i.off(e,a),n.apply(this,arguments)}return a._origin=n,this._on(e,a,r,o)},C.prototype.emit=function(){if(!this._events&&!this._all)return!1;this._events||h.call(this);var e,t,n,r,o,i,a=arguments[0],s=this.wildcard;if("newListener"===a&&!this._newListener&&!this._events.newListener)return!1;if(s&&(e=a,"newListener"!==a&&"removeListener"!==a&&"object"==typeof a)){if(n=a.length,c)for(r=0;r<n;r++)if("symbol"==typeof a[r]){i=!0;break}i||(a=a.join(this.delimiter))}var u,l=arguments.length;if(this._all&&this._all.length)for(r=0,n=(u=this._all.slice()).length;r<n;r++)switch(this.event=a,l){case 1:u[r].call(this,a);break;case 2:u[r].call(this,a,arguments[1]);break;case 3:u[r].call(this,a,arguments[1],arguments[2]);break;default:u[r].apply(this,arguments)}if(s)u=[],T.call(this,u,e,this.listenerTree,0,n);else{if("function"==typeof(u=this._events[a])){switch(this.event=a,l){case 1:u.call(this);break;case 2:u.call(this,arguments[1]);break;case 3:u.call(this,arguments[1],arguments[2]);break;default:for(t=new Array(l-1),o=1;o<l;o++)t[o-1]=arguments[o];u.apply(this,t)}return!0}u&&(u=u.slice())}if(u&&u.length){if(l>3)for(t=new Array(l-1),o=1;o<l;o++)t[o-1]=arguments[o];for(r=0,n=u.length;r<n;r++)switch(this.event=a,l){case 1:u[r].call(this);break;case 2:u[r].call(this,arguments[1]);break;case 3:u[r].call(this,arguments[1],arguments[2]);break;default:u[r].apply(this,t)}return!0}if(!this.ignoreErrors&&!this._all&&"error"===a)throw arguments[1]instanceof Error?arguments[1]:new Error("Uncaught, unspecified 'error' event.");return!!this._all},C.prototype.emitAsync=function(){if(!this._events&&!this._all)return!1;this._events||h.call(this);var e,t,n,r,o,i,a=arguments[0],s=this.wildcard;if("newListener"===a&&!this._newListener&&!this._events.newListener)return Promise.resolve([!1]);if(s&&(e=a,"newListener"!==a&&"removeListener"!==a&&"object"==typeof a)){if(r=a.length,c)for(o=0;o<r;o++)if("symbol"==typeof a[o]){t=!0;break}t||(a=a.join(this.delimiter))}var u,l=[],f=arguments.length;if(this._all)for(o=0,r=this._all.length;o<r;o++)switch(this.event=a,f){case 1:l.push(this._all[o].call(this,a));break;case 2:l.push(this._all[o].call(this,a,arguments[1]));break;case 3:l.push(this._all[o].call(this,a,arguments[1],arguments[2]));break;default:l.push(this._all[o].apply(this,arguments))}if(s?(u=[],T.call(this,u,e,this.listenerTree,0)):u=this._events[a],"function"==typeof u)switch(this.event=a,f){case 1:l.push(u.call(this));break;case 2:l.push(u.call(this,arguments[1]));break;case 3:l.push(u.call(this,arguments[1],arguments[2]));break;default:for(n=new Array(f-1),i=1;i<f;i++)n[i-1]=arguments[i];l.push(u.apply(this,n))}else if(u&&u.length){if(u=u.slice(),f>3)for(n=new Array(f-1),i=1;i<f;i++)n[i-1]=arguments[i];for(o=0,r=u.length;o<r;o++)switch(this.event=a,f){case 1:l.push(u[o].call(this));break;case 2:l.push(u[o].call(this,arguments[1]));break;case 3:l.push(u[o].call(this,arguments[1],arguments[2]));break;default:l.push(u[o].apply(this,n))}}else if(!this.ignoreErrors&&!this._all&&"error"===a)return arguments[1]instanceof Error?Promise.reject(arguments[1]):Promise.reject("Uncaught, unspecified 'error' event.");return Promise.all(l)},C.prototype.on=function(e,t,n){return this._on(e,t,!1,n)},C.prototype.prependListener=function(e,t,n){return this._on(e,t,!0,n)},C.prototype.onAny=function(e){return this._onAny(e,!1)},C.prototype.prependAny=function(e){return this._onAny(e,!0)},C.prototype.addListener=C.prototype.on,C.prototype._onAny=function(e,t){if("function"!=typeof e)throw new Error("onAny only accepts instances of Function");return this._all||(this._all=[]),t?this._all.unshift(e):this._all.push(e),this},C.prototype._on=function(e,t,n,r){if("function"==typeof e)return this._onAny(e,t),this;if("function"!=typeof t)throw new Error("on only accepts instances of Function");this._events||h.call(this);var i,a=this;return r!==o&&(t=(i=j.call(this,e,t,r))[0],a=i[1]),this._newListener&&this.emit("newListener",e,t),this.wildcard?(k.call(this,e,t,n),a):(this._events[e]?("function"==typeof this._events[e]&&(this._events[e]=[this._events[e]]),n?this._events[e].unshift(t):this._events[e].push(t),!this._events[e].warned&&this._maxListeners>0&&this._events[e].length>this._maxListeners&&(this._events[e].warned=!0,d.call(this,this._events[e].length,e))):this._events[e]=t,a)},C.prototype.off=function(e,t){if("function"!=typeof t)throw new Error("removeListener only takes instances of Function");var n,r=[];if(this.wildcard){var o="string"==typeof e?e.split(this.delimiter):e.slice();if(!(r=T.call(this,null,o,this.listenerTree,0)))return this}else{if(!this._events[e])return this;n=this._events[e],r.push({_listeners:n})}for(var i=0;i<r.length;i++){var s=r[i];if(n=s._listeners,a(n)){for(var c=-1,u=0,l=n.length;u<l;u++)if(n[u]===t||n[u].listener&&n[u].listener===t||n[u]._origin&&n[u]._origin===t){c=u;break}if(c<0)continue;return this.wildcard?s._listeners.splice(c,1):this._events[e].splice(c,1),0===n.length&&(this.wildcard?delete s._listeners:delete this._events[e]),this._removeListener&&this.emit("removeListener",e,t),this}(n===t||n.listener&&n.listener===t||n._origin&&n._origin===t)&&(this.wildcard?delete s._listeners:delete this._events[e],this._removeListener&&this.emit("removeListener",e,t))}return this.listenerTree&&x(this.listenerTree),this},C.prototype.offAny=function(e){var t,n=0,r=0;if(e&&this._all&&this._all.length>0){for(n=0,r=(t=this._all).length;n<r;n++)if(e===t[n])return t.splice(n,1),this._removeListener&&this.emit("removeListenerAny",e),this}else{if(t=this._all,this._removeListener)for(n=0,r=t.length;n<r;n++)this.emit("removeListenerAny",t[n]);this._all=[]}return this},C.prototype.removeListener=C.prototype.off,C.prototype.removeAllListeners=function(e){if(e===o)return!this._events||h.call(this),this;if(this.wildcard){var t,n=T.call(this,null,e,this.listenerTree,0);if(!n)return this;for(t=0;t<n.length;t++)n[t]._listeners=null;this.listenerTree&&x(this.listenerTree)}else this._events&&(this._events[e]=null);return this},C.prototype.listeners=function(e){var t,n,r,i,a,s=this._events;if(e===o){if(this.wildcard)throw Error("event name required for wildcard emitter");if(!s)return[];for(i=(t=f(s)).length,r=[];i-- >0;)"function"==typeof(n=s[t[i]])?r.push(n):r.push.apply(r,n);return r}if(this.wildcard){if(!(a=this.listenerTree))return[];var c=[],u="string"==typeof e?e.split(this.delimiter):e.slice();return T.call(this,c,u,a,0),c}return s&&(n=s[e])?"function"==typeof n?[n]:n:[]},C.prototype.eventNames=function(e){var t=this._events;return this.wildcard?P.call(this,this.listenerTree,[],null,e):t?f(t):[]},C.prototype.listenerCount=function(e){return this.listeners(e).length},C.prototype.hasListeners=function(e){if(this.wildcard){var t=[],n="string"==typeof e?e.split(this.delimiter):e.slice();return T.call(this,t,n,this.listenerTree,0),t.length>0}var r=this._events,i=this._all;return!!(i&&i.length||r&&(e===o?f(r).length:r[e]))},C.prototype.listenersAny=function(){return this._all?this._all:[]},C.prototype.waitFor=function(e,t){var n=this,r=typeof t;return"number"===r?t={timeout:t}:"function"===r&&(t={filter:t}),S((t=b(t,{timeout:0,filter:o,handleError:!1,Promise:Promise,overload:!1},{filter:E,Promise:m})).Promise,function(r,o,i){function a(){var i=t.filter;if(!i||i.apply(n,arguments))if(n.off(e,a),t.handleError){var s=arguments[0];s?o(s):r(v.apply(null,arguments).slice(1))}else r(v.apply(null,arguments))}i(function(){n.off(e,a)}),n._on(e,a,!1)},{timeout:t.timeout,overload:t.overload})};var M=C.prototype;Object.defineProperties(C,{defaultMaxListeners:{get:function(){return M._maxListeners},set:function(e){if("number"!=typeof e||e<0||Number.isNaN(e))throw TypeError("n must be a non-negative number");M._maxListeners=e},enumerable:!0},once:{value:function(e,t,n){return S((n=b(n,{Promise:Promise,timeout:0,overload:!1},{Promise:m})).Promise,function(n,r,o){var i;if("function"==typeof e.addEventListener)return i=function(){n(v.apply(null,arguments))},o(function(){e.removeEventListener(t,i)}),void e.addEventListener(t,i,{once:!0});var a,s=function(){a&&e.removeListener("error",a),n(v.apply(null,arguments))};"error"!==t&&(a=function(n){e.removeListener(t,s),r(n)},e.once("error",a)),o(function(){a&&e.removeListener("error",a),e.removeListener(t,s)}),e.once(t,s)},{timeout:n.timeout,overload:n.overload})},writable:!0,configurable:!0}}),Object.defineProperties(M,{_maxListeners:{value:10,writable:!0,configurable:!0},_observers:{value:null,writable:!0,configurable:!0}}),(r=function(){return C}.call(t,n,t,e))===o||(e.exports=r)}()},26:function(e,t,n){"use strict";t.Engine=void 0;var r=s(n(589)),o=s(n(153)),i=s(n(273)),a=s(n(759));function s(e){return e&&e.__esModule?e:{default:e}}o.default,i.default,a.default,t.Engine=r.default},89:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,o=(r=n(759))&&r.__esModule?r:{default:r},i=[];function a(e){return"NaN"!==Number.parseFloat(e).toString()}i.push(new o.default("equal",function(e,t){return e===t})),i.push(new o.default("notEqual",function(e,t){return e!==t})),i.push(new o.default("in",function(e,t){return t.indexOf(e)>-1})),i.push(new o.default("notIn",function(e,t){return-1===t.indexOf(e)})),i.push(new o.default("contains",function(e,t){return e.indexOf(t)>-1},Array.isArray)),i.push(new o.default("doesNotContain",function(e,t){return-1===e.indexOf(t)},Array.isArray)),i.push(new o.default("lessThan",function(e,t){return e<t},a)),i.push(new o.default("lessThanInclusive",function(e,t){return e<=t},a)),i.push(new o.default("greaterThan",function(e,t){return e>t},a)),i.push(new o.default("greaterThanInclusive",function(e,t){return e>=t},a)),t.default=i},138:function(e){e.exports=function(e){return!!e&&"object"==typeof e}},148:function(e){e.exports=function(){"use strict";var e=function(e,t){return e.reduce(function(e,n){var r="[object "+n+"]";return t?e[r]=n:e[n]=r,e},{})},t=function(e){return e.reduce(function(e,t){return e[t]=!0,e},{})},n=["Array","Arguments","Object","RegExp","Symbol","Map","Set","Date","Error","Event","Generator","Promise","WeakMap","WeakSet","DocumentFragment","Float32Array","Float64Array","Int8Array","Int16Array","Int32Array","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","ArrayBuffer","DataView","DocumentFragment","Window","String","Number","Boolean","Function","Undefined","GeneratorFunction","BigInt","Null"],r=e(n,!1),o=e(n,!0),i=t([r.Generator,r.Promise,r.WeakMap,r.WeakSet]),a=t([r.Map,r.Set]),s=t([r.Date,r.RegExp]),c=t(["bigint","boolean","function","number","string","undefined"]),u=t([r.Arguments,r.Array]),l=t([r.RegExp,r.Symbol]),f=t([r.Float32Array,r.Float64Array,r.Int8Array,r.Int16Array,r.Int32Array,r.Uint8Array,r.Uint8ClampedArray,r.Uint16Array,r.Uint32Array]),h="undefined"!=typeof Buffer&&"function"==typeof Buffer.from,p="function"==typeof Uint16Array;var d=h?function(e){return Buffer.from(e).toString("utf8")}:p?function(e){return String.fromCharCode.apply(null,new Uint16Array(e))}:function(e){return""},v=/\[object ([HTML|SVG](.*)Element)\]/,y=Object.prototype.toString,g=Object.keys;function b(e,t){return e>t}function m(e,t){return e[0]>t[0]}function w(e,t){for(var n,r,o=0;o<e.length;++o){for(r=e[o],n=o-1;~n&&t(e[n],r);--n)e[n+1]=e[n];e[n+1]=r}return e}function E(e){for(var t,n=w(g(e),b),r={},o=0;o<n.length;++o)r[t=n[o]]=e[t];return r}function _(e,t){for(var n=0;n<e.length;++n)if(e[n]===t)return n+1;return 0}function S(e,t,n,s){if(!s){var h=typeof e;if(c[h])return h+"|"+e;if(null===e)return e+"|"+e}var p,g=s||y.call(e);return u[g]?e:g===r.Object?E(e):l[g]?o[g]+"|"+e.toString():a[g]?e instanceof Map?function(e,t,n){var r=[];e.forEach(function(e,o){r.push([O(o,t,n),O(e,t,n)])}),w(r,m);for(var o,i=0;i<r.length;++i)o=r[i],r[i]="["+o[0]+","+o[1]+"]";return"Map|["+r.join(",")+"]"}(e,t,n):function(e,t,n){var r=[];return e.forEach(function(e){r.push(O(e,t,n))}),w(r,b),"Set|["+r.join(",")+"]"}(e,t,n):g===r.Date?o[g]+"|"+e.getTime():g===r.Error?o[g]+"|"+e.stack:g===r.Event?{bubbles:(p=e).bubbles,cancelBubble:p.cancelBubble,cancelable:p.cancelable,composed:p.composed,currentTarget:p.currentTarget,defaultPrevented:p.defaultPrevented,eventPhase:p.eventPhase,isTrusted:p.isTrusted,returnValue:p.returnValue,target:p.target,type:p.type}:i[g]?o[g]+"|NOT_ENUMERABLE":v.test(g)?g.slice(8,-1)+"|"+e.outerHTML:g===r.DocumentFragment?o[g]+"|"+function(e){for(var t=e.children,n=[],r=0;r<t.length;++r)n.push(t[r].outerHTML);return n.join(",")}(e):f[g]?o[g]+"|"+e.join(","):g===r.ArrayBuffer?o[g]+"|"+d(e):g===r.DataView?o[g]+"|"+d(e.buffer):e}function O(e,t,n){if(!e||"object"!=typeof e)return S(e,t,n);var o=y.call(e);return s[o]?S(e,t,n,o):JSON.stringify(e,function(e,t){return void 0===e&&(e=[]),void 0===t&&(t=[]),function(n,o){if("object"==typeof o)if(e.length){var i=_(e,this);0===i?e.push(this):(e.splice(i),t.splice(i)),t.push(n);var a=_(e,o);if(0!==a)return"[~"+(t.slice(0,a).join(".")||".")+"]";e.push(o)}else e[0]=o,t[0]=n;return n&&this[n]instanceof Date?S(this[n],e,t,r.Date):S(o,e,t)}}(t,n))}function T(e){return function(e){for(var t,n=e.length,r=5381,o=52711;n--;)r=33*r^(t=e.charCodeAt(n)),o=33*o^t;return 4096*(r>>>0)+(o>>>0)}(O(e))}function k(e,t){return T(e)===T(t)}return k.all=function(e){for(var t=0;t<(arguments.length<=1?0:arguments.length-1);++t)if(!k(e,t+1<1||arguments.length<=t+1?void 0:arguments[t+1]))return!1;return!0},k.any=function(e){for(var t=0;t<(arguments.length<=1?0:arguments.length-1);++t)if(k(e,t+1<1||arguments.length<=t+1?void 0:arguments[t+1]))return!0;return!1},k.not=function(e,t){return T(e)!==T(t)},T.is=k,T}()},151:function(e,t,n){"use strict";function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function o(e){return o=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},o(e)}function i(e,t){return i=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},i(e,t)}function a(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}function s(e,t,n){return s=a()?Reflect.construct:function(e,t,n){var r=[null];r.push.apply(r,t);var o=new(Function.bind.apply(e,r));return n&&i(o,n.prototype),o},s.apply(null,arguments)}function c(e){var t="function"==typeof Map?new Map:void 0;return c=function(e){if(null===e||(n=e,-1===Function.toString.call(n).indexOf("[native code]")))return e;var n;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return s(e,arguments,o(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),i(r,e)},c(e)}function u(e){return function(e){if(Array.isArray(e))return f(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||l(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(e,t){if(e){if("string"==typeof e)return f(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(e,t):void 0}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}n.r(t),n.d(t,{JSONPath:function(){return y}});var h=Object.prototype.hasOwnProperty;function p(e,t){return(e=e.slice()).push(t),e}function d(e,t){return(t=t.slice()).unshift(e),t}var v=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&i(e,t)}(s,e);var t,n,r=(t=s,n=a(),function(){var e,r=o(t);if(n){var i=o(this).constructor;e=Reflect.construct(r,arguments,i)}else e=r.apply(this,arguments);return function(e,t){return!t||"object"!=typeof t&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}(this,e)});function s(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),(t=r.call(this,'JSONPath should not be called with "new" (it prevents return of (unwrapped) scalar values)')).avoidNew=!0,t.value=e,t.name="NewError",t}return s}(c(Error));function y(e,t,n,o,i){if(!(this instanceof y))try{return new y(e,t,n,o,i)}catch(e){if(!e.avoidNew)throw e;return e.value}"string"==typeof e&&(i=o,o=n,n=t,t=e,e=null);var a=e&&"object"===r(e);if(e=e||{},this.json=e.json||n,this.path=e.path||t,this.resultType=e.resultType||"value",this.flatten=e.flatten||!1,this.wrap=!h.call(e,"wrap")||e.wrap,this.sandbox=e.sandbox||{},this.preventEval=e.preventEval||!1,this.parent=e.parent||null,this.parentProperty=e.parentProperty||null,this.callback=e.callback||o||null,this.otherTypeCallback=e.otherTypeCallback||i||function(){throw new TypeError("You must supply an otherTypeCallback callback option with the @other() operator.")},!1!==e.autostart){var s={path:a?e.path:t};a?"json"in e&&(s.json=e.json):s.json=n;var c=this.evaluate(s);if(!c||"object"!==r(c))throw new v(c);return c}}y.prototype.evaluate=function(e,t,n,o){var i=this,a=this.parent,s=this.parentProperty,c=this.flatten,u=this.wrap;if(this.currResultType=this.resultType,this.currPreventEval=this.preventEval,this.currSandbox=this.sandbox,n=n||this.callback,this.currOtherTypeCallback=o||this.otherTypeCallback,t=t||this.json,(e=e||this.path)&&"object"===r(e)&&!Array.isArray(e)){if(!e.path&&""!==e.path)throw new TypeError('You must supply a "path" property when providing an object argument to JSONPath.evaluate().');if(!h.call(e,"json"))throw new TypeError('You must supply a "json" property when providing an object argument to JSONPath.evaluate().');t=e.json,c=h.call(e,"flatten")?e.flatten:c,this.currResultType=h.call(e,"resultType")?e.resultType:this.currResultType,this.currSandbox=h.call(e,"sandbox")?e.sandbox:this.currSandbox,u=h.call(e,"wrap")?e.wrap:u,this.currPreventEval=h.call(e,"preventEval")?e.preventEval:this.currPreventEval,n=h.call(e,"callback")?e.callback:n,this.currOtherTypeCallback=h.call(e,"otherTypeCallback")?e.otherTypeCallback:this.currOtherTypeCallback,a=h.call(e,"parent")?e.parent:a,s=h.call(e,"parentProperty")?e.parentProperty:s,e=e.path}if(a=a||null,s=s||null,Array.isArray(e)&&(e=y.toPathString(e)),(e||""===e)&&t){var l=y.toPathArray(e);"$"===l[0]&&l.length>1&&l.shift(),this._hasParentSelector=null;var f=this._trace(l,t,["$"],a,s,n).filter(function(e){return e&&!e.isParentSelector});return f.length?u||1!==f.length||f[0].hasArrExpr?f.reduce(function(e,t){var n=i._getPreferredOutput(t);return c&&Array.isArray(n)?e=e.concat(n):e.push(n),e},[]):this._getPreferredOutput(f[0]):u?[]:void 0}},y.prototype._getPreferredOutput=function(e){var t=this.currResultType;switch(t){case"all":var n=Array.isArray(e.path)?e.path:y.toPathArray(e.path);return e.pointer=y.toPointer(n),e.path="string"==typeof e.path?e.path:y.toPathString(e.path),e;case"value":case"parent":case"parentProperty":return e[t];case"path":return y.toPathString(e[t]);case"pointer":return y.toPointer(e.path);default:throw new TypeError("Unknown result type")}},y.prototype._handleCallback=function(e,t,n){if(t){var r=this._getPreferredOutput(e);e.path="string"==typeof e.path?e.path:y.toPathString(e.path),t(r,n,e)}},y.prototype._trace=function(e,t,n,o,i,a,s,c){var u,f=this;if(!e.length)return u={path:n,value:t,parent:o,parentProperty:i,hasArrExpr:s},this._handleCallback(u,a,"value"),u;var v=e[0],y=e.slice(1),g=[];function b(e){Array.isArray(e)?e.forEach(function(e){g.push(e)}):g.push(e)}if(("string"!=typeof v||c)&&t&&h.call(t,v))b(this._trace(y,t[v],p(n,v),t,v,a,s));else if("*"===v)this._walk(v,y,t,n,o,i,a,function(e,t,n,r,o,i,a,s){b(f._trace(d(e,n),r,o,i,a,s,!0,!0))});else if(".."===v)b(this._trace(y,t,n,o,i,a,s)),this._walk(v,y,t,n,o,i,a,function(e,t,n,o,i,a,s,c){"object"===r(o[e])&&b(f._trace(d(t,n),o[e],p(i,e),o,e,c,!0))});else{if("^"===v)return this._hasParentSelector=!0,{path:n.slice(0,-1),expr:y,isParentSelector:!0};if("~"===v)return u={path:p(n,v),value:i,parent:o,parentProperty:null},this._handleCallback(u,a,"property"),u;if("$"===v)b(this._trace(y,t,n,null,null,a,s));else if(/^(\x2D?[0-9]*):(\x2D?[0-9]*):?([0-9]*)$/.test(v))b(this._slice(v,y,t,n,o,i,a));else if(0===v.indexOf("?(")){if(this.currPreventEval)throw new Error("Eval [?(expr)] prevented in JSONPath expression.");this._walk(v,y,t,n,o,i,a,function(e,t,n,r,o,i,a,s){f._eval(t.replace(/^\?\(((?:[\0-\t\x0B\f\x0E-\u2027\u202A-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*?)\)$/,"$1"),r[e],e,o,i,a)&&b(f._trace(d(e,n),r,o,i,a,s,!0))})}else if("("===v[0]){if(this.currPreventEval)throw new Error("Eval [(expr)] prevented in JSONPath expression.");b(this._trace(d(this._eval(v,t,n[n.length-1],n.slice(0,-1),o,i),y),t,n,o,i,a,s))}else if("@"===v[0]){var m=!1,w=v.slice(1,-2);switch(w){case"scalar":t&&["object","function"].includes(r(t))||(m=!0);break;case"boolean":case"string":case"undefined":case"function":r(t)===w&&(m=!0);break;case"integer":!Number.isFinite(t)||t%1||(m=!0);break;case"number":Number.isFinite(t)&&(m=!0);break;case"nonFinite":"number"!=typeof t||Number.isFinite(t)||(m=!0);break;case"object":t&&r(t)===w&&(m=!0);break;case"array":Array.isArray(t)&&(m=!0);break;case"other":m=this.currOtherTypeCallback(t,n,o,i);break;case"null":null===t&&(m=!0);break;default:throw new TypeError("Unknown value type "+w)}if(m)return u={path:n,value:t,parent:o,parentProperty:i},this._handleCallback(u,a,"value"),u}else if("`"===v[0]&&t&&h.call(t,v.slice(1))){var E=v.slice(1);b(this._trace(y,t[E],p(n,E),t,E,a,s,!0))}else if(v.includes(",")){var _,S=function(e){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=l(e))){t&&(e=t);var n=0,r=function(){};return{s:r,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,a=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return i=e.done,e},e:function(e){a=!0,o=e},f:function(){try{i||null==t.return||t.return()}finally{if(a)throw o}}}}(v.split(","));try{for(S.s();!(_=S.n()).done;){var O=_.value;b(this._trace(d(O,y),t,n,o,i,a,!0))}}catch(e){S.e(e)}finally{S.f()}}else!c&&t&&h.call(t,v)&&b(this._trace(y,t[v],p(n,v),t,v,a,s,!0))}if(this._hasParentSelector)for(var T=0;T<g.length;T++){var k=g[T];if(k&&k.isParentSelector){var P=this._trace(k.expr,t,k.path,o,i,a,s);if(Array.isArray(P)){g[T]=P[0];for(var x=P.length,A=1;A<x;A++)T++,g.splice(T,0,P[A])}else g[T]=P}}return g},y.prototype._walk=function(e,t,n,o,i,a,s,c){if(Array.isArray(n))for(var u=n.length,l=0;l<u;l++)c(l,e,t,n,o,i,a,s);else n&&"object"===r(n)&&Object.keys(n).forEach(function(r){c(r,e,t,n,o,i,a,s)})},y.prototype._slice=function(e,t,n,r,o,i,a){if(Array.isArray(n)){var s=n.length,c=e.split(":"),u=c[2]&&Number.parseInt(c[2])||1,l=c[0]&&Number.parseInt(c[0])||0,f=c[1]&&Number.parseInt(c[1])||s;l=l<0?Math.max(0,l+s):Math.min(s,l),f=f<0?Math.max(0,f+s):Math.min(s,f);for(var h=[],p=l;p<f;p+=u)this._trace(d(p,t),n,r,o,i,a,!0).forEach(function(e){h.push(e)});return h}},y.prototype._eval=function(e,t,n,r,o,i){e.includes("@parentProperty")&&(this.currSandbox._$_parentProperty=i,e=e.replace(/@parentProperty/g,"_$_parentProperty")),e.includes("@parent")&&(this.currSandbox._$_parent=o,e=e.replace(/@parent/g,"_$_parent")),e.includes("@property")&&(this.currSandbox._$_property=n,e=e.replace(/@property/g,"_$_property")),e.includes("@path")&&(this.currSandbox._$_path=y.toPathString(r.concat([n])),e=e.replace(/@path/g,"_$_path")),e.includes("@root")&&(this.currSandbox._$_root=this.json,e=e.replace(/@root/g,"_$_root")),/@([\t-\r \)\.\[\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF])/.test(e)&&(this.currSandbox._$_v=t,e=e.replace(/@([\t-\r \)\.\[\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF])/g,"_$_v$1"));try{return this.vm.runInNewContext(e,this.currSandbox)}catch(t){throw console.log(t),new Error("jsonPath: "+t.message+": "+e)}},y.cache={},y.toPathString=function(e){for(var t=e,n=t.length,r="$",o=1;o<n;o++)/^(~|\^|@(?:[\0-\t\x0B\f\x0E-\u2027\u202A-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*?\(\))$/.test(t[o])||(r+=/^[\*0-9]+$/.test(t[o])?"["+t[o]+"]":"['"+t[o]+"']");return r},y.toPointer=function(e){for(var t=e,n=t.length,r="",o=1;o<n;o++)/^(~|\^|@(?:[\0-\t\x0B\f\x0E-\u2027\u202A-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*?\(\))$/.test(t[o])||(r+="/"+t[o].toString().replace(/~/g,"~0").replace(/\//g,"~1"));return r},y.toPathArray=function(e){var t=y.cache;if(t[e])return t[e].concat();var n=[],r=e.replace(/@(?:null|boolean|number|string|integer|undefined|nonFinite|scalar|array|object|function|other)\(\)/g,";$&;").replace(/['\[](\??\((?:[\0-\t\x0B\f\x0E-\u2027\u202A-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*?\))['\]]/g,function(e,t){return"[#"+(n.push(t)-1)+"]"}).replace(/\[["']((?:(?!['\]])[\s\S])*)["']\]/g,function(e,t){return"['"+t.replace(/\./g,"%@%").replace(/~/g,"%%@@%%")+"']"}).replace(/~/g,";~;").replace(/["']?\.["']?(?!(?:(?!\[)[\s\S])*\])|\[["']?/g,";").replace(/%@%/g,".").replace(/%%@@%%/g,"~").replace(/(?:;)?(\^+)(?:;)?/g,function(e,t){return";"+t.split("").join(";")+";"}).replace(/;;;|;;/g,";..;").replace(/;$|'?\]|'$/g,"").split(";").map(function(e){var t=e.match(/#([0-9]+)/);return t&&t[1]?n[t[1]]:e});return t[e]=r,t[e].concat()},y.prototype.vm={runInNewContext:function(e,t){var n=Object.keys(t),r=[];!function(e,t,n){for(var r=e.length,o=0;o<r;o++)n(e[o])&&t.push(e.splice(o--,1)[0])}(n,r,function(e){return"function"==typeof t[e]});var o=n.map(function(e,n){return t[e]}),i=r.reduce(function(e,n){var r=t[n].toString();return/function/.test(r)||(r="function "+r),"var "+n+"="+r+";"+e},"");/(["'])use strict\1/.test(e=i+e)||n.includes("arguments")||(e="var arguments = undefined;"+e);var a=(e=e.replace(/;[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*$/,"")).lastIndexOf(";"),c=a>-1?e.slice(0,a+1)+" return "+e.slice(a+1):" return "+e;return s(Function,u(n).concat([c])).apply(void 0,u(o))}}},153:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=(r=n(148))&&r.__esModule?r:{default:r},a=function(){function e(t,n,r){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.id=t;var o={cache:!0};if(void 0===r&&(r=o),"function"!=typeof n?(this.value=n,this.type=this.constructor.CONSTANT):(this.calculationMethod=n,this.type=this.constructor.DYNAMIC),!this.id)throw new Error("factId required");return this.priority=parseInt(r.priority||1,10),this.options=Object.assign({},o,r),this.cacheKeyMethod=this.defaultCacheKeys,this}return o(e,[{key:"isConstant",value:function(){return this.type===this.constructor.CONSTANT}},{key:"isDynamic",value:function(){return this.type===this.constructor.DYNAMIC}},{key:"calculate",value:function(e,t){return Object.prototype.hasOwnProperty.call(this,"value")?this.value:this.calculationMethod(e,t)}},{key:"defaultCacheKeys",value:function(e,t){return{params:t,id:e}}},{key:"getCacheKey",value:function(t){if(!0===this.options.cache){var n=this.cacheKeyMethod(this.id,t);return e.hashFromObject(n)}}}],[{key:"hashFromObject",value:function(e){return(0,i.default)(e)}}]),e}();a.CONSTANT="CONSTANT",a.DYNAMIC="DYNAMIC",t.default=a},239:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=(r=n(779))&&r.__esModule?r:{default:r},a=function(){function e(t,n,r,o){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.conditions=(0,i.default)(t),this.event=(0,i.default)(n),this.priority=(0,i.default)(r),this.name=(0,i.default)(o),this.result=null}return o(e,[{key:"setResult",value:function(e){this.result=e}},{key:"toJSON",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t={conditions:this.conditions.toJSON(!1),event:this.event,priority:this.priority,name:this.name,result:this.result};return e?JSON.stringify(t):t}}]),e}();t.default=a},273:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),o=s(n(694)),i=s(n(239)),a=s(n(572));function s(e){return e&&e.__esModule?e:{default:e}}var c=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));"string"==typeof e&&(e=JSON.parse(e)),e&&e.conditions&&n.setConditions(e.conditions),e&&e.onSuccess&&n.on("success",e.onSuccess),e&&e.onFailure&&n.on("failure",e.onFailure),e&&(e.name||0===e.name)&&n.setName(e.name);var r=e&&e.priority||1;n.setPriority(r);var o=e&&e.event||{type:"unknown"};return n.setEvent(o),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),r(t,[{key:"setPriority",value:function(e){if((e=parseInt(e,10))<=0)throw new Error("Priority must be greater than zero");return this.priority=e,this}},{key:"setName",value:function(e){if(!e&&0!==e)throw new Error('Rule "name" must be defined');return this.name=e,this}},{key:"setConditions",value:function(e){if(!Object.prototype.hasOwnProperty.call(e,"all")&&!Object.prototype.hasOwnProperty.call(e,"any"))throw new Error('"conditions" root must contain a single instance of "all" or "any"');return this.conditions=new o.default(e),this}},{key:"setEvent",value:function(e){if(!e)throw new Error("Rule: setEvent() requires event object");if(!Object.prototype.hasOwnProperty.call(e,"type"))throw new Error('Rule: setEvent() requires event object with "type" property');return this.ruleEvent={type:e.type},e.params&&(this.ruleEvent.params=e.params),this}},{key:"getEvent",value:function(){return this.ruleEvent}},{key:"getPriority",value:function(){return this.priority}},{key:"getConditions",value:function(){return this.conditions}},{key:"getEngine",value:function(){return this.engine}},{key:"setEngine",value:function(e){return this.engine=e,this}},{key:"toJSON",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t={conditions:this.conditions.toJSON(!1),priority:this.priority,event:this.ruleEvent,name:this.name};return e?JSON.stringify(t):t}},{key:"prioritizeConditions",value:function(e){var t=this,n=e.reduce(function(e,n){var r=n.priority;if(!r){var o=t.engine.getFact(n.fact);r=o&&o.priority||1}return e[r]||(e[r]=[]),e[r].push(n),e},{});return Object.keys(n).sort(function(e,t){return Number(e)>Number(t)?-1:1}).map(function(e){return n[e]})}},{key:"evaluate",value:function(e){var t=this,n=new i.default(this.conditions,this.ruleEvent,this.priority,this.name),r=function(n,r){if(0===n.length)return Promise.resolve(!0);var i=Array.prototype.some;"all"===r&&(i=Array.prototype.every);for(var c=t.prioritizeConditions(n),u=Promise.resolve(),l=function(n){var l=c[n],f=!1;u=u.then(function(n){return"any"===r&&!0===n||f?((0,a.default)("prioritizeAndRun::detected truthy result; skipping remaining conditions"),f=!0,!0):"all"===r&&!1===n||f?((0,a.default)("prioritizeAndRun::detected falsey result; skipping remaining conditions"),f=!0,!1):function(n,r){return Array.isArray(n)||(n=[n]),Promise.all(n.map(function(n){return function(n){if(n.isBooleanOperator()){var r=n[n.operator];return("all"===n.operator?s(r):o(r)).then(function(e){var t=!0===e;return n.result=t,t})}return n.evaluate(e,t.engine.operators).then(function(e){var t=e.result;return n.factResult=e.leftHandSideValue,n.result=t,t})}(n)})).then(function(e){return(0,a.default)("rule::evaluateConditions results",e),r.call(e,function(e){return!0===e})})}(l,i)})},f=0;f<c.length;f++)l(f);return u},o=function(e){return r(e,"any")},s=function(e){return r(e,"all")},c=function(r){n.setResult(r);var o=r?"success":"failure";return t.emitAsync(o,n.event,e,n).then(function(){return n})};return n.conditions.any?o(n.conditions.any).then(function(e){return c(e)}):s(n.conditions.all).then(function(e){return c(e)})}}]),t}(s(n(11)).default);t.default=c},528:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=l(n(153)),a=n(716),s=l(n(572)),c=n(151),u=l(n(138));function l(e){return e&&e.__esModule?e:{default:e}}function f(e,t){return(0,c.JSONPath)({path:t,json:e,wrap:!1})}var h=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};for(var a in function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.factMap=new Map(t),this.factResultsCache=new Map,this.allowUndefinedFacts=Boolean(o.allowUndefinedFacts),this.pathResolver=o.pathResolver||f,this.events={success:[],failure:[]},this.ruleResults=[],n){var c;c=n[a]instanceof i.default?n[a]:new i.default(a,n[a]),this._addConstantFact(c),(0,s.default)("almanac::constructor initialized runtime fact:"+c.id+" with "+c.value+"<"+r(c.value)+">")}}return o(e,[{key:"addEvent",value:function(e,t){if(!t)throw new Error('outcome required: "success" | "failure"]');this.events[t].push(e)}},{key:"getEvents",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e?this.events[e]:this.events.success.concat(this.events.failure)}},{key:"addResult",value:function(e){this.ruleResults.push(e)}},{key:"getResults",value:function(){return this.ruleResults}},{key:"_getFact",value:function(e){return this.factMap.get(e)}},{key:"_addConstantFact",value:function(e){this.factMap.set(e.id,e),this._setFactValue(e,{},e.value)}},{key:"_setFactValue",value:function(e,t,n){var r=e.getCacheKey(t),o=Promise.resolve(n);return r&&this.factResultsCache.set(r,o),o}},{key:"addRuntimeFact",value:function(e,t){(0,s.default)("almanac::addRuntimeFact id:"+e);var n=new i.default(e,t);return this._addConstantFact(n)}},{key:"factValue",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",i=void 0,c=this._getFact(e);if(void 0===c)return this.allowUndefinedFacts?Promise.resolve(void 0):Promise.reject(new a.UndefinedFactError("Undefined fact: "+e));if(c.isConstant())i=Promise.resolve(c.calculate(n,this));else{var l=c.getCacheKey(n),f=l&&this.factResultsCache.get(l);f?(i=Promise.resolve(f),(0,s.default)("almanac::factValue cache hit for fact:"+e)):((0,s.default)("almanac::factValue cache miss for fact:"+e+"; calculating"),i=this._setFactValue(c,n,c.calculate(n,this)))}return o?((0,s.default)("condition::evaluate extracting object property "+o),i.then(function(e){if((0,u.default)(e)){var n=t.pathResolver(e,o);return(0,s.default)("condition::evaluate extracting object property "+o+", received: "+JSON.stringify(n)),n}return(0,s.default)("condition::evaluate could not compute object path("+o+") of non-object: "+e+" <"+(void 0===e?"undefined":r(e))+">; continuing with "+e),e})):i}}]),e}();t.default=h},572:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){try{("undefined"!=typeof process&&process.env&&process.env.DEBUG&&process.env.DEBUG.match(/json-rules-engine/)||"undefined"!=typeof window&&window.localStorage&&window.localStorage.debug&&window.localStorage.debug.match(/json-rules-engine/))&&console.log(e)}catch(e){}}},589:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FINISHED=t.RUNNING=t.READY=void 0;var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),o=f(n(153)),i=f(n(273)),a=f(n(759)),s=f(n(528)),c=f(n(11)),u=f(n(89)),l=f(n(572));function f(e){return e&&e.__esModule?e:{default:e}}var h=t.READY="READY",p=t.RUNNING="RUNNING",d=t.FINISHED="FINISHED",v=function(e){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var r=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return r.rules=[],r.allowUndefinedFacts=n.allowUndefinedFacts||!1,r.pathResolver=n.pathResolver,r.operators=new Map,r.facts=new Map,r.status=h,e.map(function(e){return r.addRule(e)}),u.default.map(function(e){return r.addOperator(e)}),r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),r(t,[{key:"addRule",value:function(e){if(!e)throw new Error("Engine: addRule() requires options");var t=void 0;if(e instanceof i.default)t=e;else{if(!Object.prototype.hasOwnProperty.call(e,"event"))throw new Error('Engine: addRule() argument requires "event" property');if(!Object.prototype.hasOwnProperty.call(e,"conditions"))throw new Error('Engine: addRule() argument requires "conditions" property');t=new i.default(e)}return t.setEngine(this),this.rules.push(t),this.prioritizedRules=null,this}},{key:"updateRule",value:function(e){var t=this.rules.findIndex(function(t){return t.name===e.name});if(!(t>-1))throw new Error("Engine: updateRule() rule not found");this.rules.splice(t,1),this.addRule(e),this.prioritizedRules=null}},{key:"removeRule",value:function(e){var t=!1;if(e instanceof i.default){var n=this.rules.indexOf(e);n>-1&&(t=Boolean(this.rules.splice(n,1).length))}else{var r=this.rules.filter(function(t){return t.name!==e});t=r.length!==this.rules.length,this.rules=r}return t&&(this.prioritizedRules=null),t}},{key:"addOperator",value:function(e,t){var n;n=e instanceof a.default?e:new a.default(e,t),(0,l.default)("engine::addOperator name:"+n.name),this.operators.set(n.name,n)}},{key:"removeOperator",value:function(e){var t;return t=e instanceof a.default?e.name:e,this.operators.delete(t)}},{key:"addFact",value:function(e,t,n){var r=e,i=void 0;return e instanceof o.default?(r=e.id,i=e):i=new o.default(e,t,n),(0,l.default)("engine::addFact id:"+r),this.facts.set(r,i),this}},{key:"removeFact",value:function(e){var t;return t=e instanceof o.default?e.id:e,this.facts.delete(t)}},{key:"prioritizeRules",value:function(){if(!this.prioritizedRules){var e=this.rules.reduce(function(e,t){var n=t.priority;return e[n]||(e[n]=[]),e[n].push(t),e},{});this.prioritizedRules=Object.keys(e).sort(function(e,t){return Number(e)>Number(t)?-1:1}).map(function(t){return e[t]})}return this.prioritizedRules}},{key:"stop",value:function(){return this.status=d,this}},{key:"getFact",value:function(e){return this.facts.get(e)}},{key:"evaluateRules",value:function(e,t){var n=this;return Promise.all(e.map(function(e){return n.status!==p?((0,l.default)("engine::run status:"+n.status+"; skipping remaining rules"),Promise.resolve()):e.evaluate(t).then(function(e){return(0,l.default)("engine::run ruleResult:"+e.result),t.addResult(e),e.result?(t.addEvent(e.event,"success"),n.emitAsync("success",e.event,t,e).then(function(){return n.emitAsync(e.event.type,e.event.params,t,e)})):(t.addEvent(e.event,"failure"),n.emitAsync("failure",e.event,t,e))})}))}},{key:"run",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,l.default)("engine::run started"),this.status=p;var n={allowUndefinedFacts:this.allowUndefinedFacts,pathResolver:this.pathResolver},r=new s.default(this.facts,t,n),o=this.prioritizeRules(),i=Promise.resolve();return new Promise(function(t,n){o.map(function(t){return i=i.then(function(){return e.evaluateRules(t,r)}).catch(n)}),i.then(function(){e.status=d,(0,l.default)("engine::run completed");var n=r.getResults().reduce(function(e,t){return e[t.result?"results":"failureResults"].push(t),e},{results:[],failureResults:[]}),o=n.results,i=n.failureResults;t({almanac:r,results:o,failureResults:i,events:r.getEvents("success"),failureEvents:r.getEvents("failure")})}).catch(n)})}}]),t}(c.default);t.default=v},694:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),o=a(n(572)),i=a(n(138));function a(e){return e&&e.__esModule?e:{default:e}}var s=function(){function e(t){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),!t)throw new Error("Condition: constructor options required");var n=e.booleanOperator(t);if(Object.assign(this,t),n){var r=t[n];if(!Array.isArray(r))throw new Error('"'+n+'" must be an array');this.operator=n,this.priority=parseInt(t.priority,10)||1,this[n]=r.map(function(t){return new e(t)})}else{if(!Object.prototype.hasOwnProperty.call(t,"fact"))throw new Error('Condition: constructor "fact" property required');if(!Object.prototype.hasOwnProperty.call(t,"operator"))throw new Error('Condition: constructor "operator" property required');if(!Object.prototype.hasOwnProperty.call(t,"value"))throw new Error('Condition: constructor "value" property required');Object.prototype.hasOwnProperty.call(t,"priority")&&(t.priority=parseInt(t.priority,10))}}return r(e,[{key:"toJSON",value:function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],n={};this.priority&&(n.priority=this.priority);var r=e.booleanOperator(this);return r?n[r]=this[r].map(function(e){return e.toJSON(t)}):(n.operator=this.operator,n.value=this.value,n.fact=this.fact,void 0!==this.factResult&&(n.factResult=this.factResult),void 0!==this.result&&(n.result=this.result),this.params&&(n.params=this.params),this.path&&(n.path=this.path)),t?JSON.stringify(n):n}},{key:"_getValue",value:function(e){var t=this.value;return(0,i.default)(t)&&Object.prototype.hasOwnProperty.call(t,"fact")?e.factValue(t.fact,t.params,t.path):Promise.resolve(t)}},{key:"evaluate",value:function(e,t){var n=this;if(!e)return Promise.reject(new Error("almanac required"));if(!t)return Promise.reject(new Error("operatorMap required"));if(this.isBooleanOperator())return Promise.reject(new Error("Cannot evaluate() a boolean condition"));var r=t.get(this.operator);return r?this._getValue(e).then(function(t){return e.factValue(n.fact,n.params,n.path).then(function(e){var i=r.evaluate(e,t);return(0,o.default)("condition::evaluate <"+JSON.stringify(e)+" "+n.operator+" "+JSON.stringify(t)+"?> ("+i+")"),{result:i,leftHandSideValue:e,rightHandSideValue:t,operator:n.operator}})}):Promise.reject(new Error("Unknown operator: "+this.operator))}},{key:"booleanOperator",value:function(){return e.booleanOperator(this)}},{key:"isBooleanOperator",value:function(){return void 0!==e.booleanOperator(this)}}],[{key:"booleanOperator",value:function(e){return Object.prototype.hasOwnProperty.call(e,"any")?"any":Object.prototype.hasOwnProperty.call(e,"all")?"all":void 0}}]),e}();t.default=s},716:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.UndefinedFactError=function(){function e(){var t;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);for(var n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];var i=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t=e.__proto__||Object.getPrototypeOf(e)).call.apply(t,[this].concat(r)));return i.code="UNDEFINED_FACT",i}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,Error),e}()},759:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),r=function(){function e(t,n,r){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.name=String(t),!t)throw new Error("Missing operator name");if("function"!=typeof n)throw new Error("Missing operator callback");this.cb=n,this.factValueValidator=r,this.factValueValidator||(this.factValueValidator=function(){return!0})}return n(e,[{key:"evaluate",value:function(e,t){return this.factValueValidator(e)&&this.cb(e,t)}}]),e}();t.default=r},763:function(e,t,n){"use strict";e.exports=n(26)},779:function(e){var t=function(){"use strict";function e(e,t){return null!=t&&e instanceof t}var t,n,r;try{t=Map}catch(e){t=function(){}}try{n=Set}catch(e){n=function(){}}try{r=Promise}catch(e){r=function(){}}function o(i,s,c,u,l){"object"==typeof s&&(c=s.depth,u=s.prototype,l=s.includeNonEnumerable,s=s.circular);var f=[],h=[],p="undefined"!=typeof Buffer;return void 0===s&&(s=!0),void 0===c&&(c=1/0),function i(c,d){if(null===c)return null;if(0===d)return c;var v,y;if("object"!=typeof c)return c;if(e(c,t))v=new t;else if(e(c,n))v=new n;else if(e(c,r))v=new r(function(e,t){c.then(function(t){e(i(t,d-1))},function(e){t(i(e,d-1))})});else if(o.__isArray(c))v=[];else if(o.__isRegExp(c))v=new RegExp(c.source,a(c)),c.lastIndex&&(v.lastIndex=c.lastIndex);else if(o.__isDate(c))v=new Date(c.getTime());else{if(p&&Buffer.isBuffer(c))return v=Buffer.allocUnsafe?Buffer.allocUnsafe(c.length):new Buffer(c.length),c.copy(v),v;e(c,Error)?v=Object.create(c):void 0===u?(y=Object.getPrototypeOf(c),v=Object.create(y)):(v=Object.create(u),y=u)}if(s){var g=f.indexOf(c);if(-1!=g)return h[g];f.push(c),h.push(v)}for(var b in e(c,t)&&c.forEach(function(e,t){var n=i(t,d-1),r=i(e,d-1);v.set(n,r)}),e(c,n)&&c.forEach(function(e){var t=i(e,d-1);v.add(t)}),c){var m;y&&(m=Object.getOwnPropertyDescriptor(y,b)),m&&null==m.set||(v[b]=i(c[b],d-1))}if(Object.getOwnPropertySymbols){var w=Object.getOwnPropertySymbols(c);for(b=0;b<w.length;b++){var E=w[b];(!(S=Object.getOwnPropertyDescriptor(c,E))||S.enumerable||l)&&(v[E]=i(c[E],d-1),S.enumerable||Object.defineProperty(v,E,{enumerable:!1}))}}if(l){var _=Object.getOwnPropertyNames(c);for(b=0;b<_.length;b++){var S,O=_[b];(S=Object.getOwnPropertyDescriptor(c,O))&&S.enumerable||(v[O]=i(c[O],d-1),Object.defineProperty(v,O,{enumerable:!1}))}}return v}(i,c)}function i(e){return Object.prototype.toString.call(e)}function a(e){var t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),t}return o.clonePrototype=function(e){if(null===e)return null;var t=function(){};return t.prototype=e,new t},o.__objToStr=i,o.__isDate=function(e){return"object"==typeof e&&"[object Date]"===i(e)},o.__isArray=function(e){return"object"==typeof e&&"[object Array]"===i(e)},o.__isRegExp=function(e){return"object"==typeof e&&"[object RegExp]"===i(e)},o.__getRegExpFlags=a,o}();e.exports&&(e.exports=t)}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r].call(i.exports,i,i.exports,n),i.exports}n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};return function(){"use strict";n.d(r,{default:function(){return he}});var e=n(763);function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}function o(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",a=n.toStringTag||"@@toStringTag";function s(n,r,o,a){var s=r&&r.prototype instanceof u?r:u,l=Object.create(s.prototype);return i(l,"_invoke",function(n,r,o){var i,a,s,u=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,a=0,s=e,h.n=n,c}};function p(n,r){for(a=n,s=r,t=0;!f&&u&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(s=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(a=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,a=0))}if(o||n>1)return c;throw f=!0,r}return function(o,l,d){if(u>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),a=l,s=d;(t=a<2?e:s)||!f;){i||(a?a<3?(a>1&&(h.n=-1),p(a,s)):h.n=s:h.v=s);try{if(u=2,i){if(a||(o="next"),t=i[o]){if(!(t=t.call(i,s)))throw TypeError("iterator result is not an object");if(!t.done)return t;s=t.value,a<2&&(a=0)}else 1===a&&(t=i.return)&&t.call(i),a<2&&(s=TypeError("The iterator does not provide a '"+o+"' method"),a=1);i=e}else if((t=(f=h.n<0)?s:n.call(r,h))!==c)break}catch(t){i=e,a=1,s=t}finally{u=1}}return{value:t,done:f}}}(n,o,a),!0),l}var c={};function u(){}function l(){}function f(){}t=Object.getPrototypeOf;var h=[][r]?t(t([][r]())):(i(t={},r,function(){return this}),t),p=f.prototype=u.prototype=Object.create(h);function d(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,i(e,a,"GeneratorFunction")),e.prototype=Object.create(p),e}return l.prototype=f,i(p,"constructor",f),i(f,"constructor",l),l.displayName="GeneratorFunction",i(f,a,"GeneratorFunction"),i(p),i(p,a,"Generator"),i(p,r,function(){return this}),i(p,"toString",function(){return"[object Generator]"}),(o=function(){return{w:s,m:d}})()}function i(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}i=function(e,t,n,r){function a(t,n){i(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(a("next",0),a("throw",1),a("return",2))},i(e,t,n,r)}function a(e,t,n,r,o,i,a){try{var s=e[i](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,o)}function s(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function s(e){a(i,r,o,s,c,"next",e)}function c(e){a(i,r,o,s,c,"throw",e)}s(void 0)})}}function c(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,u(r.key),r)}}function u(e){var n=function(e){if("object"!=t(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=t(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==t(n)?n:n+""}var l=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.sharedConfig=null,this.configUrl="lib/shared-config.json"},t=[{key:"loadSharedConfig",value:(l=s(o().m(function e(){var t,n;return o().w(function(e){for(;;)switch(e.p=e.n){case 0:if(!this.sharedConfig){e.n=1;break}return e.a(2,this.sharedConfig);case 1:return e.p=1,console.log("🔍 Loading shared config from:",this.configUrl),e.n=2,fetch(chrome.runtime.getURL(this.configUrl));case 2:if((t=e.v).ok){e.n=3;break}throw new Error("HTTP ".concat(t.status,": ").concat(t.statusText));case 3:return e.n=4,t.json();case 4:return this.sharedConfig=e.v,console.log("✅ Shared config loaded successfully"),e.a(2,this.sharedConfig);case 5:return e.p=5,n=e.v,console.error("❌ Failed to load shared config:",n),console.log("🔄 Using fallback config"),this.sharedConfig=this.getFallbackConfig(),e.a(2,this.sharedConfig)}},e,this,[[1,5]])})),function(){return l.apply(this,arguments)})},{key:"getFallbackConfig",value:function(){return{SUPPORTED_SITES:{kiteByZerodha:{name:"Kite by Zerodha",url:"https://kite.zerodha.com/",contentScript:"content-scripts/zerodha.js"}},ACTION_ARGUMENTS:{BUY:["SYMBOL","QUANTITY","EXCHANGE","PRODUCT_TYPE","PRODUCTTYPE"],SELL:["SYMBOL","QUANTITY","EXCHANGE","PRODUCT_TYPE","PRODUCTTYPE"],MONITORPROFIT:["TARGET_PROFIT_AMOUNT","TARGET_PROFIT_PERCENTAGE","MONITOR_INTERVAL_SECONDS"]},MESSAGE_TYPES:{EXECUTE_ACTIONS:"EXECUTE_ACTIONS",PERFORM_SITE_ACTIONS:"PERFORM_SITE_ACTIONS",ACTION_STATUS_UPDATE:"ACTION_STATUS_UPDATE",EXECUTION_COMPLETE:"EXECUTION_COMPLETE"},ENVIRONMENT:{slow_execute:!1,close_tabs_after_execution:!0}}}},{key:"getSupportedSites",value:(u=s(o().m(function e(){var t;return o().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,this.loadSharedConfig();case 1:return t=e.v,e.a(2,t.SUPPORTED_SITES)}},e,this)})),function(){return u.apply(this,arguments)})},{key:"getActionArguments",value:(a=s(o().m(function e(){var t;return o().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,this.loadSharedConfig();case 1:return t=e.v,e.a(2,t.ACTION_ARGUMENTS)}},e,this)})),function(){return a.apply(this,arguments)})},{key:"getMessageTypes",value:(i=s(o().m(function e(){var t;return o().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,this.loadSharedConfig();case 1:return t=e.v,e.a(2,t.MESSAGE_TYPES)}},e,this)})),function(){return i.apply(this,arguments)})},{key:"getEnvironment",value:(r=s(o().m(function e(){var t;return o().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,this.loadSharedConfig();case 1:return t=e.v,e.a(2,t.ENVIRONMENT)}},e,this)})),function(){return r.apply(this,arguments)})},{key:"getFullConfig",value:(n=s(o().m(function e(){return o().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,this.loadSharedConfig();case 1:return e.a(2,e.v)}},e,this)})),function(){return n.apply(this,arguments)})}],t&&c(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,r,i,a,u,l}(),f=new l;function h(){return p.apply(this,arguments)}function p(){return(p=s(o().m(function e(){return o().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,f.getSupportedSites();case 1:return e.a(2,e.v)}},e)}))).apply(this,arguments)}function d(){return v.apply(this,arguments)}function v(){return(v=s(o().m(function e(){return o().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,f.getActionArguments();case 1:return e.a(2,e.v)}},e)}))).apply(this,arguments)}function y(){return g.apply(this,arguments)}function g(){return(g=s(o().m(function e(){return o().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,f.getEnvironment();case 1:return e.a(2,e.v)}},e)}))).apply(this,arguments)}var b="PERFORM_SITE_ACTIONS",m={BUY:"BUY",SELL:"SELL",PLACE_BUY_LIMIT_ORDER:"PlaceBuyLimitOrder",PLACE_SELL_LIMIT_ORDER:"PlaceSellLimitOrder",PLACE_BUY_STOP_LOSS_MARKET_ORDER:"PlaceBuyStopLossMarketOrder",PLACE_SELL_STOP_LOSS_MARKET_ORDER:"PlaceSellStopLossMarketOrder",PLACE_BUY_STOP_LOSS_LIMIT_ORDER:"PlaceBuyStopLossLimitOrder",PLACE_SELL_STOP_LOSS_LIMIT_ORDER:"PlaceSellStopLossLimitOrder",MONITOR_PROFIT:"MONITORPROFIT",CANCEL_ORDER:"CancelOrder",GET_PORTFOLIO_STATS:"GetPortfolioStats",GET_OPEN_POSITION_PNL:"GetOpenPositionPnL",SELECT_ORDER_BY_CRITERIA:"SelectOrderByCriteria"},w="string",E="number",_="object",S={SYMBOL:{type:w},QUANTITY:{type:E},PRICE:{type:E},TRIGGER_PRICE:{type:E},TARGET_PROFIT_PERCENTAGE:{type:E},MONITOR_INTERVAL_SECONDS:{type:E},TARGET_PROFIT_AMOUNT:{type:E},EXCHANGE:{type:w},PRODUCT_TYPE:{type:w},PRODUCT:{type:w},PRODUCTTYPE:{type:w},TYPE:{type:w},type:{type:w},symbol:{type:w},quantity:{type:E},exchange:{type:w},product:{type:w},condition:{type:_},on_trigger:{type:_}};function O(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var c=r&&r.prototype instanceof s?r:s,u=Object.create(c.prototype);return T(u,"_invoke",function(n,r,o){var i,s,c,u=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,s=0,c=e,h.n=n,a}};function p(n,r){for(s=n,c=r,t=0;!f&&u&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(c=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(s=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,s=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,d){if(u>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),s=l,c=d;(t=s<2?e:c)||!f;){i||(s?s<3?(s>1&&(h.n=-1),p(s,c)):h.n=c:h.v=c);try{if(u=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(f=h.n<0)?c:n.call(r,h))!==a)break}catch(t){i=e,s=1,c=t}finally{u=1}}return{value:t,done:f}}}(n,o,i),!0),u}var a={};function s(){}function c(){}function u(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(T(t={},r,function(){return this}),t),f=u.prototype=s.prototype=Object.create(l);function h(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,u):(e.__proto__=u,T(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return c.prototype=u,T(f,"constructor",u),T(u,"constructor",c),c.displayName="GeneratorFunction",T(u,o,"GeneratorFunction"),T(f),T(f,o,"Generator"),T(f,r,function(){return this}),T(f,"toString",function(){return"[object Generator]"}),(O=function(){return{w:i,m:h}})()}function T(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}T=function(e,t,n,r){function i(t,n){T(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},T(e,t,n,r)}function k(e){return k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},k(e)}function P(e,t,n,r,o,i,a){try{var s=e[i](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,o)}function x(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=A(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,i=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw i}}}}function A(e,t){if(e){if("string"==typeof e)return j(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?j(e,t):void 0}}function j(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function C(e){return"string"==typeof e?e.toUpperCase():e}function M(e,t){return F.apply(this,arguments)}function F(){var e;return e=O().m(function e(t,n){var r,o;return O().w(function(e){for(;;)switch(e.n){case 0:if(Array.isArray(t)){e.n=1;break}return e.a(2,{isValid:!1,message:"Input must be a JSON array of action objects."});case 1:r=0;case 2:if(!(r<t.length)){e.n=4;break}if((o=I(t[r],r,n)).isValid){e.n=3;break}return e.a(2,o);case 3:r++,e.n=2;break;case 4:return e.a(2,{isValid:!0,message:"Validation successful."})}},e)}),F=function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){P(i,r,o,a,s,"next",e)}function s(e){P(i,r,o,a,s,"throw",e)}a(void 0)})},F.apply(this,arguments)}function I(e,t,n){if(!R(e))return{isValid:!1,message:"Action at index ".concat(t," is not a valid object.")};var r=e.action,o=e.arguments;if("string"!=typeof r)return{isValid:!1,message:"Action at index ".concat(t," is missing a valid 'action' string.")};var i=C(r),a=Object.keys(n).find(function(e){return C(e)===i});if(!a)return{isValid:!1,message:"Action at index ".concat(t," has unrecognized action type '").concat(r,"'.")};if(!R(o))return{isValid:!1,message:"Action at index ".concat(t," is missing a valid 'arguments' object.")};var s=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n={};e.productType&&!n.PRODUCT_TYPE&&(n.PRODUCT_TYPE=e.productType),e.triggerPrice&&!n.TRIGGER_PRICE&&(n.TRIGGER_PRICE=e.triggerPrice);var r,o=x(t);try{var i=function(){var t=r.value,o=Object.keys(e).find(function(e){return C(e)===C(t)});o&&(n[t]=e[o])};for(o.s();!(r=o.n()).done;)i()}catch(e){o.e(e)}finally{o.f()}for(var a=function(){var e,r,o=(e=c[s],r=2,function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return s}}(e,r)||A(e,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),i=o[0],a=o[1];t.some(function(e){return C(e)===C(i)})||"productType"===i||"triggerPrice"===i||(n[i]=a)},s=0,c=Object.entries(e);s<c.length;s++)a();return n._originalArgs=e,n}(o,Object.keys(n[a]));return function(e,t,n,r){var o,i=r[e],a=t._originalArgs||t,s=x(i);try{var c,u=function(){var r=o.value,i=null;if((i=Object.keys(t).find(function(e){return C(e)===C(r)}))||(i=Object.keys(a).find(function(e){return C(e)===C(r)})),!i){var s,c=x(L(r));try{var u=function(){var e=s.value;if(i=Object.keys(a).find(function(t){return C(t)===C(e)}))return 1};for(c.s();!(s=c.n()).done&&!u(););}catch(e){c.e(e)}finally{c.f()}}if(!i){var l=L(r);return{v:{isValid:!1,message:"Action '".concat(e,"' at index ").concat(n," is missing required argument '").concat(r,"'. Accepted variations: ").concat(l.join(", "),".")}}}var f=t[i]||a[i],h=function(e,t,n,r){console.log("🔍 validateArgumentType called with:"),console.log('  - argKey: "'.concat(e,'"')),console.log("  - value:",t),console.log('  - action: "'.concat(n,'"')),console.log("  - index: ".concat(r));var o=function(e){console.log('🔍 getExpectedType called with argKey: "'.concat(e,'"'));var t=S[e];if(console.log("🔍 Exact match result:",t),!t){var n=C(e);console.log('🔍 Normalized argKey: "'.concat(n,'"'));var r=Object.keys(S).find(function(e){return C(e)===n});console.log('🔍 Found key: "'.concat(r,'"')),r&&(t=S[r],console.log("🔍 Case-insensitive match result:",t))}console.log("🔍 Final config:",t);var o=t?t.type:w;return console.log('🔍 Returning type: "'.concat(o,'"')),o}(e),i=k(t);return console.log('🔍 Type comparison: expected="'.concat(o,'", actual="').concat(i,'"')),i!==o?(console.log("❌ Type validation failed!"),{isValid:!1,message:"Argument '".concat(e,"' for action '").concat(n,"' at index ").concat(r," must be a ").concat(o,".")}):(console.log("✅ Type validation passed!"),{isValid:!0,message:"Type validation successful."})}(r,f,e,n);if(!h.isValid)return{v:h}};for(s.s();!(o=s.n()).done;)if(c=u())return c.v}catch(e){s.e(e)}finally{s.f()}return{isValid:!0,message:"Arguments validation successful."}}(a,s,t,n)}function L(e){var t,n=[e];if(n.push(e.toLowerCase()),n.push(e.toUpperCase()),e.includes("_")){var r=e.toLowerCase().split("_").map(function(e,t){return 0===t?e:e.charAt(0).toUpperCase()+e.slice(1)}).join("");n.push(r),n.push(e.replace(/_/g,"")),n.push(e.replace(/_/g,"").toLowerCase()),n.push(e.replace(/_/g,"").toUpperCase())}if(/[A-Z]/.test(e)&&!e.includes("_")){var o=e.replace(/([A-Z])/g,"_$1").toLowerCase().replace(/^_/,"");n.push(o.toUpperCase()),n.push(o)}return function(e){if(Array.isArray(e))return j(e)}(t=new Set(n))||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||A(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function R(e){return"object"===k(e)&&null!==e}function N(e){return N="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},N(e)}function D(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return s}}(e,t)||U(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function G(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=U(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,i=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw i}}}}function U(e,t){if(e){if("string"==typeof e)return B(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?B(e,t):void 0}}function B(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function z(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var c=r&&r.prototype instanceof s?r:s,u=Object.create(c.prototype);return V(u,"_invoke",function(n,r,o){var i,s,c,u=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,s=0,c=e,h.n=n,a}};function p(n,r){for(s=n,c=r,t=0;!f&&u&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(c=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(s=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,s=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,d){if(u>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),s=l,c=d;(t=s<2?e:c)||!f;){i||(s?s<3?(s>1&&(h.n=-1),p(s,c)):h.n=c:h.v=c);try{if(u=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(f=h.n<0)?c:n.call(r,h))!==a)break}catch(t){i=e,s=1,c=t}finally{u=1}}return{value:t,done:f}}}(n,o,i),!0),u}var a={};function s(){}function c(){}function u(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(V(t={},r,function(){return this}),t),f=u.prototype=s.prototype=Object.create(l);function h(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,u):(e.__proto__=u,V(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return c.prototype=u,V(f,"constructor",u),V(u,"constructor",c),c.displayName="GeneratorFunction",V(u,o,"GeneratorFunction"),V(f),V(f,o,"Generator"),V(f,r,function(){return this}),V(f,"toString",function(){return"[object Generator]"}),(z=function(){return{w:i,m:h}})()}function V(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}V=function(e,t,n,r){function i(t,n){V(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},V(e,t,n,r)}function q(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?q(Object(n),!0).forEach(function(t){$(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):q(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function $(e,t,n){return(t=W(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function J(e,t,n,r,o,i,a){try{var s=e[i](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,o)}function H(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){J(i,r,o,a,s,"next",e)}function s(e){J(i,r,o,a,s,"throw",e)}a(void 0)})}}function K(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,W(r.key),r)}}function W(e){var t=function(e){if("object"!=N(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=N(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==N(t)?t:t+""}var X=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.messageTypes=null,this.timeoutSettings={execution_service_general_timeout_seconds:30,execution_service_monitoring_timeout_seconds:600},this.activeTabs=new Map,this.activeGraphs=new Map,this.nodeExecutions=new Map,this.maxConcurrentTabs=5,this.tabPool=new Set,this.cancelledActions=new Set,this.tabAcquisitionLocks=new Map,this.defaultActionTimeout=3e5,this.pausedGraphs=new Set,this.pauseResolvers=new Map,this.loginMonitor={tabId:null,intervalId:null,checking:!1,required:null,lastChecked:null,pausedGraphsSet:new Set,started:!1,isDedicated:!1,creationPromise:null,onRemovedHandler:null,fetchedThisSession:!1,lastFetchAt:null}},t=[{key:"loadTimeoutSettings",value:(O=H(z().m(function e(){var t,n,r;return z().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,fetch(chrome.runtime.getURL("lib/shared-config.json"));case 1:return t=e.v,e.n=2,t.json();case 2:(n=e.v).TIMEOUT_SETTINGS&&(this.timeoutSettings=Y(Y({},this.timeoutSettings),n.TIMEOUT_SETTINGS),console.log("[ExecutionService] Loaded timeout settings:",this.timeoutSettings)),e.n=4;break;case 3:e.p=3,r=e.v,console.warn("[ExecutionService] Failed to load timeout config, using defaults:",r);case 4:return e.a(2)}},e,this,[[0,3]])})),function(){return O.apply(this,arguments)})},{key:"initialize",value:function(e){this.messageTypes=e}},{key:"generateArgumentVariations",value:function(e){var t=[e];if(t.push(e.toLowerCase()),t.push(e.toUpperCase()),e.includes("_")){var n=e.toLowerCase().split("_").map(function(e,t){return 0===t?e:e.charAt(0).toUpperCase()+e.slice(1)}).join("");t.push(n),t.push(e.replace(/_/g,"")),t.push(e.replace(/_/g,"").toLowerCase()),t.push(e.replace(/_/g,"").toUpperCase())}if(/[A-Z]/.test(e)&&!e.includes("_")){var r=e.replace(/([A-Z])/g,"_$1").toLowerCase().replace(/^_/,"");t.push(r.toUpperCase()),t.push(r)}return function(e){return function(e){if(Array.isArray(e))return B(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||U(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(new Set(t))}},{key:"normalizeActions",value:function(e,t){var n=this;return e.map(function(e){var r=e.action,o=e.arguments,i=Object.keys(t).find(function(e){return e.toUpperCase()===r.toUpperCase()});if(!i)return e;var a,s={},c=t[i],u=G(c);try{var l=function(){var e=a.value,t=null,r=Object.keys(o).find(function(t){return t.toUpperCase()===e.toUpperCase()});if(r)t=o[r];else{var i,c=G(n.generateArgumentVariations(e));try{var u=function(){var e=i.value,n=Object.keys(o).find(function(t){return t.toUpperCase()===e.toUpperCase()});if(n)return t=o[n],1};for(c.s();!(i=c.n()).done&&!u(););}catch(e){c.e(e)}finally{c.f()}}null!==t&&(s[e]=t)};for(u.s();!(a=u.n()).done;)l()}catch(e){u.e(e)}finally{u.f()}for(var f=function(){var e=D(p[h],2),t=e[0],r=e[1];c.some(function(e){return n.generateArgumentVariations(e).some(function(e){return e.toUpperCase()===t.toUpperCase()})})||(s[t]=r)},h=0,p=Object.entries(o);h<p.length;h++)f();return{action:i,arguments:s}})}},{key:"executeActions",value:(S=H(z().m(function e(t){var n,r,o,i,a,s,c;return z().w(function(e){for(;;)switch(e.n){case 0:return n=t.actions,r=t.site,o=t.automationMode,i=t.tabId,a=t.actionArgumentsConfig,e.n=1,M(n,a);case 1:if((s=e.v).isValid){e.n=2;break}return e.a(2,{success:!1,message:s.message});case 2:if(c=this.normalizeActions(n,a),"currentTab"!==o){e.n=3;break}return e.a(2,this.executeInCurrentTab(i,r,c));case 3:if("background"!==o){e.n=4;break}return e.a(2,this.executeInBackground(r,c));case 4:return e.a(2,{success:!1,message:"Unknown automation mode: ".concat(o)});case 5:return e.a(2)}},e,this)})),function(e){return S.apply(this,arguments)})},{key:"executeGraphNodeByNode",value:(_=H(z().m(function e(t,n){var r,o,i,a,s,c,u,l,f,h,p,d,v,y,g,b,m,w,E,_,S,O,T,k,P,x,A,j,C=this,M=arguments;return z().w(function(e){for(;;)switch(e.p=e.n){case 0:return r=M.length>2&&void 0!==M[2]?M[2]:{},e.p=1,console.log("[ExecutionService] Starting node-by-node graph execution: ".concat(t," with ").concat(n.length," actions")),console.log("[ExecutionService] Performing pre-start login check for graph: ".concat(t)),e.p=2,e.n=3,this.ensureLoginMonitorTab();case 3:return e.n=4,this.checkLoginAndTogglePause();case 4:e.n=6;break;case 5:e.p=5,x=e.v,console.warn("[ExecutionService] Pre-start login check failed:",x);case 6:if(!0!==this.loginMonitor.required){e.n=11;break}console.warn("[ExecutionService] Login required - starting graph ".concat(t," in paused state and waiting")),this.activeGraphs.set(t,{status:"running",nodes:new Map,progress:{completed:0,failed:0,total:n.length},startTime:new Date,options:r}),this.pauseGraph(t),this.loginMonitor.pausedGraphsSet.add(t);case 7:if(!1===this.loginMonitor.required){e.n=10;break}return e.n=8,this.checkLoginAndTogglePause().catch(function(){});case 8:return e.n=9,this.delay(500);case 9:e.n=7;break;case 10:this.resumeGraph(t);case 11:this.activeGraphs.set(t,{status:"running",nodes:new Map,progress:{completed:0,failed:0,total:n.length},startTime:new Date,options:r}),o=this.buildDependencyMap(n),i=o.dependencies,a=o.dependents,s=new Set,c=new Set,u=[],l=new Map,f=new Map(n.map(function(e){return[e.id||e.action,e]})),console.log("[ExecutionService] Node-by-node execution initialized"),console.log("[ExecutionService] Dependency analysis complete - actions with dependencies:",Array.from(i.entries()).filter(function(e){var t=D(e,2);return t[0],t[1].length>0})),h=function(e){return!(s.has(e)||c.has(e)||l.has(e))&&(i.get(e)||[]).every(function(e){return s.has(e)})},p=function(){var e=H(z().m(function e(n){var o,i,h,p;return z().w(function(e){for(;;)switch(e.n){case 0:if(o=f.get(n)){e.n=1;break}return e.a(2);case 1:return console.log("[ExecutionService] Starting action: ".concat(n)),i=r.actionTimeout||C.defaultActionTimeout,h=new Promise(function(e,t){setTimeout(function(){t(new Error("Action timeout: ".concat(n," exceeded ").concat(i/1e3,"s")))},i)}),p=Promise.race([C.executeNodeWithTab(t,o,r),h]).then(function(e){return l.delete(n),e.success?(s.add(n),u.push(e),C.updateNodeStatus(t,n,"completed",e),console.log("[ExecutionService] Action completed: ".concat(n))):(c.add(n),u.push({success:!1,action:o.action,error:e.error||e.message}),C.updateNodeStatus(t,n,"failed",e.error||e),console.log("[ExecutionService] Action failed: ".concat(n)),!1!==r.failDependentsOnError&&(C.failDependentActions(t,n,a),(a.get(n)||[]).forEach(function(e){c.add(e),l.has(e)&&C.cancelledActions.add(e)}))),e}).catch(function(e){l.delete(n),c.add(n);var i={success:!1,action:o.action,error:e.message};return u.push(i),C.updateNodeStatus(t,n,"failed",e),console.error("[ExecutionService] Action errored: ".concat(n),e),!1!==r.failDependentsOnError&&(C.failDependentActions(t,n,a),(a.get(n)||[]).forEach(function(e){c.add(e),l.has(e)&&C.cancelledActions.add(e)})),i}),l.set(n,p),e.a(2,p)}},e)}));return function(t){return e.apply(this,arguments)}}(),d=function(){var e,t=[],r=G(n);try{for(r.s();!(e=r.n()).done;){var o=e.value,i=o.id||o.action;h(i)&&t.push(i)}}catch(e){r.e(e)}finally{r.f()}return t.length>0&&console.log("[ExecutionService] Starting ".concat(t.length," ready actions:"),t),t.forEach(function(e){return p(e)}),t.length},v=0,y=3*n.length,g=0;case 12:if(!(s.size+c.size<n.length&&v<y)){e.n=20;break}return v++,e.n=13,this.checkForPause(t);case 13:if(0!==(b=d())||0!==l.size){e.n=14;break}return m=n.filter(function(e){var t=e.id||e.action;return!s.has(t)&&!c.has(t)}),m.length>0&&(console.error("[ExecutionService] Deadlock detected at iteration ".concat(v,". Remaining actions:"),m.map(function(e){var t=e.id||e.action,n=(i.get(t)||[]).filter(function(e){return!s.has(e)});return"".concat(t," (waiting for: ").concat(n.join(", "),")")})),m.forEach(function(e){var n=e.id||e.action;c.add(n),C.updateNodeStatus(t,n,"failed",{reason:"deadlock_detected",iteration:v,remainingDependencies:(i.get(n)||[]).filter(function(e){return!s.has(e)})})})),e.a(3,20);case 14:if(0===b?++g>10&&console.warn("[ExecutionService] No progress for ".concat(g," iterations. Active promises: ").concat(l.size)):g=0,!(l.size>0)){e.n=18;break}return e.p=15,w=Array.from(l.values()),e.n=16,Promise.race(w);case 16:e.n=18;break;case 17:e.p=17,A=e.v,console.error("[ExecutionService] Error in Promise.race:",A);case 18:if(!(r.stopOnError&&c.size>0)){e.n=19;break}return console.log("[ExecutionService] Stopping execution due to error at iteration ".concat(v)),e.a(3,20);case 19:e.n=12;break;case 20:if(v>=y){console.error("[ExecutionService] Max iterations exceeded (".concat(y,"). Forcing completion.")),E=n.filter(function(e){var t=e.id||e.action;return!s.has(t)&&!c.has(t)}),E.forEach(function(e){var n=e.id||e.action;c.add(n),C.updateNodeStatus(t,n,"failed",{reason:"max_iterations_exceeded",iteration:y})}),_=G(l.keys());try{for(_.s();!(S=_.n()).done;)O=S.value,this.cancelledActions.add(O)}catch(e){_.e(e)}finally{_.f()}}if(!(l.size>0)){e.n=21;break}return console.log("[ExecutionService] Waiting for ".concat(l.size," remaining actions to complete")),e.n=21,Promise.allSettled(Array.from(l.values()));case 21:return T=this.activeGraphs.get(t),k=0===c.size,T.status=k?"completed":"failed",T.endTime=new Date,T.duration=T.endTime-T.startTime,e.n=22,this.cleanupGraphTabs(t);case 22:return 0===this.activeGraphs.size&&0===this.nodeExecutions.size&&(console.log("[ExecutionService] All graphs completed. Stopping login monitor and closing monitor tab."),this.stopLoginMonitor(!0)),console.log("[ExecutionService] Node-by-node graph execution completed: ".concat(t,", success: ").concat(k)),console.log("[ExecutionService] Final stats - Completed: ".concat(s.size,", Failed: ").concat(c.size,", Total: ").concat(n.length)),e.a(2,{success:k,graphId:t,results:u,summary:{total:n.length,completed:s.size,failed:c.size,duration:T.duration}});case 23:return e.p=23,j=e.v,console.error("[ExecutionService] Node-by-node graph execution failed: ".concat(t),j),this.activeGraphs.has(t)&&((P=this.activeGraphs.get(t)).status="failed",P.error=j.message,P.endTime=new Date),e.a(2,{success:!1,graphId:t,error:j.message,message:"Node-by-node graph execution failed: ".concat(j.message)})}},e,this,[[15,17],[2,5],[1,23]])})),function(e,t){return _.apply(this,arguments)})},{key:"getSupportedActions",value:(E=H(z().m(function e(){var t;return z().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,d();case 1:return t=e.v,e.a(2,Object.keys(t));case 2:return e.p=2,e.v,e.a(2,["BUY","SELL","PlaceBuyLimitOrder","PlaceSellLimitOrder","PlaceBuyStopLossMarketOrder","PlaceSellStopLossMarketOrder","MONITORPROFIT","ExitAllPositions","NavigateToProfile","GetProfileInfo"])}},e,null,[[0,2]])})),function(){return E.apply(this,arguments)})},{key:"executeAction",value:(w=H(z().m(function e(t,n,r){var o,i,a,s,c,u;return z().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,o={action:t,arguments:n},e.n=1,d();case 1:return i=e.v,e.n=2,M([o],i);case 2:if((a=e.v).isValid){e.n=3;break}return e.a(2,{success:!1,message:a.message});case 3:return s=this.normalizeActions([o],i)[0],e.n=4,this.findSiteForTab(r);case 4:return c=e.v,e.a(2,this.executeInTab(r,c,[s]));case 5:return e.p=5,u=e.v,e.a(2,{success:!1,message:"Action execution failed: ".concat(u.message)})}},e,this,[[0,5]])})),function(e,t,n){return w.apply(this,arguments)})},{key:"findSiteForTab",value:(m=H(z().m(function e(t){var n,r,o,i,a,s,c,u,l,f;return z().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.n=1,h();case 1:if(n=e.v,!t){e.n=8;break}return e.p=2,e.n=3,chrome.tabs.get(t);case 3:r=e.v,o=0,i=Object.entries(n);case 4:if(!(o<i.length)){e.n=6;break}if(a=D(i[o],2),s=a[0],c=a[1],!((u=c.url||c.urlPrefix)&&r.url&&r.url.includes(u))){e.n=5;break}return e.a(2,Y(Y({},c),{},{siteId:s,url:u}));case 5:o++,e.n=4;break;case 6:e.n=8;break;case 7:e.p=7,e.v;case 8:return l=Object.values(n)[0],f=l.url||l.urlPrefix,e.a(2,Y(Y({},l),{},{url:f}))}},e,null,[[2,7]])})),function(e){return m.apply(this,arguments)})},{key:"executeInCurrentTab",value:(g=H(z().m(function e(t,n,r){var o;return z().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,this.ensureContentScript(t,n.contentScript);case 1:return e.a(2,this.sendMessageToTab(t,{type:b,actions:r}));case 2:return e.p=2,o=e.v,e.a(2,{success:!1,message:"Execution error: ".concat(o.message)})}},e,this,[[0,2]])})),function(e,t,n){return g.apply(this,arguments)})},{key:"executeInBackground",value:(v=H(z().m(function e(t,n){var r,o,i,a;return z().w(function(e){for(;;)switch(e.p=e.n){case 0:return r=null,e.p=1,e.n=2,chrome.tabs.create({url:t.urlPrefix||t.url,active:!1});case 2:return r=e.v,e.n=3,this.waitForTabLoad(r.id);case 3:return e.n=4,this.delay(2e3);case 4:return e.n=5,this.ensureContentScript(r.id,t.contentScript);case 5:return e.n=6,this.sendMessageToTab(r.id,{type:b,actions:n});case 6:return o=e.v,e.n=7,y();case 7:if(!1===e.v.close_tabs_after_execution||!r){e.n=11;break}return e.p=8,console.log("[ExecutionService] Closing background tab ".concat(r.id," after successful execution")),e.n=9,chrome.tabs.remove(r.id);case 9:r=null,e.n=11;break;case 10:e.p=10,i=e.v,console.warn("[ExecutionService] Failed to close tab ".concat(r.id,":"),i.message);case 11:return e.a(2,o);case 12:return e.p=12,a=e.v,e.a(2,{success:!1,message:"Background execution error: ".concat(a.message)});case 13:if(e.p=13,!r){e.n=18;break}return e.n=14,y();case 14:if(!1===e.v.close_tabs_after_execution){e.n=18;break}return e.p=15,console.log("[ExecutionService] Cleaning up tab ".concat(r.id," after error")),e.n=16,chrome.tabs.remove(r.id);case 16:e.n=18;break;case 17:e.p=17,e.v;case 18:return e.f(13);case 19:return e.a(2)}},e,this,[[15,17],[8,10],[1,12,13,19]])})),function(e,t){return v.apply(this,arguments)})},{key:"executeInTab",value:(p=H(z().m(function e(t,n,r){var o,i,a;return z().w(function(e){for(;;)switch(e.p=e.n){case 0:o=2,i=0;case 1:if(!(i<=o)){e.n=13;break}return e.p=2,e.n=3,this.waitForTabLoad(t);case 3:if(!n.contentScript){e.n=4;break}return e.n=4,this.ensureContentScript(t,n.contentScript);case 4:return e.a(2,this.sendMessageToTab(t,{type:b,actions:r}));case 5:return e.p=5,a=e.v,e.p=6,e.n=7,chrome.tabs.get(t);case 7:e.n=9;break;case 8:return e.p=8,e.v,e.a(2,{success:!1,message:"Tab execution failed: Tab no longer exists (".concat(a.message,")"),error:"TAB_CLOSED"});case 9:if(i!==o){e.n=11;break}if(!(a.message.includes("message channel closed")||a.message.includes("asynchronous response")||a.message.includes("Could not establish connection"))){e.n=10;break}return e.a(2,{success:!1,message:"Tab execution failed after ".concat(o+1," attempts: ").concat(a.message,". The content script may not be ready or the tab was navigated."),error:"MESSAGE_CHANNEL_ERROR"});case 10:return e.a(2,{success:!1,message:"Tab execution failed after ".concat(o+1," attempts: ").concat(a.message)});case 11:return console.log("[ExecutionService] Execution attempt ".concat(i+1," failed for tab ").concat(t,", retrying in 1 second..."),a.message),e.n=12,this.delay(1e3);case 12:i++,e.n=1;break;case 13:return e.a(2)}},e,this,[[6,8],[2,5]])})),function(e,t,n){return p.apply(this,arguments)})},{key:"ensureContentScript",value:(f=H(z().m(function e(t,n){var r;return z().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,chrome.tabs.get(t);case 1:return e.n=2,chrome.scripting.executeScript({target:{tabId:t},func:function(e){return!0===window[e]},args:["SmartFinAgentContentScriptLoaded"],world:"ISOLATED"});case 2:if(D(e.v,1)[0].result){e.n=4;break}return e.n=3,chrome.scripting.executeScript({target:{tabId:t},files:[n],world:"ISOLATED"});case 3:return e.n=4,this.delay(500);case 4:e.n=7;break;case 5:if(e.p=5,!(r=e.v).message.includes("Cannot access contents of url")){e.n=6;break}throw new Error("Tab is not accessible for content script injection.");case 6:throw new Error("Failed to inject content script: ".concat(r.message));case 7:return e.a(2)}},e,this,[[0,5]])})),function(e,t){return f.apply(this,arguments)})},{key:"waitForTabLoad",value:function(e){return new Promise(function(){var t=H(z().m(function t(n){var r;return z().w(function(t){for(;;)switch(t.p=t.n){case 0:return t.p=0,t.n=1,chrome.tabs.get(e);case 1:if("complete"!==t.v.status){t.n=2;break}return n(),t.a(2);case 2:r=function(t,o){t===e&&"complete"===o.status&&(chrome.tabs.onUpdated.removeListener(r),n())},chrome.tabs.onUpdated.addListener(r),t.n=4;break;case 3:t.p=3,t.v,n();case 4:return t.a(2)}},t,null,[[0,3]])}));return function(e){return t.apply(this,arguments)}}())}},{key:"sendMessageToTab",value:function(e,t){var n=this;return new Promise(function(){var r=H(z().m(function r(o,i){var a,s,c,u,l,f;return z().w(function(r){for(;;)switch(r.p=r.n){case 0:if(n.timeoutSettings.execution_service_monitoring_timeout_seconds){r.n=5;break}return r.n=1,n.loadTimeoutSettings();case 1:return r.p=1,r.n=2,fetch(chrome.runtime.getURL("lib/shared-config.json"));case 2:return a=r.v,r.n=3,a.json();case 3:(s=r.v).EXECUTION_SERVICE&&(n.maxConcurrentTabs=s.EXECUTION_SERVICE.max_concurrent_tabs||5),r.n=5;break;case 4:r.p=4,f=r.v,console.warn("[ExecutionService] Failed to load execution service config, using defaults:",f);case 5:console.log("[ExecutionService] Sending message to tab ".concat(e,":"),t),c=t.actions&&t.actions.some(function(e){return["MonitorConditionThenAct","MONITORPROFIT","MonitorSymbolFromWatchlist"].includes(e.action)}),u=c?1e3*n.timeoutSettings.execution_service_monitoring_timeout_seconds:1e3*n.timeoutSettings.execution_service_general_timeout_seconds,console.log("[ExecutionService] Using ".concat(u/1e3,"s timeout for ").concat(c?"monitoring":"regular"," action")),l=setTimeout(function(){console.error("[ExecutionService] Message timeout for tab ".concat(e," after ").concat(u/1e3,"s")),i(new Error("Message timeout: No response received within ".concat(u/1e3," seconds")))},u),chrome.tabs.sendMessage(e,t,function(t){if(clearTimeout(l),chrome.runtime.lastError){var n=chrome.runtime.lastError.message;return console.error("[ExecutionService] Chrome runtime error for tab ".concat(e,":"),n),void(n.includes("message channel closed")||n.includes("asynchronous response")||n.includes("Could not establish connection")?(console.error("[ExecutionService] Message channel closed error detected"),i(new Error("Message channel closed before response received. This usually happens when the tab was closed or navigated away during execution."))):i(new Error("Failed to send message: ".concat(n))))}if(!t)return console.error("[ExecutionService] No response received from tab ".concat(e)),void i(new Error("No response from content script"));console.log("[ExecutionService] Received response from tab ".concat(e,":"),t),o(t)});case 6:return r.a(2)}},r,null,[[1,4]])}));return function(e,t){return r.apply(this,arguments)}}())}},{key:"delay",value:function(e){return new Promise(function(t){return setTimeout(t,e)})}},{key:"buildDependencyMap",value:function(e){var t=new Map,n=new Map,r=new Set(e.map(function(e){return e.id||e.action}));return e.forEach(function(e){var o=e.id||e.action;if(t.set(o,[]),n.set(o,[]),e.depends_on){var i=[];Array.isArray(e.depends_on)?i=e.depends_on.filter(function(e){return!!r.has(e)||(console.log("[ExecutionService] Missing dependency '".concat(e,"' for action '").concat(o,"' - treating as independent")),!1)}):r.has(e.depends_on)?i=[e.depends_on]:(console.log("[ExecutionService] Missing dependency '".concat(e.depends_on,"' for action '").concat(o,"' - treating as independent")),i=[]),t.set(o,i),i.forEach(function(e){n.has(e)||n.set(e,[]),n.get(e).push(o)})}}),{dependencies:t,dependents:n}}},{key:"executeNodeWithTab",value:(l=H(z().m(function e(t,n){var r,o,i,a,s,c,u,l,f,h=arguments;return z().w(function(e){for(;;)switch(e.p=e.n){case 0:if(r=h.length>2&&void 0!==h[2]?h[2]:{},o=n.id||n.action,i=null,a=null,console.log("[ExecutionService] Starting node execution: ".concat(o)),this.nodeExecutions.set(o,{status:"running",graphId:t,action:n,startTime:new Date,tabId:null}),e.p=1,!this.cancelledActions.has(o)){e.n=2;break}throw new Error("Action ".concat(o," was cancelled before execution"));case 2:return e.n=3,this.getOrCreateTab(n,r);case 3:return s=e.v,i=s.tabId,a=this.activeTabs.get(i),(c=this.nodeExecutions.get(o)).tabId=i,e.n=4,this.ensureContentScript(i,s.site.contentScript);case 4:if(!this.cancelledActions.has(o)){e.n=5;break}throw new Error("Action ".concat(o," was cancelled during setup"));case 5:return e.n=6,this.checkLoginBeforeExecution(i,o,t);case 6:return e.n=7,this.sendMessageToTab(i,{type:b,actions:[n]});case 7:return u=e.v,c.status=u.success?"completed":"failed",c.result=u,c.endTime=new Date,c.duration=c.endTime-c.startTime,console.log("[ExecutionService] Node execution completed: ".concat(o,", success: ").concat(u.success)),e.a(2,{success:u.success,nodeId:o,action:n.action,result:u.results?u.results[0]:u,tabId:i,duration:c.duration});case 8:return e.p=8,f=e.v,console.error("[ExecutionService] Node execution failed: ".concat(o),f),(l=this.nodeExecutions.get(o))&&(l.status="failed",l.error=f.message,l.endTime=new Date),e.a(2,{success:!1,nodeId:o,action:n.action,error:f.message,tabId:i});case 9:return e.p=9,i&&a&&!1!==r.reuseTab&&"busy"===a.status&&a.acquiredBy===o&&(a.status="available",a.lastUsed=new Date,a.acquiredBy=null,console.log("[ExecutionService] Released tab ".concat(i," for reuse"))),this.cancelledActions.delete(o),e.f(9);case 10:return e.a(2)}},e,this,[[1,8,9,10]])})),function(e,t){return l.apply(this,arguments)})},{key:"getOrCreateTab",value:(u=H(z().m(function e(t){var n,r,o,i,a,s,c=this,u=arguments;return z().w(function(e){for(;;)switch(e.n){case 0:return n=u.length>1&&void 0!==u[1]?u[1]:{},e.n=1,h();case 1:if(r=e.v,o=null,o=n.siteId?r[n.siteId]:Object.values(r)[0]){e.n=2;break}throw new Error("No supported site found for action execution");case 2:if(i=o.url,!1===n.reuseTab){e.n=4;break}return this.tabAcquisitionLocks.has(i)||this.tabAcquisitionLocks.set(i,Promise.resolve()),e.n=3,this.tabAcquisitionLocks.get(i).then(H(z().m(function e(){var n,r,i,a,s,u;return z().w(function(e){for(;;)switch(e.p=e.n){case 0:n=G(c.activeTabs),e.p=1,n.s();case 2:if((r=n.n()).done){e.n=4;break}if(i=D(r.value,2),a=i[0],(s=i[1]).site.url!==o.url||"available"!==s.status){e.n=3;break}if("available"!==s.status){e.n=3;break}return s.status="busy",s.acquiredBy=t.id||t.action,s.lastAcquired=new Date,console.log("[ExecutionService] Reusing existing tab: ".concat(a," for action: ").concat(t.id||t.action)),e.a(2,{tabId:a,site:o,reused:!0});case 3:e.n=2;break;case 4:e.n=6;break;case 5:e.p=5,u=e.v,n.e(u);case 6:return e.p=6,n.f(),e.f(6);case 7:return e.a(2,null)}},e,null,[[1,5,6,7]])})));case 3:if(!(a=e.v)){e.n=4;break}return e.a(2,a);case 4:if(!(this.activeTabs.size>=this.maxConcurrentTabs)){e.n=5;break}throw new Error("Maximum concurrent tabs (".concat(this.maxConcurrentTabs,") reached. Consider increasing limit or enabling tab reuse."));case 5:return s=this.tabAcquisitionLocks.get(i).then(H(z().m(function e(){var n;return z().w(function(e){for(;;)switch(e.n){case 0:return console.log("[ExecutionService] Creating new tab for site: ".concat(o.url)),e.n=1,chrome.tabs.create({url:o.url,active:!1});case 1:return n=e.v,c.activeTabs.set(n.id,{tab:n,site:o,status:"busy",created:new Date,acquiredBy:t.id||t.action,actions:[]}),e.n=2,c.waitForTabLoad(n.id);case 2:return e.n=3,c.delay(2e3);case 3:return console.log("[ExecutionService] New tab created and loaded: ".concat(n.id)),e.a(2,{tabId:n.id,site:o,reused:!1})}},e)}))),this.tabAcquisitionLocks.set(i,s),e.n=6,s;case 6:return e.a(2,e.v)}},e,this)})),function(e){return u.apply(this,arguments)})},{key:"updateNodeStatus",value:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if(this.activeGraphs.has(e)){var o=this.activeGraphs.get(e);o.nodes.set(t,{status:n,data:r,timestamp:new Date}),"completed"===n?o.progress.completed++:"failed"===n&&o.progress.failed++}}},{key:"failDependentActions",value:function(e,t,n){var r=this,o=n.get(t)||[],i=new Set,a=function(o){i.has(o)||(i.add(o),r.cancelledActions.add(o),console.log("[ExecutionService] Cancelling running action due to dependency failure: ".concat(o)),r.updateNodeStatus(e,o,"failed",{reason:"dependency_failed",failedDependency:t,timestamp:new Date}),(n.get(o)||[]).forEach(a))};console.log("[ExecutionService] Failing ".concat(o.length," dependent actions for failed node: ").concat(t)),o.forEach(a)}},{key:"cleanupGraphTabs",value:(c=H(z().m(function e(t){var n,r,o,i,a,s,c,u,l,f,h,p,d,v;return z().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.n=1,y();case 1:if(!1!==e.v.close_tabs_after_execution){e.n=2;break}return console.log("[ExecutionService] Tab cleanup disabled for graph: ".concat(t)),e.a(2);case 2:console.log("[ExecutionService] Cleaning up tabs for graph: ".concat(t)),n=new Set,r=G(this.nodeExecutions);try{for(r.s();!(o=r.n()).done;)(i=D(o.value,2))[0],(a=i[1]).graphId===t&&a.tabId&&n.add(a.tabId)}catch(e){r.e(e)}finally{r.f()}s=G(n),e.p=3,s.s();case 4:if((c=s.n()).done){e.n=9;break}return u=c.value,e.p=5,e.n=6,chrome.tabs.remove(u);case 6:this.activeTabs.delete(u),console.log("[ExecutionService] Closed tab: ".concat(u)),e.n=8;break;case 7:e.p=7,d=e.v,console.warn("[ExecutionService] Failed to close tab ".concat(u,":"),d.message);case 8:e.n=4;break;case 9:e.n=11;break;case 10:e.p=10,v=e.v,s.e(v);case 11:return e.p=11,s.f(),e.f(11);case 12:l=G(this.nodeExecutions);try{for(l.s();!(f=l.n()).done;)h=D(f.value,2),p=h[0],h[1].graphId===t&&this.nodeExecutions.delete(p)}catch(e){l.e(e)}finally{l.f()}this.activeGraphs.delete(t),0===this.activeGraphs.size&&0===this.nodeExecutions.size&&(console.log("[ExecutionService] No active graphs or nodes remaining. Stopping login monitor and closing monitor tab."),this.stopLoginMonitor(!0));case 13:return e.a(2)}},e,this,[[5,7],[3,10,11,12]])})),function(e){return c.apply(this,arguments)})},{key:"getGraphStatus",value:function(e){var t=this.activeGraphs.get(e);if(!t)return{found:!1};var n,r=[],o=G(this.nodeExecutions);try{for(o.s();!(n=o.n()).done;){var i=D(n.value,2),a=i[0],s=i[1];s.graphId===e&&r.push({nodeId:a,status:s.status,action:s.action.action,tabId:s.tabId,duration:s.duration,startTime:s.startTime,endTime:s.endTime})}}catch(e){o.e(e)}finally{o.f()}return{found:!0,graphId:e,status:t.status,progress:t.progress,nodeStatuses:r,startTime:t.startTime,endTime:t.endTime,duration:t.duration}}},{key:"getAllExecutionStatus",value:function(){var e,t=[],n=G(this.activeGraphs.keys());try{for(n.s();!(e=n.n()).done;){var r=e.value;t.push(this.getGraphStatus(r))}}catch(e){n.e(e)}finally{n.f()}return{activeGraphs:t.length,activeTabs:this.activeTabs.size,activeNodes:this.nodeExecutions.size,pausedGraphs:this.pausedGraphs.size,graphs:t,tabs:Array.from(this.activeTabs.entries()).map(function(e){var t=D(e,2),n=t[0],r=t[1];return{tabId:n,url:r.site.url,status:r.status,created:r.created}}),login:{required:this.loginMonitor.required,lastChecked:this.loginMonitor.lastChecked,monitorTabId:this.loginMonitor.tabId,pausedByLogin:Array.from(this.loginMonitor.pausedGraphsSet)}}}},{key:"pauseGraph",value:function(e){try{var t=this.activeGraphs.get(e);return t?"running"!==t.status?{success:!1,message:"Graph '".concat(e,"' is not running (status: ").concat(t.status,")")}:this.pausedGraphs.has(e)?{success:!1,message:"Graph '".concat(e,"' is already paused")}:(this.pausedGraphs.add(e),t.status="paused",t.pausedAt=new Date,console.log("[ExecutionService] Graph paused: ".concat(e)),{success:!0,message:"Graph '".concat(e,"' paused successfully"),pausedAt:t.pausedAt}):{success:!1,message:"Graph '".concat(e,"' not found")}}catch(t){return console.error("[ExecutionService] Error pausing graph ".concat(e,":"),t),{success:!1,message:"Failed to pause graph: ".concat(t.message)}}}},{key:"resumeGraph",value:function(e){try{var t=this.activeGraphs.get(e);if(!t)return{success:!1,message:"Graph '".concat(e,"' not found")};if("paused"!==t.status)return{success:!1,message:"Graph '".concat(e,"' is not paused (status: ").concat(t.status,")")};if(!this.pausedGraphs.has(e))return{success:!1,message:"Graph '".concat(e,"' is not in paused state")};this.pausedGraphs.delete(e),t.status="running",t.resumedAt=new Date,t.pausedAt&&(t.pauseDuration=(t.pauseDuration||0)+(t.resumedAt-t.pausedAt));var n=this.pauseResolvers.get(e);return n&&(n(),this.pauseResolvers.delete(e)),console.log("[ExecutionService] Graph resumed: ".concat(e)),{success:!0,message:"Graph '".concat(e,"' resumed successfully"),resumedAt:t.resumedAt,totalPauseDuration:t.pauseDuration||0}}catch(t){return console.error("[ExecutionService] Error resuming graph ".concat(e,":"),t),{success:!1,message:"Failed to resume graph: ".concat(t.message)}}}},{key:"checkForPause",value:(s=H(z().m(function e(t){var n=this;return z().w(function(e){for(;;)switch(e.n){case 0:if(!this.pausedGraphs.has(t)){e.n=1;break}return console.log("[ExecutionService] Graph ".concat(t," is paused, waiting for resume...")),e.a(2,new Promise(function(e){n.pausedGraphs.has(t)?n.pauseResolvers.set(t,e):e()}));case 1:return e.a(2)}},e,this)})),function(e){return s.apply(this,arguments)})},{key:"getKiteBaseUrl",value:(a=H(z().m(function e(){var t,n,r;return z().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,fetch(chrome.runtime.getURL("lib/shared-config.json"));case 1:return t=e.v,e.n=2,t.json();case 2:return n=e.v,r=(null==n?void 0:n.SUPPORTED_SITES)&&Object.values(n.SUPPORTED_SITES)[0],e.a(2,(null==r?void 0:r.urlPrefix)||(null==r?void 0:r.url)||"https://kite.zerodha.com/");case 3:return e.p=3,e.v,e.a(2,"https://kite.zerodha.com/")}},e,null,[[0,3]])})),function(){return a.apply(this,arguments)})},{key:"getOrdersPageUrl",value:(i=H(z().m(function e(){var t;return z().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,this.getKiteBaseUrl();case 1:return t=e.v,e.a(2,new URL("orders",t).toString())}},e,this)})),function(){return i.apply(this,arguments)})},{key:"ensureLoginMonitorTab",value:(o=H(z().m(function e(){var t,n,r,o=this;return z().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.n=1,this.getOrdersPageUrl();case 1:if(t=e.v,!this.loginMonitor.tabId){e.n=5;break}return e.p=2,e.n=3,chrome.tabs.get(this.loginMonitor.tabId);case 3:e.n=5;break;case 4:e.p=4,e.v,this.loginMonitor.tabId=null;case 5:if(this.loginMonitor.tabId){e.n=6;break}return this.loginMonitor.creationPromise||(this.loginMonitor.creationPromise=chrome.tabs.create({url:t,pinned:!0,active:!1}).then(function(e){o.loginMonitor.tabId=e.id,o.loginMonitor.isDedicated=!0}).catch(function(){}).finally(function(){o.loginMonitor.creationPromise=null})),e.n=6,this.loginMonitor.creationPromise;case 6:return e.n=7,h();case 7:return n=e.v,r=Object.values(n)[0],e.n=8,this.waitForTabLoad(this.loginMonitor.tabId);case 8:return e.n=9,this.ensureContentScript(this.loginMonitor.tabId,r.contentScript);case 9:return e.a(2)}},e,this,[[2,4]])})),function(){return o.apply(this,arguments)})},{key:"checkLoginAndTogglePause",value:(r=H(z().m(function e(){var t,n,r,o,i,a,s,c,u,l,f,h,p,d,v,y,g,m;return z().w(function(e){for(;;)switch(e.p=e.n){case 0:if(!this.loginMonitor.checking){e.n=1;break}return e.a(2);case 1:if(this.loginMonitor.tabId){e.n=2;break}return e.a(2);case 2:return this.loginMonitor.checking=!0,e.p=3,e.n=4,this.getOrdersPageUrl();case 4:if(t=e.v,!this.loginMonitor.isDedicated){e.n=5;break}return e.n=5,chrome.tabs.update(this.loginMonitor.tabId,{url:t,pinned:!0,active:!1});case 5:return e.n=6,this.waitForTabLoad(this.loginMonitor.tabId);case 6:return e.n=7,this.sendMessageToTab(this.loginMonitor.tabId,{type:"PERFORM_SITE_ACTIONS",actions:[{action:"IsLoginRequired",arguments:{}}]});case 7:if(n=e.v,r=null,n&&Array.isArray(n.results)&&null!=(i=n.results[0])&&i.success&&(r=!(null==i||null===(o=i.data)||void 0===o||!o.loginRequired)),a=this.loginMonitor.required,this.loginMonitor.required=r,this.loginMonitor.lastChecked=new Date,console.log("[ExecutionService] Login check: required=".concat(r,", previous=").concat(a)),!1===r&&!1!==a&&(this.loginMonitor.fetchedThisSession=!1),!0===r&&!0!==a){console.log("[ExecutionService] Login required - pausing all running graphs"),s=G(this.activeGraphs.entries());try{for(s.s();!(c=s.n()).done;)u=D(c.value,2),l=u[0],"running"===u[1].status&&(console.log("[ExecutionService] Pausing graph ".concat(l," due to login requirement")),null!=(f=this.pauseGraph(l))&&f.success&&this.loginMonitor.pausedGraphsSet.add(l))}catch(e){s.e(e)}finally{s.f()}}else if(!1===r&&!0===a&&this.loginMonitor.pausedGraphsSet.size>0)for(console.log("[ExecutionService] Login no longer required - resuming paused graphs"),h=0,p=Array.from(this.loginMonitor.pausedGraphsSet);h<p.length;h++)d=p[h],console.log("[ExecutionService] Resuming graph ".concat(d," - login resolved")),null!=(v=this.resumeGraph(d))&&v.success&&this.loginMonitor.pausedGraphsSet.delete(d);if(!1!==r){e.n=14;break}return e.p=8,console.log("[ExecutionService] Logged in on monitor tab. Fetching orders (recurring)..."),e.n=9,this.sendMessageToTab(this.loginMonitor.tabId,{type:b,actions:[{action:"GetOpenOrders",arguments:{}},{action:"GetCompletedOrders",arguments:{}}]});case 9:y=e.v,console.log("[ExecutionService] Orders fetch result:",y),this.loginMonitor.lastFetchAt=new Date,e.n=11;break;case 10:e.p=10,g=e.v,console.warn("[ExecutionService] Failed to fetch orders on monitor tab:",g);case 11:return e.p=11,e.n=12,chrome.tabs.reload(this.loginMonitor.tabId);case 12:e.n=14;break;case 13:e.p=13,e.v;case 14:e.n=19;break;case 15:return e.p=15,m=e.v,console.warn("[ExecutionService] Login check failed:",m),e.p=16,e.n=17,chrome.tabs.get(this.loginMonitor.tabId);case 17:e.n=19;break;case 18:e.p=18,e.v,this.loginMonitor.tabId=null;case 19:return e.p=19,this.loginMonitor.checking=!1,e.f(19);case 20:return e.a(2)}},e,this,[[16,18],[11,13],[8,10],[3,15,19,20]])})),function(){return r.apply(this,arguments)})},{key:"startLoginMonitor",value:function(){var e=this;this.loginMonitor.started||(this.loginMonitor.started=!0,console.log("[ExecutionService] Starting login monitor"),this.ensureLoginMonitorTab().catch(function(e){console.warn("[ExecutionService] Failed to ensure login monitor tab:",e)}),this.loginMonitor.onRemovedHandler=function(t){e.loginMonitor.started&&t===e.loginMonitor.tabId&&e.loginMonitor.isDedicated&&(console.log("[ExecutionService] Login monitor tab closed, recreating"),e.loginMonitor.tabId=null,setTimeout(function(){e.loginMonitor.started&&(e.loginMonitor.tabId||e.loginMonitor.creationPromise||e.ensureLoginMonitorTab().catch(function(e){console.warn("[ExecutionService] Failed to recreate login monitor tab:",e)}))},200))},chrome.tabs.onRemoved.addListener(this.loginMonitor.onRemovedHandler),this.loginMonitor.intervalId=setInterval(H(z().m(function t(){return z().w(function(t){for(;;)switch(t.n){case 0:if(e.loginMonitor.tabId||e.loginMonitor.creationPromise){t.n=2;break}return t.n=1,e.ensureLoginMonitorTab().catch(function(e){console.warn("[ExecutionService] Failed to ensure login monitor tab in interval:",e)});case 1:case 3:return t.a(2);case 2:if(!e.loginMonitor.tabId){t.n=3;break}return t.n=3,e.checkLoginAndTogglePause()}},t)})),1e3),this.loginMonitor.checkImmediately=function(){!e.loginMonitor.checking&&e.loginMonitor.tabId&&e.checkLoginAndTogglePause().catch(function(e){console.warn("[ExecutionService] Immediate login check failed:",e)})},setTimeout(function(){e.loginMonitor.tabId&&e.checkLoginAndTogglePause().catch(function(e){console.warn("[ExecutionService] Initial login check failed:",e)})},500))}},{key:"stopLoginMonitor",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.loginMonitor.started||this.loginMonitor.tabId){if(console.log("[ExecutionService] Stopping login monitor"),this.loginMonitor.intervalId&&(clearInterval(this.loginMonitor.intervalId),this.loginMonitor.intervalId=null),this.loginMonitor.onRemovedHandler){try{chrome.tabs.onRemoved.removeListener(this.loginMonitor.onRemovedHandler)}catch(e){}this.loginMonitor.onRemovedHandler=null}if(e&&this.loginMonitor.tabId&&this.loginMonitor.isDedicated){var t=this.loginMonitor.tabId;try{chrome.tabs.remove(t)}catch(e){}this.loginMonitor.tabId=null,this.loginMonitor.isDedicated=!1}this.loginMonitor.started=!1}}},{key:"checkLoginBeforeExecution",value:(n=H(z().m(function e(t,n,r){var o,i,a,s,c;return z().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,console.log("[ExecutionService] Pre-execution login check for node: ".concat(n)),e.n=1,this.sendMessageToTab(t,{type:"PERFORM_SITE_ACTIONS",actions:[{action:"IsLoginRequired",arguments:{}}]});case 1:if(o=e.v,i=!1,o&&Array.isArray(o.results)&&null!=(a=o.results[0])&&a.success&&(i=!(null==a||null===(s=a.data)||void 0===s||!s.loginRequired)),console.log("[ExecutionService] Pre-execution login check result: loginRequired=".concat(i)),!i){e.n=6;break}this.loginMonitor.required=!0,this.loginMonitor.lastChecked=new Date,r&&(this.pauseGraph(r),this.loginMonitor.pausedGraphsSet.add(r)),console.log("[ExecutionService] Waiting for login before executing node ".concat(n));case 2:if(!1===this.loginMonitor.required){e.n=5;break}return e.n=3,this.checkLoginAndTogglePause().catch(function(){});case 3:return e.n=4,this.delay(500);case 4:e.n=2;break;case 5:r&&this.pausedGraphs.has(r)&&this.resumeGraph(r);case 6:e.n=12;break;case 7:if(e.p=7,c=e.v,console.warn("[ExecutionService] Pre-execution login check failed for node ".concat(n,":"),c.message),!0!==this.loginMonitor.required){e.n=12;break}r&&(this.pauseGraph(r),this.loginMonitor.pausedGraphsSet.add(r));case 8:if(!1===this.loginMonitor.required){e.n=11;break}return e.n=9,this.checkLoginAndTogglePause().catch(function(){});case 9:return e.n=10,this.delay(500);case 10:e.n=8;break;case 11:r&&this.pausedGraphs.has(r)&&this.resumeGraph(r);case 12:return e.a(2)}},e,this,[[0,7]])})),function(e,t,r){return n.apply(this,arguments)})}],t&&K(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,r,o,i,a,s,c,u,l,f,p,v,g,m,w,E,_,S,O}();function Z(e){return Z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Z(e)}function Q(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return s}}(e,t)||ee(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ee(e,t){if(e){if("string"==typeof e)return te(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?te(e,t):void 0}}function te(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function ne(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function re(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ne(Object(n),!0).forEach(function(t){oe(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ne(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function oe(e,t,n){return(t=le(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ie(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var c=r&&r.prototype instanceof s?r:s,u=Object.create(c.prototype);return ae(u,"_invoke",function(n,r,o){var i,s,c,u=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,s=0,c=e,h.n=n,a}};function p(n,r){for(s=n,c=r,t=0;!f&&u&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(c=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(s=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,s=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,d){if(u>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),s=l,c=d;(t=s<2?e:c)||!f;){i||(s?s<3?(s>1&&(h.n=-1),p(s,c)):h.n=c:h.v=c);try{if(u=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(f=h.n<0)?c:n.call(r,h))!==a)break}catch(t){i=e,s=1,c=t}finally{u=1}}return{value:t,done:f}}}(n,o,i),!0),u}var a={};function s(){}function c(){}function u(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(ae(t={},r,function(){return this}),t),f=u.prototype=s.prototype=Object.create(l);function h(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,u):(e.__proto__=u,ae(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return c.prototype=u,ae(f,"constructor",u),ae(u,"constructor",c),c.displayName="GeneratorFunction",ae(u,o,"GeneratorFunction"),ae(f),ae(f,o,"Generator"),ae(f,r,function(){return this}),ae(f,"toString",function(){return"[object Generator]"}),(ie=function(){return{w:i,m:h}})()}function ae(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}ae=function(e,t,n,r){function i(t,n){ae(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},ae(e,t,n,r)}function se(e,t,n,r,o,i,a){try{var s=e[i](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,o)}function ce(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){se(i,r,o,a,s,"next",e)}function s(e){se(i,r,o,a,s,"throw",e)}a(void 0)})}}function ue(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,le(r.key),r)}}function le(e){var t=function(e){if("object"!=Z(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=Z(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Z(t)?t:t+""}var fe=function(){return t=function t(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),this.ruleEngine=new e.Engine,this.facts=new Map,this.initialized=!1,this.graphs=new Map,this.nodeStatuses=new Map,this.parentStatusListeners=new Map,this.activeTabs=0,this.executionService=new X},n=[{key:"initialize",value:(p=ce(ie().m(function e(){return ie().w(function(e){for(;;)switch(e.n){case 0:if(this.initialized){e.n=2;break}return e.n=1,this.executionService.loadTimeoutSettings();case 1:this.executionService.startLoginMonitor(),this.initialized=!0,console.log("[PrimitiveEngineController] Initialized with enhanced execution service and login monitor");case 2:return e.a(2)}},e,this)})),function(){return p.apply(this,arguments)})},{key:"ensureInitialized",value:(f=ce(ie().m(function e(){return ie().w(function(e){for(;;)switch(e.n){case 0:if(this.initialized){e.n=1;break}return e.n=1,this.initialize();case 1:return e.a(2)}},e,this)})),function(){return f.apply(this,arguments)})},{key:"addRule",value:function(e){if(!e.conditions||!e.event)throw new Error("Rule must have conditions and event properties");try{this.ruleEngine.addRule(e)}catch(e){throw console.error("Failed to add rule:",e),e}}},{key:"addFact",value:function(e,t){this.facts.set(e,t),"function"==typeof t?this.ruleEngine.addFact(e,t):this.ruleEngine.addFact(e,function(){return t})}},{key:"removeRule",value:function(e){return this.ruleEngine.removeRule(e)}},{key:"removeFact",value:function(e){return this.facts.delete(e),this.ruleEngine.removeFact(e)}},{key:"run",value:(l=ce(ie().m(function e(){var t,n,r,o=this,i=arguments;return ie().w(function(e){for(;;)switch(e.p=e.n){case 0:return t=i.length>0&&void 0!==i[0]?i[0]:{},e.p=1,Object.keys(t).forEach(function(e){o.addFact(e,t[e])}),e.n=2,this.ruleEngine.run();case 2:return n=e.v,e.a(2,{events:n.events||[],results:n.results||[],failureEvents:n.failureEvents||[],failureResults:n.failureResults||[]});case 3:throw e.p=3,r=e.v,console.error("Rule engine execution failed:",r),r;case 4:return e.a(2)}},e,this,[[1,3]])})),function(){return l.apply(this,arguments)})},{key:"clearRules",value:function(){this.ruleEngine=new e.Engine,this.facts.clear()}},{key:"getFacts",value:function(){return new Map(this.facts)}},{key:"hasFact",value:function(e){return this.facts.has(e)}},{key:"getRuleCount",value:function(){return this.ruleEngine.rules?this.ruleEngine.rules.length:0}},{key:"addGraph",value:function(e,t){try{return this.graphs.set(e,{actions:t||[],nodeStatuses:new Map,created:new Date}),{isValid:!0,message:"Graph '".concat(e,"' added with ").concat(t.length," actions")}}catch(e){return{isValid:!1,message:e.message}}}},{key:"executeActionArray",value:(u=ce(ie().m(function e(t,n){var r,o,i=arguments;return ie().w(function(e){for(;;)switch(e.p=e.n){case 0:return r=i.length>2&&void 0!==i[2]?i[2]:{},e.n=1,this.ensureInitialized();case 1:return e.p=1,console.log("[PrimitiveEngineController] Executing action array for graph ".concat(t," using node-by-node execution")),e.n=2,this.executionService.executeGraphNodeByNode(t,n,re(re({},r),{},{reuseTab:!1!==r.reuseTab,stopOnError:!1!==r.stopOnError,failDependentsOnError:!1!==r.failDependentsOnError}));case 2:return e.a(2,e.v);case 3:return e.p=3,o=e.v,console.error("[PrimitiveEngineController] Failed to execute action array for graph ".concat(t,":"),o),e.a(2,{success:!1,error:o.message,message:"Failed to execute actions for graph ".concat(t,": ").concat(o.message),graphId:t})}},e,this,[[1,3]])})),function(e,t){return u.apply(this,arguments)})},{key:"triggerGraph",value:(c=ce(ie().m(function e(t,n){var r,o,i,a;return ie().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.n=1,this.ensureInitialized();case 1:if(r=this.graphs.get(t)){e.n=2;break}throw new Error("Graph '".concat(t,"' not found"));case 2:if(e.p=2,o=r.actions.find(function(e){return e.id===n})){e.n=3;break}throw new Error("Start action '".concat(n,"' not found in graph '").concat(t,"'"));case 3:return this.setNodeStatus(t,n,"running"),e.n=4,this.executeActionArray(t,[o]);case 4:return i=e.v,this.setNodeStatus(t,n,"completed"),e.a(2,i);case 5:throw e.p=5,a=e.v,this.setNodeStatus(t,n,"failed"),a;case 6:return e.a(2)}},e,this,[[2,5]])})),function(e,t){return c.apply(this,arguments)})},{key:"triggerGraphWithParentTracking",value:(s=ce(ie().m(function e(t,n){var r,o,i,a=arguments;return ie().w(function(e){for(;;)switch(e.n){case 0:return r=a.length>2&&void 0!==a[2]?a[2]:{},e.n=1,this.ensureInitialized();case 1:return o=r.parentActionId,(void 0===(i=r.trackingEnabled)||i)&&o&&this.addParentStatusListener(t,o,function(e){console.log("Parent action ".concat(o," status changed to: ").concat(e))}),e.a(2,this.triggerGraph(t,n))}},e,this)})),function(e,t){return s.apply(this,arguments)})},{key:"getParentStatusBeforeSubtree",value:(a=ce(ie().m(function e(t,n){return ie().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,this.ensureInitialized();case 1:return e.a(2,this.getNodeStatus(t,n))}},e,this)})),function(e,t){return a.apply(this,arguments)})},{key:"setNodeStatus",value:function(e,t,n){var r="".concat(e,":").concat(t);this.nodeStatuses.set(r,{status:n,timestamp:new Date,graphId:e,actionId:t}),this.notifyParentStatusListeners(e,t,n)}},{key:"getNodeStatus",value:function(e,t){var n="".concat(e,":").concat(t),r=this.nodeStatuses.get(n);return r?r.status:null}},{key:"getNodeStatuses",value:function(e){var t,n=[],r=function(e){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=ee(e))){t&&(e=t);var n=0,r=function(){};return{s:r,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,a=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return i=e.done,e},e:function(e){a=!0,o=e},f:function(){try{i||null==t.return||t.return()}finally{if(a)throw o}}}}(this.nodeStatuses.entries());try{for(r.s();!(t=r.n()).done;){var o=Q(t.value,2),i=(o[0],o[1]);i.graphId===e&&n.push(i)}}catch(e){r.e(e)}finally{r.f()}return n}},{key:"addParentStatusListener",value:function(e,t,n){var r="".concat(e,":").concat(t);this.parentStatusListeners.has(r)||this.parentStatusListeners.set(r,new Set),this.parentStatusListeners.get(r).add(n)}},{key:"removeParentStatusListener",value:function(e,t,n){var r="".concat(e,":").concat(t),o=this.parentStatusListeners.get(r);o&&(o.delete(n),0===o.size&&this.parentStatusListeners.delete(r))}},{key:"notifyParentStatusListeners",value:function(e,t,n){var r="".concat(e,":").concat(t),o=this.parentStatusListeners.get(r);o&&o.forEach(function(e){try{e(n)}catch(e){console.error("Error in parent status listener:",e)}})}},{key:"getStatus",value:function(){var e=this.executionService?this.executionService.getAllExecutionStatus():{};return{initialized:this.initialized,activeTabs:this.activeTabs,graphs:this.graphs.size,rules:this.getRuleCount(),facts:this.facts.size,nodeStatuses:this.nodeStatuses.size,executionService:{activeGraphs:e.activeGraphs||0,activeTabs:e.activeTabs||0,activeNodes:e.activeNodes||0,login:e.login||{required:null,lastChecked:null,monitorTabId:null,pausedByLogin:[]}}}}},{key:"getGraphExecutionStatus",value:function(e){return this.executionService?this.executionService.getGraphStatus(e):{found:!1,error:"Execution service not initialized"}}},{key:"getAllExecutionsStatus",value:function(){return this.executionService?this.executionService.getAllExecutionStatus():{error:"Execution service not initialized"}}},{key:"cancelGraphExecution",value:(i=ce(ie().m(function e(t){var n,r;return ie().w(function(e){for(;;)switch(e.p=e.n){case 0:if(this.executionService){e.n=1;break}return e.a(2,{success:!1,message:"Execution service not initialized"});case 1:return e.p=1,e.n=2,this.executionService.cleanupGraphTabs(t);case 2:return this.executionService.activeGraphs.has(t)&&((n=this.executionService.activeGraphs.get(t)).status="cancelled",n.endTime=new Date),console.log("[PrimitiveEngineController] Cancelled graph execution: ".concat(t)),e.a(2,{success:!0,message:"Graph execution cancelled: ".concat(t)});case 3:return e.p=3,r=e.v,console.error("[PrimitiveEngineController] Error cancelling graph execution: ".concat(t),r),e.a(2,{success:!1,message:"Failed to cancel graph execution: ".concat(r.message)})}},e,this,[[1,3]])})),function(e){return i.apply(this,arguments)})},{key:"pauseGraphExecution",value:function(e){if(!this.executionService)return{success:!1,message:"Execution service not initialized"};try{console.log("[PrimitiveEngineController] Pausing graph execution: ".concat(e));var t=this.executionService.pauseGraph(e);return t.success&&console.log("[PrimitiveEngineController] Successfully paused graph: ".concat(e)),t}catch(t){return console.error("[PrimitiveEngineController] Error pausing graph execution: ".concat(e),t),{success:!1,message:"Failed to pause graph execution: ".concat(t.message)}}}},{key:"resumeGraphExecution",value:function(e){if(!this.executionService)return{success:!1,message:"Execution service not initialized"};try{console.log("[PrimitiveEngineController] Resuming graph execution: ".concat(e));var t=this.executionService.resumeGraph(e);return t.success&&console.log("[PrimitiveEngineController] Successfully resumed graph: ".concat(e)),t}catch(t){return console.error("[PrimitiveEngineController] Error resuming graph execution: ".concat(e),t),{success:!1,message:"Failed to resume graph execution: ".concat(t.message)}}}},{key:"getConfiguration",value:(o=ce(ie().m(function e(){var t,n,r;return ie().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,h();case 1:return t=e.v,e.n=2,d();case 2:return n=e.v,r=m,e.a(2,{supportedSites:t,actionArguments:n,constants:r});case 3:return e.p=3,e.v,e.a(2,{supportedSites:{},actionArguments:{},constants:{}})}},e,null,[[0,3]])})),function(){return o.apply(this,arguments)})},{key:"cleanup",value:(r=ce(ie().m(function e(){return ie().w(function(e){for(;;)switch(e.n){case 0:this.clearRules(),this.graphs.clear(),this.nodeStatuses.clear(),this.parentStatusListeners.clear(),this.initialized=!1,this.activeTabs=0;case 1:return e.a(2)}},e,this)})),function(){return r.apply(this,arguments)})}],n&&ue(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r,o,i,a,s,c,u,l,f,p}(),he=fe;try{"undefined"!=typeof globalThis?globalThis.PrimitiveEngineController=fe:"undefined"!=typeof window?window.PrimitiveEngineController=fe:"undefined"!=typeof global?global.PrimitiveEngineController=fe:"undefined"!=typeof self&&(self.PrimitiveEngineController=fe)}catch(e){console.warn("Could not attach PrimitiveEngineController to global scope:",e)}}(),r.default}()});