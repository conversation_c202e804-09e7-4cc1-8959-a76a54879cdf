// Background script for the executor extension
import './lib/json-rule-engine-bundle.bundle.js';

// Function to get PrimitiveEngineController from global scope with fallbacks
function getPrimitiveEngineController() {
  // Try different global objects in order of preference
  try {
    if (typeof globalThis !== 'undefined' && globalThis.PrimitiveEngineController) {
      return globalThis.PrimitiveEngineController;
    }
    if (typeof self !== 'undefined' && self.PrimitiveEngineController) {
      return self.PrimitiveEngineController;
    }
    if (typeof window !== 'undefined' && window.PrimitiveEngineController) {
      return window.PrimitiveEngineController;
    }
    if (typeof global !== 'undefined' && global.PrimitiveEngineController) {
      return global.PrimitiveEngineController;
    }
  } catch (error) {
    console.warn('[Background] Error accessing global PrimitiveEngineController:', error);
  }
  
  return null;
}

// Wait for PrimitiveEngineController to be available
async function waitForPrimitiveEngineController(maxRetries = 10, delay = 100) {
  for (let i = 0; i < maxRetries; i++) {
    const controller = getPrimitiveEngineController();
    if (controller) {
      return controller;
    }
    
    // Wait before retrying
    await new Promise(resolve => setTimeout(resolve, delay));
  }
  
  throw new Error('PrimitiveEngineController not found in any global scope after waiting');
}


// Global engine controller instance
let engineController = null;
let initializing = false;

// Global timeout settings variable
let timeoutSettings = {
  background_test_timeout_seconds: 10
};

// Load timeout settings from shared config
async function loadTimeoutSettings() {
  try {
    const response = await fetch(chrome.runtime.getURL('lib/shared-config.json'));
    const config = await response.json();
    if (config.TIMEOUT_SETTINGS) {
      timeoutSettings = { ...timeoutSettings, ...config.TIMEOUT_SETTINGS };
      console.log('[Background] Loaded timeout settings:', timeoutSettings);
    }
  } catch (error) {
    console.warn('[Background] Failed to load timeout config, using defaults:', error);
  }
}

/**
 * Initialize the background script
 */
async function initializeBackground() {
  if (initializing) {
    return; // Prevent concurrent inits
  }
  if (engineController) {
    return; // Already initialized
  }
  initializing = true;
  try {
    console.log('🚀 Initializing background script...');

    // Load timeout settings
    await loadTimeoutSettings();

    // Wait for PrimitiveEngineController to be available
    const PrimitiveEngineController = await waitForPrimitiveEngineController();
    console.log('✅ PrimitiveEngineController found in global scope');

    // Initialize engine controller
    engineController = new PrimitiveEngineController();
    await engineController.initialize();

    console.log('✅ Background script initialized successfully');
  } catch (error) {
    console.error('❌ Error initializing background script:', error);
  }
  finally {
    initializing = false;
  }
}

/**
 * Handle execution requests using node-by-node execution
 */
async function handleExecutionRequest(request) {
  try {
    const { actions, automationMode } = request;
    if (!actions || !Array.isArray(actions)) {
      return { success: false, message: 'Invalid actions array' };
    }

    console.log('🔍 Execution request details:', {
      siteId: request.siteId,
      tabId: request.tabId,
      automationMode: automationMode || 'currentTab',
      actionsCount: actions.length
    });

    // Initialize engine controller if not already done
    if (!engineController) {
      await initializeBackground();
    }

    // Create a unique graph ID for execution
    const tempGraphId = `graph_${crypto.randomUUID()}_${Date.now()}`;

    // Execute using node-by-node execution through the engine controller
    const result = await engineController.executeActionArray(tempGraphId, actions, {
      siteId: request.siteId,
      tabId: request.tabId,
      reuseTab: true,
      stopOnError: true,
      failDependentsOnError: true
    });

    return result;
  } catch (error) {
    console.error('Error in node-by-node execution:', error);
    return { success: false, message: `Error: ${error?.message || error}` };
  }
}



/**
 * Handle messages from popup and content scripts
 * Only supports node-by-node execution actions
 */
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('📨 Received message:', request.type);

  switch (request.type) {
    case 'EXECUTE_ACTIONS':
    case 'RULE_ENGINE_EXECUTE_ACTIONS':
      // Handle node-by-node execution requests
      handleExecutionRequest(request).then(sendResponse);
      return true; // Keep message channel open for async response

    case 'GET_ENGINE_STATUS':
      // Get engine status for testing
      if (engineController) {
        sendResponse(engineController.getStatus());
      } else {
        sendResponse({ success: false, message: 'Engine controller not initialized' });
      }
      break;

    case 'GET_EXECUTION_STATUS':
      // Get execution service status for testing
      if (engineController) {
        sendResponse(engineController.getAllExecutionsStatus());
      } else {
        sendResponse({ success: false, message: 'Engine controller not initialized' });
      }
      break;

    case 'PAUSE_GRAPH':
      // Pause a graph execution
      if (engineController && request.graphId) {
        const result = engineController.pauseGraphExecution(request.graphId);
        sendResponse(result);
      } else {
        sendResponse({ success: false, message: 'Engine controller not initialized or graph ID missing' });
      }
      break;

    case 'RESUME_GRAPH':
      // Resume a graph execution
      if (engineController && request.graphId) {
        const result = engineController.resumeGraphExecution(request.graphId);
        sendResponse(result);
      } else {
        sendResponse({ success: false, message: 'Engine controller not initialized or graph ID missing' });
      }
      break;

    case 'CANCEL_GRAPH':
      // Cancel a graph execution
      if (engineController && request.graphId) {
        engineController.cancelGraphExecution(request.graphId).then(sendResponse);
        return true; // Keep message channel open for async response
      } else {
        sendResponse({ success: false, message: 'Engine controller not initialized or graph ID missing' });
      }
      break;

    default:
      sendResponse({ success: false, message: `Unknown message type: ${request.type}. Only execution actions are supported.` });
  }
});



/**
 * Initialize background script when extension loads
 */
chrome.runtime.onInstalled.addListener(() => {
  console.log('🔧 Extension installed, initializing background script...');
  initializeBackground();
});

chrome.runtime.onStartup.addListener(() => {
  console.log('🚀 Extension started, initializing background script...');
  initializeBackground();
});

// Initialize immediately if already running
if (chrome.runtime) {
  initializeBackground();
}

chrome.action.onClicked.addListener((tab) => {
  // Use the tabId from the tab that was clicked to open the side panel
  chrome.sidePanel.open({ tabId: tab.id });
});
