// Background script for the executor extension
console.log("🚀 [DEBUG] Background script starting...");

// Load PouchDB for execution polling
console.log("🔧 [DEBUG] Loading PouchDB...");
try {
  importScripts("lib/pouchdb.min.js");
  console.log("✅ [DEBUG] PouchDB loaded successfully");
} catch (error) {
  console.error("❌ [DEBUG] Failed to load PouchDB:", error);
}

// Load importScripts-compatible executor modules
console.log("🔧 [DEBUG] Loading executor modules...");
try {
  // Load the unified bundle which exposes globals expected by background.js
  importScripts("lib/json-rule-engine-bundle.bundle.js");
  console.log("✅ [DEBUG] Executor modules loaded successfully");
} catch (error) {
  console.error("❌ [DEBUG] Failed to load executor modules:", error);
}

// PouchDB variables for execution polling
let localDB = null;
let remoteDB = null;
let changeListener = null; // Track the change listener

// Global engine controller instance
let engineController = null;
let initializing = false;

// Global settings
let timeoutSettings = { background_test_timeout_seconds: 10 };
let executionDefaults = {
  max_concurrent_tabs: 2,
  stop_on_error: true,
  fail_dependents_on_error: true,
};

// Cache of login status for UI dedupe and initial load
let cachedLoginStatus = {
  required: null,
  brokerName: null,
  updatedAt: null,
};

// Load execution and timeout settings from shared config
async function loadTimeoutSettings() {
  try {
    const response = await fetch(
      chrome.runtime.getURL("lib/shared-config.json")
    );
    const config = await response.json();
    if (config.TIMEOUT_SETTINGS) {
      timeoutSettings = { ...timeoutSettings, ...config.TIMEOUT_SETTINGS };
      console.log("[Background] Loaded timeout settings:", timeoutSettings);
    }
    if (config.EXECUTION_SERVICE) {
      executionDefaults = {
        ...executionDefaults,
        max_concurrent_tabs: Number.isFinite(
          config.EXECUTION_SERVICE.max_concurrent_tabs
        )
          ? config.EXECUTION_SERVICE.max_concurrent_tabs
          : executionDefaults.max_concurrent_tabs,
        stop_on_error: config.EXECUTION_SERVICE.stop_on_error !== false,
        fail_dependents_on_error:
          config.EXECUTION_SERVICE.fail_dependents_on_error !== false,
      };
      console.log("[Background] Loaded execution defaults:", executionDefaults);
    }
  } catch (error) {
    console.warn(
      "[Background] Failed to load timeout config, using defaults:",
      error
    );
  }
}

/**
 * Initialize PouchDB for execution polling
 */

// SHA-256 hash generator
async function generateHash(message) {
  // Convert the string to a Uint8Array
  const msgUint8 = new TextEncoder().encode(message);
  // Hash the message
  const hashBuffer = await crypto.subtle.digest("SHA-256", msgUint8);
  // Convert the buffer to a hex string
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray
    .map((b) => b.toString(16).padStart(2, "0"))
    .join("");
  return hashHex;
}

const CACHE_NAME = "user-data-cache-v1";
// Store data in the cache
async function storeDataInCache(key, value) {
  await chrome.storage.local.set({ [key]: value });
}

// Retrieve data from the cache
async function getDataFromCache(key) {
  try {
    const data = await chrome.storage.local.get(key);
    // The data is returned as an object, so you access your value by key
    const value = data[key];

    if (value !== undefined) {
      return value;
    } else {
      console.log(`No string found for key "${key}".`);
      return null;
    }
  } catch (error) {
    console.error("Error retrieving string:", error);
    return null;
  }
}

async function initializePouchDB() {
  try {
    console.log("🔧 [DEBUG] Initializing PouchDB...");
    // Get "firebase_uid" from cache
    const firebaseUID = await getDataFromCache("firebase_uid");
    if (!firebaseUID) {
      console.warn(
        "Waiting for login since there is no firebase_uid in localStorage yet."
      );
      return false;
    }
    const manifest = chrome.runtime.getManifest();
    const couchdbUrl = manifest.aagman_config.couchdb_url;
    const couchdbDbPrefix = manifest.aagman_config.couchdb_db_prefix;

    // Use the same database names as the frontend (with proper hashing)
    const localDBName = `execution_${firebaseUID}_local`;
    const hash = await generateHash(firebaseUID);
    const remoteDBName = `${couchdbDbPrefix}${hash}`;

    console.log("🔧 [DEBUG] Using database names:");
    console.log("🔧 [DEBUG] - Local:", localDBName);
    console.log("🔧 [DEBUG] - Firebase UID:", firebaseUID);
    console.log("🔧 [DEBUG] - Hash:", hash);
    console.log("🔧 [DEBUG] - Remote:", `${couchdbUrl}/${remoteDBName}`);

    localDB = new PouchDB(localDBName);
    remoteDB = new PouchDB(`${couchdbUrl}/${remoteDBName}`);

    console.log("✅ [DEBUG] PouchDB databases initialized");

    // Set up sync
    const sync = localDB.sync(remoteDB, {
      live: true,
      retry: true,
    });

    sync
      .on("change", function (change) {
        console.log("🔄 [SYNC] Change detected:", change.direction);
      })
      .on("error", function (err) {
        console.error("❌ [SYNC] Sync error:", err);
      });

    console.log("✅ [DEBUG] PouchDB sync initialized");
    return true;
  } catch (error) {
    console.error("❌ [DEBUG] PouchDB initialization failed:", error);
    return false;
  }
}

/**
 * Start monitoring PouchDB for execution requests
 */
function startExecutionMonitoring() {
  if (!localDB) {
    console.error("❌ [EXEC] PouchDB not initialized, cannot start monitoring");
    return;
  }

  // Cancel existing listener if it exists
  if (changeListener) {
    console.log("🔄 [EXEC] Canceling existing change listener...");
    changeListener.cancel();
    changeListener = null;
  }

  console.log("🎯 [EXEC] Starting execution monitoring...");

  // One-time backfill: process any existing pending execution requests
  (async () => {
    try {
      const all = await localDB.allDocs({ include_docs: true });
      const pendingExecs = (all.rows || [])
        .map((r) => r.doc)
        .filter(
          (d) => d && d.type === "execution_request" && d.status === "pending"
        );
      if (pendingExecs.length > 0) {
        console.log(
          `📋 [EXEC] Found ${pendingExecs.length} existing pending execution requests. Processing once...`
        );
      }
      for (const doc of pendingExecs) {
        try {
          handlePouchDBExecution(doc);
        } catch (_) {}
      }
    } catch (e) {
      console.warn(
        "⚠️ [EXEC] Failed to backfill pending execution requests:",
        e
      );
    }
  })();

  changeListener = localDB.changes({
    since: "now", // Avoid replaying old changes on each wake
    live: true,
    include_docs: true,
  });

  changeListener.on("change", (change) => {
    console.log("🎯 [POUCH-MONITOR] Change detected in local PouchDB:", {
      id: change.id,
      seq: change.seq,
      deleted: change.deleted,
      doc_type: change.doc?.type,
      status: change.doc?.status,
    });
    if (change.doc && change.doc.type === "execution_request") {
      if (change.doc.status === "pending") {
        console.log(
          "🚀 [POUCH-MONITOR] Pending execution request detected:",
          change.doc._id
        );
        console.log(
          "🔄 [POUCH-MONITOR] Routing to PouchDB execution (EXECUTE_ACTIONS & RULE_ENGINE_EXECUTE_ACTIONS bypassed)"
        );
        handlePouchDBExecution(change.doc);
      } else {
        console.log(
          "⏭️ [POUCH-MONITOR] Skipping execution request due to status:",
          change.doc.status
        );
      }
    }
  });

  console.log("✅ [EXEC] Execution monitoring started");
}

// Sanitize broker order id from UI (remove leading '#' and surrounding spaces)
function cleanOrderId(val) {
  try {
    const s = (val ?? "").toString().trim();
    return s.startsWith("#") ? s.slice(1).trim() : s;
  } catch (_) {
    return (val ?? "").toString();
  }
}

/**
 * Normalize action arguments to canonical keys and types
 */
function normalizeActionArguments(action) {
  try {
    const args = action?.arguments || {};

    const pickFirst = (...candidates) => {
      for (const c of candidates) {
        if (c !== undefined && c !== null) return c;
      }
      return undefined;
    };

    const rawSymbol = pickFirst(
      args.symbol,
      args.SYMBOL,
      action?.SYMBOL,
      action?.symbol
    );
    const rawExchange = pickFirst(
      args.exchange,
      args.EXCHANGE,
      action?.exchange,
      action?.EXCHANGE
    );
    const rawQuantity = pickFirst(
      args.quantity,
      args.QUANTITY,
      action?.QUANTITY,
      action?.quantity
    );
    const rawPrice = pickFirst(
      args.price,
      args.PRICE,
      action?.PRICE,
      action?.price
    );
    const rawProductType = pickFirst(
      args.productType,
      args.PRODUCT_TYPE,
      action?.PRODUCT_TYPE,
      action?.productType
    );

    const toNumberOrZero = (val) => {
      const n = Number(val);
      return Number.isFinite(n) ? n : 0;
    };

    return {
      action: action.action.toLowerCase(),
      symbol: (rawSymbol.toLowerCase() ?? "UNKNOWN").toString(),
      exchange: rawExchange ? rawExchange.toString() : undefined,
      quantity: toNumberOrZero(rawQuantity),
      price: toNumberOrZero(rawPrice),
      productType: (rawProductType ?? "MIS").toString(),
    };
  } catch (_) {
    return {
      action: action.action.toLowerCase(),
      symbol: "UNKNOWN",
      exchange: undefined,
      quantity: 0,
      price: 0,
      productType: "MIS",
    };
  }
}

/**
 * Store execution results as order_result and monitoring_alert documents
 */
async function storeExecutionResults(executionResult, originalRequest) {
  if (!localDB) {
    console.warn(
      "⚠️ [POUCH-STORAGE] PouchDB not initialized, cannot store results"
    );
    return;
  }

  console.log(
    "🚀 [POUCH-STORAGE] ===== STARTING EXECUTION RESULTS STORAGE ====="
  );
  console.log("�� [POUCH-STORAGE] Updating execution report status...");
  console.log("📋 [POUCH-STORAGE] Original request ID:", originalRequest._id);
  console.log("📋 [POUCH-STORAGE] Firebase UID:", originalRequest.firebase_uid);
  console.log("📊 [POUCH-STORAGE] Execution success:", executionResult.success);
  console.log("📊 [POUCH-STORAGE] Execution message:", executionResult.message);

  const executionRequestId = originalRequest._id;

  try {
    // Update the execution request status to completed
    try {
      // Try to get existing execution request document
      const existingDoc = await localDB
        .get(executionRequestId)
        .catch(() => null);
      if (existingDoc) {
        console.log(
          "🔄 [POUCH-STORAGE] Execution request document exists, updating status..."
        );

        // Update only the status and add execution result
        const updatedDoc = {
          ...existingDoc,
          status: executionResult.success ? "completed" : "failed",
          execution_result: executionResult,
          updated_at: new Date().toISOString(),
        };

        await localDB.put(updatedDoc);
        console.log(
          "✅ [POUCH-STORAGE] Execution request status updated successfully!"
        );

        console.log("📊 [POUCH-STORAGE] New status:", updatedDoc.status);
      } else {
        console.log(
          "⚠️ [POUCH-STORAGE] Execution request document not found, cannot update status"
        );
      }
    } catch (updateError) {
      if (updateError.status === 409) {
        console.log(
          "⚠️ [POUCH-STORAGE] Conflict detected, retrying with fresh _rev..."
        );
        // Get fresh document and retry
        const freshDoc = await localDB.get(executionRequestId);
        const updatedDoc = {
          ...freshDoc,
          status: executionResult.success ? "completed" : "failed",
          execution_result: executionResult,
          updated_at: new Date().toISOString(),
        };

        await localDB.put(updatedDoc);
        console.log(
          "✅ [POUCH-STORAGE] Execution request status updated after conflict resolution!"
        );
      } else {
        throw updateError; // Re-throw if it's not a conflict error
      }
    }
  } catch (error) {
    console.error("❌ [POUCH-STORAGE] ===== STATUS UPDATE FAILED =====");
    console.error("❌ [POUCH-STORAGE] Error details:", error);
    console.error("❌ [POUCH-STORAGE] Error stack:", error.stack);
    console.error("❌ [POUCH-STORAGE] Request ID:", originalRequest._id);
  }
}

/**
 * Update order status by composite ID
 */
async function updateOrderStatus(
  executionRequestId,
  actionId,
  newStatus,
  additionalData = {}
) {
  if (!localDB) return;

  const docId = `order_${executionRequestId}_${actionId}`;

  try {
    const doc = await localDB.get(docId);
    const updatedDoc = {
      ...doc,
      status: newStatus,
      updated_at: new Date().toISOString(),
      ...additionalData,
    };

    await localDB.put(updatedDoc);
    console.log(
      "✅ [POUCH-STORAGE] Order status updated:",
      docId,
      "→",
      newStatus
    );
  } catch (error) {
    console.error("❌ [POUCH-STORAGE] Failed to update order status:", error);
  }
}

/**
 * Update monitoring alert progress
 */
async function updateMonitoringProgress(
  executionRequestId,
  actionId,
  currentPrice,
  progressPercent,
  status = "inProgress"
) {
  if (!localDB) return;

  const docId = `monitoring_${executionRequestId}_${actionId}`;

  try {
    const doc = await localDB.get(docId);
    const updatedDoc = {
      ...doc,
      currentPrice: currentPrice.toString(),
      progressPercent,
      status,
      updated_at: new Date().toISOString(),
    };

    await localDB.put(updatedDoc);
    console.log("✅ [POUCH-STORAGE] Monitoring progress updated:", docId);
  } catch (error) {
    console.error("❌ [POUCH-STORAGE] Failed to update monitoring:", error);
  }
}

/**
 * Upsert order_result docs using statuses from login monitor tab
 * - openOrders: orders shown in Pending table → broker_status + status=inProgress
 * - completedOrders: orders from Completed table → broker_status + status executed/cancelled
 */
async function upsertOrdersFromMonitor(openOrders = [], completedOrders = []) {
  if (!localDB) {
    console.warn("[POUCH-STORAGE] upsertOrdersFromMonitor: localDB not initialized");
    return { success: false, message: "PouchDB not initialised" };
  }

  try {
    const firebaseUID = await getDataFromCache("firebase_uid");
    if (!firebaseUID) {
      return { success: false, message: "Missing firebase_uid in cache" };
    }

    try {
      const oc = Array.isArray(openOrders) ? openOrders.length : 0;
      const cc = Array.isArray(completedOrders) ? completedOrders.length : 0;
      const oids = (openOrders || []).filter(r => !!cleanOrderId(r?.detailedInfo?.orderId)).length;
      const coids = (completedOrders || []).filter(r => !!cleanOrderId(r?.detailedInfo?.orderId)).length;
      console.log(`[POUCH-STORAGE] Monitor upsert start: open=${oc} (withId=${oids}), completed=${cc} (withId=${coids})`);
    } catch (_) {}

    const normalize = (s) => (s || "").toString().trim().toUpperCase();
    const toTradeType = (t) => (normalize(t).includes("BUY") ? "BUY" : "SELL");
    const buildKey = (symbol, type) => `${normalize(symbol)}|${toTradeType(type)}`;

    // Build monitor lookups by orderId and by composite key
    const byOrderId = new Map(); // orderId -> { row, completed: boolean }
    const byComposite = new Map(); // SYMBOL|TRADETYPE -> Array<{ row, completed }>

    const pushComposite = (row, completed) => {
      const key = buildKey(row.symbol, row.type);
      const arr = byComposite.get(key) || [];
      arr.push({ row, completed });
      byComposite.set(key, arr);
    };

    for (const row of openOrders || []) {
      const oid = cleanOrderId(row?.detailedInfo?.orderId);
      if (oid) byOrderId.set(oid, { row, completed: false });
      pushComposite(row, false);
    }
    for (const row of completedOrders || []) {
      const oid = cleanOrderId(row?.detailedInfo?.orderId);
      if (oid) byOrderId.set(oid, { row, completed: true });
      pushComposite(row, true);
    }

    // Fetch all order_result docs for this user
    const all = await localDB.allDocs({ include_docs: true, startkey: "order_", endkey: "order_\uffff" });
    const candidateDocs = (all.rows || [])
      .map((r) => r.doc)
      .filter((d) => d && d.type === "order_result" && d.firebase_uid === firebaseUID && d.status === "inProgress");
    console.log(`[POUCH-STORAGE] In-progress order docs to consider: ${candidateDocs.length}`);

    let updatedCount = 0;
    for (const doc of candidateDocs) {
      try {
        console.log("[POUCH-STORAGE] Evaluating doc for monitor upsert:", {
          id: doc._id,
          symbol: doc.symbol,
          tradeType: doc.tradeType,
          product: doc.product,
          orderType: doc.orderType,
          quantity: doc.quantity,
          status: doc.status,
          broker_order_id: doc.broker_order_id
        });
      } catch (_) {}
      // 1) Try strict orderId match first
      let source = null;
      let isCompleted = false;
      let matchedVia = null;
      if (doc.broker_order_id) {
        const found = byOrderId.get(cleanOrderId(doc.broker_order_id));
        if (found) {
          source = found.row;
          isCompleted = found.completed;
          matchedVia = "orderId";
        } else {
          console.warn("[POUCH-STORAGE] broker_order_id present on doc but not found in monitor snapshot", {
              id: doc._id,
            broker_order_id: doc.broker_order_id
          });
        }
      }
      // 2) If no orderId match, try to assign from monitor row and then use it
      if (!source) {
        const candidates = byComposite.get(buildKey(doc.symbol, doc.tradeType)) || [];
        if (candidates.length === 1) {
          source = candidates[0].row;
          isCompleted = candidates[0].completed;
          matchedVia = "composite:single";
        } else {
          // Narrow down using product/orderType/quantity and price/avgPrice where available
          const narrowed = candidates.filter(({ row }) => {
            const productMatch = !doc.product || normalize(row.product) === normalize(doc.product);
            const orderTypeMatch = !doc.orderType || normalize(row.detailedInfo?.orderType || row.orderType) === normalize(doc.orderType);
            const qtyTotal = Number(row.quantity?.total ?? row.quantity);
            const qtyMatch = !doc.quantity || Number(doc.quantity) === qtyTotal;
            const priceNum = Number(row.avgPrice ?? row.price ?? row.ltp);
            const priceMatch = !doc.price || Number(doc.price) === priceNum;
            return productMatch && orderTypeMatch && qtyMatch && priceMatch;
          });
          console.log("[POUCH-STORAGE] Composite candidates:", { total: candidates.length, narrowed: narrowed.length });
          if (narrowed.length === 1) {
            source = narrowed[0].row;
            isCompleted = narrowed[0].completed;
            matchedVia = "composite:narrowed";
          } else if (narrowed.length > 1) {
            console.warn("[POUCH-STORAGE] Multiple potential matches after narrowing; skipping doc", { id: doc._id });
          }
        }
      }
      if (!source) continue; // Could not safely match

      const brokerStatus = (source.status || "").toString().trim();
      // Map status only via monitor: keep inProgress for open; completed -> executed/cancelled
      const s = brokerStatus.toUpperCase();
      let mappedStatus = doc.status || "inProgress";
      if (isCompleted) {
        if (s.includes("CANCEL") || s.includes("REJECT")) mappedStatus = "cancelled";
        else mappedStatus = "executed";
      } else {
        mappedStatus = "inProgress";
      }
      try {
        console.log("[POUCH-STORAGE] Monitor match decided:", {
          id: doc._id,
          matchedVia,
          broker_status: brokerStatus,
          from: doc.status,
          to: mappedStatus
        });
      } catch (_) {}

      const update = {
        ...doc,
        broker_status: brokerStatus,
        status: mappedStatus,
        // Attach any identifiers we could extract (sanitized)
        broker_order_id: doc.broker_order_id || cleanOrderId(source?.detailedInfo?.orderId) || "",
        execution_details: {
          ...(doc.execution_details || {}),
          monitor_snapshot: source,
        },
        updated_at: new Date().toISOString(),
      };

      try {
        await localDB.put(update);
        updatedCount++;
        console.log("[POUCH-STORAGE] In-progress order updated from monitor", { id: doc._id, matchedVia });
      } catch (e) {
        if (e && e.status === 409) {
          const fresh = await localDB.get(doc._id);
          await localDB.put({ ...fresh, ...update, _rev: fresh._rev, updated_at: new Date().toISOString() });
          updatedCount++;
          console.log("[POUCH-STORAGE] Update after conflict (monitor)", { id: doc._id, matchedVia });
        } else {
          console.warn("[POUCH-STORAGE] Failed to upsert from monitor for doc:", doc._id, e);
        }
      }
    }

    return { success: true, updated: updatedCount };
  } catch (error) {
    console.error("[POUCH-STORAGE] upsertOrdersFromMonitor failed:", error);
    return { success: false, message: error?.message || String(error) };
  }
}

globalThis.__upsertOrdersFromMonitor = upsertOrdersFromMonitor;

/**
 * Handle execution requests from PouchDB (REPLACES Chrome runtime API for all execution)
 */
async function handlePouchDBExecution(executionDoc) {
  try {
    console.log(
      "🚀 [POUCH-EXEC] Processing execution request:",
      executionDoc._id
    );
    console.log(
      "🔄 [POUCH-EXEC] Chrome runtime API bypassed - using PouchDB model"
    );

    if (executionDoc.primitives && executionDoc.primitives.length > 0) {
      console.log(
        "📋 [POUCH-EXEC] Executing primitives:",
        executionDoc.primitives.length
      );
      console.log("🎯 [POUCH-EXEC] Primitives:", executionDoc.primitives);

      // Use real execution logic for all PouchDB execution requests
      // This will attempt real DOM automation and fail properly if not on correct site
      console.log(
        "🔄 [POUCH-EXEC] Using real execution path with ExecutionService"
      );
      // Mark the execution request as inProgress before invoking the engine
      try {
        if (localDB && executionDoc && executionDoc._id) {
          // Guard: Only proceed if currently pending
          if (executionDoc.status !== "pending") {
            console.log(
              "⏭️ [POUCH-EXEC] Execution doc is not pending (",
              executionDoc.status,
              ") — skipping execution."
            );
            return; // Do not execute again
          }
          let updatedDoc = {
            ...executionDoc,
            status: "inProgress",
            updated_at: new Date().toISOString(),
          };
          try {
            await localDB.put(updatedDoc);
          } catch (err) {
            if (err && err.status === 409) {
              const latest = await localDB.get(executionDoc._id);
              if (latest.status !== "pending") {
                console.log(
                  "⏭️ [POUCH-EXEC] Latest doc status is not pending (",
                  latest.status,
                  ") — skipping execution."
                );
                return; // Another worker already progressed it
              }
              updatedDoc = {
                ...latest,
                status: "inProgress",
                updated_at: new Date().toISOString(),
              };
              await localDB.put(updatedDoc);
            } else {
              throw err;
            }
          }
          console.log(
            "📌 [POUCH-EXEC] Marked execution request inProgress:",
            executionDoc._id
          );
        } else {
          console.warn(
            "⚠️ [POUCH-EXEC] Cannot mark inProgress - localDB or _id missing"
          );
        }
      } catch (markErr) {
        console.warn(
          "⚠️ [POUCH-EXEC] Failed to mark execution as inProgress:",
          markErr
        );
        return; // Safety: don't proceed if we couldn't secure inProgress
      }
      const result = await handleExecutionRequest({
        actions: executionDoc.primitives,
        automationMode:
          executionDoc.automationMode || globalThis.AUTOMATION_MODES.BACKGROUND,
        siteId: executionDoc.siteId || "kiteByZerodha", // Default to Zerodha
        tabId: executionDoc.tabId,
        graphId: executionDoc._id,
        firebase_uid: executionDoc.firebase_uid,
      });

      console.log(
        "✅ [POUCH-EXEC] Execution completed for:",
        executionDoc._id,
        "Result:",
        result
      );

      // Store execution results in PouchDB
      await storeExecutionResults(result, executionDoc);
    } else {
      console.warn("⚠️ [POUCH-EXEC] No primitives found in execution request");
    }
  } catch (error) {
    console.error("❌ [POUCH-EXEC] Execution failed:", error);
  }
}

/**
 * Initialize the background script
 */
async function initializeBackground() {
  try {
    // Initialize PouchDB for execution polling
    if (!(await initializePouchDB())) {
      console.warn(
        "PouchDB was not initialised. Will wait for the next invocation."
      );
      return;
    }
  } catch {}

  if (initializing) {
    console.warn("Alread being initialised.");
    return; // Prevent concurrent inits
  }
  if (engineController) {
    console.warn("Alread initialised.");
    return; // Already initialized
  }

  initializing = true;
  try {
    console.log("🚀 Initializing background script...");

    // Load timeout settings
    await loadTimeoutSettings();

    // Initialize the real EngineController
    try {
      if (typeof globalThis.EngineController !== "undefined") {
        console.log("🔧 [DEBUG] Creating EngineController instance...");
        engineController = new globalThis.EngineController();
        await engineController.initialize();
        console.log(
          "✅ [DEBUG] Real EngineController initialized successfully"
        );
        try {
          engineController.executionService.startLoginMonitor();
        } catch (e) {
          console.warn("Failed to start login monitor on init:", e);
        }
      } else {
        throw new Error("EngineController not found in global scope");
      }
    } catch (engineError) {
      console.error(
        "❌ [DEBUG] Engine controller initialization failed:",
        engineError
      );
      // Create a fallback mock
      engineController = {
        executeActionArray: async (graphId, actions) => {
          console.log(
            "🔄 [FALLBACK] Using fallback execution for:",
            actions.length,
            "actions"
          );
          return {
            success: false,
            message: "Engine controller initialization failed",
            error: engineError.message,
          };
        },
      };
    }
    startExecutionMonitoring();

    console.log("✅ Background script initialized successfully");
  } catch (error) {
    console.error("❌ Error initializing background script:", error);
  } finally {
    initializing = false;
  }
}

/**
 * Close previous version's monitor tab using stored tabId
 */
async function closeExistingMonitorTabFromStorage() {
  try {
    const data = await new Promise((res) =>
      chrome.storage.local.get(["loginMonitorTabId"], res)
    );
    const id = data?.loginMonitorTabId;
    if (id) {
      try {
        await chrome.tabs.remove(id);
      } catch (_) {}
      try {
        chrome.storage.local.remove("loginMonitorTabId");
      } catch (_) {}
    }
  } catch (_) {}
}

/**
 * Fallback: close pinned Orders tabs if storage is missing/stale
 */
async function closePinnedOrdersTabs() {
  try {
    const tabs = await chrome.tabs.query({
      pinned: true,
      url: "https://kite.zerodha.com/*",
    });
    for (const t of tabs || []) {
      if ((t.url || "").includes("/orders")) {
        try {
          await chrome.tabs.remove(t.id);

        } catch (_) {}

      }
    }
  } catch (_) {}
}

/**
 * Handle execution requests using node-by-node execution
 */
async function handleExecutionRequest(request) {
  try {
    // Initialize engine controller if not already done
    if (!engineController) {
      console.warn("engineController is not initialised.");
      await initializeBackground();
    }
    const { actions, automationMode } = request;
    if (!actions || !Array.isArray(actions)) {
      return { success: false, message: "Invalid actions array" };
    }

    // For traditional execution, we'll use the execution service directly
    // This maintains backward compatibility
    let executionService;
    try {
      if (typeof globalThis.ExecutionService !== "undefined") {
        executionService = new globalThis.ExecutionService();
      } else if (typeof ExecutionService !== "undefined") {
        executionService = new ExecutionService();
      } else {
        console.warn(
          "⚠️ [DEBUG] ExecutionService not available, using fallback"
        );
        return {
          success: false,
          message: "ExecutionService not available in service worker",
        };
      }
    } catch (error) {
      console.error(
        "❌ [DEBUG] ExecutionService initialization failed:",
        error
      );
      return {
        success: false,
        message: "ExecutionService initialization failed",
      };
    }

    // Get action arguments configuration
    let actionArgumentsConfig;
    try {
      if (typeof globalThis.getActionArguments !== "undefined") {
        actionArgumentsConfig = await globalThis.getActionArguments();
      } else if (typeof getActionArguments !== "undefined") {
        actionArgumentsConfig = await getActionArguments();
      } else {
        console.warn(
          "⚠️ [DEBUG] getActionArguments not available, using fallback"
        );
        actionArgumentsConfig = {}; // Fallback empty config
      }
    } catch (error) {
      console.error("❌ [DEBUG] getActionArguments failed:", error);
      actionArgumentsConfig = {}; // Fallback empty config
    }

    // Convert siteId to site configuration
    let site = null;
    if (request.siteId) {
      try {
        let supportedSites;
        if (typeof globalThis.getSupportedSites !== "undefined") {
          supportedSites = await globalThis.getSupportedSites();
        } else if (typeof getSupportedSites !== "undefined") {
          supportedSites = await getSupportedSites();
        } else {
          console.warn(
            "⚠️ [DEBUG] getSupportedSites not available, using fallback"
          );
          return {
            success: false,
            message: "getSupportedSites not available in service worker",
          };
        }

        site = supportedSites[request.siteId];
        if (!site) {
          return {
            success: false,
            message: `Unsupported site: ${request.siteId}`,
          };
        }
        // Add the siteId to the site object for reference
        site.siteId = request.siteId;
      } catch (error) {
        console.error("❌ [DEBUG] getSupportedSites failed:", error);
        return { success: false, message: "Failed to get supported sites" };
      }
    }

    console.log("🔍 Execution request details:", {
      siteId: request.siteId,
      tabId: request.tabId,
      automationMode: automationMode || "currentTab",
      actionsCount: actions.length,
    });

    // TODO: Remove this once we have a proper graph ID and add uuid to the graph ID
    const tempGraphId = request.graphId;

    // Map actions by id for quick lookup in callback
    const actionsById = new Map(actions.map((a) => [a.id, a]));

    // Execute the action array using the rule engine
    // Pre-create per-order docs with status inProgress so UI can render immediately
    try {
      for (const action of actions) {
        const normalizedArgs = normalizeActionArguments(action);
        const isOrderAction = [
          "buy",
          "sell",
          "placebuylimitorder",
          "placeselllimitorder",
          "placebuystoplossmarketorder",
          "placesellstoplossmarketorder",
          "placebuystoplosslimitorder",
          "placesellstoplosslimitorder",
        ].includes((normalizedArgs.action || "").toLowerCase());
        if (!isOrderAction) continue;

        const docId = `order_${tempGraphId}_${action.id}`;
        const nowIso = new Date().toISOString();
        const baseDoc = {
          _id: docId,
          type: "order_result",
          firebase_uid: request.firebase_uid,
          execution_request_id: tempGraphId,
          action_id: action.id,
          id: docId,
          symbol: normalizedArgs.symbol,
          tradeType: (action.action || "").includes("BUY") ? "BUY" : "SELL",
          quantity: (normalizedArgs.quantity || 0).toString(),
          price: (normalizedArgs.price || 0).toString(),
          status: "inProgress",
          timestamp: nowIso,
          broker: "zerodha",
          product: normalizedArgs.productType,
          orderType: (action.action || "").includes("Limit")
            ? "LIMIT"
            : "MARKET",
          broker_order_id: "",
          execution_details: {},
          created_at: nowIso,
          updated_at: nowIso,
        };

        try {
          const existing = await localDB.get(docId).catch(() => null);
          const toPut = existing
            ? {
                ...existing,
                ...baseDoc,
                _rev: existing._rev,
                updated_at: nowIso,
              }
            : baseDoc;
          await localDB.put(toPut);
          console.log(
            `✅ [POUCH-STORAGE] Order pre-upserted (inProgress):`,
            docId
          );
        } catch (e) {
          console.error("❌ [POUCH-STORAGE] Pre-upsert failed:", e);
        }
      }
    } catch (e) {
      console.warn(
        "⚠️ [POUCH-STORAGE] Failed to pre-create inProgress order docs:",
        e
      );
    }

    const result = await engineController.executeActionArray(
      tempGraphId,
      actions,
      {
        siteId: request.siteId,
        tabId: request.tabId,
        reuseTab: true,
        closeAfterNode: true,
        stopOnError: executionDefaults.stop_on_error,
        failDependentsOnError: executionDefaults.fail_dependents_on_error,
        maxParallel: executionDefaults.max_concurrent_tabs,
      },

      async (graphId, actionId, status, info = {}) => {
        try {
          const action = actionsById.get(actionId);
          if (!action || !localDB) return;

          const normalizedArgs = normalizeActionArguments(action);
          const nowIso = new Date().toISOString();
          const firebaseUid = request.firebase_uid;

          const isOrderAction = [
            "buy",
            "sell",
            "placebuylimitorder",
            "placeselllimitorder",
            "placebuystoplossmarketorder",
            "placesellstoplossmarketorder",
            "placebuystoplosslimitorder",
            "placesellstoplosslimitorder",
          ].includes(normalizedArgs.action);

          if (isOrderAction) {
            const docId = `order_${graphId}_${actionId}`;
            const contentResult=info?.resultDetail || info?.result ||null;
            try {
              console.log('[POUCH-STORAGE] Node callback result:', {
                actionId,
                nodeStatus: status,
                contentStatus: contentResult?.status,
                orderId: cleanOrderId(contentResult?.orderId)
              });
            } catch (_) {}
            const immediateMappedStatus = (() => {
              try {
                const cs = (contentResult?.status || '').toString().toUpperCase();
                const ns = (status || '').toString().toLowerCase();
                if (cs.includes('CANCEL') || cs.includes('REJECT') || ns === 'cancelled') return 'cancelled';
              } catch (_) {}
              return 'inProgress';
            })();

            const baseDoc = {
              _id: docId,
              type: "order_result",
              firebase_uid: firebaseUid,

              // Tracking
              execution_request_id: graphId,
              action_id: actionId,

              // Frontend-compatible
              id: docId,
              symbol: normalizedArgs.symbol,
              tradeType: (normalizedArgs.action || "").includes("buy")
                ? "BUY"
                : "SELL",
              quantity: (normalizedArgs.quantity || 0).toString(),
              price: (normalizedArgs.price || 0).toString(),
              // Immediate cancellation on the spot if detected; otherwise keep inProgress
              status: immediateMappedStatus,

              timestamp: nowIso,
              broker: "zerodha",
              broker_status: contentResult?.status || "",
              product: normalizedArgs.productType,
              orderType: (action.action || "").includes("Limit")
                ? "LIMIT"
                : "MARKET",

              // Additional placeholders (will be enriched later if needed)
              broker_order_id: cleanOrderId(contentResult?.orderId),

              execution_details: {
                node_status: status,
                node_result: contentResult,
              },
              created_at: nowIso,
              updated_at: nowIso,
            };

            try {
              const existing = await localDB.get(docId).catch(() => null);
              const toPut = existing
                ? {
                    ...existing,
                    ...baseDoc,
                    _rev: existing._rev,
                    updated_at: nowIso,
                  }

                : baseDoc;
              await localDB.put(toPut);
              console.log(
                `✅ [POUCH-STORAGE] Order upserted (${status}):`,
                docId
              );
            } catch (e) {
              if (e.status === 409) {
                const fresh = await localDB.get(docId);
                await localDB.put({
                  ...fresh,
                  ...baseDoc,
                  _rev: fresh._rev,
                  updated_at: nowIso,
                });
                console.log(
                  `✅ [POUCH-STORAGE] Order conflict resolved:`,
                  docId
                );
              } else {
                console.error("❌ [POUCH-STORAGE] Order upsert failed:", e);
              }
            }
            return;
          }
          console.log("normalizedArgs to debug=>", normalizedArgs)
          if (
            (normalizedArgs.action || "").toUpperCase() ===
            "MONITORCONDITIONTHENACT"
          ) {
            const condition =
              action.condition || action.arguments?.condition || {};
            const onTrigger =
              action.on_trigger || action.arguments?.on_trigger || {};

            const docId = `monitoring_${graphId}_${actionId}`;
            const baseDoc = {
              _id: docId,
              type: "monitoring_alert",
              firebase_uid: firebaseUid,

              // Tracking
              execution_request_id: graphId,
              action_id: actionId,

              // Frontend-compatible
              id: docId,
              description: `Monitor ${condition.symbol || "UNKNOWN"} ${
                condition.operator || "condition"
              } ${condition.value || ""}`,

              symbol: condition.symbol || "UNKNOWN",
              triggerPrice: (condition.value || 0).toString(),
              currentPrice: "0",
              progress: "0/0",
              progressPercent: 0,
              status:
                status === "completed" || status === "running"
                  ? "inProgress"
                  : "stopped",

              orderType: onTrigger.action || "MARKET",
              stopLoss: "",
              product:
                onTrigger.PRODUCT_TYPE ||
                onTrigger.arguments?.PRODUCT_TYPE ||
                "MIS",

              // Original data
              condition,
              onTrigger,

              created_at: nowIso,
              updated_at: nowIso,
            };

            try {
              const existing = await localDB.get(docId).catch(() => null);
              const toPut = existing
                ? {
                    ...existing,
                    ...baseDoc,
                    _rev: existing._rev,
                    updated_at: nowIso,
                  }

                : baseDoc;
              await localDB.put(toPut);
              console.log(
                `✅ [POUCH-STORAGE] Monitoring upserted (${status}):`,
                docId
              );
            } catch (e) {
              if (e.status === 409) {
                const fresh = await localDB.get(docId);
                await localDB.put({
                  ...fresh,
                  ...baseDoc,
                  _rev: fresh._rev,
                  updated_at: nowIso,
                });
                console.log(
                  `✅ [POUCH-STORAGE] Monitoring conflict resolved:`,
                  docId
                );
              } else {
                console.error(
                  "❌ [POUCH-STORAGE] Monitoring upsert failed:",
                  e
                );
              }
            }
          }
        } catch (err) {
          console.error("❌ [POUCH-STORAGE] Callback processing failed:", err);
        }
      }
    );

    return result;
  } catch (error) {
    console.error("Error in current tab test:", error);
    return {
      success: false,
      message: `Test failed: ${error?.message || error}`,
      error: error.toString(),
    };
  }
}

/**
 * Handle simple action execution with per-task tab management
 */
async function handleSimpleActionExecution(request) {
  try {
    const { actions } = request;

    if (!actions || !Array.isArray(actions)) {
      return { success: false, message: "Invalid actions array" };
    }

    console.log(
      "🎯 Executing action array directly:",
      actions.length,
      "actions"
    );

    // Create a temporary graph ID for execution
    // TODO: Remove this once we have a proper graph ID and add uuid to the graph ID
    const tempGraphId = `temp_${Date.now()}_uuid`;

    // Execute the action array using the rule engine
    const result = await engineController.executeActionArray(
      tempGraphId,
      actions,
      { closeAfterNode: true }
    );

    return result;
  } catch (error) {
    console.error("Error in traditional execution:", error);
    return { success: false, message: `Error: ${error?.message || error}` };
  }
}

/**
 * Handle rule engine requests
 */
async function handleRuleEngineRequest(request, sendResponse) {
  try {
    if (!engineController) {
      await initializeBackground();
    }

    switch (request.type) {
      case "RULE_ENGINE_ADD_GRAPH":
        engineController.addGraph(request.graphId, request.actions);
        sendResponse({
          success: true,
          message: "Action graph added to rule engine",
        });
        break;

      // REMOVED: RULE_ENGINE_EXECUTE_ACTIONS now handled via PouchDB only

      case "RULE_ENGINE_TRIGGER_GRAPH":
        const triggerResult = await engineController.triggerGraph(
          request.graphId,
          request.startActionId
        );
        sendResponse(triggerResult);
        break;

      case "RULE_ENGINE_TRIGGER_GRAPH_WITH_PARENT_TRACKING":
        const triggerWithParentResult =
          await engineController.triggerGraphWithParentTracking(
            request.graphId,
            request.startActionId,
            request.options || {}
          );
        sendResponse(triggerWithParentResult);
        break;

      case "RULE_ENGINE_GET_PARENT_STATUS":
        const parentStatusResult =
          await engineController.getParentStatusBeforeSubtree(
            request.graphId,
            request.parentActionId
          );
        sendResponse({ success: true, parentStatus: parentStatusResult });
        break;

      case "RULE_ENGINE_GET_NODE_STATUS":
        const nodeStatus = engineController.getNodeStatus(
          request.graphId,
          request.actionId
        );
        sendResponse({ success: true, nodeStatus });
        break;

      case "RULE_ENGINE_GET_NODE_STATUSES":
        const nodeStatuses = engineController.getNodeStatuses(request.graphId);
        sendResponse({ success: true, nodeStatuses });
        break;

      case "RULE_ENGINE_GET_STATUS":
        const status = engineController.getStatus();
        sendResponse({ success: true, status });
        break;

      case "RULE_ENGINE_GET_CONFIG":
        const config = await engineController.getConfiguration();
        sendResponse({ success: true, config });
        break;

      default:
        sendResponse({
          success: false,
          message: `Unknown DAG engine request type: ${request.type}`,
        });
    }
  } catch (error) {
    console.error("Error handling DAG engine request:", error);
    sendResponse({
      success: false,
      message: `Error: ${error?.message || error}`,
    });
  }
}

/**
 * Handle test current tab request
 */
async function handleTestCurrentTab(request) {
  try {
    const { tabId, action, arguments: actionArgs } = request;

    if (!tabId || !action || !actionArgs) {
      return {
        success: false,
        message: "Missing required parameters (tabId, action, arguments)",
      };
    }

    console.log("🧪 Testing current tab execution:", {
      tabId,
      action,
      actionArgs,
    });

    // Initialize engine controller if not already done
    if (!engineController) {
      await initializeBackground();
    }

    // Test 1: Check if execution service can be loaded
    console.log("🧪 Loading execution service...");
    let executionService;
    try {
      if (typeof globalThis.ExecutionService !== "undefined") {
        executionService = new globalThis.ExecutionService();
      } else if (typeof ExecutionService !== "undefined") {
        executionService = new ExecutionService();
      } else {
        console.warn("⚠️ ExecutionService not available in bundle");
        return {
          success: false,
          message:
            "ExecutionService not available in service worker environment",
        };
      }
      console.log("✅ Execution service loaded successfully");
    } catch (error) {
      console.error("❌ ExecutionService loading failed:", error);
      return {
        success: false,
        message: `ExecutionService loading failed: ${error.message}`,
      };
    }

    // Test 2: Check if action is supported (simplified)
    console.log("🧪 Checking if action is supported...");
    const supportedActions = [
      "BUY",
      "SELL",
      "PlaceBuyLimitOrder",
      "PlaceSellLimitOrder",
      "PlaceBuyStopLossMarketOrder",
      "PlaceSellStopLossMarketOrder",
      "PlaceBuyStopLossLimitOrder",
      "PlaceSellStopLossLimitOrder",
      "MONITORPROFIT",
      "ExitAllPositions",
      "MonitorConditionThenAct",
      "NavigateToProfile",
      "GetProfileInfo",
    ];
    console.log("Supported actions:", supportedActions);

    if (!supportedActions.includes(action)) {
      return {
        success: false,
        message: `Action '${action}' is not supported. Supported actions: ${supportedActions.join(
          ", "
        )}`,
      };
    }

    // Test 3: Simple test - just try to inject content script and send a message
    console.log("🧪 Testing content script injection and communication...");

    try {
      // Try to inject content script
      await chrome.scripting.executeScript({
        target: { tabId },
        files: ["content-scripts/zerodha.js"],
      });
      console.log("✅ Content script injected successfully");

      // Wait a bit for the content script to initialize
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Try to send a simple test message
      const testMessage = {
        type: "PERFORM_SITE_ACTIONS",
        actions: [
          {
            action: "GetProfileInfo",
            arguments: {},
          },
        ],
      };

      console.log("📤 Sending test message to content script...");
      const response = await new Promise((resolve, reject) => {
        // Add timeout to prevent hanging
        const timeout = setTimeout(() => {
          reject(
            new Error(
              "Test message timeout: No response received within 10 seconds"
            )
          );
        }, 10000);

        chrome.tabs.sendMessage(tabId, testMessage, (response) => {
          clearTimeout(timeout);

          if (chrome.runtime.lastError) {
            const errorMessage = chrome.runtime.lastError.message;
            console.error(
              `[Background] Chrome runtime error for tab ${tabId}:`,
              errorMessage
            );

            // Handle specific message channel closed error
            if (
              errorMessage.includes("message channel closed") ||
              errorMessage.includes("asynchronous response") ||
              errorMessage.includes("Could not establish connection")
            ) {
              reject(
                new Error(
                  `Message channel closed: ${errorMessage}. This usually happens when the tab was closed or navigated away during execution.`
                )
              );
            } else {
              reject(new Error(`Failed to send message: ${errorMessage}`));
            }
          } else if (!response) {
            reject(new Error("No response from content script"));
          } else {
            resolve(response);
          }
        });
      });

      console.log("📥 Received response from content script:", response);

      return {
        success: true,
        message: "Content script communication test successful",
        details: response,
        supportedActions: supportedActions,
      };
    } catch (injectionError) {
      console.error(
        "❌ Content script injection/communication failed:",
        injectionError
      );
      return {
        success: false,
        message: `Content script test failed: ${injectionError.message}`,
        error: injectionError.toString(),
        supportedActions: supportedActions,
      };
    }
  } catch (error) {
    console.error("Error in current tab test:", error);
    return {
      success: false,
      message: `Test failed: ${error?.message || error}`,
      error: error.toString(),
    };
  }
}

/**
 * Handle simple action execution with per-task tab management
 */
async function handleSimpleActionExecution(request) {
  try {
    const { actions } = request;

    if (!actions || !Array.isArray(actions)) {
      return { success: false, message: "Invalid actions array" };
    }

    console.log(
      "🎯 Executing action array directly:",
      actions.length,
      "actions"
    );

    // Create a temporary graph ID for execution
    // TODO: Remove this once we have a proper graph ID and add uuid to the graph ID
    const tempGraphId = `temp_${Date.now()}_uuid`;

    // Execute the action array using the rule engine
    const result = await engineController.executeActionArray(
      tempGraphId,
      actions,
      { closeAfterNode: true }
    );

    return result;
  } catch (error) {
    console.error("Error in simple action execution:", error);
    return {
      success: false,
      message: `Error: ${error?.message || error}`,
    };
  }
}

/**
 * Handle environment configuration request
 */
async function handleEnvironmentConfigRequest() {
  try {
    let environment;
    if (typeof globalThis.getEnvironment !== "undefined") {
      environment = await globalThis.getEnvironment();
    } else if (typeof getEnvironment !== "undefined") {
      environment = await getEnvironment();
    } else {
      console.warn("⚠️ [DEBUG] getEnvironment not available, using fallback");
      environment = { close_tabs_after_execution: true }; // Fallback config
    }
    return {
      success: true,
      config: environment,
    };
  } catch (error) {
    console.error("Error getting environment config:", error);
    return {
      success: false,
      message: `Error: ${error?.message || error}`,
    };
  }
}

/**
 * Handle toggle tab closing request
 */
async function handleToggleTabClosing() {
  try {
    let environment;
    if (typeof globalThis.getEnvironment !== "undefined") {
      environment = await globalThis.getEnvironment();
    } else if (typeof getEnvironment !== "undefined") {
      environment = await getEnvironment();
    } else {
      console.warn("⚠️ [DEBUG] getEnvironment not available, using fallback");
      environment = { close_tabs_after_execution: true }; // Fallback config
    }

    const currentSetting = environment.close_tabs_after_execution;
    const newSetting = !currentSetting;

    // Update the shared config
    const response = await fetch(
      chrome.runtime.getURL("lib/shared-config.json")
    );
    const config = await response.json();
    config.ENVIRONMENT.close_tabs_after_execution = newSetting;

    // Note: In a real implementation, you might want to save this to storage
    // For now, we'll just return the new setting
    console.log(
      `🔄 Tab closing setting toggled from ${currentSetting} to ${newSetting}`
    );

    return {
      success: true,
      close_tabs_after_execution: newSetting,
      message: `Tab closing setting toggled to ${newSetting}`,
    };
  } catch (error) {
    console.error("Error toggling tab closing:", error);
    return {
      success: false,
      message: `Error: ${error?.message || error}`,
    };
  }
}

/**
 * Handle messages from popup and content scripts
 */
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log(" == Received message ===>", request.type);
  switch (request.type) {
    case "GET_LOGIN_STATUS": {
      sendResponse({
        success: true,
        data: {
          required: cachedLoginStatus.required,
          brokerName: cachedLoginStatus.brokerName,
          updatedAt: cachedLoginStatus.updatedAt,
        },
      });
      break;
    }
    case "LOGIN_REQUIRED": {
      // Allow executor to update cached status when it broadcasts
      cachedLoginStatus = {
        required: true,
        brokerName:
          request.brokerName || cachedLoginStatus.brokerName || "Broker",
        updatedAt: Date.now(),
      };
      sendResponse?.({ received: true });
      break;
    }
    case "LOGIN_RESOLVED": {
      cachedLoginStatus = {
        required: false,
        brokerName:
          request.brokerName || cachedLoginStatus.brokerName || "Broker",
        updatedAt: Date.now(),
      };
      sendResponse?.({ received: true });
      break;
    }
    case "EXTENSION_OTP_VERIFIED":
    case "CONNECT_TO_COUCHDB":
      if (request.success) {
        console.log(
          `User has logged in successfully. Storing firebaseUid "${request.firebaseUid}" in cache...`
        );
        storeDataInCache("firebase_uid", request.firebaseUid)
          .then(initializeBackground)
          .then(() => console.log("Started monitoring."))
          .catch(console.error);
      } else {
        console.warn("Login was not successful");
      }
      return true;

    case "WAKE_UP_SERVICE_WORKER":
      console.log("⏰ [WAKE] Service worker wake-up requested");
      // Re-initialize PouchDB connection if needed
      if (!localDB) {
        console.log("🔧 [WAKE] PouchDB not initialized, re-initializing...");
        initializeBackground();
      } else {
        console.log("✅ [WAKE] PouchDB already initialized");
        console.log(
          "🔄 [WAKE] Restarting execution monitoring to ensure change listener is active..."
        );
        startExecutionMonitoring();
      }
      sendResponse({ success: true, message: "Service worker is awake" });
      return true;

    case "EXECUTE_ACTIONS":
    case "RULE_ENGINE_EXECUTE_ACTIONS":
      // Handle node-by-node execution requests
      handleExecutionRequest(request).then(sendResponse);
      return true; // Keep message channel open for async response

    case "GET_ENGINE_STATUS":
      // Get engine status for testing
      if (engineController) {
        sendResponse(engineController.getStatus());
      } else {
        sendResponse({
          success: false,
          message: "Engine controller not initialized",
        });
      }
      break;

    case "GET_EXECUTION_STATUS":
      // Get execution service status for testing
      if (engineController) {
        sendResponse(engineController.getAllExecutionsStatus());
      } else {
        sendResponse({
          success: false,
          message: "Engine controller not initialized",
        });
      }
      break;

    case "PAUSE_GRAPH":
      // Pause a graph execution
      if (engineController && request.graphId) {
        const result = engineController.pauseGraphExecution(request.graphId);
        sendResponse(result);
      } else {
        sendResponse({
          success: false,
          message: "Engine controller not initialized or graph ID missing",
        });
      }
      break;

    case "RESUME_GRAPH":
      // Resume a graph execution
      if (engineController && request.graphId) {
        const result = engineController.resumeGraphExecution(request.graphId);
        sendResponse(result);
      } else {
        sendResponse({
          success: false,
          message: "Engine controller not initialized or graph ID missing",
        });
      }
      break;

    case "CANCEL_GRAPH":
      // Cancel a graph execution
      if (engineController && request.graphId) {
        engineController
          .cancelGraphExecution(request.graphId)
          .then(sendResponse);
        return true; // Keep message channel open for async response
      } else {
        sendResponse({
          success: false,
          message: "Engine controller not initialized or graph ID missing",
        });
      }
      break;

    case "TEST_CURRENT_TAB":
      // Handle async operations properly with error handling
      (async () => {
        try {
          const result = await handleTestCurrentTab(request);
          sendResponse(result);
        } catch (error) {
          console.error("Error in TEST_CURRENT_TAB:", error);
          sendResponse({
            success: false,
            message: `Test failed: ${error?.message || error}`,
            error: error.toString(),
          });
        }
      })();
      return true; // Keep message channel open for async response

    case "GET_ENVIRONMENT_CONFIG":
      // Handle environment configuration request
      handleEnvironmentConfigRequest().then(sendResponse);
      return true; // Keep message channel open for async response

    case "TOGGLE_TAB_CLOSING":
      // Handle toggle tab closing request
      handleToggleTabClosing().then(sendResponse);
      return true; // Keep message channel open for async response

    default:
      sendResponse({
        success: false,
        message: `Unknown message type: ${request.type}`,
      });
  }
});

/**
 * Handle tab updates
 */
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  // Handle tab updates if needed
  if (changeInfo.status === "complete") {
    console.log(`Tab ${tabId} loaded completely`);
  }
});

/**
 * Initialize background script when extension loads
 */
chrome.runtime.onInstalled.addListener(async (details) => {
  console.log("🔧 Extension installed event:", details?.reason);
  if (details?.reason === "update") {
    await closeExistingMonitorTabFromStorage();
    await closePinnedOrdersTabs();
  }
  initializeBackground();
});

chrome.runtime.onStartup.addListener(() => {
  console.log("🚀 Extension started, initializing background script...");
  initializeBackground();
});

// Initialize immediately if already running
if (chrome.runtime) {
  initializeBackground();
}

chrome.action.onClicked.addListener((tab) => {
  // Use the tabId from the tab that was clicked to open the side panel
  chrome.sidePanel.open({ tabId: tab.id });
});
