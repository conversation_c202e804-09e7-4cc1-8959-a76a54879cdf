# Aagman SmartAgent - Chrome Extension Executor

A sophisticated Chrome extension that provides AI-powered trading automation for stock market analysis and order execution, specifically designed for Zerodha Kite platform.

## 🚀 Overview

The Aagman SmartAgent executor is a Chrome extension that enables automated trading operations through intelligent rule-based execution, real-time monitoring, and seamless integration with Zerodha's trading platform.

## ✨ Features

### Core Functionality
- **AI-Powered Trading Assistant**: Intelligent automation for stock market analysis and order execution
- **Rule-Based Execution Engine**: JSON-based rule engine for complex trading logic
- **Real-Time Monitoring**: Live tracking of orders, positions, and market data
- **Multi-Tab Automation**: Parallel execution across multiple browser tabs
- **Login State Management**: Automatic detection and handling of login states
- **Graph-Based Execution**: Support for complex dependency-based action graphs

### Trading Platform Integration
- **Zerodha Kite Integration**: Native support for Zerodha's trading platform
- **Multi-Broker Support**: Extensible architecture for additional brokers
- **Order Management**: Automated order placement, modification, and cancellation
- **Position Monitoring**: Real-time position tracking and alerts
- **Risk Management**: Built-in risk controls and validation

### User Interface
- **Side Panel Interface**: Dedicated side panel for extension controls
- **Real-Time Notifications**: Live updates and status notifications
- **Responsive Design**: Modern, intuitive user interface
- **Dark/Light Theme Support**: Customizable appearance

## 🏗️ Architecture

### Core Components
- **Background Script**: Service worker for extension lifecycle management
- **Content Scripts**: Page injection for DOM manipulation and data extraction
- **Execution Service**: High-performance action execution engine
- **Rule Engine**: JSON-based decision engine for trading logic
- **UI Layer**: React-based user interface components

### Key Modules
- `execution-service.js`: Core automation engine
- `primitive-engine-controller.js`: Rule engine controller
- `validation.js`: Input validation and sanitization
- `config.js`: Configuration management
- `constants.js`: System constants and enums

## 📦 Installation

### Prerequisites
- Node.js (v16 or higher)
- Chrome browser
- Zerodha Kite account

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd smart-agent/executor
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Build the extension**
   ```bash
   npm run build
   ```

4. **Load in Chrome**
   - Open Chrome and navigate to `chrome://extensions/`
   - Enable "Developer mode"
   - Click "Load unpacked" and select the `executor` folder

### Production Build

1. **Build for production**
   ```bash
   npm run build
   ```

2. **Package the extension**
   - The built files will be in the `dist` directory
   - Zip the contents for distribution

## 🔧 Configuration

### Environment Setup
The extension uses a shared configuration system:

```json
{
  "TIMEOUT_SETTINGS": {
    "execution_service_general_timeout_seconds": 30,
    "execution_service_monitoring_timeout_seconds": 600,
    "background_test_timeout_seconds": 10
  }
}
```

### Permissions
The extension requires the following permissions:
- `sidePanel`: For the trading interface
- `storage`: For data persistence
- `tabs`: For multi-tab automation
- `activeTab`: For current tab access
- `scripting`: For content script injection

## 🚀 Usage

### Basic Usage

1. **Install the extension** in Chrome
2. **Navigate to Zerodha Kite** (`https://kite.zerodha.com`)
3. **Open the side panel** by clicking the extension icon
4. **Configure your trading rules** through the interface
5. **Start automation** with the provided controls

### Advanced Features

#### Rule-Based Automation
```javascript
// Example rule configuration
const rule = {
  conditions: {
    all: [
      {
        fact: 'marketOpen',
        operator: 'equal',
        value: true
      },
      {
        fact: 'price',
        operator: 'greaterThan',
        value: 100
      }
    ]
  },
  event: {
    type: 'placeOrder',
    params: {
      symbol: 'RELIANCE',
      quantity: 100,
      price: 2500
    }
  }
};
```

#### Graph-Based Execution
```javascript
// Complex dependency-based execution
const graph = {
  nodes: [
    { id: 'login', action: 'login', dependencies: [] },
    { id: 'checkBalance', action: 'getBalance', dependencies: ['login'] },
    { id: 'placeOrder', action: 'placeOrder', dependencies: ['checkBalance'] }
  ]
};
```

## 🧪 Testing

### Unit Tests
```bash
npm test
```

### Integration Tests
```bash
npm run test:integration
```

### Manual Testing
1. Load the extension in Chrome
2. Navigate to test pages
3. Verify functionality through the UI

## 🔍 Debugging

### Development Mode
- Enable Chrome DevTools for the extension
- Use `console.log` statements for debugging
- Monitor background script logs

### Common Issues

#### Extension Not Loading
- Check manifest.json syntax
- Verify all required files exist
- Check Chrome extension console for errors

#### Content Script Issues
- Verify content script matches in manifest.json
- Check for JavaScript errors in page console
- Ensure proper message passing between scripts

#### Execution Failures
- Check timeout settings
- Verify login state
- Monitor execution service logs

## 📚 API Reference

### Execution Service
```javascript
// Initialize execution service
const executionService = new ExecutionService();
await executionService.loadTimeoutSettings();

// Execute actions
await executionService.executeActions({
  site: 'zerodha',
  actions: [
    { type: 'click', selector: '#login-button' },
    { type: 'input', selector: '#username', value: 'user' }
  ]
});
```

### Rule Engine
```javascript
// Add rules to engine
engineController.addRule({
  conditions: { /* conditions */ },
  event: { /* event */ }
});

// Run engine
await engineController.run({ /* facts */ });
```

## 🤝 Contributing

### Development Guidelines
1. Follow the existing code structure
2. Add comprehensive tests for new features
3. Update documentation for API changes
4. Follow the established naming conventions

### Code Style
- Use ES6+ features
- Follow JSDoc documentation standards
- Maintain consistent indentation (2 spaces)
- Use meaningful variable and function names

## 📄 License

This project is proprietary software. All rights reserved.

## 🆘 Support

For technical support or feature requests:
- Create an issue in the repository
- Contact the development team
- Check the documentation for common solutions

## 🔄 Version History

### v1.0.76 (Current)
- Enhanced execution service with parallel processing
- Improved login monitoring system
- Added graph-based execution support
- Enhanced error handling and recovery
- Updated UI components and styling

### Previous Versions
- v1.0.75: Initial public release
- v1.0.74: Beta testing version
- v1.0.73: Alpha development version

---

**Note**: This extension is designed for educational and research purposes. Users are responsible for complying with all applicable laws and regulations regarding automated trading.
