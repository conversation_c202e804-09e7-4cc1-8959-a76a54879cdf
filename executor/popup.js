// Popup script for the executor extension
import { UIService } from './lib/ui-service.js';

// UI elements
const actionsJsonTextarea = document.getElementById('actions-json');
const runActionsBtn = document.getElementById('run-actions');
const testCurrentTabBtn = document.getElementById('test-current-tab');
const reloadExtensionBtn = document.getElementById('reload-extension');
const clearBtn = document.getElementById('clear');
const statusMessage = document.getElementById('status-message');
const executionLog = document.getElementById('execution-log');

// UI service
const uiService = new UIService(statusMessage);

// Storage keys
const STORAGE_KEYS = {
    ACTION_ARRAY: 'popup_action_array',
    EXECUTION_LOGS: 'popup_execution_logs',
    LAST_EXECUTION_TIME: 'popup_last_execution_time'
};

/**
 * Check if Chrome storage API is available
 */
function isStorageAvailable() {
    return typeof chrome !== 'undefined' && 
           chrome.storage && 
           chrome.storage.local;
}

/**
 * Initialize the popup
 */
async function initializePopup() {
    console.log('🚀 Initializing popup...');
    
    // Validate required DOM elements
    if (!actionsJsonTextarea || !runActionsBtn || !clearBtn || !statusMessage || !executionLog) {
        console.error('❌ Required DOM elements not found');
        return;
    }
    
    // Restore saved state if storage is available
    if (isStorageAvailable()) {
        await restorePopupState();
        setupAutoSave();
    } else {
        console.warn('⚠️ Chrome storage API not available, state persistence disabled');
    }
    
    // Display current environment configuration
    await displayEnvironmentConfig();
    
    console.log('✅ Popup initialized');
}

/**
 * Save popup state to storage
 */
async function savePopupState() {
    if (!isStorageAvailable()) return;
    
    try {
        await chrome.storage.local.set({
            [STORAGE_KEYS.ACTION_ARRAY]: actionsJsonTextarea.value,
            [STORAGE_KEYS.EXECUTION_LOGS]: executionLog.innerHTML,
            [STORAGE_KEYS.LAST_EXECUTION_TIME]: Date.now()
        });
        console.log('💾 Popup state saved');
    } catch (error) {
        console.error('Error saving popup state:', error);
    }
}

/**
 * Restore popup state from storage
 */
async function restorePopupState() {
    if (!isStorageAvailable()) return;
    
    try {
        const result = await chrome.storage.local.get([
            STORAGE_KEYS.ACTION_ARRAY,
            STORAGE_KEYS.EXECUTION_LOGS,
            STORAGE_KEYS.LAST_EXECUTION_TIME
        ]);
        
        if (result[STORAGE_KEYS.ACTION_ARRAY]) {
            actionsJsonTextarea.value = result[STORAGE_KEYS.ACTION_ARRAY];
            console.log('📝 Restored action array from storage');
        }
        
        if (result[STORAGE_KEYS.EXECUTION_LOGS]) {
            executionLog.innerHTML = result[STORAGE_KEYS.EXECUTION_LOGS];
            console.log('📝 Restored execution logs from storage');
        }
        
        if (result[STORAGE_KEYS.LAST_EXECUTION_TIME]) {
            const timeAgo = getTimeAgo(result[STORAGE_KEYS.LAST_EXECUTION_TIME]);
            logExecution(`📅 Last saved: ${timeAgo}`, 'info');
        }
    } catch (error) {
        console.error('Error restoring popup state:', error);
    }
}

/**
 * Set up auto-save functionality
 */
function setupAutoSave() {
    if (!isStorageAvailable()) return;
    
    // Auto-save on textarea changes
    actionsJsonTextarea.addEventListener('input', () => {
        clearTimeout(window.autoSaveTimeout);
        window.autoSaveTimeout = setTimeout(savePopupState, 1000);
    });
}

/**
 * Get time ago string
 */
function getTimeAgo(date) {
    const now = Date.now();
    const diff = now - date;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (days > 0) return `${days} day(s) ago`;
    if (hours > 0) return `${hours} hour(s) ago`;
    if (minutes > 0) return `${minutes} minute(s) ago`;
    return 'Just now';
}

/**
 * Handle reload extension button click
 */
async function handleReloadExtension() {
    try {
        logExecution('🔄 Reloading extension...', 'info');
        uiService.showInfo('Reloading extension...');
        
        // Send reload message to background script
        await chrome.runtime.sendMessage({ type: 'RELOAD_EXTENSION' });
        
        // Reload the extension
        chrome.runtime.reload();
        
    } catch (error) {
        console.error('Error reloading extension:', error);
        logExecution(`❌ Error reloading extension: ${error.message}`, 'error');
        uiService.showError(`Reload failed: ${error.message}`);
    }
}

/**
 * Handle test current tab button click
 */
async function handleTestCurrentTab() {
    try {
        logExecution('🧪 Testing current tab functionality...', 'info');
        uiService.showInfo('Testing current tab...');
        
        // Get current active tab
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        
        if (!tab) {
            throw new Error('No active tab found');
        }
        
        logExecution(`📋 Testing tab: ${tab.url}`, 'info');
        
        // Send test message to background script
        const response = await chrome.runtime.sendMessage({
            type: 'TEST_CURRENT_TAB',
            tabId: tab.id,
            action: 'GetProfileInfo',
            arguments: {}
        });
        
        if (response && response.success) {
            logExecution('✅ Current tab test successful!', 'success');
            logExecution(`📊 Supported actions: ${response.details?.supportedActions?.join(', ') || 'N/A'}`, 'info');
            
            if (response.details) {
                logExecution(`📝 Test details: ${JSON.stringify(response.details, null, 2)}`, 'info');
            }
            
            uiService.showSuccess('Current tab test successful!');
        } else {
            logExecution(`❌ Current tab test failed: ${response?.message || 'Unknown error'}`, 'error');
            uiService.showError(`Test failed: ${response?.message || 'Unknown error'}`);
        }
        
    } catch (error) {
        console.error('Error testing current tab:', error);
        logExecution(`❌ Error testing current tab: ${error.message}`, 'error');
        uiService.showError(`Test failed: ${error.message}`);
    }
}

/**
 * Handle run actions button click
 */
async function handleRunActions() {
    try {
        const actionsJson = actionsJsonTextarea.value.trim();
        
        if (!actionsJson) {
            uiService.showError('Please enter action array JSON');
            return;
        }
        
        let actions;
        try {
            actions = JSON.parse(actionsJson);
        } catch (parseError) {
            uiService.showError('Invalid JSON format');
            logExecution(`❌ JSON parse error: ${parseError.message}`, 'error');
            return;
        }
        
        if (!Array.isArray(actions)) {
            uiService.showError('Actions must be an array');
            return;
        }
        
        logExecution('🚀 Starting action execution...', 'info');
        uiService.showInfo('Executing actions...');
        
        // Send execution message to background script
        const response = await chrome.runtime.sendMessage({
            type: 'RULE_ENGINE_EXECUTE_ACTIONS',
            actions: actions
        });
        
        if (response && response.success) {
            logExecution('✅ Action execution completed successfully!', 'success');
            uiService.showSuccess('Actions executed successfully!');
            
            // Log detailed results for each action
            if (response.results && Array.isArray(response.results)) {
                console.log('📊 Processing execution results:', response.results);
                console.log('📋 Original actions:', actions);
                
                response.results.forEach((result, index) => {
                    console.log(`🔍 Processing result ${index}:`, result);
                    
                    const action = actions.find(a => a.id === result.actionId) || actions[index];
                    console.log(`📝 Found action for result ${index}:`, action);
                    
                    const status = result.success ? '✅' : '❌';
                    const timestamp = result.timestamp ? new Date(result.timestamp).toLocaleTimeString() : '';
                    
                    // Handle cases where action.id might be undefined
                    const actionId = action.id || action.action || `Action ${index + 1}`;
                    console.log(`🏷️ Action ID for result ${index}:`, actionId);
                    
                    let logMessage = `${status} **${actionId}** (${action.action})`;
                    if (timestamp) {
                        logMessage += ` [${timestamp}]`;
                    }
                    logMessage += `: ${result.message}`;
                    
                    if (action.human_friendly_explanation) {
                        logMessage += `\n   💡 ${action.human_friendly_explanation}`;
                    }
                    
                    logExecution(logMessage, result.success ? 'success' : 'error');
                });
                
                // Show overall execution time if available
                if (response.results.length > 0) {
                    const firstAction = response.results[0];
                    const lastAction = response.results[response.results.length - 1];
                    if (firstAction.timestamp && lastAction.timestamp) {
                        const startTime = new Date(firstAction.timestamp);
                        const endTime = new Date(lastAction.timestamp);
                        const duration = Math.round((endTime - startTime) / 1000);
                        logExecution(`⏱️ Total execution time: ${duration} seconds`, 'info');
                    }
                }
            }
        } else {
            logExecution(`❌ Action execution failed: ${response?.message || 'Unknown error'}`, 'error');
            uiService.showError(`Execution failed: ${response?.message || 'Unknown error'}`);
        }
        
    } catch (error) {
        console.error('Error running actions:', error);
        logExecution(`❌ Error running actions: ${error.message}`, 'error');
        uiService.showError(`Execution failed: ${error.message}`);
    }
}

/**
 * Handle clear button click
 */
async function handleClear() {
    try {
        actionsJsonTextarea.value = '';
        executionLog.innerHTML = '';
        
        // Clear storage
        if (isStorageAvailable()) {
            await chrome.storage.local.remove([
                STORAGE_KEYS.ACTION_ARRAY,
                STORAGE_KEYS.EXECUTION_LOGS,
                STORAGE_KEYS.LAST_EXECUTION_TIME
            ]);
        }
        
        logExecution('🧹 Cleared all data', 'info');
        uiService.showInfo('All data cleared');
        
    } catch (error) {
        console.error('Error clearing data:', error);
        logExecution(`❌ Error clearing data: ${error.message}`, 'error');
        uiService.showError(`Clear failed: ${error.message}`);
    }
}

/**
 * Log execution message
 */
function logExecution(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry log-${type}`;
    
    let formattedMessage = message;
    if (type === 'error') {
        formattedMessage = `❌ ${message}`;
    } else if (type === 'success') {
        formattedMessage = `✅ ${message}`;
    } else if (type === 'warning') {
        formattedMessage = `⚠️ ${message}`;
    }
    
    logEntry.innerHTML = `<span class="log-time">[${timestamp}]</span> ${formattedMessage}`;
    executionLog.appendChild(logEntry);
    executionLog.scrollTop = executionLog.scrollHeight;
    
    // Auto-save logs
    if (isStorageAvailable()) {
        setTimeout(savePopupState, 100);
    }
}

/**
 * Display current environment configuration
 */
async function displayEnvironmentConfig() {
    try {
        const response = await chrome.runtime.sendMessage({
            type: 'GET_ENVIRONMENT_CONFIG'
        });
        
        if (response && response.success) {
            const config = response.config;
            logExecution('🔧 Environment Configuration:', 'info');
            logExecution(`   • slow_execute: ${config.slow_execute}`, 'info');
            logExecution(`   • close_tabs_after_execution: ${config.close_tabs_after_execution}`, 'info');
        } else {
            logExecution('❌ Failed to get environment configuration', 'error');
        }
    } catch (error) {
        logExecution(`❌ Error getting environment config: ${error.message}`, 'error');
    }
}

/**
 * Toggle tab closing setting
 */
async function toggleTabClosing() {
    try {
        const response = await chrome.runtime.sendMessage({
            type: 'TOGGLE_TAB_CLOSING'
        });
        
        if (response && response.success) {
            const newSetting = response.close_tabs_after_execution;
            logExecution(`🔄 Tab closing toggled to: ${newSetting}`, 'info');
        } else {
            logExecution('❌ Failed to toggle tab closing setting', 'error');
        }
    } catch (error) {
        logExecution(`❌ Error toggling tab closing: ${error.message}`, 'error');
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await initializePopup();
        
        // Set up event listeners
        runActionsBtn.addEventListener('click', handleRunActions);
        testCurrentTabBtn.addEventListener('click', handleTestCurrentTab);
        reloadExtensionBtn.addEventListener('click', handleReloadExtension);
        clearBtn.addEventListener('click', handleClear);
        
        // Add toggle button for tab closing
        const toggleBtn = document.createElement('button');
        toggleBtn.textContent = '🔄 Toggle Tab Closing';
        toggleBtn.className = 'btn btn-secondary';
        toggleBtn.style.marginTop = '10px';
        toggleBtn.addEventListener('click', toggleTabClosing);
        document.querySelector('.button-group').appendChild(toggleBtn);
        
        console.log('✅ Popup initialized successfully');
    } catch (error) {
        console.error('❌ Error initializing popup:', error);
        uiService.showError(`Initialization failed: ${error.message}`);
    }
});