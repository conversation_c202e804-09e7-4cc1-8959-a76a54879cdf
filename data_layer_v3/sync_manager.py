"""
Sync Manager for Data Layer V3.

Handles synchronization between CouchDB and PouchDB.
"""

import time
import threading
import structlog
from typing import Dict, Any, Optional
from config import DataLayerConfig
from exceptions import SyncError
from couchdb_manager import CouchDBManager
from pouchdb_manager import PouchDBManager

logger = structlog.get_logger(__name__)


class SyncManager:
    """Manages synchronization between CouchDB and PouchDB."""
    
    def __init__(self, config: DataLayerConfig, couchdb_manager: CouchDBManager, pouchdb_manager: PouchDBManager):
        """Initialize sync manager."""
        self.config = config
        self.couchdb = couchdb_manager
        self.pouchdb = pouchdb_manager
        self.sync_thread = None
        self.sync_running = False
        self.last_sync_time = None
        self.sync_stats = {
            'total_syncs': 0,
            'last_sync_duration': 0,
            'errors': 0
        }
    
    def start_sync(self):
        """Start continuous synchronization."""
        if self.sync_running:
            logger.warning("Sync already running")
            return
        
        if not self.config.sync_enabled:
            logger.info("Sync disabled in configuration")
            return
        
        self.sync_running = True
        self.sync_thread = threading.Thread(target=self._sync_loop, daemon=True)
        self.sync_thread.start()
        logger.info("Sync started")
    
    def stop_sync(self):
        """Stop continuous synchronization."""
        self.sync_running = False
        if self.sync_thread:
            self.sync_thread.join(timeout=5)
        logger.info("Sync stopped")
    
    def _sync_loop(self):
        """Main sync loop."""
        while self.sync_running:
            try:
                start_time = time.time()
                self._perform_sync()
                duration = time.time() - start_time
                
                self.sync_stats['total_syncs'] += 1
                self.sync_stats['last_sync_duration'] = duration
                self.last_sync_time = time.time()
                
                logger.info("Sync completed", duration=duration)
                
                # Wait for next sync interval
                time.sleep(self.config.sync_interval / 1000)
                
            except Exception as e:
                self.sync_stats['errors'] += 1
                logger.error("Sync error", error=str(e))
                time.sleep(5)  # Wait before retry
    
    def _perform_sync(self):
        """Perform a single sync operation."""
        try:
            # Get changes from both databases
            couchdb_changes = self.couchdb.get_changes()
            pouchdb_changes = self.pouchdb.get_changes()
            
            # Sync from CouchDB to PouchDB
            self._sync_to_pouchdb(couchdb_changes)
            
            # Sync from PouchDB to CouchDB
            self._sync_to_couchdb(pouchdb_changes)
            
        except Exception as e:
            raise SyncError(f"Sync failed: {e}")
    
    def _sync_to_pouchdb(self, couchdb_changes: Dict[str, Any]):
        """Sync changes from CouchDB to PouchDB."""
        for change in couchdb_changes.get('results', []):
            doc_id = change['id']
            
            # Skip design documents
            if doc_id.startswith('_design/'):
                continue
            
            try:
                # Get document from CouchDB
                doc = self.couchdb.get_document(doc_id)
                
                if doc:
                    # Save to PouchDB
                    self.pouchdb.save_document(doc_id, doc)
                    logger.debug("Synced document to PouchDB", doc_id=doc_id)
                else:
                    # Document was deleted
                    logger.debug("Document deleted in CouchDB", doc_id=doc_id)
                    
            except Exception as e:
                logger.error("Failed to sync document to PouchDB", doc_id=doc_id, error=str(e))
    
    def _sync_to_couchdb(self, pouchdb_changes: Dict[str, Any]):
        """Sync changes from PouchDB to CouchDB."""
        for change in pouchdb_changes.get('results', []):
            doc_id = change['id']
            
            # Skip design documents
            if doc_id.startswith('_design/'):
                continue
            
            try:
                # Get document from PouchDB
                doc = self.pouchdb.get_document(doc_id)
                
                if doc:
                    # Save to CouchDB
                    self.couchdb.save_document(doc_id, doc)
                    logger.debug("Synced document to CouchDB", doc_id=doc_id)
                else:
                    # Document was deleted
                    logger.debug("Document deleted in PouchDB", doc_id=doc_id)
                    
            except Exception as e:
                logger.error("Failed to sync document to CouchDB", doc_id=doc_id, error=str(e))
    
    def manual_sync(self):
        """Perform a manual sync operation."""
        try:
            start_time = time.time()
            self._perform_sync()
            duration = time.time() - start_time
            
            self.sync_stats['total_syncs'] += 1
            self.sync_stats['last_sync_duration'] = duration
            self.last_sync_time = time.time()
            
            logger.info("Manual sync completed", duration=duration)
            return True
            
        except Exception as e:
            self.sync_stats['errors'] += 1
            logger.error("Manual sync failed", error=str(e))
            return False
    
    def get_sync_status(self) -> Dict[str, Any]:
        """Get sync status information."""
        return {
            'sync_enabled': self.config.sync_enabled,
            'sync_running': self.sync_running,
            'last_sync_time': self.last_sync_time,
            'sync_interval': self.config.sync_interval,
            'stats': self.sync_stats.copy()
        }
    
    def reset_sync_stats(self):
        """Reset sync statistics."""
        self.sync_stats = {
            'total_syncs': 0,
            'last_sync_duration': 0,
            'errors': 0
        }
        logger.info("Sync stats reset") 