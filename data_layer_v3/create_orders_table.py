#!/usr/bin/env python3
"""
Create Orders Table for Data Layer V3.

This script creates the orders table in the aagmanai database based on the orders.csv schema.
"""

import uuid
import json
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_layer import DataLayer
from config import DataLayerConfig


def create_orders_schema() -> Dict[str, Any]:
    """Create orders table schema based on orders.csv."""
    return {
        "type": "object",
        "properties": {
            "order_id": {"type": "string"},
            "user_id": {"type": "string"},
            "account_id": {"type": "string"},
            "broker_id": {"type": "string"},
            "instrument_id": {"type": "string"},
            "symbol": {"type": "string"},
            "broker_order_id": {"type": ["string", "null"]},
            "order_type": {"type": "string"},
            "transaction_type": {"type": "string"},
            "quantity": {"type": "integer"},
            "price": {"type": ["number", "null"]},
            "trigger_price": {"type": ["number", "null"]},
            "status": {"type": "string"},
            "validity": {"type": "string"},
            "product_type": {"type": "string"},
            "created_at": {"type": "string"},
            "updated_at": {"type": "string"},
            "completed_at": {"type": ["string", "null"]},
            "parent_order_id": {"type": ["string", "null"]},
            "comments": {"type": ["string", "null"]},
            "submitted_by": {"type": "string"},
            "source": {"type": "string"},
            "llm_intent_id": {"type": ["string", "null"]},
            "strategy_id": {"type": ["string", "null"]},
            "is_automated": {"type": "boolean"},
            "risk_score": {"type": ["number", "null"]},
            "stop_loss_price": {"type": ["number", "null"]},
            "take_profit_price": {"type": ["number", "null"]},
            "trailing_stop_percent": {"type": ["number", "null"]},
            "portfolio_id": {"type": ["string", "null"]},
            "goal_id": {"type": ["string", "null"]}
        },
        "required": [
            "order_id", "user_id", "account_id", "broker_id", "instrument_id", "symbol", 
            "order_type", "transaction_type", "quantity", "status", "validity", 
            "product_type", "created_at", "updated_at", "submitted_by", "source", "is_automated"
        ]
    }


def create_sample_orders_data(count: int = 20) -> List[Dict[str, Any]]:
    """Create sample orders data for testing."""
    orders_data = []
    
    # Sample data for orders
    users = ["user-123", "user-456", "user-789", "user-101", "user-202"]
    accounts = ["acc-001", "acc-002", "acc-003", "acc-004", "acc-005"]
    brokers = ["broker-001", "broker-002", "broker-003", "broker-004", "broker-005"]
    instruments = ["inst-001", "inst-002", "inst-003", "inst-004", "inst-005"]
    symbols = ["RELIANCE", "TCS", "INFY", "HDFC", "ICICIBANK", "WIPRO", "HCLTECH", "TECHM"]
    order_types = ["LIMIT", "MARKET", "STOP_LOSS", "STOP_LIMIT"]
    transaction_types = ["BUY", "SELL"]
    statuses = ["PENDING", "OPEN", "EXECUTED", "CANCELLED", "REJECTED"]
    validities = ["DAY", "IOC", "GTT"]
    product_types = ["CNC", "MIS", "NRML"]
    sources = ["extension", "web", "api"]
    submitted_by = ["user", "llm", "auto"]
    
    for i in range(count):
        # Generate timestamps
        created_at = datetime.now(timezone.utc) - timedelta(hours=count-i, minutes=i*5)
        updated_at = created_at + timedelta(minutes=i*2)
        completed_at = None
        if i % 3 == 0:  # Some orders are completed
            completed_at = updated_at + timedelta(minutes=30)
        
        # Generate order data
        order = {
            "order_id": str(uuid.uuid4()),
            "user_id": users[i % len(users)],
            "account_id": accounts[i % len(accounts)],
            "broker_id": brokers[i % len(brokers)],
            "instrument_id": instruments[i % len(instruments)],
            "symbol": symbols[i % len(symbols)],
            "broker_order_id": f"BROKER_{i+1000}" if i % 2 == 0 else None,
            "order_type": order_types[i % len(order_types)],
            "transaction_type": transaction_types[i % 2],
            "quantity": 100 + (i * 50),
            "price": 1500.0 + (i * 25.5) if i % 2 == 0 else None,
            "trigger_price": 1450.0 + (i * 20.0) if i % 4 == 0 else None,
            "status": statuses[i % len(statuses)],
            "validity": validities[i % len(validities)],
            "product_type": product_types[i % len(product_types)],
            "created_at": created_at.isoformat(),
            "updated_at": updated_at.isoformat(),
            "completed_at": completed_at.isoformat() if completed_at else None,
            "parent_order_id": str(uuid.uuid4()) if i % 5 == 0 else None,
            "comments": f"Order placed for {symbols[i % len(symbols)]} - Sample order {i+1}",
            "submitted_by": submitted_by[i % len(submitted_by)],
            "source": sources[i % len(sources)],
            "llm_intent_id": str(uuid.uuid4()) if i % 3 == 0 else None,
            "strategy_id": str(uuid.uuid4()) if i % 4 == 0 else None,
            "is_automated": i % 3 == 0,
            "risk_score": 0.1 + (i * 0.05),
            "stop_loss_price": 1400.0 + (i * 15.0) if i % 3 == 0 else None,
            "take_profit_price": 1600.0 + (i * 35.0) if i % 3 == 0 else None,
            "trailing_stop_percent": 2.5 + (i * 0.5) if i % 4 == 0 else None,
            "portfolio_id": str(uuid.uuid4()) if i % 2 == 0 else None,
            "goal_id": str(uuid.uuid4()) if i % 3 == 0 else None
        }
        
        orders_data.append(order)
    
    return orders_data


def create_orders_table():
    """Create the orders table and populate with sample data."""
    print("=== Creating Orders Table ===")
    
    # Configuration
    config = DataLayerConfig(sync_enabled=False)  # Loads all DB credentials from environment
    
    # Initialize Data Layer
    data_layer = DataLayer(config)
    
    try:
        # Connect to databases
        print("Connecting to databases...")
        data_layer.connect()
        print("✓ Connected successfully")
        
        # Define orders schema
        print("\nDefining orders schema...")
        orders_schema = create_orders_schema()
        data_layer.define_schema("orders", orders_schema, version=1)
        print("✓ Schema defined for orders table")
        
        # Generate sample data
        print("\nGenerating sample orders data...")
        sample_orders = create_sample_orders_data(count=30)
        print(f"✓ Generated {len(sample_orders)} sample orders")
        
        # Save data to database
        print("\nSaving orders data to database...")
        saved_count = 0
        
        for order in sample_orders:
            try:
                doc_id = data_layer.save_document("orders", order, order["order_id"])
                saved_count += 1
                if saved_count % 10 == 0:
                    print(f"✓ Saved {saved_count}/{len(sample_orders)} orders")
            except Exception as e:
                print(f"⚠️  Failed to save order {order['order_id']}: {e}")
        
        print(f"\n=== Orders Table Creation Complete ===")
        print(f"✓ Successfully saved {saved_count} orders")
        print(f"✓ Table: orders")
        print(f"✓ Database: aagmanai")
        
        # Verify data
        print("\nVerifying saved orders...")
        saved_orders = data_layer.find_documents("orders", {})
        if saved_orders:
            print(f"✓ Found {len(saved_orders)} orders in database")
            
            # Show sample order
            sample_order = saved_orders[0]
            print(f"\n📄 Sample Order:")
            print(f"  - Order ID: {sample_order['order_id']}")
            print(f"  - User ID: {sample_order['user_id']}")
            print(f"  - Symbol: {sample_order['symbol']}")
            print(f"  - Type: {sample_order['transaction_type']} {sample_order['order_type']}")
            print(f"  - Quantity: {sample_order['quantity']}")
            print(f"  - Status: {sample_order['status']}")
            print(f"  - Price: {sample_order.get('price', 'N/A')}")
            print(f"  - Automated: {sample_order['is_automated']}")
        else:
            print("❌ Could not find saved orders")
        
    except Exception as e:
        print(f"\n❌ Error creating orders table: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Disconnect
        data_layer.disconnect()
        print("✓ Disconnected from databases")


def get_orders_stats():
    """Get statistics about the orders table."""
    print("=== Orders Table Statistics ===")
    
    config = DataLayerConfig(sync_enabled=False)  # Loads all DB credentials from environment
    
    data_layer = DataLayer(config)
    
    try:
        data_layer.connect()
        
        # Get all orders
        all_orders = data_layer.find_documents("orders", {})
        
        if not all_orders:
            print("No orders found")
            return
        
        # Calculate statistics
        total_orders = len(all_orders)
        buy_orders = len([o for o in all_orders if o.get("transaction_type") == "BUY"])
        sell_orders = len([o for o in all_orders if o.get("transaction_type") == "SELL"])
        automated_orders = len([o for o in all_orders if o.get("is_automated")])
        
        unique_users = len(set(o.get("user_id") for o in all_orders))
        unique_symbols = len(set(o.get("symbol") for o in all_orders))
        
        status_counts = {}
        for order in all_orders:
            status = order.get("status", "UNKNOWN")
            status_counts[status] = status_counts.get(status, 0) + 1
        
        print(f"Total orders: {total_orders}")
        print(f"Buy orders: {buy_orders}")
        print(f"Sell orders: {sell_orders}")
        print(f"Automated orders: {automated_orders}")
        print(f"Unique users: {unique_users}")
        print(f"Unique symbols: {unique_symbols}")
        
        print(f"\nStatus distribution:")
        for status, count in status_counts.items():
            print(f"  {status}: {count}")
        
    except Exception as e:
        print(f"Error getting orders stats: {e}")
    finally:
        data_layer.disconnect()


if __name__ == "__main__":
    create_orders_table()
    print("\n" + "="*50)
    get_orders_stats()
