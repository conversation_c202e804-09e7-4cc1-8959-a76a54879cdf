#!/usr/bin/env python3
"""
Script to clear user data from CouchDB for testing purposes.
This allows testing the new user flow by removing existing user profiles and session mappings.
"""

import sys
import os

# Add backend_api_module/src to Python path to import env_config
backend_src_path = os.path.join(os.path.dirname(__file__), '..', 'backend_api_module', 'src')
sys.path.insert(0, backend_src_path)

# Import and setup environment from backend
from env_config import setup_real_db_environment
setup_real_db_environment()

from sql_queries import get_couchdb_connection

def clear_user_data(firebase_uid: str):
    """
    Clear user profile and session mapping data for a Firebase UID.
    
    Args:
        firebase_uid: Firebase user UID to clear data for
    """
    try:
        db = get_couchdb_connection()
        
        # Document IDs to delete
        user_profile_id = f"user_profile_{firebase_uid}"
        user_session_id = f"user_session_{firebase_uid}"
        
        deleted_count = 0
        
        # Delete user profile document
        if user_profile_id in db:
            doc = db[user_profile_id]
            db.delete(doc)
            print(f"✅ Deleted user profile: {user_profile_id}")
            deleted_count += 1
        else:
            print(f"ℹ️  User profile not found: {user_profile_id}")
        
        # Delete user session mapping
        if user_session_id in db:
            doc = db[user_session_id]
            db.delete(doc)
            print(f"✅ Deleted session mapping: {user_session_id}")
            deleted_count += 1
        else:
            print(f"ℹ️  Session mapping not found: {user_session_id}")
        
        if deleted_count > 0:
            print(f"\n🎉 Successfully cleared {deleted_count} documents for Firebase UID: {firebase_uid}")
            print("Now you can test the new user flow (step 3 should appear)!")
        else:
            print(f"\n⚠️  No documents found for Firebase UID: {firebase_uid}")
            
    except Exception as e:
        print(f"❌ Error clearing user data: {e}")
        raise


def main():
    """Main function to clear user data."""
    # Default Firebase UID from the logs
    default_firebase_uid = "2b0tSFkvEZZUJ2DZPJWgV4OVcAi1"
    
    if len(sys.argv) > 1:
        firebase_uid = sys.argv[1]
    else:
        firebase_uid = default_firebase_uid
        print(f"Using default Firebase UID from logs: {firebase_uid}")
    
    print(f"🗑️  Clearing user data for Firebase UID: {firebase_uid}")
    print("-" * 60)
    
    clear_user_data(firebase_uid)


if __name__ == "__main__":
    main() 