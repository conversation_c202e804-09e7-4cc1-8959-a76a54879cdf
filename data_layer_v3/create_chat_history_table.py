#!/usr/bin/env python3
"""
Create Tables for Data Layer V3.

This script creates tables in the aagmanai database based on CSV schemas.
"""

import uuid
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List
from data_layer import DataLayer
from config import DataLayerConfig


def create_chat_history_schema() -> Dict[str, Any]:
    """Create chat_history table schema based on CSV."""
    return {
        "type": "object",
        "properties": {
            "chat_id": {"type": "string"},
            "user_id": {"type": "string"},
            "conversation_id": {"type": "string"},
            "timestamp": {"type": "string"},
            "role": {"type": "string"},
            "message": {"type": "string"},
            "llm_model_version": {"type": ["string", "null"]},
            "meta_json": {"type": "object"},
            "order_id": {"type": ["string", "null"]},
            "type": {"type": "string"}
        },
        "required": ["chat_id", "user_id", "conversation_id", "message", "role", "timestamp"]
    }


def generate_dummy_chat_history(count: int = 20) -> List[Dict[str, Any]]:
    """Generate dummy chat history data."""
    dummy_data = []
    
    # Sample users and conversations
    users = [
        "user-123", "user-456", "user-789", "user-101", "user-202"
    ]
    
    conversations = [
        "conv-001", "conv-002", "conv-003", "conv-004", "conv-005"
    ]
    
    roles = ["user", "agent", "system", "assistant"]
    conversation_types = ["chat", "order", "monitoring"]
    llm_models = ["gpt-4", "gpt-3.5-turbo", "claude-3", "gemini-pro"]
    
    sample_messages = [
        "Hello, how can I help you today?",
        "I'd like to place an order for some stocks",
        "What's the current market status?",
        "Can you show me my portfolio?",
        "I want to set up price alerts",
        "What are the best performing stocks today?",
        "How do I transfer funds between accounts?",
        "Can you explain the trading fees?",
        "I need help with my account settings",
        "What's the difference between market and limit orders?"
    ]
    
    for i in range(count):
        # Generate timestamps in sequence
        timestamp = datetime.now() - timedelta(hours=count-i, minutes=i*2)
        
        # Alternate between user and agent messages
        is_user = i % 2 == 0
        role = "user" if is_user else "agent"
        
        # Create chat history entry
        chat_entry = {
            "chat_id": str(uuid.uuid4()),
            "user_id": users[i % len(users)],
            "conversation_id": conversations[i % len(conversations)],
            "timestamp": timestamp.isoformat(),
            "role": role,
            "message": sample_messages[i % len(sample_messages)],
            "llm_model_version": llm_models[i % len(llm_models)] if not is_user else None,
            "meta_json": {
                "tokens_used": 150 + (i * 10),
                "response_time": 1.2 + (i * 0.1),
                "confidence_score": 0.85 + (i * 0.01),
                "actions_suggested": ["view_portfolio", "place_order", "set_alert"] if not is_user else []
            },
            "order_id": str(uuid.uuid4()) if i % 5 == 0 else None,  # Some messages linked to orders
            "type": conversation_types[i % len(conversation_types)]
        }
        
        dummy_data.append(chat_entry)
    
    return dummy_data


def create_chat_history_table():
    """Create the chat_history table and populate with dummy data."""
    print("=== Creating Chat History Table ===")
    
    # Configuration - use environment variables
    config = DataLayerConfig()
    
    # Initialize Data Layer
    data_layer = DataLayer(config)
    
    try:
        # Connect to databases
        print("Connecting to databases...")
        data_layer.connect()
        print("✓ Connected successfully")
        
        # Define chat_history schema
        print("\nDefining chat_history schema...")
        chat_schema = create_chat_history_schema()
        data_layer.define_schema("chat_history", chat_schema, version=1)
        print("✓ Schema defined for chat_history")
        
        # Generate dummy data
        print("\nGenerating dummy chat history data...")
        dummy_chat_data = generate_dummy_chat_history(count=50)
        print(f"✓ Generated {len(dummy_chat_data)} chat history entries")
        
        # Save data to database
        print("\nSaving chat history data to database...")
        saved_count = 0
        
        for chat_entry in dummy_chat_data:
            try:
                doc_id = data_layer.save_document("chat_history", chat_entry, chat_entry["chat_id"])
                saved_count += 1
                if saved_count % 10 == 0:
                    print(f"✓ Saved {saved_count}/{len(dummy_chat_data)} entries")
            except Exception as e:
                print(f"⚠️  Failed to save chat entry {chat_entry['chat_id']}: {e}")
        
        print(f"\n=== Chat History Table Creation Complete ===")
        print(f"✓ Successfully saved {saved_count} chat history entries")
        print(f"✓ Table: chat_history")
        print(f"✓ Database: aagmanai")
        
        # Verify data
        print("\nVerifying data...")
        all_chat_entries = data_layer.find_documents("chat_history", {})
        print(f"✓ Found {len(all_chat_entries)} total chat history entries in database")
        
        # Show sample data
        if all_chat_entries:
            sample = all_chat_entries[0]
            print(f"✓ Sample entry: {sample['role']} - {sample['message'][:50]}...")
        
    except Exception as e:
        print(f"\n❌ Error creating chat_history table: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Disconnect
        data_layer.disconnect()
        print("✓ Disconnected from databases")


def get_chat_history_stats():
    """Get statistics about the chat_history table."""
    print("=== Chat History Table Statistics ===")
    
    config = DataLayerConfig()
    
    data_layer = DataLayer(config)
    
    try:
        data_layer.connect()
        
        # Get all chat history entries
        all_entries = data_layer.find_documents("chat_history", {})
        
        if not all_entries:
            print("No chat history entries found")
            return
        
        # Calculate statistics
        total_entries = len(all_entries)
        user_messages = len([e for e in all_entries if e.get("role") == "user"])
        agent_messages = len([e for e in all_entries if e.get("role") == "agent"])
        system_messages = len([e for e in all_entries if e.get("role") == "system"])
        assistant_messages = len([e for e in all_entries if e.get("role") == "assistant"])
        
        unique_users = len(set(e.get("user_id") for e in all_entries))
        unique_conversations = len(set(e.get("conversation_id") for e in all_entries))
        
        entries_with_orders = len([e for e in all_entries if e.get("order_id")])
        
        print(f"Total entries: {total_entries}")
        print(f"User messages: {user_messages}")
        print(f"Agent messages: {agent_messages}")
        print(f"System messages: {system_messages}")
        print(f"Assistant messages: {assistant_messages}")
        print(f"Unique users: {unique_users}")
        print(f"Unique conversations: {unique_conversations}")
        print(f"Entries linked to orders: {entries_with_orders}")
        
        # Show conversation types
        conversation_types = {}
        for entry in all_entries:
            conv_type = entry.get("type", "unknown")
            conversation_types[conv_type] = conversation_types.get(conv_type, 0) + 1
        
        print("\nConversation types:")
        for conv_type, count in conversation_types.items():
            print(f"  {conv_type}: {count}")
        
    except Exception as e:
        print(f"❌ Error getting statistics: {e}")
    
    finally:
        data_layer.disconnect()


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "stats":
        get_chat_history_stats()
    else:
        create_chat_history_table() 