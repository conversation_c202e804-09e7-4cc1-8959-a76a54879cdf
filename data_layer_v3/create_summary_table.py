#!/usr/bin/env python3
"""
Create Summary Table for Data Layer V3.

This script creates the summary table in the aagmanai database based on the summary.csv schema.
"""

import uuid
import json
from datetime import datetime, timezone
from typing import Dict, Any
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_layer import DataLayer
from config import DataLayerConfig


def create_summary_schema() -> Dict[str, Any]:
    """Create summary table schema based on summary.csv."""
    return {
        "type": "object",
        "properties": {
            "summary_id": {"type": "string"},
            "user_id": {"type": "string"},
            "conversation_id": {"type": "string"},
            "timestamp": {"type": "string"},
            "summary": {"type": "string"},
            "llm_model_version": {"type": ["string", "null"]},
            "meta_json": {"type": "object"}
        },
        "required": ["summary_id", "user_id", "conversation_id", "summary", "timestamp"]
    }


def create_sample_summary_record() -> Dict[str, Any]:
    """Create a sample summary record for testing."""
    return {
        "summary_id": str(uuid.uuid4()),
        "user_id": "user-123",
        "conversation_id": "conv-001",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "summary": "User inquired about portfolio performance and requested assistance with setting up price alerts for tech stocks. Agent provided portfolio overview and helped configure alerts for AAPL, GOOGL, and TSLA with 5% threshold notifications.",
        "llm_model_version": "gpt-4-turbo",
        "meta_json": {
            "tokens_used": 245,
            "response_time": 1.8,
            "confidence_score": 0.92,
            "actions_performed": ["view_portfolio", "set_price_alerts"],
            "stocks_mentioned": ["AAPL", "GOOGL", "TSLA"],
            "alert_threshold": "5%"
        }
    }


def create_summary_table():
    """Create the summary table and save a single test record."""
    print("=== Creating Summary Table ===")
    
    # Configuration
    config = DataLayerConfig(sync_enabled=False)  # Loads all DB credentials from environment
    
    # Initialize Data Layer
    data_layer = DataLayer(config)
    
    try:
        # Connect to databases
        print("Connecting to databases...")
        data_layer.connect()
        print("✓ Connected successfully")
        
        # Define summary schema
        print("\nDefining summary schema...")
        summary_schema = create_summary_schema()
        data_layer.define_schema("summary", summary_schema, version=1)
        print("✓ Schema defined for summary table")
        
        # Create sample record
        print("\nCreating sample summary record...")
        sample_record = create_sample_summary_record()
        print(f"✓ Created sample record with ID: {sample_record['summary_id']}")
        
        # Save record to database
        print("\nSaving summary record to database...")
        doc_id = data_layer.save_document("summary", sample_record, sample_record["summary_id"])
        print(f"✓ Successfully saved summary record with document ID: {doc_id}")
        
        print(f"\n=== Summary Table Creation Complete ===")
        print(f"✓ Table: summary")
        print(f"✓ Database: aagmanai")
        print(f"✓ Record saved with ID: {sample_record['summary_id']}")
        
        # Verify data
        print("\nVerifying saved record...")
        saved_record = data_layer.find_documents("summary", {"summary_id": sample_record["summary_id"]})
        if saved_record:
            record = saved_record[0]
            print(f"✓ Found saved record:")
            print(f"  - Summary ID: {record['summary_id']}")
            print(f"  - User ID: {record['user_id']}")
            print(f"  - Conversation ID: {record['conversation_id']}")
            print(f"  - Summary: {record['summary'][:100]}...")
            print(f"  - LLM Model: {record.get('llm_model_version', 'N/A')}")
            print(f"  - Tokens Used: {record['meta_json'].get('tokens_used', 'N/A')}")
        else:
            print("❌ Could not find saved record")
        
    except Exception as e:
        print(f"\n❌ Error creating summary table: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Disconnect
        data_layer.disconnect()
        print("✓ Disconnected from databases")


if __name__ == "__main__":
    create_summary_table()
