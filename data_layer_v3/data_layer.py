"""
Main Data Layer V3 class.

Combines CouchDB and PouchDB functionality with synchronization.
"""

import structlog
from typing import Dict, Any, List, Optional
from config import DataLayerConfig
from exceptions import (
    ConnectionError,
    DocumentNotFoundError,
    SchemaValidationError,
    ConfigurationError
)
from couchdb_manager import CouchDBManager
from pouchdb_manager import PouchDBManager
from sync_manager import SyncManager

logger = structlog.get_logger(__name__)


class DataLayer:
    """Main Data Layer V3 class."""
    
    def __init__(self, config: DataLayerConfig):
        """Initialize Data Layer V3."""
        self.config = config
        self.couchdb_manager = None
        self.pouchdb_manager = None
        self.sync_manager = None
        self.schemas = {}
        self.connected = False
        
        logger.info("Data Layer V3 initialized", 
                   couchdb_url=config.couchdb_url,
                   pouchdb_name=config.pouchdb_name)
    
    def connect(self):
        """Connect to both CouchDB and PouchDB."""
        try:
            # Initialize CouchDB manager
            self.couchdb_manager = CouchDBManager(self.config)
            
            # Test CouchDB connection
            if not self.couchdb_manager.test_connection():
                raise ConnectionError("Failed to connect to CouchDB")
            
            # Create CouchDB database if it doesn't exist
            self.couchdb_manager.create_database()
            
            # Initialize PouchDB manager
            self.pouchdb_manager = PouchDBManager(self.config)
            
            # Initialize sync manager
            self.sync_manager = SyncManager(self.config, self.couchdb_manager, self.pouchdb_manager)
            
            # Start sync if enabled
            if self.config.sync_enabled:
                self.sync_manager.start_sync()
            
            self.connected = True
            logger.info("Data Layer V3 connected successfully")
            
        except Exception as e:
            logger.error("Failed to connect", error=str(e))
            raise ConnectionError(f"Failed to connect: {e}")
    
    def disconnect(self):
        """Disconnect from databases."""
        try:
            # Stop sync
            if self.sync_manager:
                self.sync_manager.stop_sync()
            
            # Close PouchDB connection
            if self.pouchdb_manager:
                self.pouchdb_manager.close()
            
            self.connected = False
            logger.info("Data Layer V3 disconnected")
            
        except Exception as e:
            logger.error("Error during disconnect", error=str(e))
    
    def define_schema(self, table_name: str, schema: Dict[str, Any], version: int = 1):
        """Define a schema for a table."""
        try:
            if not self.connected:
                raise ConnectionError("Not connected to databases")
            
            # Store schema locally
            self.schemas[table_name] = {
                "schema": schema,
                "version": version
            }
            
            logger.info("Schema defined", table_name=table_name, version=version)
            
        except Exception as e:
            logger.error("Failed to define schema", table_name=table_name, error=str(e))
            raise SchemaValidationError(f"Failed to define schema: {e}")
    
    def validate_document(self, table_name: str, document: Dict[str, Any]) -> bool:
        """Validate a document against its schema."""
        try:
            if table_name not in self.schemas:
                raise SchemaValidationError(f"Schema not defined for table '{table_name}'")
            
            schema = self.schemas[table_name]["schema"]
            required_fields = schema.get("required", [])
            
            # Check required fields
            for field in required_fields:
                if field not in document:
                    raise SchemaValidationError(f"Missing required field: {field}")
            
            # Check field types (simplified validation)
            properties = schema.get("properties", {})
            for field_name, field_value in document.items():
                if field_name in properties:
                    field_type = properties[field_name].get("type")
                    if field_type == "integer" and not isinstance(field_value, int):
                        raise SchemaValidationError(f"Field '{field_name}' must be integer")
                    elif field_type == "string" and not isinstance(field_value, str):
                        raise SchemaValidationError(f"Field '{field_name}' must be string")
                    elif field_type == "boolean" and not isinstance(field_value, bool):
                        raise SchemaValidationError(f"Field '{field_name}' must be boolean")
            
            return True
            
        except Exception as e:
            logger.error("Document validation failed", table_name=table_name, error=str(e))
            raise SchemaValidationError(f"Validation failed: {e}")
    
    def save_document(self, table_name: str, document: Dict[str, Any], doc_id: Optional[str] = None) -> str:
        """Save a document to both databases."""
        try:
            if not self.connected:
                raise ConnectionError("Not connected to databases")
            
            # Validate document if schema validation is enabled
            if self.config.schema_validation_enabled:
                self.validate_document(table_name, document)
            
            # Add table name to document
            document["table_name"] = table_name
            
            # Generate document ID if not provided
            if not doc_id:
                import uuid
                doc_id = str(uuid.uuid4())
            
            # Save to PouchDB (local)
            pouchdb_result = self.pouchdb_manager.save_document(doc_id, document)
            
            # Save to CouchDB (remote)
            couchdb_result = self.couchdb_manager.save_document(doc_id, document)
            
            logger.info("Document saved successfully", 
                       table_name=table_name, 
                       doc_id=doc_id,
                       pouchdb_rev=pouchdb_result.get("rev"),
                       couchdb_rev=couchdb_result.get("rev"))
            
            return doc_id
            
        except Exception as e:
            logger.error("Failed to save document", table_name=table_name, doc_id=doc_id, error=str(e))
            raise
    
    def get_document(self, table_name: str, doc_id: str) -> Optional[Dict[str, Any]]:
        """Get a document from local PouchDB first, then CouchDB if not found."""
        try:
            if not self.connected:
                raise ConnectionError("Not connected to databases")
            
            # Try PouchDB first (local)
            document = self.pouchdb_manager.get_document(doc_id)
            
            if document and document.get("table_name") == table_name:
                logger.debug("Document found in PouchDB", table_name=table_name, doc_id=doc_id)
                return document
            
            # Try CouchDB (remote)
            document = self.couchdb_manager.get_document(doc_id)
            
            if document and document.get("table_name") == table_name:
                logger.debug("Document found in CouchDB", table_name=table_name, doc_id=doc_id)
                # Sync to PouchDB for future local access
                self.pouchdb_manager.save_document(doc_id, document)
                return document
            
            logger.debug("Document not found", table_name=table_name, doc_id=doc_id)
            return None
            
        except Exception as e:
            logger.error("Failed to get document", table_name=table_name, doc_id=doc_id, error=str(e))
            raise
    
    def find_documents(self, table_name: str, selector: Dict[str, Any], limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Find documents using selector."""
        try:
            if not self.connected:
                raise ConnectionError("Not connected to databases")
            
            # Add table name to selector
            selector["table_name"] = table_name
            
            # Try PouchDB first (local)
            documents = self.pouchdb_manager.find_documents(selector, limit)
            
            if documents:
                logger.debug("Documents found in PouchDB", table_name=table_name, count=len(documents))
                return documents
            
            # Try CouchDB (remote)
            documents = self.couchdb_manager.find_documents(selector, limit)
            
            if documents:
                logger.debug("Documents found in CouchDB", table_name=table_name, count=len(documents))
                # Sync to PouchDB for future local access
                for doc in documents:
                    doc_id = doc.get("_id") or doc.get("id")
                    if doc_id:
                        self.pouchdb_manager.save_document(doc_id, doc)
                return documents
            
            return []
            
        except Exception as e:
            logger.error("Failed to find documents", table_name=table_name, selector=selector, error=str(e))
            raise
    
    def update_document(self, table_name: str, doc_id: str, document: Dict[str, Any]) -> Dict[str, Any]:
        """Update a document."""
        try:
            if not self.connected:
                raise ConnectionError("Not connected to databases")
            
            # Get existing document
            existing_doc = self.get_document(table_name, doc_id)
            if not existing_doc:
                raise DocumentNotFoundError(f"Document not found: {doc_id}")
            
            # Merge with existing document
            existing_doc.update(document)
            existing_doc["table_name"] = table_name
            
            # Save updated document
            self.save_document(table_name, existing_doc, doc_id)
            
            logger.info("Document updated successfully", table_name=table_name, doc_id=doc_id)
            return existing_doc
            
        except Exception as e:
            logger.error("Failed to update document", table_name=table_name, doc_id=doc_id, error=str(e))
            raise
    
    def delete_document(self, table_name: str, doc_id: str) -> bool:
        """Delete a document from both databases."""
        try:
            if not self.connected:
                raise ConnectionError("Not connected to databases")
            
            # Get document to get revision
            document = self.get_document(table_name, doc_id)
            if not document:
                raise DocumentNotFoundError(f"Document not found: {doc_id}")
            
            # Delete from both databases
            pouchdb_rev = document.get("_rev") or "1-0"
            couchdb_rev = document.get("_rev") or "1-0"
            
            self.pouchdb_manager.delete_document(doc_id, pouchdb_rev)
            self.couchdb_manager.delete_document(doc_id, couchdb_rev)
            
            logger.info("Document deleted successfully", table_name=table_name, doc_id=doc_id)
            return True
            
        except Exception as e:
            logger.error("Failed to delete document", table_name=table_name, doc_id=doc_id, error=str(e))
            raise
    
    def manual_sync(self) -> bool:
        """Perform manual synchronization."""
        if not self.connected or not self.sync_manager:
            raise ConnectionError("Not connected or sync not available")
        
        return self.sync_manager.manual_sync()
    
    def get_sync_status(self) -> Dict[str, Any]:
        """Get synchronization status."""
        if not self.sync_manager:
            return {"sync_enabled": False, "sync_running": False}
        
        return self.sync_manager.get_sync_status()
    
    def __enter__(self):
        """Context manager entry."""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect() 