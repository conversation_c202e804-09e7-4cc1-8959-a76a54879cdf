# CouchDB-compatible functions for Backend API Module
import os
import uuid
import couchdb
from datetime import datetime
from typing import Optional, Dict, Any, List, Tuple
import json

def get_couchdb_connection() -> couchdb.Database:
    """Get CouchDB database connection."""
    host = os.getenv("COUCHDB_HOST", "localhost")
    port = os.getenv("COUCHDB_PORT", "5984")
    username = os.getenv("COUCHDB_USERNAME")
    password = os.getenv("COUCHDB_PASSWORD")
    db_name = os.getenv("COUCHDB_DATABASE", "aagmanai")
    
    if not username:
        raise ValueError("COUCHDB_USERNAME must be set in environment variables")
    if not password:
        raise ValueError("COUCHDB_PASSWORD must be set in environment variables")
    
    # URL encode username and password to handle special characters
    import urllib.parse
    encoded_username = urllib.parse.quote(username, safe='')
    encoded_password = urllib.parse.quote(password, safe='')
    
    # Build URL with proper encoding
    url = f"http://{encoded_username}:{encoded_password}@{host}:{port}/"
    
    try:
        server = couchdb.Server(url)
        
        # Test connection by listing databases (works with older versions)
        try:
            databases = list(server)
            print(f"Connected to CouchDB. Available databases: {databases}")
        except Exception as e:
            # If we get an auth error, try the alternative connection method
            if 'unauthorized' in str(e).lower() or 'not a server admin' in str(e).lower():
                print(f"Auth error with URL method: {e}")
                raise e
            else:
                # Other error, try to continue
                print(f"Warning: {e}")
        
        # Get or create database
        if db_name in server:
            print(f"Using existing database: {db_name}")
            return server[db_name]
        else:
            print(f"Creating new database: {db_name}")
            return server.create(db_name)
            
    except Exception as e:
        print(f"Error connecting to CouchDB: {e}")
        print(f"URL (with encoded credentials): http://{encoded_username}:***@{host}:{port}/")
        print(f"Original username: {username}")
        print(f"Original password length: {len(password) if password else 0}")
        raise

# SQL Query equivalents (for reference)
SQL_QUERIES = {
    "extract_chat_history": """
        SELECT 
            chat_id,
            user_id,
            conversation_id,
            timestamp,
            role,
            message,
            llm_model_version,
            meta_json,
            order_id,
            type
        FROM chat_history 
        WHERE user_id = %s AND conversation_id = %s
        ORDER BY timestamp ASC
    """,
    
    "save_chat_message": """
        INSERT INTO chat_history (
            user_id, conversation_id, timestamp, role, message,
            llm_model_version, meta_json, order_id, type
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        RETURNING chat_id
    """,
    
    "fetch_summary": """
        SELECT 
            summary_id,
            user_id,
            conversation_id,
            timestamp,
            summary,
            llm_model_version,
            meta_json
        FROM summary 
        WHERE user_id = %s AND conversation_id = %s
        ORDER BY timestamp DESC
    """,
    
    "fetch_summary_by_id": """
        SELECT 
            summary_id,
            user_id,
            conversation_id,
            timestamp,
            summary,
            llm_model_version,
            meta_json
        FROM summary 
        WHERE summary_id = %s
    """,
    
    "fetch_summaries_by_user": """
        SELECT 
            summary_id,
            user_id,
            conversation_id,
            timestamp,
            summary,
            llm_model_version,
            meta_json
        FROM summary 
        WHERE user_id = %s
        ORDER BY timestamp DESC
    """,
    
    "fetch_chat_history_after_timestamp": """
        SELECT 
            chat_id,
            user_id,
            conversation_id,
            timestamp,
            role,
            message,
            llm_model_version,
            meta_json,
            order_id,
            type
        FROM chat_history 
        WHERE user_id = %s AND conversation_id = %s AND timestamp > %s
        ORDER BY timestamp ASC
    """,
    
    "save_summary": """
        INSERT INTO summary (
            summary_id,
            user_id,
            conversation_id,
            timestamp,
            summary,
            llm_model_version,
            meta_json
        ) VALUES (%s, %s, %s, %s, %s, %s, %s)
        RETURNING summary_id
    """,
    
    "fetch_orders_by_user": """
        SELECT 
            order_id,
            user_id,
            account_id,
            broker_id,
            instrument_id,
            symbol,
            broker_order_id,
            order_type,
            transaction_type,
            quantity,
            price,
            trigger_price,
            status,
            validity,
            product_type,
            created_at,
            updated_at,
            completed_at,
            parent_order_id,
            comments,
            submitted_by,
            source,
            llm_intent_id,
            strategy_id,
            is_automated,
            risk_score,
            stop_loss_price,
            take_profit_price,
            trailing_stop_percent,
            portfolio_id,
            goal_id
        FROM orders 
        WHERE user_id = %s
        AND (%s IS NULL OR broker_id = %s)
        AND (%s IS NULL OR status = %s)
        ORDER BY created_at DESC
    """,
    
    "fetch_monitoring_by_user": """
        SELECT 
            monitoring_id,
            user_id,
            account_id,
            broker_id,
            symbol,
            broker_trade_id,
            execution_time,
            executed_quantity,
            executed_price,
            transaction_type,
            exchange,
            brokerage_fee,
            taxes_fees,
            net_amount,
            status,
            exec_ref,
            created_at,
            order_type,
            quantity,
            price,
            trigger_price,
            validity,
            product_type,
            desc,
            stop_loss_price,
            take_profit_price,
            trailing_stop_percent
        FROM monitoring 
        WHERE user_id = %s
        AND (%s IS NULL OR status = %s)
        AND (%s IS NULL OR execution_time >= %s)
        ORDER BY execution_time DESC
    """
}


def extract_chat_history(user_id: str, conversation_id: str) -> List[Dict[str, Any]]:
    """
    Extract chat history for a specific user and conversation.
    
    Args:
        user_id: The user's unique identifier
        conversation_id: The conversation's unique identifier
    
    Returns:
        List[Dict[str, Any]]: List of chat history records as dictionaries
        
        Example: [{'chat_id': '005fe020-1942-4432-a620-7a6d1edf1607', 'user_id': 'user-123', 'conversation_id': 'conv-001', 'timestamp': '2025-07-16T12:24:00.860668', 'role': 'user', 'message': 'Hello, how can I help you today?', 'llm_model_version': None, 'meta_json': {'tokens_used': 350, 'response_time': 3.2}, 'order_id': '69a6c75a-08dc-43f9-9d6a-27ba12fa7557', 'type': 'monitoring'}]
    """
    db = get_couchdb_connection()
    
    # Query CouchDB for chat history documents
    # Using manual iteration based on user_id and conversation_id
    results = []
    
    try:
        # Use manual document iteration (no temporary views)
        for doc_id in db:
            try:
                doc = db[doc_id]
                
                # Check for chat_history documents using multiple criteria
                is_chat_history = (
                    doc.get('table_name') == 'chat_history' or 
                    'chat_id' in doc or
                    (doc.get('user_id') and doc.get('conversation_id') and doc.get('message'))
                )
                
                if (is_chat_history and 
                    doc.get('user_id') == user_id and 
                    doc.get('conversation_id') == conversation_id):
                    results.append({
                        'chat_id': doc.get('chat_id', doc.get('_id')),
                        'user_id': doc.get('user_id'),
                        'conversation_id': doc.get('conversation_id'),
                        'timestamp': doc.get('timestamp'),
                        'role': doc.get('role'),
                        'message': doc.get('message'),
                        'llm_model_version': doc.get('llm_model_version'),
                        'meta_json': doc.get('meta_json', {}),
                        'order_id': doc.get('order_id'),
                        'type': doc.get('type', doc.get('message_type', 'message')),  # Use 'type' field first
                        'sender': doc.get('role'),  # For compatibility
                        'message_type': doc.get('type', 'message')  # Add message_type for compatibility
                    })
            except Exception as doc_error:
                # Skip invalid documents
                continue
    
    except Exception as e:
        print(f"Error querying chat history: {e}")
        return []
    
    # Sort by timestamp
    results.sort(key=lambda x: x.get('timestamp', ''))
    return results


def save_chat_message(
    cursor,  # For compatibility with existing interface
    user_id: str,
    conversation_id: str,
    role: str,
    message: str,
    llm_model_version: Optional[str] = None,
    meta_json: Optional[Dict[str, Any]] = None,
    order_id: Optional[str] = None,
    type: str = "message",
    timestamp: Optional[datetime] = None
) -> Optional[str]:
    """
    Save a chat message to the conversation history.
    
    Args:
        cursor: Database cursor (for compatibility - not used in CouchDB)
        user_id: The user's unique identifier
        conversation_id: The conversation's unique identifier
        role: The role of the message sender (e.g., 'user', 'assistant', 'system')
        message: The message content
        llm_model_version: The LLM model version used (optional)
        meta_json: Additional metadata as a dictionary (optional)
        order_id: Message order in the conversation (optional)
        type: Message type (default: 'message')
        timestamp: Message timestamp (default: current time)
    
    Returns:
        str: The chat_id of the inserted message
    """
    if timestamp is None:
        timestamp = datetime.now().isoformat()
    elif isinstance(timestamp, datetime):
        timestamp = timestamp.isoformat()
    
    # Handle meta_json
    if meta_json is None:
        meta_json = {}
    
    db = get_couchdb_connection()
    
    try:
        # Generate unique chat_id
        chat_id = str(uuid.uuid4())
        
        # Create document
        doc = {
            '_id': chat_id,
            'type': 'chat_history',
            'user_id': user_id,
            'conversation_id': conversation_id,
            'timestamp': timestamp,
            'role': role,
            'message': message,
            'llm_model_version': llm_model_version,
            'meta_json': meta_json,
            'order_id': order_id,
            'message_type': type
        }
        
        # Save to CouchDB
        result = db.save(doc)
        # Handle different return types from CouchDB save method
        if isinstance(result, tuple):
            doc_id, rev = result
        else:
            doc_id = result
        return doc_id
        
    except Exception as e:
        print(f"Error saving chat message: {e}")
        raise e


def fetch_summary(cursor, user_id: str, conversation_id: str) -> List[Dict[str, Any]]:
    """
    Fetch summary records from the summary table by user_id and conversation_id.
    
    Args:
        cursor: Database cursor (for compatibility - not used in CouchDB)
        user_id: The user's unique identifier
        conversation_id: The conversation's unique identifier
    
    Returns:
        List[Dict[str, Any]]: List of summary records as dictionaries
        
        Example: [{'summary_id': '0d586242-e66c-4ec7-838f-c8826cd146c4', 'user_id': 'user-123', 'conversation_id': 'conv-001', 'timestamp': '2025-07-18T13:51:45.937990', 'summary': 'This is a test summary created by CouchDB test', 'llm_model_version': 'gpt-4-test', 'meta_json': {'test': True, 'created_by': 'couchdb_test'}}]
    """
    db = get_couchdb_connection()
    results = []
    
    try:
        # Use manual document iteration (no temporary views)
        for doc_id in db:
            try:
                doc = db[doc_id]
                # Check for summary documents - check type field first, then fallbacks
                is_summary = (doc.get('type') == 'summary' or 
                             doc.get('table_name') == 'summary' or 
                             doc.get('_id', '').startswith('summary') or
                             'summary_id' in doc)  # Has summary_id field indicates summary
                
                if (is_summary and 
                    doc.get('user_id') == user_id and 
                    doc.get('conversation_id') == conversation_id):
                    results.append({
                        'summary_id': doc.get('summary_id', doc.get('_id')),
                        'user_id': doc.get('user_id'),
                        'conversation_id': doc.get('conversation_id'),
                        'timestamp': doc.get('timestamp'),
                        'summary': doc.get('summary'),
                        'llm_model_version': doc.get('llm_model_version'),
                        'meta_json': doc.get('meta_json', {}),
                        'datetime': doc.get('timestamp')  # For compatibility
                    })
            except Exception as doc_error:
                # Skip invalid documents
                continue
            
    except Exception as e:
        print(f"Error querying summaries: {e}")
        return []
    
    # Sort by timestamp descending
    results.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
    return results


def fetch_summary_by_id_sql(summary_id: str) -> Optional[Tuple[str, str, str, str, str, str, Dict[str, Any]]]:
    """
    Fetch a specific summary record by summary_id.
    
    Args:
        summary_id: The summary's unique identifier
    
    Returns:
        Optional[Tuple[str, str, str, str, str, str, Dict[str, Any]]]: 
        Summary record as tuple or None if not found, with the following structure:
        (summary_id, user_id, conversation_id, timestamp, summary, llm_model_version, meta_json)
        
        Example: ('0d586242-e66c-4ec7-838f-c8826cd146c4', 'user-123', 'conv-001', '2025-07-18T13:51:45.937990', 'This is a test summary created by CouchDB test', 'gpt-4-test', {'test': True, 'created_by': 'couchdb_test'})
    """
    conn = psycopg2.connect(get_postgres_connection_string())
    cursor = conn.cursor()
    
    try:
        cursor.execute(SQL_QUERIES["fetch_summary_by_id"], (summary_id,))
        result = cursor.fetchone()
        return result
        
    except Exception as e:
        print(f"Error fetching summary by ID: {e}")
        return None
    finally:
        cursor.close()
        conn.close()


def fetch_summaries_by_user_sql(user_id: str) -> List[Tuple[str, str, str, str, str, str, Dict[str, Any]]]:
    """
    Fetch all summary records for a specific user.
    
    Args:
        user_id: The user's unique identifier
    
    Returns:
        List[Tuple[str, str, str, str, str, str, Dict[str, Any]]]: 
        List of summary records for the user as tuples with the following structure:
        (summary_id, user_id, conversation_id, timestamp, summary, llm_model_version, meta_json)
        
        Example: [('0d586242-e66c-4ec7-838f-c8826cd146c4', 'user-123', 'conv-001', '2025-07-18T13:51:45.937990', 'This is a test summary created by CouchDB test', 'gpt-4-test', {'test': True, 'created_by': 'couchdb_test'})]
    """
    conn = psycopg2.connect(get_postgres_connection_string())
    cursor = conn.cursor()
    
    try:
        cursor.execute(SQL_QUERIES["fetch_summaries_by_user"], (user_id,))
        results = cursor.fetchall()
        return results
        
    except Exception as e:
        print(f"Error fetching summaries by user: {e}")
        return []
    finally:
        cursor.close()
        conn.close()


def fetch_chat_history_after_timestamp_sql(user_id: str, conversation_id: str, timestamp: datetime) -> List[Tuple[str, str, str, str, str, str, Optional[str], Dict[str, Any], str, str]]:
    """
    Fetch chat history after a particular timestamp for a specific user and conversation.
    
    Args:
        user_id: The user's unique identifier
        conversation_id: The conversation's unique identifier
        timestamp: The timestamp to filter after (messages after this time will be returned)
    
    Returns:
        List[Tuple[str, str, str, str, str, str, Optional[str], Dict[str, Any], str, str]]: 
        List of chat history records as tuples with the following structure:
        (chat_id, user_id, conversation_id, timestamp, role, message, llm_model_version, meta_json, order_id, type)
        
        Example: [('005fe020-1942-4432-a620-7a6d1edf1607', 'user-123', 'conv-001', '2025-07-16T12:24:00.860668', 'user', 'Hello, how can I help you today?', None, {'tokens_used': 350, 'response_time': 3.2}, '69a6c75a-08dc-43f9-9d6a-27ba12fa7557', 'monitoring')]
    """
    conn = psycopg2.connect(get_postgres_connection_string())
    cursor = conn.cursor()
    
    try:
        cursor.execute(SQL_QUERIES["fetch_chat_history_after_timestamp"], (user_id, conversation_id, timestamp))
        results = cursor.fetchall()
        return results
        
    except Exception as e:
        print(f"Error fetching chat history after timestamp: {e}")
        return []
    finally:
        cursor.close()
        conn.close()


def save_summary(
    cursor,  # For compatibility with existing interface
    user_id: str,
    conversation_id: str,
    summary: str,
    llm_model_version: Optional[str] = None,
    meta_json: Optional[Dict[str, Any]] = None,
    timestamp: Optional[datetime] = None
) -> Optional[str]:
    """
    Save a summary to the summary table.
    
    Args:
        cursor: Database cursor (for compatibility - not used in CouchDB)
        user_id: The user's unique identifier
        conversation_id: The conversation's unique identifier
        summary: The summary text content
        llm_model_version: The LLM model version used (optional)
        meta_json: Additional metadata as a dictionary (optional)
        timestamp: Summary timestamp (default: current time)
    
    Returns:
        str: The summary_id of the inserted summary, or None if failed
    """
    if timestamp is None:
        timestamp = datetime.now().isoformat()
    elif isinstance(timestamp, datetime):
        timestamp = timestamp.isoformat()
    
    # Generate a unique summary_id
    summary_id = str(uuid.uuid4())
    
    # Handle meta_json
    if meta_json is None:
        meta_json = {}
    
    db = get_couchdb_connection()
    
    try:
        # Create document
        doc = {
            '_id': summary_id,
            'type': 'summary',
            'user_id': user_id,
            'conversation_id': conversation_id,
            'timestamp': timestamp,
            'summary': summary,
            'llm_model_version': llm_model_version,
            'meta_json': meta_json
        }
        
        # Save to CouchDB
        result = db.save(doc)
        # Handle different return types from CouchDB save method
        if isinstance(result, tuple):
            doc_id, rev = result
        else:
            doc_id = result
        return summary_id
        
    except Exception as e:
        print(f"Error saving summary: {e}")
        return None


def fetch_orders_by_user(
    cursor,  # For compatibility with existing interface
    user_id: str, 
    broker_id: Optional[str] = None, 
    status: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Fetch all orders for a specific user with optional filtering by broker_id and status.
    
    Args:
        cursor: Database cursor (for compatibility - not used in CouchDB)
        user_id: The user's unique identifier
        broker_id: Optional broker ID to filter by (if None, returns orders from all brokers)
        status: Optional order status to filter by (if None, returns orders with all statuses)
    
    Returns:
        List[Dict[str, Any]]: List of order records as dictionaries
        
        Example: [{'order_id': '070df86b-b679-495b-8b0d-2aae60e6662f', 'user_id': 'user-456', 'account_id': 'acc-002', 'broker_id': None, 'instrument_id': 'inst-002', 'symbol': 'HDFC', 'broker_order_id': None, 'order_type': 'STOP_LIMIT', 'transaction_type': 'SELL', 'quantity': 650, 'price': None, 'trigger_price': None, 'status': 'OPEN', 'validity': 'GTT', 'product_type': 'NRML', 'created_at': '2025-07-17T11:17:56.034953+00:00', 'updated_at': '2025-07-17T11:39:56.034953+00:00', 'completed_at': None, 'parent_order_id': None, 'comments': 'Order placed for HDFC - Sample order 12', 'submitted_by': 'auto', 'source': 'api', 'llm_intent_id': None, 'strategy_id': None, 'is_automated': False, 'risk_score': 0.65, 'stop_loss_price': None, 'take_profit_price': None, 'trailing_stop_percent': None, 'portfolio_id': None, 'goal_id': None}]
    """
    db = get_couchdb_connection()
    results = []
    
    try:
        # Query CouchDB for order documents
        for doc_id in db:
            doc = db[doc_id]
            # Check for order documents - could be table_name or has order_id field
            is_order = (doc.get('table_name') == 'orders' or 
                       doc.get('_id', '').startswith('orders') or
                       'order_id' in doc)  # Has order_id field indicates order
            
            if (is_order and doc.get('user_id') == user_id):
                # Apply filters
                if broker_id and doc.get('broker_id') != broker_id:
                    continue
                if status and doc.get('status') != status:
                    continue
                
                results.append({
                    'order_id': doc.get('_id'),
                    'user_id': doc.get('user_id'),
                    'account_id': doc.get('account_id'),
                    'broker_id': doc.get('broker_id'),
                    'instrument_id': doc.get('instrument_id'),
                    'symbol': doc.get('symbol'),
                    'broker_order_id': doc.get('broker_order_id'),
                    'order_type': doc.get('order_type'),
                    'transaction_type': doc.get('transaction_type'),
                    'quantity': doc.get('quantity'),
                    'price': doc.get('price'),
                    'trigger_price': doc.get('trigger_price'),
                    'status': doc.get('status'),
                    'validity': doc.get('validity'),
                    'product_type': doc.get('product_type'),
                    'created_at': doc.get('created_at'),
                    'updated_at': doc.get('updated_at'),
                    'completed_at': doc.get('completed_at'),
                    'parent_order_id': doc.get('parent_order_id'),
                    'comments': doc.get('comments'),
                    'submitted_by': doc.get('submitted_by'),
                    'source': doc.get('source'),
                    'llm_intent_id': doc.get('llm_intent_id'),
                    'strategy_id': doc.get('strategy_id'),
                    'is_automated': doc.get('is_automated'),
                    'risk_score': doc.get('risk_score'),
                    'stop_loss_price': doc.get('stop_loss_price'),
                    'take_profit_price': doc.get('take_profit_price'),
                    'trailing_stop_percent': doc.get('trailing_stop_percent'),
                    'portfolio_id': doc.get('portfolio_id'),
                    'goal_id': doc.get('goal_id')
                })
        
        # Sort by created_at descending, handle None values
        results.sort(key=lambda x: x.get('created_at') or '', reverse=True)
        return results
        
    except Exception as e:
        print(f"Error fetching orders by user: {e}")
        return []


def fetch_monitoring_by_user(
    cursor,  # For compatibility with existing interface
    user_id: str, 
    status: Optional[str] = None, 
    time_from: Optional[datetime] = None
) -> List[Dict[str, Any]]:
    """
    Fetch all monitoring records for a specific user with optional filtering by status and time.
    
    Args:
        cursor: Database cursor (for compatibility - not used in CouchDB)
        user_id: The user's unique identifier
        status: Optional status to filter by (if None, returns records with all statuses)
        time_from: Optional timestamp to filter from (if None, returns all records)
    
    Returns:
        List[Dict[str, Any]]: List of monitoring records as dictionaries
        
        Example: [{'monitoring_id': '00c9804a-034a-414b-a719-d742b961b286', 'user_id': 'user-123', 'account_id': 'acc-001', 'broker_id': 'broker-001', 'symbol': 'TECHM', 'broker_trade_id': 'TRADE-E377D5FA', 'execution_time': '2025-07-17T07:08:46.176802', 'executed_quantity': 616, 'executed_price': 2441.77, 'transaction_type': 'SELL', 'exchange': 'BSE', 'brokerage_fee': 51.4, 'taxes_fees': 16.49, 'net_amount': 1504062.43, 'status': 'filled', 'exec_ref': 'EXEC-CE37345F', 'created_at': '2025-07-18T14:08:46.176808', 'order_type': 'STOP_LOSS', 'quantity': 93, 'price': 1792.17, 'trigger_price': 2624.6, 'validity': 'DAY', 'product_type': 'CNC', 'desc': 'Monitoring record for TECHM - Sample monitoring 16', 'stop_loss_price': None, 'take_profit_price': 589.39, 'trailing_stop_percent': 6.65}]
    """
    db = get_couchdb_connection()
    results = []
    
    try:
        # Convert time_from to string for comparison if provided
        time_from_str = None
        if time_from:
            if isinstance(time_from, datetime):
                time_from_str = time_from.isoformat()
            else:
                time_from_str = str(time_from)
        
        # Query CouchDB for monitoring documents
        for doc_id in db:
            doc = db[doc_id]
            # Check for monitoring documents - could be table_name or has monitoring_id field
            is_monitoring = (doc.get('table_name') == 'monitoring' or 
                           doc.get('_id', '').startswith('monitoring') or
                           'monitoring_id' in doc)  # Has monitoring_id field indicates monitoring
            
            if (is_monitoring and doc.get('user_id') == user_id):
                # Apply filters
                if status and doc.get('status') != status:
                    continue
                if time_from_str and doc.get('execution_time', '') < time_from_str:
                    continue
                
                results.append({
                    'monitoring_id': doc.get('_id'),
                    'user_id': doc.get('user_id'),
                    'account_id': doc.get('account_id'),
                    'broker_id': doc.get('broker_id'),
                    'symbol': doc.get('symbol'),
                    'broker_trade_id': doc.get('broker_trade_id'),
                    'execution_time': doc.get('execution_time'),
                    'executed_quantity': doc.get('executed_quantity'),
                    'executed_price': doc.get('executed_price'),
                    'transaction_type': doc.get('transaction_type'),
                    'exchange': doc.get('exchange'),
                    'brokerage_fee': doc.get('brokerage_fee'),
                    'taxes_fees': doc.get('taxes_fees'),
                    'net_amount': doc.get('net_amount'),
                    'status': doc.get('status'),
                    'exec_ref': doc.get('exec_ref'),
                    'created_at': doc.get('created_at'),
                    'order_type': doc.get('order_type'),
                    'quantity': doc.get('quantity'),
                    'price': doc.get('price'),
                    'trigger_price': doc.get('trigger_price'),
                    'validity': doc.get('validity'),
                    'product_type': doc.get('product_type'),
                    'desc': doc.get('desc'),
                    'stop_loss_price': doc.get('stop_loss_price'),
                    'take_profit_price': doc.get('take_profit_price'),
                    'trailing_stop_percent': doc.get('trailing_stop_percent')
                })
        
        # Sort by execution_time descending, handle None values
        results.sort(key=lambda x: x.get('execution_time') or '', reverse=True)
        return results
        
    except Exception as e:
        print(f"Error fetching monitoring by user: {e}")
        return []


def save_user_profile_simple(firebase_uid: str, name: str, phone: Optional[str] = None) -> str:
    """
    Save user profile to CouchDB.
    
    Args:
        firebase_uid: Firebase user UID
        name: User's full name
        phone: User's phone number (optional)
        
    Returns:
        Document ID of saved user profile
    """
    try:
        db = get_couchdb_connection()
        
        # Create user profile document
        doc_id = f"user_profile_{firebase_uid}"
        user_doc = {
            '_id': doc_id,
            'type': 'user_profile',
            'firebase_uid': firebase_uid,
            'name': name,
            'phone': phone,
            'created_at': datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat()
        }
        
        # Check if document already exists
        if doc_id in db:
            existing_doc = db[doc_id]
            user_doc['_rev'] = existing_doc['_rev']
            user_doc['created_at'] = existing_doc.get('created_at', user_doc['created_at'])
        
        # Save document
        result = db.save(user_doc)
        # Handle different return types from CouchDB save method
        if isinstance(result, tuple):
            doc_id, rev = result
        else:
            doc_id = result
        return doc_id
        
    except Exception as e:
        print(f"Error saving user profile: {e}")
        raise


def get_user_by_auth_uid_simple(firebase_uid: str) -> Optional[Dict[str, Any]]:
    """
    Get user profile by Firebase UID.
    
    Args:
        firebase_uid: Firebase user UID
        
    Returns:
        User profile dictionary or None if not found
    """
    try:
        db = get_couchdb_connection()
        doc_id = f"user_profile_{firebase_uid}"
        
        if doc_id in db:
            doc = db[doc_id]
            return {
                'firebase_uid': doc.get('firebase_uid'),
                'name': doc.get('name'),
                'phone': doc.get('phone'),
                'created_at': doc.get('created_at'),
                'updated_at': doc.get('updated_at')
            }
        return None
        
    except Exception as e:
        print(f"Error getting user by auth UID: {e}")
        return None


def check_user_exists_simple(firebase_uid: str) -> bool:
    """
    Check if user exists in the database.
    
    Args:
        firebase_uid: Firebase user UID
        
    Returns:
        True if user exists, False otherwise
    """
    try:
        db = get_couchdb_connection()
        doc_id = f"user_profile_{firebase_uid}"
        return doc_id in db
        
    except Exception as e:
        print(f"Error checking user existence: {e}")
        return False


def get_user_id_by_auth_uid_simple(firebase_uid: str) -> Optional[str]:
    """
    Get session user ID by Firebase UID.
    
    Args:
        firebase_uid: Firebase user UID
        
    Returns:
        Session user ID or None if not found
    """
    try:
        db = get_couchdb_connection()
        session_doc_id = f"user_session_{firebase_uid}"
        
        if session_doc_id in db:
            doc = db[session_doc_id]
            return doc.get('session_user_id')
        return None
        
    except Exception as e:
        print(f"Error getting user ID by auth UID: {e}")
        return None


def create_user_session_mapping_simple(firebase_uid: str) -> str:
    """
    Create user session mapping for Firebase UID.
    
    Args:
        firebase_uid: Firebase user UID
        
    Returns:
        Session user ID
    """
    try:
        db = get_couchdb_connection()
        
        # Generate a session user ID
        session_user_id = str(uuid.uuid4())
        
        # Create session mapping document
        session_doc_id = f"user_session_{firebase_uid}"
        session_doc = {
            '_id': session_doc_id,
            'type': 'user_session_mapping',
            'firebase_uid': firebase_uid,
            'session_user_id': session_user_id,
            'created_at': datetime.utcnow().isoformat()
        }
        
        # Check if document already exists
        if session_doc_id in db:
            existing_doc = db[session_doc_id]
            return existing_doc.get('session_user_id')
        
        # Save new session mapping
        result = db.save(session_doc)
        # Handle different return types from CouchDB save method
        if isinstance(result, tuple):
            doc_id, rev = result
        else:
            doc_id = result
        return session_user_id
        
    except Exception as e:
        print(f"Error creating user session mapping: {e}")
        raise