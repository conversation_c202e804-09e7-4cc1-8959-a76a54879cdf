#!/usr/bin/env python3
"""
Unified setup script for Data Layer V3.
Handles both environment configuration and package installation.
"""

import os
import sys
import subprocess
from pathlib import Path
from setuptools import setup, find_packages

def create_env_file():
    """Create a .env file with user input."""
    print("=== Data Layer V3 Environment Setup ===\n")
    print("This script will help you configure your database connection settings.")
    print("Press Enter to use default values.\n")
    
    # Get user input for database settings
    couchdb_host = input("CouchDB Host [localhost]: ").strip() or "localhost"
    couchdb_port = input("CouchDB Port [5984]: ").strip() or "5984"
    couchdb_username = input("CouchDB Username: ").strip()
    if not couchdb_username:
        print("❌ CouchDB Username is required!")
        return False
        
    couchdb_password = input("CouchDB Password: ").strip()
    if not couchdb_password:
        print("❌ CouchDB Password is required!")
        return False
    couchdb_database = input("CouchDB Database [aagmanai]: ").strip() or "aagmanai"
    
    print("\n--- PostgreSQL Settings (for SQL queries) ---")
    db_host = input("PostgreSQL Host [localhost]: ").strip() or "localhost"
    db_port = input("PostgreSQL Port [5432]: ").strip() or "5432"
    db_name = input("PostgreSQL Database [aagmanai]: ").strip() or "aagmanai"
    db_user = input("PostgreSQL Username [postgres]: ").strip() or "postgres"
    db_password = input("PostgreSQL Password [password]: ").strip() or "password"
    
    # Create .env file content
    env_content = f"""# Database Configuration
# Generated by setup.py

# CouchDB Configuration
COUCHDB_HOST={couchdb_host}
COUCHDB_PORT={couchdb_port}
COUCHDB_USERNAME={couchdb_username}
COUCHDB_PASSWORD={couchdb_password}
COUCHDB_DATABASE={couchdb_database}
COUCHDB_USE_SSL=false

# PouchDB Configuration
POUCHDB_NAME=aagmanai_local
POUCHDB_ADAPTER=idb

# PostgreSQL Configuration (for SQL queries)
DB_HOST={db_host}
DB_PORT={db_port}
DB_NAME={db_name}
DB_USER={db_user}
DB_PASSWORD={db_password}

# Connection Settings
DB_TIMEOUT=30
DB_MAX_RETRIES=3
DB_RETRY_DELAY=1.0
DB_POOL_SIZE=10

# Sync Settings
SYNC_ENABLED=true
SYNC_INTERVAL=5000
SYNC_LIVE=true
SYNC_RETRY=true

# Schema Settings
SCHEMA_VALIDATION_ENABLED=true
AUTO_CREATE_DESIGN_DOCS=true
"""
    
    # Write .env file
    env_file = Path(".env")
    with open(env_file, "w") as f:
        f.write(env_content)
    
    print(f"\n✅ Environment file created: {env_file.absolute()}")
    print("\nNext steps:")
    print("1. Review the .env file and update any values if needed")
    print("2. Run your scripts - they will now use these settings")
    
    return True

def check_env_file():
    """Check if .env file exists and show current settings."""
    env_file = Path(".env")
    
    if env_file.exists():
        print("✅ .env file found")
        print("\nCurrent settings:")
        
        # Load and display current settings
        try:
            from dotenv import load_dotenv
            load_dotenv()
            
            settings = [
                ("COUCHDB_HOST", "CouchDB Host"),
                ("COUCHDB_PORT", "CouchDB Port"),
                ("COUCHDB_USERNAME", "CouchDB Username"),
                ("COUCHDB_DATABASE", "CouchDB Database"),
                ("DB_HOST", "PostgreSQL Host"),
                ("DB_PORT", "PostgreSQL Port"),
                ("DB_NAME", "PostgreSQL Database"),
                ("DB_USER", "PostgreSQL Username"),
            ]
            
            for key, label in settings:
                value = os.getenv(key, "Not set")
                print(f"  {label}: {value}")
                
        except ImportError:
            print("  python-dotenv not installed. Install with: pip install python-dotenv")
            
    else:
        print("❌ .env file not found")
        print("Run this script to create one: python setup.py --env")
    
    return env_file.exists()

def install_dependencies():
    """Install required dependencies."""
    print("=== Installing Dependencies ===\n")
    
    try:
        # Read requirements
        with open("requirements.txt") as f:
            requirements = f.read().splitlines()
        
        print("Installing packages from requirements.txt...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully!")
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False
    except FileNotFoundError:
        print("❌ requirements.txt not found!")
        return False

def run_setup():
    """Run the complete setup process."""
    print("=== Data Layer V3 Complete Setup ===\n")
    
    # Step 1: Install dependencies
    print("Step 1: Installing dependencies...")
    if not install_dependencies():
        print("Setup failed at dependency installation step.")
        return False
    
    # Step 2: Environment setup
    print("\nStep 2: Environment configuration...")
    if check_env_file():
        response = input("\nDo you want to recreate the .env file? (y/N): ").strip().lower()
        if response != 'y':
            print("Environment setup skipped.")
        else:
            if not create_env_file():
                print("Setup failed at environment configuration step.")
                return False
    else:
        if not create_env_file():
            print("Setup failed at environment configuration step.")
            return False
    
    print("\n🎉 Setup completed successfully!")
    print("\nYou can now:")
    print("- Import and use the data layer modules")
    print("- Run database operations with your configured settings")
    print("- Check your configuration anytime with: python setup.py --check")
    
    return True

def main():
    """Main function that handles different setup modes."""
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "--env":
            # Environment setup only
            if check_env_file():
                response = input("\nDo you want to recreate the .env file? (y/N): ").strip().lower()
                if response != 'y':
                    print("Setup cancelled.")
                    return
            create_env_file()
            
        elif command == "--check":
            # Check environment only
            check_env_file()
            
        elif command == "--install":
            # Install dependencies only
            install_dependencies()
            
        elif command == "--help":
            print("Data Layer V3 Setup Script")
            print("\nUsage:")
            print("  python setup.py              # Complete setup (dependencies + environment)")
            print("  python setup.py --env        # Environment setup only")
            print("  python setup.py --check      # Check current environment")
            print("  python setup.py --install    # Install dependencies only")
            print("  python setup.py --help       # Show this help")
            
        else:
            print(f"Unknown command: {command}")
            print("Use --help for available options.")
    else:
        # No arguments - run complete setup
        run_setup()

# Standard setuptools setup for package installation
def setup_package():
    """Standard setuptools setup function."""
    with open("requirements.txt") as f:
        requirements = f.read().splitlines()

    setup(
        name="data-layer-v3",
        version="3.0.0",
        description="Data Layer V3 - CouchDB with PouchDB Local Database",
        author="Smart Agent Team",
        packages=find_packages(),
        install_requires=requirements,
        python_requires=">=3.8",
        classifiers=[
            "Development Status :: 4 - Beta",
            "Intended Audience :: Developers",
            "License :: OSI Approved :: MIT License",
            "Programming Language :: Python :: 3",
            "Programming Language :: Python :: 3.8",
            "Programming Language :: Python :: 3.9",
            "Programming Language :: Python :: 3.10",
            "Programming Language :: Python :: 3.11",
        ],
    )

if __name__ == "__main__":
    # Check if this is being run as a script or as part of setuptools
    if len(sys.argv) > 1 and sys.argv[1] in ["--env", "--check", "--install", "--help"]:
        main()
    elif len(sys.argv) > 1 and sys.argv[1] in ["install", "develop", "build", "sdist", "bdist_wheel", "editable_wheel"]:
        # This is being run by setuptools (pip install -e .)
        setup_package()
    else:
        # No arguments - run complete interactive setup
        main()
else:
    # This file is being imported by setuptools, so call setup()
    setup_package() 