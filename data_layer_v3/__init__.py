"""
Data Layer V3 - CouchDB with PouchDB Local Database

A hybrid data layer that connects to CouchDB for remote storage
and uses PouchDB for local database operations.
"""

"""
Data Layer V3 - CouchDB with PouchDB Local Database

A hybrid data layer that connects to CouchDB for remote storage
and uses PouchDB for local database operations.
"""

from .config import DataLayerConfig, ConfigurationError
from .data_layer import DataLayer

__version__ = "3.0.0"
__all__ = [
    "DataLayer",
    "DataLayerConfig",
    "ConfigurationError"
] 