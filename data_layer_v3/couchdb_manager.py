"""
CouchDB Manager for Data Layer V3.

Handles remote CouchDB operations and synchronization.
"""

import requests
import structlog
from typing import Dict, Any, List, Optional
from config import DataLayerConfig
from exceptions import CouchDBError, ConnectionError

logger = structlog.get_logger(__name__)


class CouchDBManager:
    """Manages CouchDB remote operations."""
    
    def __init__(self, config: DataLayerConfig):
        """Initialize CouchDB manager."""
        self.config = config
        self.session = requests.Session()
        self.session.auth = (config.couchdb_username, config.couchdb_password)
        self.session.timeout = config.timeout
        
    def test_connection(self) -> bool:
        """Test connection to CouchDB server."""
        try:
            response = self.session.get(f"{self.config.couchdb_url}/")
            response.raise_for_status()
            return True
        except Exception as e:
            logger.error("Failed to connect to CouchDB", error=str(e))
            return False
    
    def create_database(self) -> bool:
        """Create the database if it doesn't exist."""
        try:
            response = self.session.put(self.config.database_url)
            if response.status_code == 201:
                logger.info("Database created successfully", database=self.config.couchdb_database)
                return True
            elif response.status_code == 412:
                logger.info("Database already exists", database=self.config.couchdb_database)
                return True
            else:
                response.raise_for_status()
        except Exception as e:
            logger.error("Failed to create database", error=str(e))
            raise CouchDBError(f"Failed to create database: {e}")
    
    def get_document(self, doc_id: str) -> Optional[Dict[str, Any]]:
        """Get a document from CouchDB."""
        try:
            response = self.session.get(f"{self.config.database_url}/{doc_id}")
            if response.status_code == 404:
                return None
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error("Failed to get document", doc_id=doc_id, error=str(e))
            raise CouchDBError(f"Failed to get document {doc_id}: {e}")
    
    def save_document(self, doc_id: str, document: Dict[str, Any]) -> Dict[str, Any]:
        """Save a document to CouchDB."""
        try:
            response = self.session.put(f"{self.config.database_url}/{doc_id}", json=document)
            response.raise_for_status()
            result = response.json()
            logger.info("Document saved successfully", doc_id=doc_id, rev=result.get("rev"))
            return result
        except Exception as e:
            logger.error("Failed to save document", doc_id=doc_id, error=str(e))
            raise CouchDBError(f"Failed to save document {doc_id}: {e}")
    
    def delete_document(self, doc_id: str, rev: str) -> bool:
        """Delete a document from CouchDB."""
        try:
            response = self.session.delete(f"{self.config.database_url}/{doc_id}?rev={rev}")
            response.raise_for_status()
            logger.info("Document deleted successfully", doc_id=doc_id)
            return True
        except Exception as e:
            logger.error("Failed to delete document", doc_id=doc_id, error=str(e))
            raise CouchDBError(f"Failed to delete document {doc_id}: {e}")
    
    def find_documents(self, selector: Dict[str, Any], limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Find documents using Mango query."""
        try:
            query = {"selector": selector}
            if limit:
                query["limit"] = limit
            
            response = self.session.post(f"{self.config.database_url}/_find", json=query)
            response.raise_for_status()
            result = response.json()
            return result.get("docs", [])
        except Exception as e:
            logger.error("Failed to find documents", selector=selector, error=str(e))
            raise CouchDBError(f"Failed to find documents: {e}")
    
    def get_all_documents(self, include_docs: bool = True) -> List[Dict[str, Any]]:
        """Get all documents from the database."""
        try:
            params = {"include_docs": "true" if include_docs else "false"}
            response = self.session.get(f"{self.config.database_url}/_all_docs", params=params)
            response.raise_for_status()
            result = response.json()
            return result.get("rows", [])
        except Exception as e:
            logger.error("Failed to get all documents", error=str(e))
            raise CouchDBError(f"Failed to get all documents: {e}")
    
    def bulk_docs(self, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Bulk save documents."""
        try:
            response = self.session.post(f"{self.config.database_url}/_bulk_docs", json={"docs": documents})
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error("Failed to bulk save documents", error=str(e))
            raise CouchDBError(f"Failed to bulk save documents: {e}")
    
    def get_changes(self, since: Optional[str] = None, limit: Optional[int] = None) -> Dict[str, Any]:
        """Get changes feed."""
        try:
            params = {}
            if since:
                params["since"] = since
            if limit:
                params["limit"] = limit
            
            response = self.session.get(f"{self.config.database_url}/_changes", params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error("Failed to get changes", error=str(e))
            raise CouchDBError(f"Failed to get changes: {e}")
    
    def create_design_doc(self, design_doc_id: str, views: Dict[str, Any]) -> bool:
        """Create a design document."""
        try:
            design_doc = {
                "_id": f"_design/{design_doc_id}",
                "views": views
            }
            response = self.session.put(f"{self.config.database_url}/_design/{design_doc_id}", json=design_doc)
            response.raise_for_status()
            logger.info("Design document created", design_doc_id=design_doc_id)
            return True
        except Exception as e:
            logger.error("Failed to create design document", design_doc_id=design_doc_id, error=str(e))
            raise CouchDBError(f"Failed to create design document: {e}")
    
    def query_view(self, design_doc: str, view_name: str, **params) -> List[Dict[str, Any]]:
        """Query a view."""
        try:
            response = self.session.get(f"{self.config.database_url}/_design/{design_doc}/_view/{view_name}", params=params)
            response.raise_for_status()
            result = response.json()
            return result.get("rows", [])
        except Exception as e:
            logger.error("Failed to query view", design_doc=design_doc, view_name=view_name, error=str(e))
            raise CouchDBError(f"Failed to query view: {e}") 